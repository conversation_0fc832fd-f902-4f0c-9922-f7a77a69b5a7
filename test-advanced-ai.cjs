const axios = require('axios');

async function testAdvancedAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test advanced AI features
    console.log('\n🧠 Testing Advanced AI Assistant Features...\n');
    
    const testMessages = [
      // Test 1: Emotional Intelligence - Sadness
      {
        message: "I'm feeling really sad and overwhelmed today 😢",
        description: "Testing emotional intelligence - sadness detection"
      },
      
      // Test 2: Spell Correction
      {
        message: "Can you chek my leav balanse? I ned to tak som tim of",
        description: "Testing spell correction and typo tolerance"
      },
      
      // Test 3: Emotional Intelligence - Anger
      {
        message: "I'm so frustrated with my workload! This is ridiculous and I'm angry!",
        description: "Testing emotional intelligence - anger detection"
      },
      
      // Test 4: Context Awareness
      {
        message: "Hello! I'm excited about my new project!",
        description: "Testing emotional intelligence - joy detection"
      },
      
      // Test 5: Casual Language Understanding
      {
        message: "hey whats up? can u help me w/ my tasks plz?",
        description: "Testing casual language and abbreviation understanding"
      },
      
      // Test 6: Complex Typos with Emotional Context
      {
        message: "im realy stresed abt my performence reveiw tomoro",
        description: "Testing complex typos with anxiety detection"
      },
      
      // Test 7: Urgency Detection
      {
        message: "URGENT: I need to check in for work ASAP!",
        description: "Testing urgency detection and priority handling"
      },
      
      // Test 8: Gratitude and Positive Emotions
      {
        message: "Thank you so much! You're amazing and I really appreciate your help! 🙏",
        description: "Testing gratitude detection and positive sentiment"
      }
    ];

    for (let i = 0; i < testMessages.length; i++) {
      const test = testMessages[i];
      console.log(`\n📝 Test ${i + 1}: ${test.description}`);
      console.log(`👤 User: "${test.message}"`);
      
      try {
        const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
          message: test.message
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = response.data.data;
        const assistantMessage = data.assistantMessage;
        const classification = data.classification;

        console.log(`🤖 Assistant: ${assistantMessage.content.substring(0, 200)}...`);
        console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
        
        // Show advanced analysis if available
        if (classification.metadata) {
          console.log(`🧠 Advanced Analysis:`);
          if (classification.metadata.hasTypos) {
            console.log(`   📝 Corrected: "${classification.metadata.correctedText}"`);
          }
          if (classification.metadata.emotionalState) {
            console.log(`   💭 Emotion: ${classification.metadata.emotionalState}`);
          }
          if (classification.metadata.urgency !== 'normal') {
            console.log(`   ⚡ Urgency: ${classification.metadata.urgency}`);
          }
          if (classification.reason) {
            console.log(`   🔍 Reason: ${classification.reason}`);
          }
        }

        // Show suggestions if available
        if (data.suggestions && data.suggestions.length > 0) {
          console.log(`💡 Suggestions: ${data.suggestions.slice(0, 3).join(', ')}`);
        }

        console.log(`⚡ Response time: ${assistantMessage.metadata.responseTime}ms`);
        
      } catch (error) {
        console.error(`❌ Error in test ${i + 1}:`, error.response?.data?.message || error.message);
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n🎉 Advanced AI Testing Complete!');
    console.log('\n📊 Summary of Advanced Features Tested:');
    console.log('✅ Emotion Detection (sadness, anger, joy, gratitude)');
    console.log('✅ Spell Correction & Typo Tolerance');
    console.log('✅ Casual Language Understanding');
    console.log('✅ Context Awareness');
    console.log('✅ Urgency Detection');
    console.log('✅ Sentiment Analysis');
    console.log('✅ Empathetic Responses');
    console.log('✅ Intelligent Fallbacks');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testAdvancedAI();
