const axios = require('axios');

async function testSimpleAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test simple message
    console.log('\n🤖 Testing simple AI message...');
    
    try {
      const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'Hello!'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📦 Response:', JSON.stringify(response.data, null, 2));
      
    } catch (error) {
      console.error('❌ Chat error details:');
      console.error('Status:', error.response?.status);
      console.error('Data:', error.response?.data);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testSimpleAI();
