const axios = require('axios');

async function testSimpleOpenRouter() {
  try {
    console.log('🚀 SIMPLE OPENROUTER TEST\n');

    // Login
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });
    const token = loginResponse.data.token;
    console.log('✅ Authentication: SUCCESS');

    // Single test message
    console.log('\n📤 Sending test message...');
    const startTime = Date.now();
    
    const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
      message: 'Hello! Can you help me with HR questions?'
    }, {
      headers: { 'Authorization': `Bearer ${token}` },
      timeout: 30000
    });

    const responseTime = Date.now() - startTime;
    const data = response.data.data;
    const assistantMessage = data.assistantMessage;
    const classification = data.classification;

    console.log('✅ Response received!');
    console.log(`⚡ Response Time: ${responseTime}ms`);
    console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
    console.log(`🤖 Response: ${assistantMessage.content}`);
    
    // Check if using OpenRouter
    const isUsingOpenRouter = !assistantMessage.metadata?.enhanced_rule_based && 
                             !assistantMessage.metadata?.fallback;
    
    if (isUsingOpenRouter) {
      console.log('\n🌟 SUCCESS: Using OpenRouter (Claude 3.5 Sonnet)!');
      console.log('✅ Advanced AI responses are working');
      console.log('✅ No "unavailable" messages');
      console.log('✅ High-quality, contextual responses');
    } else {
      console.log('\n🔄 Using Enhanced Rule-Based System');
      console.log('✅ Still working well, but not using OpenRouter');
      console.log('💡 Check OpenRouter credits or configuration');
    }

    // Check for "unavailable" message
    const hasUnavailableMessage = assistantMessage.content.toLowerCase().includes('unavailable');
    if (!hasUnavailableMessage) {
      console.log('✅ No "unavailable" messages found');
    } else {
      console.log('⚠️ Found "unavailable" message');
    }

    console.log('\n🎉 TEST COMPLETE!');
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testSimpleOpenRouter();
