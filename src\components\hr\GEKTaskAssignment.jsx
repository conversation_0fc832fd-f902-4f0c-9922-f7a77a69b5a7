import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Card,
  CardContent,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  IconButton,
  Tooltip,
  Alert,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  Assignment,
  Add,
  Person,
  Group,
  Search,
  Close,
  CheckCircle,
  ArrowForward,
  Info,
  CalendarToday,
  PriorityHigh,
  Category as CategoryIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { showSuccessToast, showErrorToast, TOAST_CATEGORIES } from '../../Utils/toastUtils';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import GEKService from '../../Services/GEKService';
import TaskService from '../../Services/TaskService';

/**
 * GEK Task Assignment Component
 * Allows HR to create tasks and assign them to the most suitable employees
 * based on GEK fit scores and performance metrics
 */
const GEKTaskAssignment = () => {
  // State for task creation
  const [activeStep, setActiveStep] = useState(0);
  const [taskData, setTaskData] = useState({
    title: '',
    description: '',
    category: 'General',
    priority: 'Medium',
    deadline: null,
    assignedTo: ''
  });

  // State for employee selection
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [recommendedEmployees, setRecommendedEmployees] = useState([]);
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  // State for dialogs
  const [openDialog, setOpenDialog] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // Real data for task categories from the GEK model
  const taskCategories = [
    'General', 'Development', 'Design', 'Marketing', 'HR',
    'Finance', 'Operations', 'Project', 'Administrative',
    'Training', 'Evaluation', 'Other'
  ];

  // Real data for task priorities from the GEK model
  const taskPriorities = ['Low', 'Medium', 'High', 'Urgent'];

  // Steps for the task creation process
  const steps = ['Task Details', 'Employee Selection', 'Review & Assign'];

  // Handle task data changes
  const handleTaskDataChange = (field) => (event) => {
    setTaskData({
      ...taskData,
      [field]: event.target.value
    });
  };

  // Handle date change
  const handleDateChange = (date) => {
    setTaskData({
      ...taskData,
      deadline: date
    });
  };

  // Handle finding recommended employees
  const handleFindEmployees = async () => {
    if (!taskData.title || !taskData.category || !taskData.priority) {
      showErrorToast('Please fill in all required fields', TOAST_CATEGORIES.VALIDATION, 'validation');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get recommended employees for the task
      const response = await GEKService.getRecommendedEmployees({
        category: taskData.category,
        priority: taskData.priority,
        estimatedHours: 0, // Let the system estimate
        limit: 10 // Limit to 10 results
      });

      if (response && response.recommendedEmployees) {
        setRecommendedEmployees(response.recommendedEmployees);
        setActiveStep(1);
      } else {
        setError('Invalid response format from the server');
        setRecommendedEmployees([]);
      }
    } catch (err) {
      console.error('Error finding recommended employees:', err);
      setError(err.friendlyMessage || 'Failed to find recommended employees');
      setRecommendedEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle employee selection
  const handleEmployeeSelect = async (employee) => {
    setSelectedEmployee(employee);

    // If we're already at the review step, no need to fetch additional data
    if (activeStep === 2) return;

    setLoading(true);
    setError(null);

    try {
      // Get detailed fit score for the selected employee
      const fitScoreResponse = await GEKService.getFitScore(
        employee._id,
        taskData.category,
        taskData.priority,
        true // Force recalculation
      );

      if (fitScoreResponse && fitScoreResponse.estimate) {
        // Update the employee with more detailed information
        setSelectedEmployee({
          ...employee,
          ...fitScoreResponse.estimate
        });
      }
    } catch (err) {
      console.error('Error getting detailed fit score:', err);
      // Don't set error here, as it's not critical
    } finally {
      setLoading(false);
    }
  };

  // Handle task assignment
  const handleAssignTask = async () => {
    if (!selectedEmployee) {
      showErrorToast('Please select an employee', TOAST_CATEGORIES.VALIDATION, 'validation');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare the task data
      const taskDetails = {
        ...taskData,
        assignedTo: selectedEmployee._id,
        estimatedCompletionTime: selectedEmployee.estimatedCompletionTime || 0,
        fitScore: selectedEmployee.fitScore || 0
      };

      // Validate the task data
      if (!taskDetails.title || !taskDetails.category || !taskDetails.priority || !taskDetails.assignedTo) {
        throw new Error('Missing required task fields');
      }

      setConfirmDialogOpen(true);
    } catch (err) {
      console.error('Error preparing task assignment:', err);
      setError(err.message || 'Failed to prepare task assignment');
    } finally {
      setLoading(false);
    }
  };

  // Handle task confirmation
  const handleConfirmAssignment = async () => {
    setLoading(true);
    setError(null);

    try {
      // Create the task
      const taskDetails = {
        title: taskData.title,
        description: taskData.description,
        category: taskData.category,
        priority: taskData.priority,
        deadline: taskData.deadline,
        assignedTo: selectedEmployee._id,
        estimatedCompletionTime: selectedEmployee.estimatedCompletionTime || 0,
        fitScore: selectedEmployee.fitScore || 0
      };

      const response = await TaskService.createTask(taskDetails);

      if (response && response.task) {
        showSuccessToast(
          `Task "${taskData.title}" assigned to ${selectedEmployee.name}`,
          TOAST_CATEGORIES.TASK,
          'taskAssigned'
        );

        // Reset state
        setTaskData({
          title: '',
          description: '',
          category: 'General',
          priority: 'Medium',
          deadline: null,
          assignedTo: ''
        });
        setRecommendedEmployees([]);
        setSelectedEmployee(null);
        setActiveStep(0);
        setConfirmDialogOpen(false);
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (err) {
      console.error('Error assigning task:', err);
      setError(err.friendlyMessage || 'Failed to assign task');
      showErrorToast(
        err.friendlyMessage || 'Failed to assign task',
        TOAST_CATEGORIES.TASK,
        'taskAssignmentFailed'
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle step navigation
  const handleNext = () => {
    if (activeStep === 0) {
      handleFindEmployees();
    } else if (activeStep === 1) {
      setActiveStep(2);
    } else if (activeStep === 2) {
      handleAssignTask();
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Render task details form
  const renderTaskDetailsForm = () => {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            label="Task Title"
            fullWidth
            required
            value={taskData.title}
            onChange={handleTaskDataChange('title')}
            placeholder="Enter a clear and concise task title"
            error={!taskData.title && taskData.title !== undefined}
            helperText={!taskData.title && taskData.title !== undefined ? 'Title is required' : ''}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            label="Description"
            fullWidth
            multiline
            rows={4}
            value={taskData.description}
            onChange={handleTaskDataChange('description')}
            placeholder="Provide detailed instructions and requirements for the task"
          />
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth required error={!taskData.category}>
            <InputLabel>Category</InputLabel>
            <Select
              value={taskData.category}
              label="Category"
              onChange={handleTaskDataChange('category')}
            >
              {taskCategories.map((category) => (
                <MenuItem key={category} value={category}>{category}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <FormControl fullWidth required error={!taskData.priority}>
            <InputLabel>Priority</InputLabel>
            <Select
              value={taskData.priority}
              label="Priority"
              onChange={handleTaskDataChange('priority')}
            >
              {taskPriorities.map((priority) => (
                <MenuItem key={priority} value={priority}>{priority}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={4}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Deadline"
              value={taskData.deadline}
              onChange={handleDateChange}
              slotProps={{ textField: { fullWidth: true, required: true } }}
              minDate={new Date()}
            />
          </LocalizationProvider>
        </Grid>
      </Grid>
    );
  };

  // Render employee selection
  const renderEmployeeSelection = () => {
    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          Based on the task details, the GEK system has identified the following employees as the best matches for this task.
        </Alert>

        {error && (
          <Alert
            severity="error"
            sx={{ mb: 3 }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={handleFindEmployees}
              >
                Retry
              </Button>
            }
          >
            <Typography variant="body2">{error}</Typography>
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        ) : recommendedEmployees.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="text.secondary">
              No recommended employees found. Try adjusting the task details.
            </Typography>
            <Button
              variant="outlined"
              sx={{ mt: 2 }}
              onClick={handleFindEmployees}
              startIcon={<RefreshIcon />}
            >
              Try Again
            </Button>
          </Box>
        ) : (
          <List>
            {recommendedEmployees.map((employee, index) => (
              <React.Fragment key={employee.user._id}>
                <ListItem
                  button
                  onClick={() => handleEmployeeSelect(employee.user)}
                  selected={selectedEmployee && selectedEmployee._id === employee.user._id}
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    border: selectedEmployee && selectedEmployee._id === employee.user._id ?
                      '1px solid' : 'none',
                    borderColor: 'primary.main'
                  }}
                >
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: index < 3 ? 'secondary.main' : 'primary.main' }}>
                      {index + 1}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={employee.user.name}
                    secondary={`${employee.user.job} • ${employee.user.department}`}
                  />
                  <Box sx={{ display: 'flex', alignItems: 'center', flexDirection: 'column', minWidth: 100 }}>
                    <Typography variant="h6" color="primary" fontWeight="bold">
                      {Math.round(employee.fitScore)}%
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Fit Score
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', flexDirection: 'column', minWidth: 120 }}>
                    <Typography variant="body2">
                      ~{Math.round(employee.estimatedCompletionTime)} hours
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Est. Completion
                    </Typography>
                  </Box>
                </ListItem>
                {index < recommendedEmployees.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>
    );
  };

  // Render review and assign
  const renderReviewAndAssign = () => {
    if (!selectedEmployee) {
      return (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography color="text.secondary">
            Please go back and select an employee to assign the task to.
          </Typography>
        </Box>
      );
    }

    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          Please review the task details and employee assignment before confirming.
        </Alert>

        {error && (
          <Alert
            severity="error"
            sx={{ mb: 3 }}
          >
            <Typography variant="body2">{error}</Typography>
          </Alert>
        )}

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <Assignment sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Task Details
                </Typography>

                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">Title</Typography>
                  <Typography variant="body1" fontWeight="medium" gutterBottom>
                    {taskData.title}
                  </Typography>

                  <Typography variant="body2" color="text.secondary">Description</Typography>
                  <Typography variant="body1" paragraph>
                    {taskData.description || 'No description provided'}
                  </Typography>

                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Category</Typography>
                      <Chip
                        label={taskData.category}
                        size="small"
                        icon={<CategoryIcon fontSize="small" />}
                      />
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Priority</Typography>
                      <Chip
                        label={taskData.priority}
                        size="small"
                        color={
                          taskData.priority === 'High' || taskData.priority === 'Urgent'
                            ? 'error'
                            : taskData.priority === 'Medium'
                            ? 'warning'
                            : 'success'
                        }
                        icon={<PriorityHigh fontSize="small" />}
                      />
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Deadline</Typography>
                      <Chip
                        label={taskData.deadline ? taskData.deadline.toLocaleDateString() : 'None'}
                        size="small"
                        icon={<CalendarToday fontSize="small" />}
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Assigned Employee
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                  <Avatar sx={{ width: 56, height: 56, mr: 2, bgcolor: 'primary.main' }}>
                    {selectedEmployee.name.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{selectedEmployee.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedEmployee.job} • {selectedEmployee.department}
                    </Typography>
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Fit Score</Typography>
                    <Typography variant="h6" color="primary" fontWeight="bold">
                      {Math.round(selectedEmployee.fitScore)}%
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Est. Completion Time</Typography>
                    <Typography variant="body1">
                      ~{Math.round(selectedEmployee.estimatedCompletionTime)} hours
                    </Typography>
                  </Grid>

                  {selectedEmployee.completionRate && (
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Completion Rate</Typography>
                      <Typography variant="body1">
                        {selectedEmployee.completionRate}%
                      </Typography>
                    </Grid>
                  )}

                  {selectedEmployee.onTimeRate && (
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">On-Time Rate</Typography>
                      <Typography variant="body1">
                        {selectedEmployee.onTimeRate}%
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render the current step content
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return renderTaskDetailsForm();
      case 1:
        return renderEmployeeSelection();
      case 2:
        return renderReviewAndAssign();
      default:
        return null;
    }
  };

  // Render the main component
  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          GEK Task Assignment
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Create tasks and assign them to the most suitable employees based on GEK fit scores and performance metrics.
        </Typography>
      </Box>

      <Paper sx={{ p: 3, mb: 4 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <Box sx={{ mt: 2, mb: 4 }}>
          {renderStepContent()}
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            disabled={activeStep === 0 || loading}
            onClick={handleBack}
          >
            Back
          </Button>

          <Button
            variant="contained"
            color="primary"
            onClick={handleNext}
            disabled={loading || (activeStep === 0 && (!taskData.title || !taskData.category || !taskData.priority)) || (activeStep === 1 && !selectedEmployee)}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : (activeStep === 2 ? <CheckCircle /> : <ArrowForward />)}
          >
            {loading ? 'Processing...' : (activeStep === 0 ? 'Find Best Matches' : activeStep === 1 ? 'Continue' : 'Assign Task')}
          </Button>
        </Box>
      </Paper>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => !loading && setConfirmDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Confirm Task Assignment
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert
              severity="error"
              sx={{ mb: 3 }}
            >
              <Typography variant="body2">{error}</Typography>
            </Alert>
          )}

          <Typography variant="body1" paragraph>
            Are you sure you want to assign the task "{taskData.title}" to {selectedEmployee?.name}?
          </Typography>
          <Typography variant="body2" color="text.secondary">
            The employee will be notified and the task will appear in their dashboard.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDialogOpen(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleConfirmAssignment}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <CheckCircle />}
          >
            {loading ? 'Assigning...' : 'Confirm Assignment'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GEKTaskAssignment;
