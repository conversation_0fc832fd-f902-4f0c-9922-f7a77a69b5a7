// Simple test script to verify AI Assistant functionality
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Test data
const testUser = {
  email: '<EMAIL>',
  password: 'test123'
};

let authToken = '';

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${API_BASE}/auth/login`, testUser);
    
    if (response.data.success) {
      authToken = response.data.token;
      console.log('✅ Login successful');
      return true;
    } else {
      console.log('❌ Login failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function testHealthCheck() {
  try {
    console.log('\n🏥 Testing AI Assistant health...');
    const response = await axios.get(`${API_BASE}/ai/health`);
    
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Health check failed:', error.response?.data || error.message);
    return false;
  }
}

async function testIntentClassification() {
  try {
    console.log('\n🧠 Testing intent classification...');
    
    const testMessages = [
      'I want to request leave',
      'Show my tasks',
      'Check my leave balance',
      'Hello there'
    ];

    for (const message of testMessages) {
      const response = await axios.post(
        `${API_BASE}/ai/classify-intent`,
        { text: message },
        { headers: { Authorization: `Bearer ${authToken}` } }
      );

      const { classification, entities } = response.data.data;
      console.log(`📝 "${message}" -> Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
    }
    
    return true;
  } catch (error) {
    console.log('❌ Intent classification failed:', error.response?.data || error.message);
    return false;
  }
}

async function testChatMessage() {
  try {
    console.log('\n💬 Testing chat message...');
    
    const response = await axios.post(
      `${API_BASE}/ai/chat/message`,
      { 
        message: 'Hello, I need help with my leave balance',
        context: {}
      },
      { headers: { Authorization: `Bearer ${authToken}` } }
    );

    if (response.data.success) {
      const { userMessage, assistantMessage, classification } = response.data.data;
      console.log('✅ Chat message successful');
      console.log('👤 User:', userMessage.content);
      console.log('🤖 Assistant:', assistantMessage.content);
      console.log('🎯 Intent:', classification.intent);
      return true;
    } else {
      console.log('❌ Chat message failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Chat message error:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting AI Assistant Tests\n');
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Login', fn: login },
    { name: 'Intent Classification', fn: testIntentClassification },
    { name: 'Chat Message', fn: testChatMessage }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
  }

  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! AI Assistant is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs above for details.');
  }
}

// Run the tests
runTests().catch(console.error);
