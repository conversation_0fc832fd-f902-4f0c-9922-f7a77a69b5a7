const User = require('../../models/user');
const LeaveRequest = require('../../models/LeaveRequest');
const Task = require('../../models/Task');

class ContextualSuggestionEngine {
  constructor() {
    this.suggestionCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Generate contextual suggestions based on user action and data
   */
  async generateSuggestions(userId, contextType, action, data = {}) {
    try {
      const cacheKey = `${userId}_${contextType}_${action}_${JSON.stringify(data)}`;
      
      // Check cache first
      if (this.suggestionCache.has(cacheKey)) {
        const cached = this.suggestionCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.suggestions;
        }
      }

      let suggestions = [];

      switch (contextType) {
        case 'leave_request_form':
          suggestions = await this.getLeaveRequestSuggestions(userId, action, data);
          break;
        case 'leave_request_dialog':
          suggestions = await this.getLeaveDialogSuggestions(userId, action, data);
          break;
        case 'user_management_form':
          suggestions = await this.getUserManagementSuggestions(userId, action, data);
          break;
        case 'task_management':
          suggestions = await this.getTaskManagementSuggestions(userId, action, data);
          break;
        default:
          suggestions = this.getGenericSuggestions(contextType, action);
      }

      // Cache the suggestions
      this.suggestionCache.set(cacheKey, {
        suggestions,
        timestamp: Date.now()
      });

      return suggestions;
    } catch (error) {
      console.error('Error generating contextual suggestions:', error);
      return this.getFallbackSuggestions(contextType);
    }
  }

  /**
   * Get leave request specific suggestions
   */
  async getLeaveRequestSuggestions(userId, action, data) {
    const user = await User.findById(userId).select('name role department');
    const currentYear = new Date().getFullYear();
    
    // Get user's leave history
    const leaveHistory = await LeaveRequest.find({
      userId,
      createdAt: { $gte: new Date(currentYear, 0, 1) }
    }).sort({ createdAt: -1 }).limit(5);

    const suggestions = [];

    // Base suggestions
    suggestions.push('📊 Check your current leave balance before submitting');
    suggestions.push('📅 Ensure your leave dates don\'t conflict with important deadlines');

    // Action-specific suggestions
    if (action === 'leave_type_selected' && data.leaveType) {
      suggestions.push(...this.getLeaveTypeSuggestions(data.leaveType));
    }

    if (action === 'dates_entered' && data.startDate && data.endDate) {
      const dateSuggestions = await this.getDateSpecificSuggestions(userId, data.startDate, data.endDate);
      suggestions.push(...dateSuggestions);
    }

    // Historical analysis
    if (leaveHistory.length > 0) {
      const rejectedRequests = leaveHistory.filter(req => req.status === 'Rejected');
      if (rejectedRequests.length > 0) {
        suggestions.push('⚠️ Review previous rejection reasons to improve approval chances');
      }

      const recentRequests = leaveHistory.filter(req => 
        new Date(req.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      );
      if (recentRequests.length > 2) {
        suggestions.push('📈 You\'ve submitted several requests recently - consider consolidating if possible');
      }
    }

    // Role-specific suggestions
    if (user.role === 'manager' || user.role === 'admin') {
      suggestions.push('👥 Consider team coverage when planning your leave');
    }

    return suggestions.slice(0, 6); // Limit to 6 suggestions
  }

  /**
   * Get leave type specific suggestions
   */
  getLeaveTypeSuggestions(leaveType) {
    const typeSpecific = {
      'Annual Leave': [
        '🏖️ Plan annual leave during less busy periods',
        '📋 Coordinate with team members for coverage'
      ],
      'Sick Leave': [
        '🏥 Medical documentation may be required for extended periods',
        '📞 Notify your manager as soon as possible'
      ],
      'Personal Leave': [
        '📝 Provide clear reason for personal leave request',
        '⏰ Submit with adequate notice when possible'
      ],
      'Maternity/Paternity Leave': [
        '👶 Review your entitlements and benefits',
        '📋 Prepare documentation well in advance'
      ]
    };

    return typeSpecific[leaveType] || [];
  }

  /**
   * Get date-specific suggestions
   */
  async getDateSpecificSuggestions(userId, startDate, endDate) {
    const suggestions = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    const duration = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

    // Duration-based suggestions
    if (duration === 1) {
      suggestions.push('⚡ Single day requests are usually processed quickly');
    } else if (duration > 10) {
      suggestions.push('📋 Extended leave may require additional approvals');
      suggestions.push('💼 Plan comprehensive work handover');
    } else if (duration > 5) {
      suggestions.push('🔄 Arrange coverage for your responsibilities');
    }

    // Check for conflicts with existing requests
    const conflictingRequests = await LeaveRequest.find({
      userId,
      status: { $in: ['Pending', 'Approved'] },
      $or: [
        { startDate: { $lte: end }, endDate: { $gte: start } }
      ]
    });

    if (conflictingRequests.length > 0) {
      suggestions.push('⚠️ You have overlapping leave requests - please review');
    }

    // Weekend/holiday considerations
    const dayOfWeek = start.getDay();
    if (dayOfWeek === 1 || dayOfWeek === 5) { // Monday or Friday
      suggestions.push('📅 Consider extending to create a long weekend');
    }

    return suggestions;
  }

  /**
   * Get user management suggestions
   */
  async getUserManagementSuggestions(userId, action, data) {
    const suggestions = [
      '🔐 Ensure strong password requirements are met',
      '📧 Use work email addresses for new users',
      '🏢 Assign appropriate department and role',
      '📱 Include emergency contact information'
    ];

    if (action === 'user_form_detected') {
      if (!data.hasEmail) {
        suggestions.unshift('📧 Email address is required for user accounts');
      }
      if (!data.hasRole) {
        suggestions.unshift('👤 Select appropriate role for user permissions');
      }
    }

    return suggestions;
  }

  /**
   * Get task management suggestions
   */
  async getTaskManagementSuggestions(userId, action, data) {
    const user = await User.findById(userId).select('role');
    const suggestions = [];

    // Base suggestions
    suggestions.push('📋 Set clear and measurable objectives');
    suggestions.push('⏰ Assign realistic deadlines');
    suggestions.push('🎯 Define success criteria clearly');

    // Role-specific suggestions
    if (user.role === 'manager' || user.role === 'admin') {
      suggestions.push('👥 Consider team member workload when assigning');
      suggestions.push('🔄 Set up regular progress check-ins');
      
      // Check team workload
      const teamTasks = await Task.find({
        status: { $in: ['Pending', 'In Progress'] }
      }).populate('assignedTo', 'name');

      if (teamTasks.length > 20) {
        suggestions.push('⚠️ Team has high task volume - prioritize carefully');
      }
    }

    return suggestions;
  }

  /**
   * Get generic suggestions for unknown contexts
   */
  getGenericSuggestions(contextType, action) {
    return [
      '💡 I\'m here to help with your current task',
      '🤖 Feel free to ask questions about the HR system',
      '📋 Double-check all required fields before submitting',
      '💾 Save your progress regularly'
    ];
  }

  /**
   * Get fallback suggestions when errors occur
   */
  getFallbackSuggestions(contextType) {
    const fallbacks = {
      leave_request_form: [
        '📊 Check your leave balance',
        '📅 Plan your dates carefully',
        '✍️ Provide clear reasons'
      ],
      user_management_form: [
        '📧 Verify email addresses',
        '🔐 Set strong passwords',
        '👤 Assign correct roles'
      ],
      task_management: [
        '📋 Set clear objectives',
        '⏰ Use realistic deadlines',
        '🎯 Define success criteria'
      ]
    };

    return fallbacks[contextType] || [
      '💡 I\'m here to help',
      '🤖 Ask me anything about the system'
    ];
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.suggestionCache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      size: this.suggestionCache.size,
      timeout: this.cacheTimeout
    };
  }
}

module.exports = new ContextualSuggestionEngine();
