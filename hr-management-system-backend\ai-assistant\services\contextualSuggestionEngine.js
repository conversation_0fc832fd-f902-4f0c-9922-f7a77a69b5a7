const User = require('../../models/user');
const LeaveRequest = require('../../models/LeaveRequest');
const Task = require('../../models/Task');

class ContextualSuggestionEngine {
  constructor() {
    this.suggestionCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.userInteractionHistory = new Map(); // Track user interactions to prevent spam
    this.cooldownPeriods = {
      leave_type_selected: 30 * 1000, // 30 seconds
      dates_entered: 45 * 1000, // 45 seconds
      reason_entered: 60 * 1000, // 1 minute
      form_detected: 2 * 60 * 1000, // 2 minutes
      form_submission: 5 * 60 * 1000 // 5 minutes
    };
  }

  /**
   * Generate contextual suggestions based on user action and data
   */
  async generateSuggestions(userId, contextType, action, data = {}) {
    try {
      // Anti-spam check
      if (!this.shouldShowSuggestions(userId, action)) {
        return [];
      }

      const cacheKey = `${userId}_${contextType}_${action}_${JSON.stringify(data)}`;

      // Check cache first
      if (this.suggestionCache.has(cacheKey)) {
        const cached = this.suggestionCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          return cached.suggestions;
        }
      }

      // Record this interaction
      this.recordUserInteraction(userId, action);

      let suggestions = [];

      switch (contextType) {
        case 'leave_request_form':
          suggestions = await this.getLeaveRequestSuggestions(userId, action, data);
          break;
        case 'leave_request_dialog':
          suggestions = await this.getLeaveDialogSuggestions(userId, action, data);
          break;
        case 'user_management_form':
          suggestions = await this.getUserManagementSuggestions(userId, action, data);
          break;
        case 'task_management':
          suggestions = await this.getTaskManagementSuggestions(userId, action, data);
          break;
        default:
          suggestions = this.getGenericSuggestions(contextType, action);
      }

      // Cache the suggestions
      this.suggestionCache.set(cacheKey, {
        suggestions,
        timestamp: Date.now()
      });

      return suggestions;
    } catch (error) {
      console.error('Error generating contextual suggestions:', error);
      return this.getFallbackSuggestions(contextType);
    }
  }

  /**
   * Get leave request specific suggestions
   */
  async getLeaveRequestSuggestions(userId, action, data) {
    const user = await User.findById(userId).select('name role department');
    const currentYear = new Date().getFullYear();
    
    // Get user's leave history
    const leaveHistory = await LeaveRequest.find({
      userId,
      createdAt: { $gte: new Date(currentYear, 0, 1) }
    }).sort({ createdAt: -1 }).limit(5);

    const suggestions = [];

    // Base suggestions
    suggestions.push('📊 Check your current leave balance before submitting');
    suggestions.push('📅 Ensure your leave dates don\'t conflict with important deadlines');

    // Action-specific suggestions with variety
    if (action === 'leave_type_selected' && data.leaveType) {
      const typeSuggestions = this.getLeaveTypeSuggestions(data.leaveType);
      suggestions.push(...this.getRandomSuggestions(typeSuggestions, 2)); // Max 2 type-specific suggestions
    }

    if (action === 'dates_entered' && data.startDate && data.endDate) {
      const dateSuggestions = await this.getDateSpecificSuggestions(userId, data.startDate, data.endDate);
      suggestions.push(...this.getRandomSuggestions(dateSuggestions, 2)); // Max 2 date-specific suggestions
    }

    // Historical analysis
    if (leaveHistory.length > 0) {
      const rejectedRequests = leaveHistory.filter(req => req.status === 'Rejected');
      if (rejectedRequests.length > 0) {
        suggestions.push('⚠️ Review previous rejection reasons to improve approval chances');
      }

      const recentRequests = leaveHistory.filter(req => 
        new Date(req.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      );
      if (recentRequests.length > 2) {
        suggestions.push('📈 You\'ve submitted several requests recently - consider consolidating if possible');
      }
    }

    // Role-specific suggestions
    if (user.role === 'manager' || user.role === 'admin') {
      suggestions.push('👥 Consider team coverage when planning your leave');
    }

    return suggestions.slice(0, 6); // Limit to 6 suggestions
  }

  /**
   * Get leave type specific suggestions with variety
   */
  getLeaveTypeSuggestions(leaveType) {
    const typeSpecific = {
      'Annual Leave': [
        '🏖️ Plan annual leave during less busy periods for better approval chances',
        '📋 Coordinate with team members to ensure proper coverage',
        '🗓️ Consider booking popular vacation times well in advance',
        '💼 Complete urgent projects before your leave starts',
        '📊 Check if you have sufficient annual leave balance remaining',
        '🌟 Annual leave helps maintain work-life balance and productivity',
        '📅 Avoid scheduling leave during critical business periods',
        '🤝 Inform clients about your absence if you work directly with them'
      ],
      'Sick Leave': [
        '🏥 Medical documentation may be required for extended sick leave',
        '📞 Notify your manager as soon as possible about your illness',
        '💊 Focus on recovery - your health is the top priority',
        '📋 Check company policy for sick leave documentation requirements',
        '🏠 Consider working from home if you feel well enough for light duties',
        '⚕️ Consult with healthcare professionals for proper treatment',
        '📱 Keep emergency contacts updated in your profile',
        '🔄 Arrange for urgent tasks to be covered during your absence'
      ],
      'Personal Leave': [
        '📝 Provide a clear and specific reason for your personal leave request',
        '⏰ Submit your request with adequate notice when possible',
        '🔄 Arrange work handover if your absence affects ongoing projects',
        '📞 Keep emergency contact information updated in the system',
        '💡 Personal leave can be used for family emergencies or important events',
        '📋 Check if your situation qualifies for other types of leave first',
        '🤝 Communicate with your team about coverage arrangements',
        '⚖️ Ensure your request complies with company personal leave policies'
      ],
      'Maternity/Paternity Leave': [
        '👶 Review your full maternity/paternity leave entitlements and benefits',
        '📋 Prepare all necessary documentation well in advance',
        '💼 Plan comprehensive work handover procedures for extended absence',
        '🏥 Coordinate with HR for benefit arrangements and payments',
        '📅 Discuss flexible return-to-work options with your manager',
        '👨‍👩‍👧‍👦 Consider partner leave coordination for optimal family support',
        '📚 Familiarize yourself with company policies on parental leave',
        '🍼 Plan for potential leave extensions if complications arise'
      ],
      'Bereavement Leave': [
        '💐 Take the time you need to grieve and support your family',
        '📋 Check company policy for bereavement leave duration and requirements',
        '🤝 Don\'t hesitate to ask for additional support if needed',
        '📞 Inform your manager about funeral arrangements if attendance is expected',
        '💙 Consider counseling services if available through company benefits',
        '📝 Documentation may be required for extended bereavement leave',
        '🕊️ Focus on your emotional well-being during this difficult time',
        '👥 Delegate urgent responsibilities to trusted colleagues'
      ],
      'Unpaid Leave': [
        '💰 Consider the financial impact of unpaid leave on your budget',
        '📋 Ensure you meet eligibility requirements for unpaid leave',
        '🏥 Check if your health benefits continue during unpaid leave',
        '📅 Discuss return date and job security with HR before taking leave',
        '💼 Complete all pending work or arrange proper handover',
        '📝 Get written confirmation of your unpaid leave approval',
        '🔄 Understand the process for returning to work after unpaid leave',
        '⚖️ Review company policies on unpaid leave duration limits'
      ]
    };

    return typeSpecific[leaveType] || [];
  }

  /**
   * Get date-specific suggestions with variety
   */
  async getDateSpecificSuggestions(userId, startDate, endDate) {
    const suggestions = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    const duration = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
    const dayOfWeek = start.getDay();
    const month = start.getMonth();

    // Duration-based suggestions with variety
    if (duration === 1) {
      const singleDaySuggestions = [
        '⚡ Single day requests are usually processed quickly',
        '🎯 One-day leave is perfect for personal appointments',
        '📅 Consider if a half-day would be sufficient instead',
        '💡 Single day requests rarely require coverage arrangements'
      ];
      suggestions.push(singleDaySuggestions[Math.floor(Math.random() * singleDaySuggestions.length)]);
    } else if (duration === 2) {
      const twoDaySuggestions = [
        '🔄 Two-day leave allows for a nice short break',
        '📋 Brief handover notes should be sufficient',
        '⚡ Short leave requests are easier to approve'
      ];
      suggestions.push(twoDaySuggestions[Math.floor(Math.random() * twoDaySuggestions.length)]);
    } else if (duration >= 3 && duration <= 5) {
      const shortLeaveSuggestions = [
        '🔄 Arrange coverage for your key responsibilities',
        '📋 Prepare handover notes for ongoing projects',
        '💼 Complete urgent tasks before your leave starts',
        '🤝 Inform team members about your absence'
      ];
      suggestions.push(shortLeaveSuggestions[Math.floor(Math.random() * shortLeaveSuggestions.length)]);
    } else if (duration >= 6 && duration <= 10) {
      const mediumLeaveSuggestions = [
        '📋 Create detailed handover documentation',
        '💼 Plan comprehensive work coverage',
        '📞 Set up out-of-office messages and auto-replies',
        '🔄 Delegate responsibilities to trusted colleagues',
        '📅 Schedule important meetings before or after your leave'
      ];
      suggestions.push(mediumLeaveSuggestions[Math.floor(Math.random() * mediumLeaveSuggestions.length)]);
    } else if (duration > 10) {
      const extendedLeaveSuggestions = [
        '📋 Extended leave may require additional approvals from senior management',
        '💼 Plan comprehensive work handover with detailed documentation',
        '🔄 Consider training a temporary replacement for your key duties',
        '📞 Arrange regular check-ins if absolutely necessary',
        '📅 Schedule a pre-leave meeting with your manager',
        '💡 Extended leave requires careful planning and early submission'
      ];
      suggestions.push(extendedLeaveSuggestions[Math.floor(Math.random() * extendedLeaveSuggestions.length)]);
    }

    // Weekend/holiday considerations with variety
    if (dayOfWeek === 1) { // Monday
      const mondaySuggestions = [
        '📅 Starting leave on Monday? Consider taking Friday too for a long weekend',
        '🌟 Monday start gives you a fresh week break',
        '💡 Weekend before your leave can help you prepare better'
      ];
      suggestions.push(mondaySuggestions[Math.floor(Math.random() * mondaySuggestions.length)]);
    } else if (dayOfWeek === 5) { // Friday
      const fridaySuggestions = [
        '🎉 Friday leave extends your weekend perfectly',
        '📅 Consider adding Monday for a 4-day weekend',
        '⚡ Friday leave requests are very common - submit early'
      ];
      suggestions.push(fridaySuggestions[Math.floor(Math.random() * fridaySuggestions.length)]);
    } else if (dayOfWeek === 0 || dayOfWeek === 6) { // Weekend
      suggestions.push('📅 Weekend dates selected - ensure this is intentional');
    }

    // Seasonal considerations
    if (month >= 5 && month <= 7) { // Summer months
      const summerSuggestions = [
        '☀️ Summer is peak vacation season - submit requests early',
        '🏖️ Popular summer dates fill up quickly',
        '🌞 Summer leave is great for outdoor activities and travel'
      ];
      suggestions.push(summerSuggestions[Math.floor(Math.random() * summerSuggestions.length)]);
    } else if (month === 11 || month === 0) { // Holiday season
      const holidaySuggestions = [
        '🎄 Holiday season leave requires early planning',
        '🎁 December dates are highly requested - submit ASAP',
        '❄️ Winter holidays are perfect for family time'
      ];
      suggestions.push(holidaySuggestions[Math.floor(Math.random() * holidaySuggestions.length)]);
    }

    // Check for conflicts with existing requests
    const conflictingRequests = await LeaveRequest.find({
      userId,
      status: { $in: ['Pending', 'Approved'] },
      $or: [
        { startDate: { $lte: end }, endDate: { $gte: start } }
      ]
    });

    if (conflictingRequests.length > 0) {
      const conflictSuggestions = [
        '⚠️ You have overlapping leave requests - please review and adjust',
        '🔍 Check your existing leave requests for date conflicts',
        '📅 Consider modifying dates to avoid overlapping requests'
      ];
      suggestions.push(conflictSuggestions[Math.floor(Math.random() * conflictSuggestions.length)]);
    }

    // Advance notice suggestions
    const today = new Date();
    const daysUntilLeave = Math.ceil((start - today) / (1000 * 60 * 60 * 24));

    if (daysUntilLeave < 7) {
      suggestions.push('⏰ Short notice request - provide clear justification');
    } else if (daysUntilLeave > 90) {
      suggestions.push('🎯 Great planning! Early requests have better approval chances');
    } else if (daysUntilLeave > 30) {
      suggestions.push('👍 Good advance notice - this helps with planning coverage');
    }

    return suggestions;
  }

  /**
   * Get user management suggestions
   */
  async getUserManagementSuggestions(userId, action, data) {
    const suggestions = [
      '🔐 Ensure strong password requirements are met',
      '📧 Use work email addresses for new users',
      '🏢 Assign appropriate department and role',
      '📱 Include emergency contact information'
    ];

    if (action === 'user_form_detected') {
      if (!data.hasEmail) {
        suggestions.unshift('📧 Email address is required for user accounts');
      }
      if (!data.hasRole) {
        suggestions.unshift('👤 Select appropriate role for user permissions');
      }
    }

    return suggestions;
  }

  /**
   * Get task management suggestions
   */
  async getTaskManagementSuggestions(userId, action, data) {
    const user = await User.findById(userId).select('role');
    const suggestions = [];

    // Base suggestions
    suggestions.push('📋 Set clear and measurable objectives');
    suggestions.push('⏰ Assign realistic deadlines');
    suggestions.push('🎯 Define success criteria clearly');

    // Role-specific suggestions
    if (user.role === 'manager' || user.role === 'admin') {
      suggestions.push('👥 Consider team member workload when assigning');
      suggestions.push('🔄 Set up regular progress check-ins');
      
      // Check team workload
      const teamTasks = await Task.find({
        status: { $in: ['Pending', 'In Progress'] }
      }).populate('assignedTo', 'name');

      if (teamTasks.length > 20) {
        suggestions.push('⚠️ Team has high task volume - prioritize carefully');
      }
    }

    return suggestions;
  }

  /**
   * Get generic suggestions for unknown contexts
   */
  getGenericSuggestions(contextType, action) {
    return [
      '💡 I\'m here to help with your current task',
      '🤖 Feel free to ask questions about the HR system',
      '📋 Double-check all required fields before submitting',
      '💾 Save your progress regularly'
    ];
  }

  /**
   * Get fallback suggestions when errors occur
   */
  getFallbackSuggestions(contextType) {
    const fallbacks = {
      leave_request_form: [
        '📊 Check your leave balance',
        '📅 Plan your dates carefully',
        '✍️ Provide clear reasons'
      ],
      user_management_form: [
        '📧 Verify email addresses',
        '🔐 Set strong passwords',
        '👤 Assign correct roles'
      ],
      task_management: [
        '📋 Set clear objectives',
        '⏰ Use realistic deadlines',
        '🎯 Define success criteria'
      ]
    };

    return fallbacks[contextType] || [
      '💡 I\'m here to help',
      '🤖 Ask me anything about the system'
    ];
  }

  /**
   * Check if suggestions should be shown (anti-spam logic)
   */
  shouldShowSuggestions(userId, action) {
    const userHistory = this.userInteractionHistory.get(userId) || [];
    const cooldownPeriod = this.cooldownPeriods[action] || 30 * 1000; // Default 30 seconds
    const now = Date.now();

    // Find the last interaction of this type
    const lastInteraction = userHistory
      .filter(interaction => interaction.action === action)
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    if (lastInteraction && (now - lastInteraction.timestamp) < cooldownPeriod) {
      return false; // Still in cooldown period
    }

    // Limit suggestions per user per hour
    const oneHourAgo = now - (60 * 60 * 1000);
    const recentInteractions = userHistory.filter(interaction => interaction.timestamp > oneHourAgo);

    if (recentInteractions.length > 10) { // Max 10 suggestions per hour
      return false;
    }

    return true;
  }

  /**
   * Record user interaction for anti-spam tracking
   */
  recordUserInteraction(userId, action) {
    if (!this.userInteractionHistory.has(userId)) {
      this.userInteractionHistory.set(userId, []);
    }

    const userHistory = this.userInteractionHistory.get(userId);
    userHistory.push({
      action,
      timestamp: Date.now()
    });

    // Keep only last 50 interactions per user
    if (userHistory.length > 50) {
      userHistory.splice(0, userHistory.length - 50);
    }

    // Clean up old interactions (older than 24 hours)
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const filteredHistory = userHistory.filter(interaction => interaction.timestamp > oneDayAgo);
    this.userInteractionHistory.set(userId, filteredHistory);
  }

  /**
   * Get random suggestions from array to add variety
   */
  getRandomSuggestions(suggestionsArray, count = 3) {
    if (suggestionsArray.length <= count) {
      return suggestionsArray;
    }

    const shuffled = [...suggestionsArray].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.suggestionCache.clear();
  }

  /**
   * Clear user interaction history
   */
  clearUserHistory(userId = null) {
    if (userId) {
      this.userInteractionHistory.delete(userId);
    } else {
      this.userInteractionHistory.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      cacheSize: this.suggestionCache.size,
      cacheTimeout: this.cacheTimeout,
      activeUsers: this.userInteractionHistory.size,
      totalInteractions: Array.from(this.userInteractionHistory.values())
        .reduce((total, history) => total + history.length, 0)
    };
  }
}

module.exports = new ContextualSuggestionEngine();
