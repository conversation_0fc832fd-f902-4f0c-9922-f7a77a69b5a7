import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  History as HistoryIcon,
  Security as SecurityIcon,
  VpnKey as VpnKeyIcon,
  Event as EventIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

// Role colors
const roleColors = {
  admin: 'error',
  hr: 'warning',
  user: 'info'
};

// Status colors
const statusColors = {
  active: 'success',
  inactive: 'error',
  locked: 'warning'
};

const UserAccountManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userStatus, setUserStatus] = useState({});
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    action: null
  });

  // Filter states
  const [filters, setFilters] = useState({
    name: '',
    email: '',
    role: '',
    status: ''
  });

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch users
  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await api.get('/admin/users/all');

      // Process users to determine their status
      const processedUsers = response.data.map(user => ({
        ...user,
        status: user.active === false ? 'inactive' :
                user.accountLocked ? 'locked' : 'active'
      }));

      setUsers(processedUsers);

      // Fetch status for each user
      processedUsers.forEach(user => {
        fetchUserStatus(user._id);
      });
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('Failed to fetch users. Please try again later.');
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user status
  const fetchUserStatus = async (userId) => {
    try {
      const response = await api.get(`/admin/users/${userId}/status`);
      setUserStatus(prev => ({
        ...prev,
        [userId]: response.data
      }));
    } catch (error) {
      console.error(`Error fetching status for user ${userId}:`, error);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchUsers();
  }, []);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Apply filters
  const applyFilters = async () => {
    setLoading(true);

    try {
      // If we have name or email filters, use the search endpoint
      if (filters.name || filters.email) {
        const searchTerm = filters.name || filters.email;
        const response = await api.get('/admin/users/search', {
          params: { search: searchTerm }
        });

        // Process users to determine their status
        let processedUsers = response.data.map(user => ({
          ...user,
          status: user.active === false ? 'inactive' :
                  user.accountLocked ? 'locked' : 'active'
        }));

        // Apply additional filters (role and status) client-side
        if (filters.role || filters.status) {
          processedUsers = processedUsers.filter(user => {
            // Filter by role
            if (filters.role && user.role !== filters.role) {
              return false;
            }

            // Filter by status
            if (filters.status && user.status !== filters.status) {
              return false;
            }

            return true;
          });
        }

        setUsers(processedUsers);
      } else {
        // If we only have role or status filters, get all users and filter client-side
        const response = await api.get('/admin/users/all');

        // Process users to determine their status
        let processedUsers = response.data.map(user => ({
          ...user,
          status: user.active === false ? 'inactive' :
                  user.accountLocked ? 'locked' : 'active'
        }));

        // Apply filters
        if (filters.role || filters.status) {
          processedUsers = processedUsers.filter(user => {
            // Filter by role
            if (filters.role && user.role !== filters.role) {
              return false;
            }

            // Filter by status
            if (filters.status && user.status !== filters.status) {
              return false;
            }

            return true;
          });
        }

        setUsers(processedUsers);
      }
    } catch (error) {
      console.error('Error applying filters:', error);
      setError('Failed to apply filters. Please try again later.');
      toast.error('Failed to apply filters');
    } finally {
      setLoading(false);
    }
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      name: '',
      email: '',
      role: '',
      status: ''
    });
    fetchUsers();
  };

  // Handle search
  const handleSearch = async (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    if (query.trim() === '') {
      // If search is cleared, reset to all users
      fetchUsers();
      return;
    }

    setLoading(true);

    try {
      const response = await api.get('/admin/users/search', {
        params: { search: query }
      });

      // Process users to determine their status
      const processedUsers = response.data.map(user => ({
        ...user,
        status: user.active === false ? 'inactive' :
                user.accountLocked ? 'locked' : 'active'
      }));

      setUsers(processedUsers);

      // Fetch status for each user
      processedUsers.forEach(user => {
        fetchUserStatus(user._id);
      });
    } catch (error) {
      console.error('Error searching users:', error);
      setError('Failed to search users. Please try again later.');
      toast.error('Failed to search users');
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // View user details
  const handleViewUserDetails = (user) => {
    setSelectedUser(user);
  };

  // Close user details
  const handleCloseUserDetails = () => {
    setSelectedUser(null);
  };

  // Toggle user active status
  const handleToggleUserStatus = (user) => {
    const newStatus = user.active === false;
    const action = newStatus ? 'activate' : 'deactivate';

    setConfirmDialog({
      open: true,
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} User Account`,
      message: `Are you sure you want to ${action} ${user.name}'s account? ${!newStatus ? 'This will prevent the user from logging in.' : ''}`,
      action: () => updateUserStatus(user._id, newStatus)
    });
  };

  // Update user status
  const updateUserStatus = async (userId, active) => {
    try {
      await api.put(`/admin/users/${userId}/status`, { active });

      toast.success(`User account ${active ? 'activated' : 'deactivated'} successfully`);

      // Update user in the list
      setUsers(users.map(user => {
        if (user._id === userId) {
          return {
            ...user,
            active,
            status: active ? 'active' : 'inactive'
          };
        }
        return user;
      }));

      // Refresh user status
      fetchUserStatus(userId);

      // Close dialog
      setConfirmDialog({ open: false });
    } catch (error) {
      console.error('Error updating user status:', error);
      toast.error('Failed to update user status');
    }
  };

  // Handle confirm dialog close
  const handleConfirmDialogClose = () => {
    setConfirmDialog({ open: false });
  };

  // Handle confirm dialog confirm
  const handleConfirmDialogConfirm = () => {
    if (confirmDialog.action) {
      confirmDialog.action();
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Typography variant="h5" component="h2" fontWeight={600}>
          User Account Management
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            placeholder="Search users..."
            size="small"
            value={searchQuery}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <SearchIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />,
              endAdornment: searchQuery ? (
                <IconButton size="small" onClick={() => { setSearchQuery(''); fetchUsers(); }}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              ) : null
            }}
            sx={{ width: { xs: '100%', sm: '250px' } }}
          />
          <Button
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ ml: { xs: 0, sm: 1 } }}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
          <Button
            startIcon={<RefreshIcon />}
            onClick={fetchUsers}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="Name"
                fullWidth
                value={filters.name}
                onChange={(e) => handleFilterChange('name', e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="Email"
                fullWidth
                value={filters.email}
                onChange={(e) => handleFilterChange('email', e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Role</InputLabel>
                <Select
                  value={filters.role}
                  label="Role"
                  onChange={(e) => handleFilterChange('role', e.target.value)}
                >
                  <MenuItem value="">All Roles</MenuItem>
                  <MenuItem value="admin">Admin</MenuItem>
                  <MenuItem value="hr">HR</MenuItem>
                  <MenuItem value="user">User</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="locked">Locked</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={applyFilters}
                >
                  Apply Filters
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<ClearIcon />}
                  onClick={resetFilters}
                >
                  Reset
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : users.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography>No users found</Typography>
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Email</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Job</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Last Login</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((user) => (
                      <TableRow key={user._id} hover>
                        <TableCell>{user.name}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Chip
                            label={user.role}
                            size="small"
                            color={roleColors[user.role] || 'default'}
                          />
                        </TableCell>
                        <TableCell>{user.job}</TableCell>
                        <TableCell>{formatDate(user.creationDate)}</TableCell>
                        <TableCell>
                          <Chip
                            icon={
                              user.status === 'active' ? <CheckCircleIcon /> :
                              user.status === 'locked' ? <LockIcon /> :
                              <BlockIcon />
                            }
                            label={user.status}
                            size="small"
                            color={statusColors[user.status] || 'default'}
                          />
                        </TableCell>
                        <TableCell>
                          {userStatus[user._id]?.accountStatus?.lastLogin ?
                            formatDate(userStatus[user._id].accountStatus.lastLogin) :
                            'Never'
                          }
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 0.5 }}>
                            <Tooltip title="View Details">
                              <IconButton size="small" onClick={() => handleViewUserDetails(user)}>
                                <InfoIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={user.active === false ? "Activate Account" : "Deactivate Account"}>
                              <IconButton
                                size="small"
                                onClick={() => handleToggleUserStatus(user)}
                                color={user.active === false ? "success" : "error"}
                              >
                                {user.active === false ? <LockOpenIcon fontSize="small" /> : <BlockIcon fontSize="small" />}
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={users.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* User Details Dialog */}
      {selectedUser && (
        <Dialog open={!!selectedUser} onClose={handleCloseUserDetails} maxWidth="md" fullWidth>
          <DialogTitle>
            User Account Details
            <IconButton
              aria-label="close"
              onClick={handleCloseUserDetails}
              sx={{ position: 'absolute', right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 80,
                          height: 80,
                          mb: 2,
                          bgcolor: roleColors[selectedUser.role] || 'primary.main'
                        }}
                      >
                        {selectedUser.name.charAt(0).toUpperCase()}
                      </Avatar>
                      <Typography variant="h6" align="center">{selectedUser.name}</Typography>
                      <Chip
                        label={selectedUser.role}
                        color={roleColors[selectedUser.role] || 'default'}
                        sx={{ mt: 1 }}
                      />
                      <Chip
                        icon={
                          selectedUser.status === 'active' ? <CheckCircleIcon /> :
                          selectedUser.status === 'locked' ? <LockIcon /> :
                          <BlockIcon />
                        }
                        label={selectedUser.status}
                        color={statusColors[selectedUser.status] || 'default'}
                        sx={{ mt: 1 }}
                      />
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Job Title"
                          secondary={selectedUser.job || 'Not specified'}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <EventIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Created On"
                          secondary={formatDate(selectedUser.creationDate)}
                        />
                      </ListItem>
                      {selectedUser.birthdate && (
                        <ListItem>
                          <ListItemIcon>
                            <EventIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary="Birthdate"
                            secondary={formatDate(selectedUser.birthdate)}
                          />
                        </ListItem>
                      )}
                    </List>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={8}>
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Account Status
                    </Typography>

                    {userStatus[selectedUser._id] ? (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">Last Login</Typography>
                          <Typography variant="body1">
                            {userStatus[selectedUser._id].accountStatus?.lastLogin ?
                              formatDate(userStatus[selectedUser._id].accountStatus.lastLogin) :
                              'Never'
                            }
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">Login Count</Typography>
                          <Typography variant="body1">
                            {userStatus[selectedUser._id].accountStatus?.loginCount || 0}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">Failed Login Attempts</Typography>
                          <Typography variant="body1">
                            {userStatus[selectedUser._id].accountStatus?.failedLoginCount || 0}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">Last Activity</Typography>
                          <Typography variant="body1">
                            {userStatus[selectedUser._id].accountStatus?.lastActivity ?
                              formatDate(userStatus[selectedUser._id].accountStatus.lastActivity) :
                              'No activity recorded'
                            }
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={selectedUser.active !== false}
                                onChange={() => handleToggleUserStatus(selectedUser)}
                                color={selectedUser.active === false ? "success" : "error"}
                              />
                            }
                            label={selectedUser.active === false ? "Activate Account" : "Deactivate Account"}
                          />
                        </Grid>
                      </Grid>
                    ) : (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                        <CircularProgress size={24} />
                      </Box>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      <HistoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                      Recent Activity
                    </Typography>

                    {userStatus[selectedUser._id]?.recentActivity ? (
                      userStatus[selectedUser._id].recentActivity.length > 0 ? (
                        <List dense>
                          {userStatus[selectedUser._id].recentActivity.map((activity, index) => (
                            <ListItem key={index} divider={index < userStatus[selectedUser._id].recentActivity.length - 1}>
                              <ListItemText
                                primary={activity.description}
                                secondary={formatDate(activity.timestamp)}
                              />
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 2 }}>
                          No recent activity recorded
                        </Typography>
                      )
                    ) : (
                      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                        <CircularProgress size={24} />
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button
              startIcon={selectedUser.active === false ? <LockOpenIcon /> : <BlockIcon />}
              color={selectedUser.active === false ? "success" : "error"}
              onClick={() => handleToggleUserStatus(selectedUser)}
            >
              {selectedUser.active === false ? "Activate Account" : "Deactivate Account"}
            </Button>
            <Button onClick={handleCloseUserDetails}>Close</Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialog.open} onClose={handleConfirmDialogClose}>
        <DialogTitle>{confirmDialog.title}</DialogTitle>
        <DialogContent>
          <Typography>{confirmDialog.message}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleConfirmDialogClose}>Cancel</Button>
          <Button
            onClick={handleConfirmDialogConfirm}
            color="primary"
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserAccountManagement;
