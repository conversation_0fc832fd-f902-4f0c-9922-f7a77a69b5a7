const mongoose = require('mongoose');

/**
 * GEKEstimate Model - Stores General Estimating Knowledge data for employee task fit
 * This model tracks fit scores, time estimates, and historical accuracy for task assignments
 */
const gekEstimateSchema = new mongoose.Schema({
  // The user being evaluated
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Task category this estimate applies to
  taskCategory: {
    type: String,
    enum: ['General', 'Development', 'Design', 'Marketing', 'HR', 'Finance', 'Operations', 'Project', 'Administrative', 'Training', 'Evaluation', 'Other'],
    required: true
  },

  // Task priority level this estimate applies to (optional)
  taskPriority: {
    type: String,
    enum: ['Low', 'Medium', 'High', 'Urgent'],
    default: 'Medium'
  },

  // Fit score (0-100) - how well the user matches this task type
  fitScore: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },

  // Estimated average completion time in hours
  estimatedCompletionTime: {
    type: Number,
    required: true,
    min: 0
  },

  // Confidence score (0-100) - how confident the system is in this estimate
  confidenceScore: {
    type: Number,
    required: true,
    min: 0,
    max: 100
  },

  // Historical accuracy of estimates for this user and task type (0-100)
  historicalAccuracy: {
    type: Number,
    default: null,
    min: 0,
    max: 100
  },

  // Performance metrics that contribute to the fit score
  performanceMetrics: {
    // Task completion rate (0-100)
    completionRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // On-time completion rate (0-100)
    onTimeRate: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // Quality score based on feedback (1-5)
    qualityScore: {
      type: Number,
      default: 3,
      min: 1,
      max: 5
    },
    
    // Efficiency score - how quickly tasks are completed relative to estimates (0-100)
    efficiencyScore: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    
    // Attendance reliability (0-100)
    attendanceReliability: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },

  // Sample size - number of tasks used to calculate this estimate
  sampleSize: {
    type: Number,
    required: true,
    min: 0
  },

  // Last updated timestamp
  lastUpdated: {
    type: Date,
    default: Date.now
  },

  // Calculation basis - what data was used to create this estimate
  calculationBasis: {
    // Time period in days used for calculation (e.g., 90 for last 90 days)
    timePeriod: {
      type: Number,
      default: 90
    },
    
    // Task IDs used in calculation
    taskIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Task'
    }],
    
    // Evaluation IDs used in calculation
    evaluationIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Evaluation'
    }],
    
    // Attendance record IDs used in calculation
    attendanceIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Attendance'
    }]
  }
}, { timestamps: true });

// Create a compound index for efficient querying
gekEstimateSchema.index({ userId: 1, taskCategory: 1, taskPriority: 1 });

// Virtual for checking if the estimate is recent (within 30 days)
gekEstimateSchema.virtual('isRecent').get(function() {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  return this.lastUpdated >= thirtyDaysAgo;
});

// Virtual for checking if the estimate has enough data to be reliable
gekEstimateSchema.virtual('isReliable').get(function() {
  return this.sampleSize >= 5 && this.confidenceScore >= 60;
});

// Method to update the estimate with new task data
gekEstimateSchema.methods.updateWithNewTaskData = function(taskData) {
  // Implementation will be added in the controller
  this.lastUpdated = new Date();
  return this.save();
};

const GEKEstimate = mongoose.model('GEKEstimate', gekEstimateSchema);

module.exports = GEKEstimate;
