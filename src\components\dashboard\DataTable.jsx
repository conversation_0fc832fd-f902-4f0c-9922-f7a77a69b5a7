import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Toolbar,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip,
  Checkbox,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Button,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterListIcon,
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  GetApp as DownloadIcon,
  Print as PrintIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { styled, alpha } from '@mui/material/styles';

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 1.5,
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  overflow: 'hidden',
}));

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.primary.main, 0.05),
  '& .MuiTableCell-head': {
    color: theme.palette.text.secondary,
    fontWeight: 600,
    fontSize: '0.875rem',
    lineHeight: '1.5rem',
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(even)': {
    backgroundColor: alpha(theme.palette.primary.main, 0.02),
  },
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.05),
  },
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.12),
    },
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  padding: theme.spacing(1.5, 2),
  fontSize: '0.875rem',
}));

const StyledToolbar = styled(Toolbar)(({ theme }) => ({
  padding: theme.spacing(2),
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  backgroundColor: theme.palette.background.paper,
}));

const SearchTextField = styled(TextField)(({ theme }) => ({
  width: 320,
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius * 3,
    backgroundColor: alpha(theme.palette.common.black, 0.03),
    '&:hover': {
      backgroundColor: alpha(theme.palette.common.black, 0.05),
    },
    '& fieldset': {
      borderColor: 'transparent',
    },
    '&:hover fieldset': {
      borderColor: theme.palette.divider,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
}));

const DataTable = ({
  title,
  columns,
  data,
  actions = [],
  selectable = false,
  searchable = true,
  pagination = true,
  onRowClick,
  emptyMessage = 'No data available',
  loading = false,
  onSearch,
  onFilter,
  onSort,
  onSelectionChange,
  onActionClick,
  initialSortBy,
  initialSortDirection = 'asc',
  rowsPerPageOptions = [5, 10, 25, 50],
  defaultRowsPerPage = 10,
  searchQuery: externalSearchQuery = '', // Accept search query from parent
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState(initialSortBy || '');
  const [sortDirection, setSortDirection] = useState(initialSortDirection);
  const [selected, setSelected] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [actionRow, setActionRow] = useState(null);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Component-specific search timeout reference
  const searchTimeoutRef = useRef(null);

  // Cleanup function to clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Enhanced real-time search with debouncing
  const handleSearch = (event) => {
    const query = event.target.value;
    setSearchQuery(query);
    setPage(0);

    // Always perform search with debouncing to avoid too many API calls
    if (onSearch) {
      // Clear any existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout - shorter delay for better real-time feel
      searchTimeoutRef.current = setTimeout(() => {
        console.log('Executing real-time search for query:', query);
        onSearch(query);
        // Update parent search query for highlighting
        setParentSearchQuery(query);
      }, 300); // 300ms delay for more responsive feel
    }
  };

  const handleSort = (property) => {
    const isAsc = sortBy === property && sortDirection === 'asc';
    const direction = isAsc ? 'desc' : 'asc';
    setSortDirection(direction);
    setSortBy(property);
    if (onSort) {
      onSort(property, direction);
    }
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = data.map((row) => row.id);
      setSelected(newSelected);
      if (onSelectionChange) {
        onSelectionChange(newSelected);
      }
      return;
    }
    setSelected([]);
    if (onSelectionChange) {
      onSelectionChange([]);
    }
  };

  const handleSelectClick = (event, id) => {
    event.stopPropagation();
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected.filter((item) => item !== id);
    }

    setSelected(newSelected);
    if (onSelectionChange) {
      onSelectionChange(newSelected);
    }
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const handleActionsClick = (event, row) => {
    event.stopPropagation();
    setActionRow(row);
    setAnchorEl(event.currentTarget);
  };

  const handleActionsClose = () => {
    setAnchorEl(null);
    setActionRow(null);
  };

  const handleActionClick = (action) => {
    if (onActionClick && actionRow) {
      onActionClick(action, actionRow);
    }
    handleActionsClose();
  };

  // Apply pagination to data if needed
  const displayData = pagination
    ? data.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : data;

  // This prop allows parent components to pass their search query to the DataTable
  // for highlighting purposes
  const [parentSearchQuery, setParentSearchQuery] = useState('');

  // Use external search query if provided, otherwise use local or parent search query
  const highlightSearchQuery = externalSearchQuery || searchQuery || parentSearchQuery;

  // Update parent search query when onSearch is called from parent
  const updateParentSearchQuery = (query) => {
    setParentSearchQuery(query);
  };

  // Update local search query when external search query changes
  useEffect(() => {
    if (externalSearchQuery) {
      setSearchQuery(externalSearchQuery);
    }
  }, [externalSearchQuery]);

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <StyledToolbar>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          {searchable && (
            <SearchTextField
              placeholder="Search..."
              variant="outlined"
              size="small"
              value={searchQuery}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: searchQuery ? (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => {
                        setSearchQuery('');
                        if (onSearch) onSearch('');
                      }}
                      aria-label="clear search"
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ) : null
              }}
              sx={{ width: 280 }}
            />
          )}
          {onFilter && (
            <Tooltip title="Filter list">
              <IconButton onClick={onFilter}>
                <FilterListIcon />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </StyledToolbar>

      <StyledTableContainer>
        <Table sx={{ minWidth: 750 }} aria-labelledby={`${title}-table`} size="medium">
          <StyledTableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < data.length}
                    checked={data.length > 0 && selected.length === data.length}
                    onChange={handleSelectAllClick}
                    inputProps={{ 'aria-label': 'select all' }}
                  />
                </TableCell>
              )}
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  padding={column.disablePadding ? 'none' : 'normal'}
                  sortDirection={sortBy === column.id ? sortDirection : false}
                  sx={{ minWidth: column.minWidth }}
                >
                  {column.sortable !== false ? (
                    <TableSortLabel
                      active={sortBy === column.id}
                      direction={sortBy === column.id ? sortDirection : 'asc'}
                      onClick={() => handleSort(column.id)}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
              {actions.length > 0 && <TableCell align="right">Actions</TableCell>}
            </TableRow>
          </StyledTableHead>
          <TableBody>
            {displayData.length > 0 ? (
              displayData.map((row) => {
                const isItemSelected = isSelected(row.id);
                return (
                  <StyledTableRow
                    hover
                    onClick={onRowClick ? () => onRowClick(row) : undefined}
                    role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={row.id}
                    selected={isItemSelected}
                    sx={{ cursor: onRowClick ? 'pointer' : 'default' }}
                  >
                    {selectable && (
                      <StyledTableCell padding="checkbox">
                        <Checkbox
                          checked={isItemSelected}
                          onClick={(event) => handleSelectClick(event, row.id)}
                          inputProps={{ 'aria-labelledby': `enhanced-table-checkbox-${row.id}` }}
                        />
                      </StyledTableCell>
                    )}
                    {columns.map((column) => {
                      const value = row[column.id];

                      // Highlight search terms if there's a search query
                      const highlightText = (text) => {
                        if ((!highlightSearchQuery || highlightSearchQuery.trim() === '') || typeof text !== 'string') return text;

                        const parts = text.split(new RegExp(`(${highlightSearchQuery})`, 'gi'));
                        return (
                          <>
                            {parts.map((part, i) =>
                              part.toLowerCase() === highlightSearchQuery.toLowerCase() ? (
                                <Box
                                  component="span"
                                  key={i}
                                  sx={{
                                    backgroundColor: 'yellow',
                                    fontWeight: 'bold',
                                    px: 0.5,
                                    borderRadius: '2px'
                                  }}
                                >
                                  {part}
                                </Box>
                              ) : (
                                part
                              )
                            )}
                          </>
                        );
                      };

                      return (
                        <StyledTableCell key={column.id} align={column.align || 'left'}>
                          {column.render ? column.render(value, row) : highlightText(value)}
                        </StyledTableCell>
                      );
                    })}
                    {actions.length > 0 && (
                      <StyledTableCell align="right">
                        <IconButton
                          aria-label="more"
                          aria-controls="row-menu"
                          aria-haspopup="true"
                          onClick={(event) => handleActionsClick(event, row)}
                          size="small"
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </StyledTableCell>
                    )}
                  </StyledTableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (selectable ? 1 : 0) + (actions.length > 0 ? 1 : 0)}
                  align="center"
                  sx={{ py: 3 }}
                >
                  <Typography variant="body1" color="text.secondary">
                    {emptyMessage}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </StyledTableContainer>

      {pagination && (
        <TablePagination
          rowsPerPageOptions={rowsPerPageOptions}
          component="div"
          count={data.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      )}

      <Menu
        id="row-menu"
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleActionsClose}
      >
        {actions.map((action) => (
          <MenuItem key={action.name} onClick={() => handleActionClick(action)}>
            <ListItemIcon>{action.icon}</ListItemIcon>
            <ListItemText primary={action.name} />
          </MenuItem>
        ))}
      </Menu>
    </Paper>
  );
};

export default DataTable;
