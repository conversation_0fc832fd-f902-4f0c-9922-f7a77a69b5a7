/**
 * Context-Aware Hook for Jarvis
 * Provides easy integration with context detection and suggestions
 */

import { useEffect, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import contextDetectionService from '../services/ContextDetectionService';
import contextApiService from '../services/ContextApiService';

export const useContextAware = (options = {}) => {
  const {
    enableAutoDetection = true,
    enableNavigationTracking = true,
    enableFormTracking = true,
    enableDialogTracking = true,
    debounceMs = 1000
  } = options;

  const location = useLocation();
  const debounceRef = useRef(null);
  const lastContextRef = useRef(null);

  /**
   * Process context with debouncing
   */
  const processContextDebounced = useCallback((contextType, action, data) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(async () => {
      try {
        const contextKey = `${contextType}_${action}`;
        
        // Avoid duplicate processing
        if (lastContextRef.current === contextKey) {
          return;
        }
        
        lastContextRef.current = contextKey;
        
        const result = await contextApiService.processContext(contextType, action, data);
        
        if (result?.data?.suggestions?.length > 0) {
          console.log('🤖 Jarvis Context Suggestions:', result.data.suggestions);
        }
        
        return result;
      } catch (error) {
        console.error('Error processing context:', error);
      }
    }, debounceMs);
  }, [debounceMs]);

  /**
   * Track form interactions
   */
  const trackFormInteraction = useCallback((formType, formData = {}) => {
    if (!enableFormTracking) return;
    
    processContextDebounced(
      contextApiService.mapFormTypeToContext(formType),
      'form_focus',
      { formType, formData, timestamp: Date.now() }
    );
  }, [enableFormTracking, processContextDebounced]);

  /**
   * Track dialog interactions
   */
  const trackDialogInteraction = useCallback((dialogType, dialogData = {}) => {
    if (!enableDialogTracking) return;
    
    processContextDebounced(
      contextApiService.mapDialogTypeToContext(dialogType),
      'dialog_open',
      { dialogType, dialogData, timestamp: Date.now() }
    );
  }, [enableDialogTracking, processContextDebounced]);

  /**
   * Track input focus
   */
  const trackInputFocus = useCallback((inputType, inputData = {}) => {
    if (!enableFormTracking) return;
    
    processContextDebounced(
      'input_focus',
      'input_focus',
      { inputType, inputData, timestamp: Date.now() }
    );
  }, [enableFormTracking, processContextDebounced]);

  /**
   * Track button clicks
   */
  const trackButtonClick = useCallback((buttonType, buttonData = {}) => {
    processContextDebounced(
      'button_click',
      'button_click',
      { buttonType, buttonData, timestamp: Date.now() }
    );
  }, [processContextDebounced]);

  /**
   * Get suggestions for specific context
   */
  const getSuggestions = useCallback(async (contextType, actionData = {}) => {
    try {
      const result = await contextApiService.getContextSuggestions(contextType, actionData);
      return result?.data?.suggestions || [];
    } catch (error) {
      console.error('Error getting suggestions:', error);
      return [];
    }
  }, []);

  /**
   * Get leave request suggestions
   */
  const getLeaveRequestSuggestions = useCallback(async (formData = {}) => {
    return await getSuggestions('leave_request_form', formData);
  }, [getSuggestions]);

  /**
   * Get user management suggestions
   */
  const getUserManagementSuggestions = useCallback(async (formData = {}) => {
    return await getSuggestions('user_management_form', formData);
  }, [getSuggestions]);

  /**
   * Get task management suggestions
   */
  const getTaskManagementSuggestions = useCallback(async (formData = {}) => {
    return await getSuggestions('task_management_form', formData);
  }, [getSuggestions]);

  /**
   * Get job application suggestions
   */
  const getJobApplicationSuggestions = useCallback(async (formData = {}) => {
    return await getSuggestions('job_application_form', formData);
  }, [getSuggestions]);

  /**
   * Track navigation changes
   */
  useEffect(() => {
    if (!enableNavigationTracking) return;

    const path = location.pathname;
    const contextType = contextApiService.mapPathToContext(path);
    
    if (contextType) {
      processContextDebounced(
        'dashboard_navigation',
        'page_visit',
        { path, contextType, timestamp: Date.now() }
      );
    }
  }, [location.pathname, enableNavigationTracking, processContextDebounced]);

  /**
   * Initialize context detection
   */
  useEffect(() => {
    if (!enableAutoDetection) return;

    // Activate context detection service
    contextDetectionService.activate();

    return () => {
      // Clean up on unmount
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [enableAutoDetection]);

  return {
    // Tracking functions
    trackFormInteraction,
    trackDialogInteraction,
    trackInputFocus,
    trackButtonClick,
    
    // Suggestion functions
    getSuggestions,
    getLeaveRequestSuggestions,
    getUserManagementSuggestions,
    getTaskManagementSuggestions,
    getJobApplicationSuggestions,
    
    // Utility functions
    processContext: processContextDebounced,
    
    // Services
    contextDetectionService,
    contextApiService
  };
};

/**
 * Hook for form-specific context awareness
 */
export const useFormContextAware = (formType, formData = {}) => {
  const { trackFormInteraction, getSuggestions } = useContextAware();

  useEffect(() => {
    if (formType) {
      trackFormInteraction(formType, formData);
    }
  }, [formType, trackFormInteraction]);

  const getFormSuggestions = useCallback(async () => {
    const contextType = contextApiService.mapFormTypeToContext(formType);
    return await getSuggestions(contextType, formData);
  }, [formType, formData, getSuggestions]);

  return {
    trackFormInteraction,
    getFormSuggestions
  };
};

/**
 * Hook for dialog-specific context awareness
 */
export const useDialogContextAware = (dialogType, dialogData = {}) => {
  const { trackDialogInteraction, getSuggestions } = useContextAware();

  useEffect(() => {
    if (dialogType) {
      trackDialogInteraction(dialogType, dialogData);
    }
  }, [dialogType, trackDialogInteraction]);

  const getDialogSuggestions = useCallback(async () => {
    const contextType = contextApiService.mapDialogTypeToContext(dialogType);
    return await getSuggestions(contextType, dialogData);
  }, [dialogType, dialogData, getSuggestions]);

  return {
    trackDialogInteraction,
    getDialogSuggestions
  };
};

/**
 * Hook for input-specific context awareness
 */
export const useInputContextAware = () => {
  const { trackInputFocus } = useContextAware();

  const handleInputFocus = useCallback((event) => {
    const input = event.target;
    const inputType = input.type || input.tagName.toLowerCase();
    const inputName = input.name || input.id;
    const inputData = {
      name: inputName,
      type: inputType,
      placeholder: input.placeholder,
      value: input.value?.substring(0, 50) // Truncate for privacy
    };

    trackInputFocus(inputType, inputData);
  }, [trackInputFocus]);

  return {
    handleInputFocus,
    trackInputFocus
  };
};

export default useContextAware;
