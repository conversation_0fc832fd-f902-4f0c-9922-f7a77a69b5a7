// utils/navigation.js
let navigator; // acts like a useRef

/**
 * Sets the navigator function from useNavigate hook
 * @param {Function} nav - The navigate function from useNavigate hook
 */
export const setNavigator = (nav) => {
  navigator = nav;
};

/**
 * Navigate to a new route immediately for real-time feel
 * @param {string} to - The route to navigate to
 * @param {Object} options - Navigation options
 * @param {Function} options.onBeforeNavigate - Callback before navigation
 * @param {Function} options.onAfterNavigate - Callback after navigation
 * @param {boolean} options.replace - Whether to replace current history entry
 */
export const navigate = (to, options = {}) => {
  if (!navigator) return;

  const {
    onBeforeNavigate = null,
    onAfterNavigate = null,
    replace = false
  } = options;

  // Execute before navigation callback
  if (onBeforeNavigate && typeof onBeforeNavigate === 'function') {
    onBeforeNavigate();
  }

  // Navigate immediately for real-time feel
  navigator(to, { replace });

  // Execute after navigation callback
  if (onAfterNavigate && typeof onAfterNavigate === 'function') {
    onAfterNavigate();
  }
};
