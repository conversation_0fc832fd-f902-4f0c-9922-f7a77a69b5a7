/**
 * Test Component for AI Suggestions
 * This component tests the contextual AI suggestions system
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Alert,
  Divider
} from '@mui/material';
import { useContextualSuggestions } from '../hooks/useContextualSuggestions';
import ContextualSuggestions from './ai-assistant/ContextualSuggestions';

const TestAISuggestions = () => {
  const { generateSuggestions, activeSuggestions, clearSuggestions } = useContextualSuggestions();
  const [testResults, setTestResults] = useState([]);

  const runTest = async (testName, context, actionType, data) => {
    console.log(`Running test: ${testName}`);
    setTestResults(prev => [...prev, { name: testName, status: 'running' }]);
    
    try {
      const suggestions = await generateSuggestions(context, actionType, data);
      console.log(`Test ${testName} results:`, suggestions);
      
      setTestResults(prev => prev.map(test => 
        test.name === testName 
          ? { ...test, status: 'success', suggestions: suggestions.length }
          : test
      ));
    } catch (error) {
      console.error(`Test ${testName} failed:`, error);
      setTestResults(prev => prev.map(test => 
        test.name === testName 
          ? { ...test, status: 'error', error: error.message }
          : test
      ));
    }
  };

  const testScenarios = [
    {
      name: 'Leave Request - Sick Leave',
      context: 'leave_request',
      actionType: 'create',
      data: {
        leaveType: 'Sick Leave',
        startDate: '2024-01-20',
        endDate: '2024-01-25',
        duration: 6,
        reason: 'Medical appointment and recovery'
      }
    },
    {
      name: 'Leave Request - Extended Vacation',
      context: 'leave_request',
      actionType: 'create',
      data: {
        leaveType: 'Annual Leave',
        startDate: '2024-02-01',
        endDate: '2024-02-15',
        duration: 15,
        reason: 'Family vacation'
      }
    },
    {
      name: 'Task Management - High Priority',
      context: 'task_management',
      actionType: 'create',
      data: {
        title: 'Critical Bug Fix',
        priority: 'High',
        deadline: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(), // 12 hours from now
        assignedTo: '<EMAIL>'
      }
    },
    {
      name: 'Attendance - Late Check-in',
      context: 'attendance',
      actionType: 'check_in',
      data: {
        time: '10:30 AM',
        location: 'Office'
      }
    },
    {
      name: 'Attendance - Overtime Check-out',
      context: 'attendance',
      actionType: 'check_out',
      data: {
        time: '8:30 PM',
        hoursWorked: 10.5,
        notes: 'Working on important project'
      }
    },
    {
      name: 'User Management - New User',
      context: 'user_management',
      actionType: 'create',
      data: {
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'Developer'
      }
    }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        AI Suggestions Test Suite
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Test the contextual AI suggestions system with various scenarios.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Test Scenarios
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                    setTestResults([]);
                    clearSuggestions();
                  }}
                  sx={{ mr: 1, mb: 1 }}
                >
                  Clear All
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={async () => {
                    setTestResults([]);
                    clearSuggestions();
                    for (const scenario of testScenarios) {
                      await runTest(scenario.name, scenario.context, scenario.actionType, scenario.data);
                      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
                    }
                  }}
                  sx={{ mb: 1 }}
                >
                  Run All Tests
                </Button>
              </Box>

              <Divider sx={{ mb: 2 }} />

              {testScenarios.map((scenario, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={() => runTest(scenario.name, scenario.context, scenario.actionType, scenario.data)}
                    sx={{ mb: 1 }}
                  >
                    {scenario.name}
                  </Button>
                  <Typography variant="caption" color="text.secondary" display="block">
                    Context: {scenario.context} | Action: {scenario.actionType}
                  </Typography>
                </Box>
              ))}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Test Results
              </Typography>
              
              {testResults.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No tests run yet. Click a test button to start.
                </Typography>
              ) : (
                testResults.map((result, index) => (
                  <Alert
                    key={index}
                    severity={
                      result.status === 'success' ? 'success' :
                      result.status === 'error' ? 'error' : 'info'
                    }
                    sx={{ mb: 1 }}
                  >
                    <Typography variant="subtitle2">
                      {result.name}
                    </Typography>
                    <Typography variant="body2">
                      {result.status === 'running' && 'Running test...'}
                      {result.status === 'success' && `Generated ${result.suggestions} suggestions`}
                      {result.status === 'error' && `Error: ${result.error}`}
                    </Typography>
                  </Alert>
                ))
              )}

              <Divider sx={{ my: 2 }} />

              <Typography variant="h6" gutterBottom>
                Active Suggestions ({activeSuggestions.length})
              </Typography>
              
              {activeSuggestions.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No active suggestions. Run a test to generate suggestions.
                </Typography>
              ) : (
                activeSuggestions.map((suggestion, index) => (
                  <Alert
                    key={index}
                    severity={
                      suggestion.priority === 'high' ? 'error' :
                      suggestion.priority === 'medium' ? 'warning' : 'info'
                    }
                    sx={{ mb: 1 }}
                  >
                    <Typography variant="subtitle2" fontWeight="bold">
                      {suggestion.title}
                    </Typography>
                    <Typography variant="body2">
                      {suggestion.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Type: {suggestion.type} | Priority: {suggestion.priority}
                    </Typography>
                  </Alert>
                ))
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Contextual Suggestions Component Test */}
      <Box sx={{ mt: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Contextual Suggestions Component Test
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              This tests the ContextualSuggestions component directly.
            </Typography>
            
            <ContextualSuggestions
              context="leave_request"
              actionType="create"
              data={{
                leaveType: 'Sick Leave',
                startDate: '2024-01-20',
                endDate: '2024-01-25',
                duration: 6,
                reason: 'Medical appointment and recovery'
              }}
              position="inline"
              onSuggestionApplied={(suggestion) => {
                console.log('Applied suggestion:', suggestion);
                alert(`Applied suggestion: ${suggestion.title}`);
              }}
            />
          </CardContent>
        </Card>
      </Box>

      {/* Debug Information */}
      <Box sx={{ mt: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Debug Information
            </Typography>
            <Typography variant="body2" component="pre" sx={{ fontSize: '0.8rem', overflow: 'auto' }}>
              {JSON.stringify({
                activeSuggestionsCount: activeSuggestions.length,
                activeSuggestions: activeSuggestions,
                testResultsCount: testResults.length
              }, null, 2)}
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default TestAISuggestions;
