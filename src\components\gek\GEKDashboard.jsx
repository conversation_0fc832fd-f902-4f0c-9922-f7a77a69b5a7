/**
 * GEK (General Estimating Knowledge) Dashboard
 * Advanced analytics and insights for employee performance and task assignment
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  Person as PersonIcon,
  Assignment as AssignmentIcon,
  Speed as SpeedIcon,
  Star as StarIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  Psychology as PsychologyIcon
} from '@mui/icons-material';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

const GEKDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [gekData, setGekData] = useState({
    overview: {
      totalEmployees: 0,
      averagePerformance: 0,
      topPerformers: 0,
      improvementNeeded: 0
    },
    performanceMetrics: [],
    skillAnalysis: [],
    taskEfficiency: [],
    departmentComparison: [],
    recentEvaluations: []
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchGEKData();
  }, []);

  const fetchGEKData = async () => {
    try {
      setLoading(true);
      
      // Fetch comprehensive GEK analytics
      const [
        usersResponse,
        tasksResponse,
        evaluationsResponse,
        attendanceResponse
      ] = await Promise.all([
        api.get('/hr/users', { params: { role: 'user' } }),
        api.get('/tasks/hr'),
        api.get('/hr/evaluations'),
        api.get('/normaluser/attendance')
      ]);

      const users = usersResponse.data || [];
      const tasks = tasksResponse.data || [];
      const evaluations = evaluationsResponse.data || [];
      const attendance = attendanceResponse.data || [];

      // Process and analyze data
      const processedData = processGEKAnalytics(users, tasks, evaluations, attendance);
      setGekData(processedData);

    } catch (error) {
      console.error('Error fetching GEK data:', error);
      toast.error('Failed to load GEK analytics');
    } finally {
      setLoading(false);
    }
  };

  const processGEKAnalytics = (users, tasks, evaluations, attendance) => {
    // Calculate performance metrics for each user
    const userMetrics = users.map(user => {
      const userTasks = tasks.filter(task => 
        task.assignedTo === user._id || task.assignedTo === user.email
      );
      const userEvaluations = evaluations.filter(eval => eval.userId === user._id);
      const userAttendance = attendance.filter(att => att.userId === user._id);

      // Task performance
      const completedTasks = userTasks.filter(task => task.status === 'Completed').length;
      const totalTasks = userTasks.length;
      const taskCompletionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // Evaluation scores
      const latestEvaluation = userEvaluations.sort((a, b) => 
        new Date(b.createdAt) - new Date(a.createdAt)
      )[0];
      
      const evaluationScore = latestEvaluation ? 
        ((latestEvaluation.performanceRating + 
          latestEvaluation.attitudeRating + 
          latestEvaluation.communicationRating + 
          latestEvaluation.teamworkRating + 
          latestEvaluation.initiativeRating) / 5) * 20 : 50;

      // Attendance rate
      const attendanceRate = userAttendance.length > 0 ? 
        (userAttendance.filter(att => att.checkIn && att.checkOut).length / userAttendance.length) * 100 : 0;

      // Overall GEK score (weighted average)
      const gekScore = (
        taskCompletionRate * 0.4 + 
        evaluationScore * 0.4 + 
        attendanceRate * 0.2
      );

      return {
        ...user,
        taskCompletionRate,
        evaluationScore,
        attendanceRate,
        gekScore: Math.round(gekScore),
        totalTasks,
        completedTasks,
        skillLevel: gekScore >= 80 ? 'Expert' : gekScore >= 60 ? 'Proficient' : gekScore >= 40 ? 'Developing' : 'Beginner'
      };
    });

    // Sort by GEK score
    userMetrics.sort((a, b) => b.gekScore - a.gekScore);

    // Calculate overview statistics
    const overview = {
      totalEmployees: users.length,
      averagePerformance: Math.round(userMetrics.reduce((sum, user) => sum + user.gekScore, 0) / userMetrics.length),
      topPerformers: userMetrics.filter(user => user.gekScore >= 80).length,
      improvementNeeded: userMetrics.filter(user => user.gekScore < 60).length
    };

    // Performance metrics for charts
    const performanceMetrics = userMetrics.slice(0, 10).map(user => ({
      name: user.name.split(' ')[0],
      gekScore: user.gekScore,
      tasks: user.taskCompletionRate,
      evaluation: user.evaluationScore,
      attendance: user.attendanceRate
    }));

    // Skill analysis by department
    const departmentGroups = users.reduce((acc, user) => {
      const dept = user.department || 'Other';
      if (!acc[dept]) acc[dept] = [];
      acc[dept].push(userMetrics.find(um => um._id === user._id));
      return acc;
    }, {});

    const departmentComparison = Object.entries(departmentGroups).map(([dept, deptUsers]) => ({
      department: dept,
      averageScore: Math.round(deptUsers.reduce((sum, user) => sum + user.gekScore, 0) / deptUsers.length),
      employeeCount: deptUsers.length,
      topPerformers: deptUsers.filter(user => user.gekScore >= 80).length
    }));

    // Skill level distribution
    const skillAnalysis = [
      { name: 'Expert', value: userMetrics.filter(u => u.skillLevel === 'Expert').length, color: '#4caf50' },
      { name: 'Proficient', value: userMetrics.filter(u => u.skillLevel === 'Proficient').length, color: '#2196f3' },
      { name: 'Developing', value: userMetrics.filter(u => u.skillLevel === 'Developing').length, color: '#ff9800' },
      { name: 'Beginner', value: userMetrics.filter(u => u.skillLevel === 'Beginner').length, color: '#f44336' }
    ];

    // Task efficiency trends (last 30 days)
    const taskEfficiency = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      const dayTasks = tasks.filter(task => {
        const taskDate = new Date(task.createdAt);
        return taskDate.toDateString() === date.toDateString();
      });
      
      return {
        date: date.toLocaleDateString('en-US', { weekday: 'short' }),
        created: dayTasks.length,
        completed: dayTasks.filter(task => task.status === 'Completed').length,
        efficiency: dayTasks.length > 0 ? (dayTasks.filter(task => task.status === 'Completed').length / dayTasks.length) * 100 : 0
      };
    });

    return {
      overview,
      performanceMetrics,
      skillAnalysis,
      taskEfficiency,
      departmentComparison,
      recentEvaluations: evaluations.slice(0, 5),
      userMetrics: userMetrics.slice(0, 20) // Top 20 for detailed view
    };
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchGEKData();
    setRefreshing(false);
    toast.success('GEK analytics refreshed');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading GEK Analytics...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
            GEK Dashboard
          </Typography>
          <Typography variant="body1" color="text.secondary">
            General Estimating Knowledge - Advanced Performance Analytics
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
          onClick={handleRefresh}
          disabled={refreshing}
        >
          {refreshing ? 'Refreshing...' : 'Refresh Data'}
        </Button>
      </Box>

      {/* Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Total Employees
                  </Typography>
                  <Typography variant="h4" component="div">
                    {gekData.overview.totalEmployees}
                  </Typography>
                </Box>
                <PersonIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Average Performance
                  </Typography>
                  <Typography variant="h4" component="div">
                    {gekData.overview.averagePerformance}%
                  </Typography>
                </Box>
                <SpeedIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Top Performers
                  </Typography>
                  <Typography variant="h4" component="div">
                    {gekData.overview.topPerformers}
                  </Typography>
                </Box>
                <StarIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Need Improvement
                  </Typography>
                  <Typography variant="h4" component="div">
                    {gekData.overview.improvementNeeded}
                  </Typography>
                </Box>
                <TrendingDownIcon color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts Row */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {/* Performance Metrics Chart */}
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Employee Performance Metrics
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={gekData.performanceMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="gekScore" fill="#2196f3" name="GEK Score" />
                  <Bar dataKey="tasks" fill="#4caf50" name="Task Completion %" />
                  <Bar dataKey="evaluation" fill="#ff9800" name="Evaluation Score" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Skill Distribution */}
        <Grid item xs={12} lg={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Skill Level Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={gekData.skillAnalysis}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {gekData.skillAnalysis.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GEKDashboard;
