const User = require('../../models/user');
const LeaveRequest = require('../../models/LeaveRequest');
const Task = require('../../models/Task');
const Application = require('../../models/application');
const Job = require('../../models/Job');
const Attendance = require('../../models/Attendance');
const Evaluation = require('../../models/Evaluation');
const Notification = require('../../models/Notification');
const bcrypt = require('bcryptjs');

class ActionService {
  constructor() {
    this.actions = new Map();
    this.initializeActions();
  }

  /**
   * Initialize all available actions
   */
  initializeActions() {
    // User Management Actions
    this.actions.set('create_user', this.createUser.bind(this));
    this.actions.set('get_users', this.getUsers.bind(this));
    this.actions.set('update_user', this.updateUser.bind(this));
    this.actions.set('delete_user', this.deleteUser.bind(this));
    this.actions.set('change_password', this.changePassword.bind(this));

    // Leave Management Actions
    this.actions.set('create_leave_request', this.createLeaveRequest.bind(this));
    this.actions.set('get_leave_requests', this.getLeaveRequests.bind(this));
    this.actions.set('update_leave_status', this.updateLeaveStatus.bind(this));
    this.actions.set('get_leave_balance', this.getLeaveBalance.bind(this));

    // Task Management Actions
    this.actions.set('create_task', this.createTask.bind(this));
    this.actions.set('get_tasks', this.getTasks.bind(this));
    this.actions.set('update_task_status', this.updateTaskStatus.bind(this));
    this.actions.set('delete_task', this.deleteTask.bind(this));

    // Job Management Actions
    this.actions.set('create_job', this.createJob.bind(this));
    this.actions.set('get_jobs', this.getJobs.bind(this));
    this.actions.set('update_job', this.updateJob.bind(this));
    this.actions.set('delete_job', this.deleteJob.bind(this));

    // Application Management Actions
    this.actions.set('get_applications', this.getApplications.bind(this));
    this.actions.set('update_application_status', this.updateApplicationStatus.bind(this));
    this.actions.set('delete_application', this.deleteApplication.bind(this));

    // Attendance Actions
    this.actions.set('check_in', this.checkIn.bind(this));
    this.actions.set('check_out', this.checkOut.bind(this));
    this.actions.set('get_attendance', this.getAttendance.bind(this));

    // Evaluation Actions
    this.actions.set('create_evaluation', this.createEvaluation.bind(this));
    this.actions.set('get_evaluations', this.getEvaluations.bind(this));
    this.actions.set('update_evaluation', this.updateEvaluation.bind(this));

    // Notification Actions
    this.actions.set('get_notifications', this.getNotifications.bind(this));
    this.actions.set('mark_notification_read', this.markNotificationRead.bind(this));
    this.actions.set('delete_notification', this.deleteNotification.bind(this));

    // Search Actions
    this.actions.set('search_users', this.searchUsers.bind(this));
    this.actions.set('search_applications', this.searchApplications.bind(this));
    this.actions.set('search_jobs', this.searchJobs.bind(this));
    this.actions.set('search_tasks', this.searchTasks.bind(this));
  }

  /**
   * Execute an action
   * @param {string} actionName - Name of the action
   * @param {Object} params - Action parameters
   * @param {Object} context - User context
   * @returns {Promise<Object>} - Action result
   */
  async executeAction(actionName, params, context) {
    try {
      const action = this.actions.get(actionName);
      if (!action) {
        throw new Error(`Action '${actionName}' not found`);
      }

      return await action(params, context);
    } catch (error) {
      console.error(`Error executing action ${actionName}:`, error);
      throw error;
    }
  }

  /**
   * Get available actions for a user role
   * @param {string} role - User role
   * @returns {Array} - Available actions
   */
  getAvailableActions(role) {
    const roleActions = {
      admin: [
        'create_user', 'get_users', 'update_user', 'delete_user', 'change_password',
        'get_leave_requests', 'update_leave_status',
        'create_task', 'get_tasks', 'update_task', 'delete_task',
        'create_job', 'get_jobs', 'update_job', 'delete_job',
        'get_applications', 'update_application_status', 'delete_application',
        'get_attendance', 'create_evaluation', 'get_evaluations', 'update_evaluation',
        'get_notifications', 'search_users', 'search_applications', 'search_jobs', 'search_tasks'
      ],
      hr: [
        'create_user', 'get_users', 'update_user', 'change_password',
        'get_leave_requests', 'update_leave_status',
        'create_task', 'get_tasks', 'update_task', 'delete_task',
        'create_job', 'get_jobs', 'update_job', 'delete_job',
        'get_applications', 'update_application_status', 'delete_application',
        'get_attendance', 'create_evaluation', 'get_evaluations', 'update_evaluation',
        'get_notifications', 'search_users', 'search_applications', 'search_jobs', 'search_tasks'
      ],
      user: [
        'create_leave_request', 'get_leave_requests', 'get_leave_balance',
        'get_tasks', 'update_task_status',
        'check_in', 'check_out', 'get_attendance',
        'get_evaluations', 'get_notifications', 'mark_notification_read', 'delete_notification',
        'change_password', 'search_jobs'
      ]
    };

    return roleActions[role] || [];
  }

  // ==================== USER MANAGEMENT ACTIONS ====================

  async createUser(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to create users');
    }

    const { name, email, password, role, job, department } = params;

    // Check if user exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    const newUser = new User({
      name,
      email,
      password,
      role: role || 'user',
      job,
      department: department || 'Other',
      creationDate: new Date()
    });

    await newUser.save();

    return {
      success: true,
      message: `User ${name} created successfully`,
      data: {
        id: newUser._id,
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        department: newUser.department
      }
    };
  }

  async getUsers(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to view users');
    }

    const { role, search, limit = 20 } = params;
    let query = {};

    if (role) query.role = role;
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { job: { $regex: search, $options: 'i' } }
      ];
    }

    const users = await User.find(query)
      .select('-password')
      .limit(parseInt(limit))
      .sort({ creationDate: -1 });

    return {
      success: true,
      message: `Found ${users.length} users`,
      data: users
    };
  }

  async updateUser(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to update users');
    }

    const { userId, updates } = params;

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Don't allow password updates through this method
    delete updates.password;

    Object.assign(user, updates);
    await user.save();

    return {
      success: true,
      message: `User ${user.name} updated successfully`,
      data: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        department: user.department
      }
    };
  }

  async deleteUser(params, context) {
    if (context.userRole !== 'admin') {
      throw new Error('Only admins can delete users');
    }

    const { userId } = params;

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    await User.findByIdAndDelete(userId);

    return {
      success: true,
      message: `User ${user.name} deleted successfully`
    };
  }

  async changePassword(params, context) {
    const { userId, newPassword, currentPassword } = params;

    // Users can only change their own password unless they're admin/hr
    if (userId !== context.userId && !['admin', 'hr'].includes(context.userRole)) {
      throw new Error('You can only change your own password');
    }

    const user = await User.findById(userId || context.userId);
    if (!user) {
      throw new Error('User not found');
    }

    // If changing own password, verify current password
    if (userId === context.userId && currentPassword) {
      const isValidPassword = await user.comparePassword(currentPassword);
      if (!isValidPassword) {
        throw new Error('Current password is incorrect');
      }
    }

    user.password = newPassword;
    await user.save();

    return {
      success: true,
      message: 'Password changed successfully'
    };
  }

  // ==================== LEAVE MANAGEMENT ACTIONS ====================

  async createLeaveRequest(params, context) {
    const { leaveType, startDate, endDate, reason } = params;

    const user = await User.findById(context.userId);
    if (!user) {
      throw new Error('User not found');
    }

    const validLeaveTypes = ['Annual Leave', 'Sick Leave', 'Personal Leave', 'Maternity/Paternity Leave', 'Bereavement Leave', 'Unpaid Leave'];
    if (!validLeaveTypes.includes(leaveType)) {
      throw new Error(`Invalid leave type. Must be one of: ${validLeaveTypes.join(', ')}`);
    }

    const newLeaveRequest = new LeaveRequest({
      userId: context.userId,
      employeeName: user.name,
      leaveType,
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      reason,
      status: 'Pending'
    });

    await newLeaveRequest.save();

    return {
      success: true,
      message: 'Leave request submitted successfully',
      data: {
        id: newLeaveRequest._id,
        leaveType: newLeaveRequest.leaveType,
        startDate: newLeaveRequest.startDate,
        endDate: newLeaveRequest.endDate,
        status: newLeaveRequest.status
      }
    };
  }

  async getLeaveRequests(params, context) {
    const { status, search, limit = 20 } = params;
    let query = {};

    // Regular users can only see their own requests
    if (context.userRole === 'user') {
      query.userId = context.userId;
    }

    if (status) query.status = status;
    if (search) {
      query.$or = [
        { employeeName: { $regex: search, $options: 'i' } },
        { leaveType: { $regex: search, $options: 'i' } },
        { reason: { $regex: search, $options: 'i' } }
      ];
    }

    const leaveRequests = await LeaveRequest.find(query)
      .populate('userId', 'name email department')
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    return {
      success: true,
      message: `Found ${leaveRequests.length} leave requests`,
      data: leaveRequests
    };
  }

  async updateLeaveStatus(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to update leave status');
    }

    const { leaveRequestId, status, feedback } = params;

    if (!['Approved', 'Rejected', 'Pending'].includes(status)) {
      throw new Error('Invalid status. Must be Approved, Rejected, or Pending');
    }

    const leaveRequest = await LeaveRequest.findById(leaveRequestId);
    if (!leaveRequest) {
      throw new Error('Leave request not found');
    }

    leaveRequest.status = status;
    if (feedback) leaveRequest.feedback = feedback;
    await leaveRequest.save();

    return {
      success: true,
      message: `Leave request ${status.toLowerCase()} successfully`,
      data: {
        id: leaveRequest._id,
        employeeName: leaveRequest.employeeName,
        status: leaveRequest.status
      }
    };
  }

  async getLeaveBalance(params, context) {
    const currentYear = new Date().getFullYear();
    const yearStart = new Date(currentYear, 0, 1);
    const yearEnd = new Date(currentYear, 11, 31);

    const leaveRequests = await LeaveRequest.find({
      userId: context.userId,
      status: { $in: ['Approved', 'Pending'] },
      startDate: { $gte: yearStart, $lte: yearEnd }
    });

    const leaveTypes = ['Annual Leave', 'Sick Leave', 'Personal Leave'];
    const maxDays = { 'Annual Leave': 25, 'Sick Leave': 10, 'Personal Leave': 5 };
    const balances = {};

    leaveTypes.forEach(type => {
      const usedRequests = leaveRequests.filter(req => req.leaveType === type);
      const usedDays = usedRequests.reduce((total, req) => {
        const days = Math.ceil((new Date(req.endDate) - new Date(req.startDate)) / (1000 * 60 * 60 * 24)) + 1;
        return total + days;
      }, 0);

      balances[type] = {
        used: usedDays,
        remaining: maxDays[type] - usedDays,
        total: maxDays[type]
      };
    });

    return {
      success: true,
      message: 'Leave balance retrieved successfully',
      data: balances
    };
  }

  // ==================== TASK MANAGEMENT ACTIONS ====================

  async createTask(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to create tasks');
    }

    const { title, description, category, priority, deadline, assignedTo, visibleToManagers } = params;

    if (!title || !description || !deadline || !assignedTo) {
      throw new Error('Missing required fields: title, description, deadline, and assignedTo are required');
    }

    const assignee = await User.findById(assignedTo);
    if (!assignee) {
      throw new Error('Assignee not found');
    }

    const newTask = new Task({
      title,
      description,
      category: category || 'General',
      priority: priority || 'Medium',
      deadline: new Date(deadline),
      createdBy: context.userId,
      assignedTo,
      visibleToManagers: visibleToManagers || false
    });

    await newTask.save();

    const populatedTask = await Task.findById(newTask._id)
      .populate('createdBy', 'name email')
      .populate('assignedTo', 'name email job');

    return {
      success: true,
      message: `Task "${title}" created and assigned to ${assignee.name}`,
      data: populatedTask
    };
  }

  async getTasks(params, context) {
    const { status, category, priority, assignedTo, search, limit = 20 } = params;
    let query = {};

    // Regular users can only see their assigned tasks
    if (context.userRole === 'user') {
      query.assignedTo = context.userId;
    } else if (context.userRole === 'hr') {
      query.createdBy = context.userId;
    }

    if (status) query.status = status;
    if (category) query.category = category;
    if (priority) query.priority = priority;
    if (assignedTo) query.assignedTo = assignedTo;
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const tasks = await Task.find(query)
      .populate('createdBy', 'name email')
      .populate('assignedTo', 'name email job')
      .limit(parseInt(limit))
      .sort({ deadline: 1 });

    return {
      success: true,
      message: `Found ${tasks.length} tasks`,
      data: tasks
    };
  }

  async updateTaskStatus(params, context) {
    const { taskId, status, comment } = params;

    const task = await Task.findById(taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    // Users can only update their own tasks
    if (context.userRole === 'user' && task.assignedTo.toString() !== context.userId) {
      throw new Error('You can only update your own tasks');
    }

    const validStatuses = ['Pending', 'In Progress', 'Completed', 'On Hold'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status. Must be one of: ${validStatuses.join(', ')}`);
    }

    task.status = status;
    if (comment) {
      task.comments = task.comments || [];
      task.comments.push({
        text: comment,
        author: context.userId,
        timestamp: new Date()
      });
    }

    await task.save();

    return {
      success: true,
      message: `Task status updated to "${status}"`,
      data: {
        id: task._id,
        title: task.title,
        status: task.status
      }
    };
  }

  // ==================== ATTENDANCE ACTIONS ====================

  async checkIn(params, context) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const existingAttendance = await Attendance.findOne({
      userId: context.userId,
      date: today
    });

    if (existingAttendance && existingAttendance.checkInTime) {
      throw new Error(`You've already checked in today at ${existingAttendance.checkInTime.toLocaleTimeString()}`);
    }

    const checkInTime = new Date();
    await Attendance.findOneAndUpdate(
      { userId: context.userId, date: today },
      {
        userId: context.userId,
        date: today,
        checkInTime,
        status: 'Present'
      },
      { upsert: true, new: true }
    );

    return {
      success: true,
      message: `Successfully checked in at ${checkInTime.toLocaleTimeString()}`,
      data: { checkInTime }
    };
  }

  async checkOut(params, context) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const attendance = await Attendance.findOne({
      userId: context.userId,
      date: today
    });

    if (!attendance || !attendance.checkInTime) {
      throw new Error('You must check in first before checking out');
    }

    if (attendance.checkOutTime) {
      throw new Error(`You've already checked out today at ${attendance.checkOutTime.toLocaleTimeString()}`);
    }

    const checkOutTime = new Date();
    attendance.checkOutTime = checkOutTime;

    // Calculate hours worked
    const hoursWorked = (checkOutTime - attendance.checkInTime) / (1000 * 60 * 60);
    attendance.hoursWorked = Math.round(hoursWorked * 100) / 100;

    await attendance.save();

    return {
      success: true,
      message: `Successfully checked out at ${checkOutTime.toLocaleTimeString()}. Hours worked: ${attendance.hoursWorked}`,
      data: {
        checkOutTime,
        hoursWorked: attendance.hoursWorked
      }
    };
  }

  async getAttendance(params, context) {
    const { startDate, endDate, limit = 30 } = params;
    let query = {};

    // Regular users can only see their own attendance
    if (context.userRole === 'user') {
      query.userId = context.userId;
    }

    if (startDate) query.date = { $gte: new Date(startDate) };
    if (endDate) query.date = { ...query.date, $lte: new Date(endDate) };

    const attendance = await Attendance.find(query)
      .populate('userId', 'name email department')
      .limit(parseInt(limit))
      .sort({ date: -1 });

    return {
      success: true,
      message: `Found ${attendance.length} attendance records`,
      data: attendance
    };
  }

  // ==================== MISSING TASK METHODS ====================

  async deleteTask(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to delete tasks');
    }

    const { taskId } = params;

    const task = await Task.findById(taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    await Task.findByIdAndDelete(taskId);

    return {
      success: true,
      message: `Task "${task.title}" deleted successfully`
    };
  }

  // ==================== MISSING JOB METHODS ====================

  async createJob(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to create jobs');
    }

    const { title, description, location, jobType, requirements } = params;

    const newJob = new Job({
      title,
      description,
      location: location || 'Remote',
      jobType: jobType || 'Full-time',
      requirements: requirements || [],
      createdBy: context.userId,
      status: 'Active'
    });

    await newJob.save();

    return {
      success: true,
      message: `Job "${title}" created successfully`,
      data: newJob
    };
  }

  async getJobs(params, context) {
    const { search, jobType, location, limit = 20 } = params;
    let query = { status: 'Active' };

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    if (jobType) query.jobType = jobType;
    if (location) query.location = { $regex: location, $options: 'i' };

    const jobs = await Job.find(query)
      .populate('createdBy', 'name email')
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    return {
      success: true,
      message: `Found ${jobs.length} jobs`,
      data: jobs
    };
  }

  async updateJob(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to update jobs');
    }

    const { jobId, updates } = params;

    const job = await Job.findById(jobId);
    if (!job) {
      throw new Error('Job not found');
    }

    Object.assign(job, updates);
    await job.save();

    return {
      success: true,
      message: `Job "${job.title}" updated successfully`,
      data: job
    };
  }

  async deleteJob(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to delete jobs');
    }

    const { jobId } = params;

    const job = await Job.findById(jobId);
    if (!job) {
      throw new Error('Job not found');
    }

    await Job.findByIdAndDelete(jobId);

    return {
      success: true,
      message: `Job "${job.title}" deleted successfully`
    };
  }

  // ==================== MISSING APPLICATION METHODS ====================

  async getApplications(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to view applications');
    }

    const { status, search, limit = 20 } = params;
    let query = {};

    if (status) query.status = status;
    if (search) {
      query.$or = [
        { fullname: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    const applications = await Application.find(query)
      .populate('jobId', 'title jobType location')
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    return {
      success: true,
      message: `Found ${applications.length} applications`,
      data: applications
    };
  }

  async updateApplicationStatus(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to update application status');
    }

    const { applicationId, status, feedback } = params;

    const application = await Application.findById(applicationId);
    if (!application) {
      throw new Error('Application not found');
    }

    application.status = status;
    if (feedback) application.feedback = feedback;
    await application.save();

    return {
      success: true,
      message: `Application status updated to "${status}"`,
      data: application
    };
  }

  async deleteApplication(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to delete applications');
    }

    const { applicationId } = params;

    const application = await Application.findById(applicationId);
    if (!application) {
      throw new Error('Application not found');
    }

    await Application.findByIdAndDelete(applicationId);

    return {
      success: true,
      message: `Application from ${application.fullname} deleted successfully`
    };
  }

  // ==================== MISSING EVALUATION METHODS ====================

  async createEvaluation(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to create evaluations');
    }

    const { userId, performance, goals, feedback } = params;

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const newEvaluation = new Evaluation({
      userId,
      evaluatorId: context.userId,
      performance: performance || 3,
      goals: goals || 'No specific goals set',
      feedback: feedback || 'No feedback provided',
      evaluationDate: new Date()
    });

    await newEvaluation.save();

    return {
      success: true,
      message: `Evaluation created for ${user.name}`,
      data: newEvaluation
    };
  }

  async getEvaluations(params, context) {
    const { userId, limit = 10 } = params;
    let query = {};

    // Regular users can only see their own evaluations
    if (context.userRole === 'user') {
      query.userId = context.userId;
    } else if (userId) {
      query.userId = userId;
    }

    const evaluations = await Evaluation.find(query)
      .populate('userId', 'name email department')
      .populate('evaluatorId', 'name email')
      .limit(parseInt(limit))
      .sort({ evaluationDate: -1 });

    return {
      success: true,
      message: `Found ${evaluations.length} evaluations`,
      data: evaluations
    };
  }

  async updateEvaluation(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to update evaluations');
    }

    const { evaluationId, updates } = params;

    const evaluation = await Evaluation.findById(evaluationId);
    if (!evaluation) {
      throw new Error('Evaluation not found');
    }

    Object.assign(evaluation, updates);
    await evaluation.save();

    return {
      success: true,
      message: 'Evaluation updated successfully',
      data: evaluation
    };
  }

  // ==================== MISSING NOTIFICATION METHODS ====================

  async getNotifications(params, context) {
    const { limit = 10 } = params;

    const notifications = await Notification.find({ userId: context.userId })
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    return {
      success: true,
      message: `Found ${notifications.length} notifications`,
      data: notifications
    };
  }

  async markNotificationRead(params, context) {
    const { notificationId } = params;

    const notification = await Notification.findOne({
      _id: notificationId,
      userId: context.userId
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    notification.read = true;
    await notification.save();

    return {
      success: true,
      message: 'Notification marked as read'
    };
  }

  async deleteNotification(params, context) {
    const { notificationId } = params;

    const notification = await Notification.findOne({
      _id: notificationId,
      userId: context.userId
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    await Notification.findByIdAndDelete(notificationId);

    return {
      success: true,
      message: 'Notification deleted successfully'
    };
  }

  // ==================== SEARCH ACTIONS ====================

  async searchUsers(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to search users');
    }

    return await this.getUsers(params, context);
  }

  async searchJobs(params, context) {
    const { search, limit = 20 } = params;
    let query = {};

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } },
        { jobType: { $regex: search, $options: 'i' } }
      ];
    }

    const jobs = await Job.find(query)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    return {
      success: true,
      message: `Found ${jobs.length} jobs`,
      data: jobs
    };
  }

  async searchTasks(params, context) {
    return await this.getTasks(params, context);
  }

  async searchApplications(params, context) {
    if (!['admin', 'hr'].includes(context.userRole)) {
      throw new Error('Insufficient permissions to search applications');
    }

    const { search, limit = 20 } = params;
    let query = {};

    if (search) {
      query.$or = [
        { fullname: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { position: { $regex: search, $options: 'i' } }
      ];
    }

    const applications = await Application.find(query)
      .populate('jobId', 'title jobType location')
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    return {
      success: true,
      message: `Found ${applications.length} applications`,
      data: applications
    };
  }
}

// Singleton instance
const actionService = new ActionService();

module.exports = actionService;
