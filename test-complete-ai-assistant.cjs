const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

class AIAssistantTester {
  constructor() {
    this.token = null;
    this.userRole = null;
  }

  async login(email, password) {
    try {
      console.log(`🔐 Logging in as ${email}...`);
      const response = await axios.post(`${BASE_URL}/api/auth/login`, {
        email,
        password
      });

      this.token = response.data.token;
      this.userRole = response.data.role || response.data.user?.role;
      console.log(`✅ Login successful! Role: ${this.userRole}`);
      return true;
    } catch (error) {
      console.error('❌ Login failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async sendMessage(message) {
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/chat`, {
        message
      }, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      return response.data;
    } catch (error) {
      console.error('❌ Chat error:', error.response?.data?.message || error.message);
      return null;
    }
  }

  async testConversation(messages) {
    console.log(`\n🤖 Testing AI Assistant Conversation (Role: ${this.userRole})`);
    console.log('=' * 60);

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      console.log(`\n👤 User: "${message}"`);

      const response = await this.sendMessage(message);
      if (response) {
        console.log(`🤖 Assistant: ${response.content.substring(0, 200)}${response.content.length > 200 ? '...' : ''}`);
        console.log(`🎯 Intent: ${response.intent} (${Math.round(response.confidence * 100)}%)`);
        console.log(`⚡ Response time: ${response.responseTime}ms`);

        if (response.suggestions && response.suggestions.length > 0) {
          console.log(`💡 Suggestions: ${response.suggestions.join(', ')}`);
        }
      }

      // Small delay between messages
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  async runTests() {
    console.log('🚀 Starting Complete AI Assistant Tests\n');

    // Test as regular user first
    const userLoginSuccess = await this.login('<EMAIL>', 'test123');
    if (userLoginSuccess) {
      await this.testConversation([
        'Hi there!',
        'What can you help me with?',
        'Check my leave balance',
        'Show my tasks',
        'Check in for work',
        'View my notifications',
        'What are the company policies?'
      ]);
    }

    console.log('\n🎉 All tests completed!');
  }
}

// Run the tests
const tester = new AIAssistantTester();
tester.runTests().catch(console.error);
