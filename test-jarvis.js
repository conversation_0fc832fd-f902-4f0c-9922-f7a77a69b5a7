/**
 * Jarvis Test Script
 * Tests all Jarvis functionality to identify and fix any issues
 */

const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'test123'
};

class JarvisTestSuite {
  constructor() {
    this.token = null;
    this.conversationId = null;
    this.testResults = [];
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🤖 Starting Jarvis Test Suite...\n');

    try {
      // Test 1: Authentication
      await this.testAuthentication();

      // Test 2: AI Service Status
      await this.testAIServiceStatus();

      // Test 3: Basic Chat Functionality
      await this.testBasicChat();

      // Test 4: Name Recognition
      await this.testNameRecognition();

      // Test 5: Intent Classification
      await this.testIntentClassification();

      // Test 6: Context Awareness
      await this.testContextAwareness();

      // Test 7: OpenAI Integration
      await this.testOpenAIIntegration();

      // Test 8: Intelligent Agent Core
      await this.testIntelligentAgentCore();

      // Print results
      this.printTestResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
    }
  }

  /**
   * Test authentication
   */
  async testAuthentication() {
    console.log('🔐 Testing Authentication...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/auth/login`, TEST_USER);
      
      if (response.data.success && response.data.token) {
        this.token = response.data.token;
        this.addTestResult('Authentication', true, 'Login successful');
      } else {
        this.addTestResult('Authentication', false, 'Login failed - no token received');
      }
    } catch (error) {
      this.addTestResult('Authentication', false, `Login error: ${error.message}`);
    }
  }

  /**
   * Test AI service status
   */
  async testAIServiceStatus() {
    console.log('🧠 Testing AI Service Status...');
    
    try {
      const response = await axios.get(`${BASE_URL}/api/ai/openai/status`, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success) {
        const status = response.data.data;
        this.addTestResult('AI Service Status', true, `Available: ${status.available}, Model: ${status.model}`);
      } else {
        this.addTestResult('AI Service Status', false, 'Status check failed');
      }
    } catch (error) {
      this.addTestResult('AI Service Status', false, `Status error: ${error.message}`);
    }
  }

  /**
   * Test basic chat functionality
   */
  async testBasicChat() {
    console.log('💬 Testing Basic Chat...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/chat/message`, {
        message: 'Hello, how are you?'
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success && response.data.data.assistantMessage) {
        this.conversationId = response.data.data.conversationId;
        this.addTestResult('Basic Chat', true, 'Chat response received');
      } else {
        this.addTestResult('Basic Chat', false, 'No chat response received');
      }
    } catch (error) {
      this.addTestResult('Basic Chat', false, `Chat error: ${error.message}`);
    }
  }

  /**
   * Test name recognition
   */
  async testNameRecognition() {
    console.log('🎯 Testing Name Recognition...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/chat/message`, {
        conversationId: this.conversationId,
        message: 'Hey Jarvis, can you help me?'
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success) {
        const assistantMessage = response.data.data.assistantMessage.content;
        const recognizedName = assistantMessage.toLowerCase().includes('jarvis');
        this.addTestResult('Name Recognition', recognizedName, recognizedName ? 'Jarvis recognized his name' : 'Name not recognized');
      } else {
        this.addTestResult('Name Recognition', false, 'Name recognition test failed');
      }
    } catch (error) {
      this.addTestResult('Name Recognition', false, `Name recognition error: ${error.message}`);
    }
  }

  /**
   * Test intent classification
   */
  async testIntentClassification() {
    console.log('🎯 Testing Intent Classification...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/classify-intent`, {
        text: 'I want to request vacation leave'
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success && response.data.data.classification) {
        const intent = response.data.data.classification.intent;
        const isLeaveIntent = intent.includes('leave');
        this.addTestResult('Intent Classification', isLeaveIntent, `Intent: ${intent}`);
      } else {
        this.addTestResult('Intent Classification', false, 'Intent classification failed');
      }
    } catch (error) {
      this.addTestResult('Intent Classification', false, `Intent error: ${error.message}`);
    }
  }

  /**
   * Test context awareness
   */
  async testContextAwareness() {
    console.log('🧭 Testing Context Awareness...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/context/process`, {
        contextType: 'leave_request',
        action: 'form_filling',
        data: { formType: 'leave_request' }
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success) {
        this.addTestResult('Context Awareness', true, 'Context processing successful');
      } else {
        this.addTestResult('Context Awareness', false, 'Context processing failed');
      }
    } catch (error) {
      this.addTestResult('Context Awareness', false, `Context error: ${error.message}`);
    }
  }

  /**
   * Test OpenAI integration
   */
  async testOpenAIIntegration() {
    console.log('🤖 Testing OpenAI Integration...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/openai/test`, {}, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success) {
        this.addTestResult('OpenAI Integration', true, 'OpenAI test successful');
      } else {
        this.addTestResult('OpenAI Integration', false, response.data.message || 'OpenAI test failed');
      }
    } catch (error) {
      this.addTestResult('OpenAI Integration', false, `OpenAI error: ${error.message}`);
    }
  }

  /**
   * Test intelligent agent core
   */
  async testIntelligentAgentCore() {
    console.log('🧠 Testing Intelligent Agent Core...');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/ai/chat/message`, {
        conversationId: this.conversationId,
        message: 'What can you tell me about the HR system?'
      }, {
        headers: { Authorization: `Bearer ${this.token}` }
      });
      
      if (response.data.success) {
        const metadata = response.data.data.assistantMessage.metadata;
        const isIntelligentAgent = metadata && metadata.intelligentAgent;
        this.addTestResult('Intelligent Agent Core', isIntelligentAgent, 
          isIntelligentAgent ? 'Intelligent agent active' : 'Using fallback system');
      } else {
        this.addTestResult('Intelligent Agent Core', false, 'Agent core test failed');
      }
    } catch (error) {
      this.addTestResult('Intelligent Agent Core', false, `Agent core error: ${error.message}`);
    }
  }

  /**
   * Add test result
   */
  addTestResult(testName, passed, message) {
    this.testResults.push({
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
  }

  /**
   * Print test results summary
   */
  printTestResults() {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    console.log('\nDetailed Results:');
    this.testResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.message}`);
    });
    
    if (passed === total) {
      console.log('\n🎉 All tests passed! Jarvis is working perfectly!');
    } else {
      console.log('\n⚠️  Some tests failed. Check the issues above.');
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new JarvisTestSuite();
  testSuite.runAllTests().catch(console.error);
}

module.exports = JarvisTestSuite;
