const express = require('express');
const router = express.Router();
const LeaveRequest = require('../models/LeaveRequest');

// POST /api/leaves
router.post('/', async (req, res) => {
  try {
    const { userId, startDate, endDate, reason } = req.body;

    const leave = new LeaveRequest({ userId, startDate, endDate, reason });
    await leave.save();

    res.status(201).json({ message: 'Leave request submitted', leave });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
