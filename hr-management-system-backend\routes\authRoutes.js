const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/user');
const LoginHistory = require('../models/LoginHistory');
const AuditLog = require('../models/AuditLog');
const router = express.Router();

// Helper function to parse user agent
const parseUserAgent = (userAgentString) => {
  if (!userAgentString) return { browser: { name: 'Unknown', version: '' }, os: { name: 'Unknown', version: '' }, device: 'Unknown' };

  // Simple parsing logic - in production, consider using a library like 'ua-parser-js'
  const browser = userAgentString.includes('Chrome') ? 'Chrome' :
                 userAgentString.includes('Firefox') ? 'Firefox' :
                 userAgentString.includes('Safari') ? 'Safari' :
                 userAgentString.includes('Edge') ? 'Edge' :
                 userAgentString.includes('MSIE') || userAgentString.includes('Trident') ? 'Internet Explorer' :
                 'Unknown';

  const os = userAgentString.includes('Windows') ? 'Windows' :
            userAgentString.includes('Mac') ? 'MacOS' :
            userAgentString.includes('Linux') ? 'Linux' :
            userAgentString.includes('Android') ? 'Android' :
            userAgentString.includes('iOS') ? 'iOS' :
            'Unknown';

  const device = userAgentString.includes('Mobile') ? 'Mobile' :
                userAgentString.includes('Tablet') ? 'Tablet' :
                'Desktop';

  return {
    browser: { name: browser, version: '' },
    os: { name: os, version: '' },
    device
  };
};

// Test route to create a test user (REMOVE IN PRODUCTION)
router.get('/create-test-user', async (req, res) => {
  try {
    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });

    if (existingUser) {
      // Update the existing user's password
      existingUser.password = await bcrypt.hash('password123', 10);
      existingUser.role = 'user';
      await existingUser.save();

      return res.json({
        message: 'Test user updated successfully',
        user: {
          email: existingUser.email,
          role: existingUser.role,
          id: existingUser._id
        }
      });
    }

    // Create a new test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUser = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'user',
      job: 'Tester',
      birthdate: new Date('1990-01-01'),
      creationDate: new Date(),
      department: 'IT'
    });

    await testUser.save();

    res.json({
      message: 'Test user created successfully',
      user: {
        email: testUser.email,
        role: testUser.role,
        id: testUser._id
      }
    });
  } catch (error) {
    console.error('Error creating test user:', error);
    res.status(500).json({ message: 'Error creating test user' });
  }
});



// POST route for login
router.post('/login', async (req, res) => {
  const { email, password } = req.body;
  const ipAddress = req.ip || req.connection.remoteAddress || 'Unknown';
  const userAgentString = req.headers['user-agent'] || 'Unknown';
  const { browser, os, device } = parseUserAgent(userAgentString);

  console.log('Login attempt for email:', email);
  console.log('IP Address:', ipAddress);

  try {
    // Validate input
    if (!email || !password) {
      // Log failed login attempt due to missing fields
      try {
        await new LoginHistory({
          userId: null,
          userInfo: { email },
          status: 'FAILED',
          failureReason: 'INVALID_CREDENTIALS',
          ipAddress,
          device,
          browser,
          operatingSystem: os,
          userAgent: userAgentString
        }).save();
      } catch (logError) {
        console.error('Error logging failed login attempt:', logError);
      }

      return res.status(400).json({
        success: false,
        message: 'Email and password are required',
        errorType: 'MISSING_FIELDS'
      });
    }

    // Find the user by email
    const user = await User.findOne({ email });

    if (!user) {
      console.log('Login failed: Email not found:', email);

      // Log failed login attempt for non-existent user
      try {
        await new LoginHistory({
          userId: null,
          userInfo: { email },
          status: 'FAILED',
          failureReason: 'INVALID_CREDENTIALS',
          ipAddress,
          device,
          browser,
          operatingSystem: os,
          userAgent: userAgentString
        }).save();
      } catch (logError) {
        console.error('Error logging failed login attempt:', logError);
      }

      return res.status(401).json({
        success: false,
        message: 'Invalid email address',
        errorType: 'INVALID_EMAIL'
      });
    }

    console.log('User found:', {
      id: user._id,
      email: user.email,
      role: user.role,
      name: user.name
    });

    // Compare the entered password with the hashed password
    console.log('Comparing password for user:', email);

    try {
      // Use direct bcrypt comparison for password verification
      const isMatch = await bcrypt.compare(password, user.password);
      console.log('Password comparison result:', isMatch);

      if (!isMatch) {
        console.log('Login failed: Invalid password for user:', email);

        // Log failed login attempt due to invalid password
        try {
          await new LoginHistory({
            userId: user._id,
            userInfo: {
              name: user.name,
              email: user.email,
              role: user.role
            },
            status: 'FAILED',
            failureReason: 'INVALID_CREDENTIALS',
            ipAddress,
            device,
            browser,
            operatingSystem: os,
            userAgent: userAgentString
          }).save();
        } catch (logError) {
          console.error('Error logging failed login attempt:', logError);
        }

        return res.status(401).json({
          success: false,
          message: 'Invalid email or password',
          errorType: 'INVALID_CREDENTIALS'
        });
      }

      console.log('Password match successful');
    } catch (compareError) {
      console.error('Error during password comparison:', compareError);

      // Log error during login
      try {
        await new LoginHistory({
          userId: user._id,
          userInfo: {
            name: user.name,
            email: user.email,
            role: user.role
          },
          status: 'FAILED',
          failureReason: 'SERVER_ERROR',
          ipAddress,
          device,
          browser,
          operatingSystem: os,
          userAgent: userAgentString
        }).save();
      } catch (logError) {
        console.error('Error logging failed login attempt:', logError);
      }

      return res.status(500).json({
        success: false,
        message: 'Server error during authentication',
        errorType: 'SERVER_ERROR'
      });
    }

    console.log('Login successful for user:', email, 'with role:', user.role);

    // Generate a JWT token with the user data and role
    console.log('Creating token for user:', user.email, 'with role:', user.role);

    const payload = {
      id: user._id,
      email: user.email,
      role: user.role,
      name: user.name
    };

    const token = jwt.sign(
      payload,
      'yourSecretKey', // Secret key for JWT signing
      { expiresIn: '24h' } // Set token expiration time to 24 hours
    );

    // Verify the token was created correctly
    try {
      const decoded = jwt.verify(token, 'yourSecretKey');
      console.log('Token verified successfully');
    } catch (verifyError) {
      console.error('Token verification failed:', verifyError);

      // Log token verification failure
      try {
        await new LoginHistory({
          userId: user._id,
          userInfo: {
            name: user.name,
            email: user.email,
            role: user.role
          },
          status: 'FAILED',
          failureReason: 'SERVER_ERROR',
          ipAddress,
          device,
          browser,
          operatingSystem: os,
          userAgent: userAgentString
        }).save();
      } catch (logError) {
        console.error('Error logging failed login attempt:', logError);
      }

      return res.status(500).json({
        success: false,
        message: 'Error generating authentication token',
        errorType: 'SERVER_ERROR'
      });
    }

    // Log successful login
    let loginHistoryEntry;
    try {
      loginHistoryEntry = await new LoginHistory({
        userId: user._id,
        userInfo: {
          name: user.name,
          email: user.email,
          role: user.role
        },
        status: 'SUCCESS',
        ipAddress,
        device,
        browser,
        operatingSystem: os,
        userAgent: userAgentString
      }).save();

      // Also create an audit log entry
      await new AuditLog({
        userId: user._id,
        userInfo: {
          name: user.name,
          email: user.email,
          role: user.role
        },
        action: 'USER_LOGIN',
        resourceType: 'USER',
        resourceId: user._id,
        description: `User ${user.name} (${user.email}) logged in successfully`,
        ipAddress,
        userAgent: userAgentString
      }).save();

      console.log('Login history and audit log created successfully');
    } catch (logError) {
      console.error('Error logging successful login:', logError);
      // Continue with login process even if logging fails
    }

    // Send the token and role in the response
    const response = {
      success: true,
      token,
      role: user.role, // Send the role so the frontend can decide where to redirect
      name: user.name, // Include the user's name
      userId: user._id, // Include the user's ID
      loginId: loginHistoryEntry?._id // Include login history ID for logout tracking
    };

    console.log('Login successful, sending response');

    res.json(response);
  } catch (error) {
    console.error('Login error:', error);

    // Log server error during login
    try {
      await new LoginHistory({
        userId: null,
        userInfo: { email },
        status: 'FAILED',
        failureReason: 'SERVER_ERROR',
        ipAddress,
        device,
        browser,
        operatingSystem: os,
        userAgent: userAgentString
      }).save();
    } catch (logError) {
      console.error('Error logging failed login attempt:', logError);
    }

    res.status(500).json({
      success: false,
      message: 'Server error during login',
      errorType: 'SERVER_ERROR'
    });
  }
});

/**
 * @route   GET /api/auth/reset-password/:email
 * @desc    Reset a user's password to a known value for testing
 * @access  Public (should be removed in production)
 */
router.get('/reset-password/:email', async (req, res) => {
  try {
    const email = req.params.email;
    const newPassword = '00000000'; // Simple password for testing

    // Find the user by email
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        email
      });
    }

    // Set the plain password - the pre-save hook will handle hashing
    user.password = newPassword;

    // Save the user - the pre-save hook will hash the password if needed
    await user.save();

    // Get the hashed password for logging
    const hashedPassword = user.password;

    console.log('Password reset for user:', email);
    console.log('New password (unhashed):', newPassword);
    console.log('New hashed password:', hashedPassword);

    res.json({
      success: true,
      message: 'Password reset successfully',
      email,
      newPassword
    });
  } catch (error) {
    console.error('Error resetting password:', error);
    res.status(500).json({
      success: false,
      message: 'Error resetting password',
      error: error.message,
      stack: error.stack
    });
  }
});

/**
 * @route   GET /api/auth/test-password/:email/:password
 * @desc    Test if a password matches the stored hash for a user
 * @access  Public (should be removed in production)
 */
router.get('/test-password/:email/:password', async (req, res) => {
  try {
    const { email, password } = req.params;

    // Find the user by email
    const user = await User.findOne({ email });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        email
      });
    }

    console.log('Testing password for user:', email);
    console.log('Password to test:', password);
    console.log('Stored hashed password:', user.password);

    // Test direct bcrypt comparison
    const bcryptResult = await bcrypt.compare(password, user.password);
    console.log('Direct bcrypt.compare result:', bcryptResult);

    // Test model method
    const modelResult = await user.comparePassword(password);
    console.log('Model comparePassword result:', modelResult);

    // Return results
    res.json({
      success: true,
      email,
      password,
      storedHash: user.password,
      bcryptResult,
      modelResult,
      match: bcryptResult && modelResult
    });
  } catch (error) {
    console.error('Error testing password:', error);
    res.status(500).json({
      success: false,
      message: 'Error testing password',
      error: error.message,
      stack: error.stack
    });
  }
});

/**
 * @route   POST /api/auth/forgot-password
 * @desc    Reset user's password and send it to their email
 * @access  Public
 */
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    console.log('Forgot password request for email:', email);

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Find the user by email
    const user = await User.findOne({ email });

    if (!user) {
      console.log('User not found for email:', email);
      return res.status(404).json({
        success: false,
        message: 'Email not found. Please check and try again.'
      });
    }

    console.log('User found for forgot password:', user.email);

    // Reset password to a default value
    const newPassword = '00000000';

    // Set the plain password - the pre-save hook will handle hashing
    user.password = newPassword;
    await user.save();

    console.log('Password reset successfully for user:', email);

    // Return the user's information with the new password
    res.status(200).json({
      success: true,
      email: user.email,
      newPassword: newPassword, // Send the plain password for email
      name: user.name,
      message: 'Password reset successfully'
    });
  } catch (err) {
    console.error('Error in forgot password:', err);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: err.message
    });
  }
});

// Logout route
router.post('/logout', async (req, res) => {
  try {
    const { userId, loginId } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress || 'Unknown';
    const userAgentString = req.headers['user-agent'] || 'Unknown';

    console.log('Logout request received for user ID:', userId);

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required for logout'
      });
    }

    // Find the user
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update login history if loginId is provided
    if (loginId) {
      try {
        const loginHistory = await LoginHistory.findById(loginId);
        if (loginHistory) {
          await loginHistory.recordLogout();
          console.log('Login history updated with logout time');
        }
      } catch (error) {
        console.error('Error updating login history with logout time:', error);
        // Continue with logout process even if updating login history fails
      }
    }

    // Create audit log entry for logout
    try {
      await new AuditLog({
        userId: user._id,
        userInfo: {
          name: user.name,
          email: user.email,
          role: user.role
        },
        action: 'USER_LOGOUT',
        resourceType: 'USER',
        resourceId: user._id,
        description: `User ${user.name} (${user.email}) logged out`,
        ipAddress,
        userAgent: userAgentString
      }).save();

      console.log('Logout audit log created successfully');
    } catch (error) {
      console.error('Error creating logout audit log:', error);
      // Continue with logout process even if creating audit log fails
    }

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during logout'
    });
  }
});

module.exports = router; // Make sure to export 'router' here