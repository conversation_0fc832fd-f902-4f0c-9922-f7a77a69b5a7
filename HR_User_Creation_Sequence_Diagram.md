# HR User Creation - MVC Sequence Diagram

## Overview

This advanced sequence diagram illustrates the complete MVC architecture flow for HR user creation in the HR Management System. It shows the interaction between frontend React components, backend Express.js routes, middleware layers, Mongoose models, and MongoDB database.

## Features

- **Modern Mermaid Syntax**: Advanced styling and interactive elements
- **Color-coded Sections**: Different phases highlighted with distinct colors
- **Emoji Icons**: Visual indicators for different components
- **Detailed Error Handling**: Complete alternative flows for validation and errors
- **Real-time Updates**: Optional section showing list refresh functionality

```mermaid
sequenceDiagram
    participant HRM as 👤 HR Manager
    participant AD as 🖥️ Admin Dashboard<br/>(React Component)
    participant UFD as 📝 User Form Dialog<br/>(Material-UI)
    participant API as 🔗 API Service<br/>(Axios Client)
    participant AR as 🛡️ Express Router<br/>(Admin Routes)
    participant AM as 🔐 Auth Middleware
    participant VM as ✅ Validation Middleware
    participant UM as 📊 User Model<br/>(Mongoose)
    participant DB as 🗄️ MongoDB Database

    Note over HRM, DB: HR User Creation - MVC Architecture Flow

    rect rgb(240, 248, 255)
        Note over HRM, UFD: 🚀 User Initiates HR Creation
        HRM->>+AD: Click "Add New User" button
        AD->>+UFD: Open dialog (dialogType='add')
        UFD->>UFD: Initialize empty form state
        Note right of UFD: userData = {<br/>  name: '',<br/>  email: '',<br/>  password: '',<br/>  role: 'user',<br/>  job: '',<br/>  department: 'Other',<br/>  birthdate: ''<br/>}
        UFD-->>-HRM: Display user creation form
    end

    rect rgb(248, 255, 248)
        Note over HRM, API: 📝 Form Data Entry & Validation
        HRM->>+UFD: Fill form fields
        UFD->>UFD: Update form state on change
        Note right of UFD: setUserData({<br/>  ...userData,<br/>  [field]: value<br/>})
        HRM->>UFD: Submit form
        UFD->>UFD: Validate form data (client-side)
        
        alt ❌ Form validation fails
            UFD->>UFD: Display error toast
            UFD-->>HRM: Show validation errors
            Note right of UFD: Validation checks:<br/>• Required fields<br/>• Name ≥ 3 chars<br/>• Job ≥ 3 chars<br/>• Password ≥ 8 chars<br/>• Age ≥ 18 years
        else ✅ Form validation passes
            UFD->>+API: handleUserFormSubmit()
        end
    end

    rect rgb(255, 248, 248)
        Note over API, AM: 🔒 API Request Processing & Authentication
        API->>API: Add JWT token to headers
        Note right of API: Authorization: Bearer {token}
        API->>+AR: POST /api/admin/users/create
        AR->>+AM: authenticate(req, res, next)
        AM->>AM: Verify JWT token
        AM->>AM: Extract user from token
        
        alt ❌ Token invalid or expired
            AM-->>API: 401 Unauthorized
            API-->>UFD: Authentication error
            UFD-->>HRM: Display error message
        else ✅ Token valid
            AM->>AR: next() - Continue
            deactivate AM
        end

        AR->>+AM: authorizeRoles('admin')
        AM->>AM: Check user role
        
        alt ❌ User not admin
            AM-->>API: 403 Forbidden
            API-->>UFD: Authorization error
            UFD-->>HRM: Display error message
        else ✅ User is admin
            AM->>AR: next() - Continue
            deactivate AM
        end
    end

    rect rgb(255, 255, 240)
        Note over AR, VM: ✅ Server-Side Validation
        AR->>+VM: Express validator checks
        VM->>VM: Validate request body
        Note right of VM: Validation rules:<br/>• name: notEmpty()<br/>• email: isEmail()<br/>• password: min(8)<br/>• role: notEmpty()
        
        alt ❌ Validation errors exist
            VM-->>API: 400 Bad Request
            API-->>UFD: Validation errors
            UFD-->>HRM: Display field errors
        else ✅ Validation passes
            VM->>AR: Continue processing
            deactivate VM
        end
    end

    rect rgb(248, 248, 255)
        Note over AR, DB: 🔍 Business Logic Processing
        AR->>+UM: findOne({ email })
        UM->>+DB: Query users collection
        DB-->>-UM: Return existing user or null
        
        alt ❌ User already exists
            UM-->>AR: Existing user found
            AR-->>API: 409 Conflict - User exists
            API-->>UFD: User exists error
            UFD-->>HRM: Display error message
        else ✅ User doesn't exist
            UM-->>AR: null (no existing user)
        end
    end

    rect rgb(240, 255, 240)
        Note over AR, DB: 👤 User Creation Process
        AR->>UM: new User(userData)
        UM->>UM: Create user instance
        Note right of UM: User data:<br/>• name, email, password<br/>• role, job, department<br/>• creationDate: new Date()<br/>• birthdate (if provided)
        
        UM->>UM: Pre-save hook triggered
        UM->>UM: Hash password with bcrypt
        Note right of UM: Password hashing:<br/>• Check if already hashed<br/>• Generate salt (10 rounds)<br/>• Hash plain password
        
        UM->>+DB: save() - Insert new user
        
        alt ❌ Database error
            DB-->>UM: Save error
            UM-->>AR: Database error
            AR-->>API: 500 Server Error
            API-->>UFD: Server error
            UFD-->>HRM: Display error message
        else ✅ Save successful
            DB-->>-UM: User saved successfully
            UM-->>-AR: Return created user
        end
    end

    rect rgb(240, 255, 240)
        Note over AR, AD: 🎉 Success Response & UI Updates
        AR-->>-API: 201 Created - User created
        Note right of AR: Response:<br/>{<br/>  message: 'User created successfully',<br/>  user: newUser<br/>}
        API-->>-UFD: Success response
        UFD->>UFD: Display success toast
        UFD->>UFD: Reset form state
        UFD->>-AD: Close dialog
        AD->>AD: Refresh users list
        AD-->>-HRM: Show success notification
    end

    rect rgb(255, 250, 240)
        Note over AD, DB: 🔄 Optional: Real-time Updates
        AD->>+API: Fetch updated users list
        API->>+AR: GET /api/admin/users
        AR->>+UM: find() - Get all users
        UM->>+DB: Query users collection
        DB-->>-UM: Return users array
        UM-->>-AR: Users list
        AR-->>-API: 200 OK - Users data
        API-->>-AD: Updated users list
        AD->>AD: Update state with new data
        AD-->>HRM: Display updated user list
    end

    Note over HRM, DB: ✨ HR User Creation Complete ✨
```

## Technical Implementation Details

### Frontend Components
- **Admin Dashboard**: Main React component managing user interface
- **User Form Dialog**: Material-UI dialog with form validation
- **API Service**: Axios-based HTTP client with JWT authentication

### Backend Architecture
- **Express Router**: RESTful API endpoints with middleware chain
- **Authentication Middleware**: JWT token verification and role-based access
- **Validation Middleware**: Express-validator for input sanitization
- **User Model**: Mongoose schema with pre-save password hashing

### Database Operations
- **MongoDB**: Document-based storage with user collection
- **Indexing**: Email field indexed for unique constraint
- **Validation**: Schema-level validation for data integrity

### Security Features
- **JWT Authentication**: Stateless token-based authentication
- **Role-based Authorization**: Admin-only user creation access
- **Password Hashing**: Bcrypt with salt rounds for security
- **Input Validation**: Client and server-side validation layers

### Error Handling
- **Client-side Validation**: Real-time form validation with user feedback
- **Server-side Validation**: Express-validator with detailed error messages
- **Database Constraints**: Unique email validation and error handling
- **HTTP Status Codes**: Proper REST API status code implementation

## Advantages of Mermaid over PlantUML

1. **Modern Syntax**: Cleaner, more readable diagram syntax
2. **Better Styling**: Advanced color coding and visual elements
3. **Interactive Elements**: Clickable components and hover effects
4. **GitHub Integration**: Native support in GitHub and GitLab
5. **Real-time Rendering**: Faster rendering in modern browsers
6. **Responsive Design**: Better mobile and responsive display
7. **Emoji Support**: Visual icons for better component identification
8. **Color Sections**: Background colors for logical grouping
