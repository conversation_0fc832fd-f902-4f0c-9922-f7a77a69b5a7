import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import PropTypes from 'prop-types';
import { Box, LinearProgress } from '@mui/material';

/**
 * TransitionLayout component that provides page transition animations
 * and loading indicators for route changes
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to be wrapped
 * @returns {JSX.Element} Animated layout component
 */
const TransitionLayout = ({ children }) => {
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);

  // Instant transitions - no artificial delays
  useEffect(() => {
    setIsLoading(true);
    // Use requestAnimationFrame for smooth, immediate transitions
    const frame = requestAnimationFrame(() => {
      setIsLoading(false);
    });

    return () => cancelAnimationFrame(frame);
  }, [location.pathname]);

  return (
    <Box className="page-transition-container">
      {isLoading && (
        <LinearProgress
          color="primary"
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 9999,
            height: 3
          }}
        />
      )}

      <AnimatePresence mode="sync" initial={false}>
        <Box key={location.pathname} style={{ width: '100%', height: '100%' }}>
          {children}
        </Box>
      </AnimatePresence>
    </Box>
  );
};

TransitionLayout.propTypes = {
  children: PropTypes.node.isRequired,
};

export default TransitionLayout;
