const axios = require('axios');

async function testAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test AI chat
    console.log('\n🤖 Testing AI Assistant...');

    const messages = [
      'Hello!',
      'What can you help me with?',
      'Check my leave balance',
      'Show my tasks',
      'Check in for work'
    ];

    for (const message of messages) {
      console.log(`\n👤 User: "${message}"`);

      try {
        const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
          message
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const assistantMessage = response.data.data.assistantMessage;
        const classification = response.data.data.classification;
        const suggestions = response.data.data.suggestions;

        console.log(`🤖 Assistant: ${assistantMessage.content}`);
        console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
        console.log(`⚡ Response time: ${assistantMessage.metadata.responseTime}ms`);

        if (suggestions && suggestions.length > 0) {
          console.log(`💡 Suggestions: ${suggestions.join(', ')}`);
        }
      } catch (error) {
        console.error('❌ Chat error:', error.response?.data?.message || error.message);
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testAI();
