import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Checkbox,
  FormGroup,
  FormControlLabel,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Stepper,
  Step,
  StepLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Description as DescriptionIcon,
  PictureAsPdf as PdfIcon,
  GetApp as DownloadIcon,
  Print as PrintIcon,

  Close as CloseIcon,
  People as PeopleIcon,
  Work as WorkIcon,
  EventNote as EventNoteIcon,
  Assignment as AssignmentIcon,
  CalendarToday as CalendarIcon,
  CheckCircle as CheckCircleIcon,
  TableChart as TableChartIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

// Report types
const reportTypes = [
  { id: 'users', name: 'Users Report', icon: <PeopleIcon />, description: 'Generate a report of all users in the system with their details.' },
  { id: 'applications', name: 'Applications Report', icon: <AssignmentIcon />, description: 'Generate a report of job applications with status and details.' },
  { id: 'leave_requests', name: 'Leave Requests Report', icon: <EventNoteIcon />, description: 'Generate a report of leave requests with status and details.' },
  { id: 'jobs', name: 'Jobs Report', icon: <WorkIcon />, description: 'Generate a report of all job postings with details.' },
  { id: 'attendance', name: 'Attendance Report', icon: <CalendarIcon />, description: 'Generate a report of employee attendance records.' }
];

const ReportGenerator = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [reportType, setReportType] = useState('');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)), // Last month
    endDate: new Date()
  });
  // Initialize with empty filters - will be set based on report type
  const [filters, setFilters] = useState({});
  const [fields, setFields] = useState({
    users: {
      name: true,
      email: true,
      role: true,
      job: true,
      department: true,
      creationDate: true,
      lastLogin: true,
      status: true
    },
    applications: {
      applicantName: true,
      jobTitle: true,
      applicationDate: true,
      status: true,
      cvScore: true,
      contactInfo: true
    },
    leave_requests: {
      employeeName: true,
      leaveType: true,
      startDate: true,
      endDate: true,
      status: true,
      requestDate: true,
      approverName: true
    },
    jobs: {
      title: true,
      department: true,
      location: true,
      postDate: true,
      closingDate: true,
      status: true,
      applicantCount: true
    },
    attendance: {
      employeeName: true,
      department: true,
      date: true,
      checkInTime: true,
      checkOutTime: true,
      totalHours: true,
      status: true
    }
  });
  const [reportPreview, setReportPreview] = useState(null);
  const [reportUrl, setReportUrl] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Steps for the report generation process
  const steps = ['Select Report Type', 'Configure Filters', 'Select Fields', 'Generate Report'];

  // Handle report type change
  const handleReportTypeChange = (event) => {
    setReportType(event.target.value);
  };

  // Update filters when report type changes
  useEffect(() => {
    if (reportType) {
      // Set default filters based on report type
      switch (reportType) {
        case 'users':
          setFilters({
            status: 'all',
            role: 'all',
            includeInactive: false
          });
          break;
        case 'applications':
          setFilters({
            status: 'all',
            jobId: 'all',
            dateRange: 'all'
          });
          break;
        case 'leave_requests':
          setFilters({
            status: 'all',
            leaveType: 'all',
            employeeId: 'all'
          });
          break;
        case 'jobs':
          setFilters({
            status: 'all',
            department: 'all',
            location: 'all',
            includeInactive: false
          });
          break;
        case 'attendance':
          setFilters({
            status: 'all',
            employeeId: 'all',
            department: 'all',
            dateRange: 'current_month'
          });
          break;
        default:
          setFilters({});
      }
    } else {
      setFilters({});
    }
  }, [reportType]);

  // Handle date range change
  const handleDateRangeChange = (field, value) => {
    setDateRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle field selection change
  const handleFieldChange = (reportTypeId, field, checked) => {
    setFields(prev => ({
      ...prev,
      [reportTypeId]: {
        ...prev[reportTypeId],
        [field]: checked
      }
    }));
  };

  // Handle next step
  const handleNext = () => {
    if (activeStep === 0 && !reportType) {
      toast.error('Please select a report type');
      return;
    }

    if (activeStep === steps.length - 1) {
      generateReport();
    } else {
      setActiveStep(prevStep => prevStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  // Handle reset
  const handleReset = () => {
    setActiveStep(0);
    setReportType('');
    setDateRange({
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
      endDate: new Date()
    });
    setFilters({});
    setReportPreview(null);
    setReportUrl(null);
  };

  // Generate report
  const generateReport = async () => {
    setLoading(true);
    setError(null);

    try {
      // Format dates for API
      const startDate = dateRange.startDate.toISOString().split('T')[0];
      const endDate = dateRange.endDate.toISOString().split('T')[0];

      // Build request body
      const requestBody = {
        reportType,
        dateRange: { startDate, endDate },
        filters,
        fields: fields[reportType]
      };

      // Call API to generate report
      const response = await api.post('/statistics/reports/generate', requestBody);

      // Set report preview and URL
      setReportPreview(response.data.preview);
      setReportUrl(response.data.reportUrl);

      // Show success message
      toast.success('Report generated successfully');

      // Open preview
      setPreviewOpen(true);
    } catch (error) {
      console.error('Error generating report:', error);
      setError('Failed to generate report. Please try again later.');
      toast.error('Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  // Download report
  const downloadReport = () => {
    if (reportUrl) {
      setLoading(true);
      try {
        // Get the token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          toast.error('Authentication token not found. Please log in again.');
          setLoading(false);
          return;
        }

        // Log the request details for debugging
        console.log('Downloading report from URL:', reportUrl);

        // Create a direct API call to download the report
        api.get(reportUrl, {
          responseType: 'blob',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        .then(response => {
          // Check if the response is valid
          if (!response.data || response.data.size === 0) {
            throw new Error('Empty response received');
          }

          // Check if the response is a PDF
          const contentType = response.headers['content-type'];
          if (contentType && !contentType.includes('application/pdf')) {
            // If it's not a PDF, it might be an error response
            return response.data.text().then(text => {
              try {
                const errorData = JSON.parse(text);
                throw new Error(errorData.message || 'Unknown error');
              } catch (e) {
                throw new Error('Invalid response format');
              }
            });
          }

          // Create a blob URL for the PDF
          const blob = new Blob([response.data], { type: 'application/pdf' });
          const url = window.URL.createObjectURL(blob);

          // Create a temporary link and click it to download
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `report-${Date.now()}.pdf`);
          document.body.appendChild(link);
          link.click();

          // Clean up
          window.URL.revokeObjectURL(url);
          document.body.removeChild(link);

          toast.success('Report downloaded successfully');
          setLoading(false);
        })
        .catch(error => {
          console.error('Error downloading report:', error);
          toast.error(`Failed to download report: ${error.message || 'Unknown error'}`);
          setLoading(false);
        });
      } catch (error) {
        console.error('Error downloading report:', error);
        toast.error(`Failed to download report: ${error.message || 'Unknown error'}`);
        setLoading(false);
      }
    } else {
      toast.error('No report available for download');
    }
  };

  // Print report
  const printReport = () => {
    if (reportPreview) {
      window.print();
    } else {
      toast.error('No report available for printing');
    }
  };

  // Email report functionality removed as requested

  // Close preview
  const handleClosePreview = () => {
    setPreviewOpen(false);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Format column headers for better display
  const formatColumnHeader = (key) => {
    // Convert camelCase to Title Case with spaces
    return key
      .replace(/([A-Z])/g, ' $1') // Insert a space before all capital letters
      .replace(/^./, (str) => str.toUpperCase()) // Capitalize the first letter
      .replace(/Id$/, 'ID') // Replace Id with ID
      .replace(/Cv/, 'CV'); // Replace Cv with CV
  };

  // Format cell values based on their type and column
  const formatCellValue = (key, value) => {
    if (value === null || value === undefined) return 'N/A';

    // Handle dates
    if (
      typeof value === 'string' &&
      (value.includes('T') || key.toLowerCase().includes('date') || key.toLowerCase().includes('time'))
    ) {
      try {
        const date = new Date(value);

        // Check if it's a valid date
        if (!isNaN(date.getTime())) {
          // If the key suggests it's a time field
          if (key.toLowerCase().includes('time')) {
            return date.toLocaleTimeString();
          }
          // Otherwise treat as a date
          return date.toLocaleDateString();
        }
      } catch (e) {
        // If date parsing fails, return the original value
      }
    }

    // Handle boolean values
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No';
    }

    // Handle role values with appropriate styling
    if (key === 'role') {
      // Make sure role is a valid value
      const validRoles = ['admin', 'hr', 'user'];
      const role = validRoles.includes(value) ? value : 'user';

      return (
        <Box
          component="span"
          sx={{
            display: 'inline-block',
            px: 1,
            py: 0.25,
            borderRadius: 1,
            fontSize: '0.75rem',
            fontWeight: 'bold',
            textTransform: 'capitalize',
            bgcolor:
              role === 'admin' ? 'error.light' :
              role === 'hr' ? 'warning.light' :
              'info.light',
            color:
              role === 'admin' ? 'error.dark' :
              role === 'hr' ? 'warning.dark' :
              'info.dark',
          }}
        >
          {role}
        </Box>
      );
    }

    // Handle department values with appropriate styling
    if (key === 'department') {
      // Make sure department is a valid value
      const validDepartments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations', 'IT', 'Customer Support', 'Executive', 'Other'];
      const department = validDepartments.includes(value) ? value : 'Other';

      // Color mapping for departments
      const getDepartmentColor = (dept) => {
        switch(dept) {
          case 'Engineering': return { bg: '#e3f2fd', color: '#0d47a1' }; // Blue
          case 'Marketing': return { bg: '#f3e5f5', color: '#4a148c' }; // Purple
          case 'Sales': return { bg: '#e8f5e9', color: '#1b5e20' }; // Green
          case 'HR': return { bg: '#fff3e0', color: '#e65100' }; // Orange
          case 'Finance': return { bg: '#e0f7fa', color: '#006064' }; // Cyan
          case 'Operations': return { bg: '#fce4ec', color: '#880e4f' }; // Pink
          case 'IT': return { bg: '#e8eaf6', color: '#1a237e' }; // Indigo
          case 'Customer Support': return { bg: '#f1f8e9', color: '#33691e' }; // Light Green
          case 'Executive': return { bg: '#ede7f6', color: '#4527a0' }; // Deep Purple
          default: return { bg: '#f5f5f5', color: '#424242' }; // Grey
        }
      };

      const { bg, color } = getDepartmentColor(department);

      return (
        <Box
          component="span"
          sx={{
            display: 'inline-block',
            px: 1,
            py: 0.25,
            borderRadius: 1,
            fontSize: '0.75rem',
            fontWeight: 'bold',
            bgcolor: bg,
            color: color,
          }}
        >
          {department}
        </Box>
      );
    }

    // Handle status values with appropriate styling
    if (key === 'status') {
      return (
        <Box
          component="span"
          sx={{
            display: 'inline-block',
            px: 1,
            py: 0.25,
            borderRadius: 1,
            fontSize: '0.75rem',
            fontWeight: 'bold',
            bgcolor:
              value === 'Approved' || value === 'Active' || value === 'Present' ? 'success.light' :
              value === 'Rejected' || value === 'Inactive' || value === 'Absent' ? 'error.light' :
              value === 'Pending' ? 'warning.light' :
              value === 'Late' || value === 'Half-Day' ? 'info.light' :
              'grey.200',
            color:
              value === 'Approved' || value === 'Active' || value === 'Present' ? 'success.dark' :
              value === 'Rejected' || value === 'Inactive' || value === 'Absent' ? 'error.dark' :
              value === 'Pending' ? 'warning.dark' :
              value === 'Late' || value === 'Half-Day' ? 'info.dark' :
              'grey.800',
          }}
        >
          {value}
        </Box>
      );
    }

    // Handle numeric values
    if (typeof value === 'number') {
      // Format as currency if the key suggests it's a monetary value
      if (key.toLowerCase().includes('salary') || key.toLowerCase().includes('cost') || key.toLowerCase().includes('price')) {
        return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
      }

      // Format as percentage if the key suggests it's a percentage
      if (key.toLowerCase().includes('percent') || key.toLowerCase().includes('rate') || key.toLowerCase().includes('score')) {
        return `${value.toFixed(1)}%`;
      }

      // Format with commas for large numbers
      if (value >= 1000) {
        return value.toLocaleString();
      }

      // Format with decimal places for small numbers
      if (key.toLowerCase().includes('hours')) {
        return value.toFixed(1);
      }
    }

    // Return the value as is for all other cases
    return value;
  };

  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Select Report Type
            </Typography>
            <Grid container spacing={3}>
              {reportTypes.map((type) => (
                <Grid item xs={12} sm={6} md={4} key={type.id}>
                  <Card
                    sx={{
                      height: '100%',
                      cursor: 'pointer',
                      border: reportType === type.id ? '2px solid' : '1px solid',
                      borderColor: reportType === type.id ? 'primary.main' : 'divider',
                      bgcolor: reportType === type.id ? 'action.selected' : 'background.paper'
                    }}
                    onClick={() => setReportType(type.id)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Box sx={{ mr: 2, color: 'primary.main' }}>
                          {type.icon}
                        </Box>
                        <Typography variant="h6" component="div">
                          {type.name}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {type.description}
                      </Typography>
                    </CardContent>
                    {reportType === type.id && (
                      <Box sx={{ position: 'absolute', top: 10, right: 10, color: 'primary.main' }}>
                        <CheckCircleIcon />
                      </Box>
                    )}
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Configure Filters
            </Typography>
            <Grid container spacing={3}>
              {/* Date range picker */}
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Start Date"
                    value={dateRange.startDate}
                    onChange={(date) => handleDateRangeChange('startDate', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="End Date"
                    value={dateRange.endDate}
                    onChange={(date) => handleDateRangeChange('endDate', date)}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                    slotProps={{ textField: { fullWidth: true } }}
                  />
                </LocalizationProvider>
              </Grid>

              {/* Date range presets for attendance reports */}
              {reportType === 'attendance' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Date Range Preset</InputLabel>
                    <Select
                      value={filters.dateRange || 'custom'}
                      label="Date Range Preset"
                      onChange={(e) => {
                        const value = e.target.value;
                        handleFilterChange('dateRange', value);

                        // Update date range based on selection
                        const today = new Date();
                        let startDate = new Date();
                        let endDate = new Date();

                        switch(value) {
                          case 'today':
                            // Keep startDate and endDate as today
                            break;
                          case 'yesterday':
                            startDate.setDate(today.getDate() - 1);
                            endDate.setDate(today.getDate() - 1);
                            break;
                          case 'this_week':
                            // Start from Sunday of this week
                            startDate.setDate(today.getDate() - today.getDay());
                            break;
                          case 'last_week':
                            // Start from Sunday of last week
                            startDate.setDate(today.getDate() - today.getDay() - 7);
                            endDate.setDate(today.getDate() - today.getDay() - 1);
                            break;
                          case 'current_month':
                            startDate.setDate(1);
                            break;
                          case 'last_month':
                            startDate.setMonth(today.getMonth() - 1);
                            startDate.setDate(1);
                            endDate.setDate(0); // Last day of previous month
                            break;
                          default:
                            // Don't change dates for custom
                            return;
                        }

                        setDateRange({
                          startDate,
                          endDate
                        });
                      }}
                    >
                      <MenuItem value="custom">Custom Range</MenuItem>
                      <MenuItem value="today">Today</MenuItem>
                      <MenuItem value="yesterday">Yesterday</MenuItem>
                      <MenuItem value="this_week">This Week</MenuItem>
                      <MenuItem value="last_week">Last Week</MenuItem>
                      <MenuItem value="current_month">Current Month</MenuItem>
                      <MenuItem value="last_month">Last Month</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}
              {/* Status filter - shown for all report types */}
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.status || 'all'}
                    label="Status"
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                  >
                    <MenuItem value="all">All Statuses</MenuItem>
                    {/* Render status options based on report type */}
                    {reportType === 'users' && [
                      <MenuItem key="active" value="active">Active</MenuItem>,
                      <MenuItem key="inactive" value="inactive">Inactive</MenuItem>
                    ]}
                    {reportType === 'applications' && [
                      <MenuItem key="pending" value="Pending">Pending</MenuItem>,
                      <MenuItem key="approved" value="Approved">Approved</MenuItem>,
                      <MenuItem key="rejected" value="Rejected">Rejected</MenuItem>
                    ]}
                    {reportType === 'leave_requests' && [
                      <MenuItem key="pending" value="Pending">Pending</MenuItem>,
                      <MenuItem key="approved" value="Approved">Approved</MenuItem>,
                      <MenuItem key="rejected" value="Rejected">Rejected</MenuItem>
                    ]}
                    {reportType === 'jobs' && [
                      <MenuItem key="active" value="active">Active</MenuItem>,
                      <MenuItem key="inactive" value="inactive">Inactive</MenuItem>
                    ]}
                    {reportType === 'attendance' && [
                      <MenuItem key="present" value="Present">Present</MenuItem>,
                      <MenuItem key="late" value="Late">Late</MenuItem>,
                      <MenuItem key="absent" value="Absent">Absent</MenuItem>,
                      <MenuItem key="half-day" value="Half-Day">Half-Day</MenuItem>,
                      <MenuItem key="on-leave" value="On Leave">On Leave</MenuItem>
                    ]}
                  </Select>
                </FormControl>
              </Grid>

              {/* Role filter - only for users */}
              {reportType === 'users' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Role</InputLabel>
                    <Select
                      value={filters.role || 'all'}
                      label="Role"
                      onChange={(e) => handleFilterChange('role', e.target.value)}
                    >
                      <MenuItem value="all">All Roles</MenuItem>
                      <MenuItem value="admin">Admin</MenuItem>
                      <MenuItem value="hr">HR</MenuItem>
                      <MenuItem value="user">User</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Job filter - only for applications */}
              {reportType === 'applications' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Job</InputLabel>
                    <Select
                      value={filters.jobId || 'all'}
                      label="Job"
                      onChange={(e) => handleFilterChange('jobId', e.target.value)}
                    >
                      <MenuItem value="all">All Jobs</MenuItem>
                      <MenuItem value="job1">Software Engineer</MenuItem>
                      <MenuItem value="job2">Product Manager</MenuItem>
                      <MenuItem value="job3">UX Designer</MenuItem>
                      <MenuItem value="job4">Data Scientist</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Leave Type filter - only for leave requests */}
              {reportType === 'leave_requests' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Leave Type</InputLabel>
                    <Select
                      value={filters.leaveType || 'all'}
                      label="Leave Type"
                      onChange={(e) => handleFilterChange('leaveType', e.target.value)}
                    >
                      <MenuItem value="all">All Types</MenuItem>
                      <MenuItem value="Annual Leave">Annual Leave</MenuItem>
                      <MenuItem value="Sick Leave">Sick Leave</MenuItem>
                      <MenuItem value="Personal Leave">Personal Leave</MenuItem>
                      <MenuItem value="Maternity/Paternity Leave">Maternity/Paternity Leave</MenuItem>
                      <MenuItem value="Bereavement Leave">Bereavement Leave</MenuItem>
                      <MenuItem value="Unpaid Leave">Unpaid Leave</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Department filter - for users, jobs and attendance */}
              {(reportType === 'users' || reportType === 'jobs' || reportType === 'attendance') && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Department</InputLabel>
                    <Select
                      value={filters.department || 'all'}
                      label="Department"
                      onChange={(e) => handleFilterChange('department', e.target.value)}
                    >
                      <MenuItem value="all">All Departments</MenuItem>
                      <MenuItem value="Engineering">Engineering</MenuItem>
                      <MenuItem value="Marketing">Marketing</MenuItem>
                      <MenuItem value="Sales">Sales</MenuItem>
                      <MenuItem value="HR">HR</MenuItem>
                      <MenuItem value="Finance">Finance</MenuItem>
                      <MenuItem value="Operations">Operations</MenuItem>
                      <MenuItem value="IT">IT</MenuItem>
                      <MenuItem value="Customer Support">Customer Support</MenuItem>
                      <MenuItem value="Executive">Executive</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Employee filter - only for attendance */}
              {reportType === 'attendance' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Employee</InputLabel>
                    <Select
                      value={filters.employeeId || 'all'}
                      label="Employee"
                      onChange={(e) => handleFilterChange('employeeId', e.target.value)}
                    >
                      <MenuItem value="all">All Employees</MenuItem>
                      {/* We'll fetch this dynamically in a real implementation */}
                      <MenuItem value="employee1">John Doe</MenuItem>
                      <MenuItem value="employee2">Jane Smith</MenuItem>
                      <MenuItem value="employee3">Bob Johnson</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Location filter - only for jobs */}
              {reportType === 'jobs' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Location</InputLabel>
                    <Select
                      value={filters.location || 'all'}
                      label="Location"
                      onChange={(e) => handleFilterChange('location', e.target.value)}
                    >
                      <MenuItem value="all">All Locations</MenuItem>
                      <MenuItem value="remote">Remote</MenuItem>
                      <MenuItem value="onsite">Onsite</MenuItem>
                      <MenuItem value="hybrid">Hybrid</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Include inactive checkbox - for users and jobs */}
              {(reportType === 'users' || reportType === 'jobs') && (
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={filters.includeInactive || false}
                        onChange={(e) => handleFilterChange('includeInactive', e.target.checked)}
                      />
                    }
                    label="Include inactive records"
                  />
                </Grid>
              )}
            </Grid>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Select Fields to Include
            </Typography>
            {reportType && (
              <FormGroup>
                <Grid container spacing={2}>
                  {Object.entries(fields[reportType]).map(([field, checked]) => (
                    <Grid item xs={12} sm={6} md={4} key={field}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={checked}
                            onChange={(e) => handleFieldChange(reportType, field, e.target.checked)}
                          />
                        }
                        label={field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      />
                    </Grid>
                  ))}
                </Grid>
              </FormGroup>
            )}
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Generate Report
            </Typography>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <DescriptionIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />
              <Typography variant="h5" gutterBottom>
                Ready to Generate Report
              </Typography>
              <Typography variant="body1" paragraph>
                You have selected to generate a {reportTypes.find(r => r.id === reportType)?.name || 'report'}.
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Date Range: {formatDate(dateRange.startDate)} - {formatDate(dateRange.endDate)}
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Click the button below to generate your report.
              </Typography>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<PdfIcon />}
                onClick={generateReport}
                disabled={loading}
                sx={{ mt: 2 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Generate PDF Report'}
              </Button>
            </Paper>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" component="h2" fontWeight={600}>
          Report Generator
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Stepper activeStep={activeStep} alternativeLabel sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <Box sx={{ mt: 2, mb: 4 }}>
          {getStepContent(activeStep)}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          <Box>
            <Button onClick={handleReset} sx={{ mr: 1 }}>
              Reset
            </Button>
            <Button
              variant="contained"
              onClick={handleNext}
              disabled={loading}
            >
              {activeStep === steps.length - 1 ? 'Generate' : 'Next'}
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Report Preview Dialog */}
      <Dialog open={previewOpen} onClose={handleClosePreview} maxWidth="lg" fullWidth>
        <DialogTitle sx={{
          bgcolor: 'primary.main',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          py: 1.5
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DescriptionIcon sx={{ mr: 1 }} />
            <Typography variant="h6" component="span">
              {reportPreview?.title || 'Report Preview'}
            </Typography>
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleClosePreview}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers sx={{ p: 0 }}>
          {reportPreview ? (
            <Box>
              {/* Report Header */}
              <Box sx={{
                p: 2,
                bgcolor: 'grey.50',
                borderBottom: '1px solid',
                borderColor: 'divider',
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                justifyContent: 'space-between',
                alignItems: { xs: 'flex-start', sm: 'center' }
              }}>
                <Box>
                  <Typography variant="h6" color="primary.main" sx={{ fontWeight: 600 }}>
                    {reportPreview.title}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Generated on: {new Date(reportPreview.generatedAt).toLocaleString()}
                  </Typography>
                </Box>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mt: { xs: 1, sm: 0 },
                  bgcolor: 'primary.light',
                  color: 'primary.contrastText',
                  px: 2,
                  py: 0.5,
                  borderRadius: 1
                }}>
                  <Typography variant="body2" fontWeight={500}>
                    Total Records: {reportPreview.totalRecords}
                  </Typography>
                </Box>
              </Box>

              {/* Report Summary */}
              <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                  {reportPreview.summary}
                </Typography>
              </Box>

              {/* Sample Data */}
              <Box sx={{ p: 2 }}>
                <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                  <TableChartIcon fontSize="small" sx={{ mr: 0.5 }} />
                  Sample Data Preview
                </Typography>

                <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 350 }}>
                  <Table size="small" stickyHeader>
                    <TableHead>
                      <TableRow sx={{ '& th': { fontWeight: 'bold', bgcolor: 'grey.100' } }}>
                        {reportPreview.sampleData && reportPreview.sampleData.length > 0 &&
                          Object.keys(reportPreview.sampleData[0]).map((key) => (
                            <TableCell key={key} sx={{ py: 1 }}>
                              {formatColumnHeader(key)}
                            </TableCell>
                          ))}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {reportPreview.sampleData && reportPreview.sampleData.map((row, index) => (
                        <TableRow
                          key={index}
                          sx={{
                            '&:nth-of-type(odd)': { bgcolor: 'rgba(0, 0, 0, 0.02)' },
                            '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                          }}
                        >
                          {Object.entries(row).map(([key, value], i) => (
                            <TableCell key={i} sx={{ py: 0.75, fontSize: '0.8125rem' }}>
                              {formatCellValue(key, value)}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {reportPreview.sampleData && reportPreview.sampleData.length > 0 && (
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1, textAlign: 'right' }}>
                    Showing {reportPreview.sampleData.length} of {reportPreview.totalRecords} records
                  </Typography>
                )}
              </Box>
            </Box>
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'grey.50' }}>
          <Button
            startIcon={<DownloadIcon />}
            onClick={downloadReport}
            variant="contained"
            color="primary"
            size="small"
          >
            Download PDF
          </Button>
          <Button
            startIcon={<PrintIcon />}
            onClick={printReport}
            variant="outlined"
            color="primary"
            size="small"
          >
            Print
          </Button>
          <Button
            onClick={handleClosePreview}
            color="inherit"
            size="small"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReportGenerator;
