import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  TextField,
  IconButton,
  Typography,
  Avatar,
  Chip,
  CircularProgress,
  Fab,
  Drawer,
  useTheme,
  useMediaQuery,
  Divider,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Close as CloseIcon,
  Chat as ChatIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import api from '../../Services/ApiService';

// Styled components
const ChatContainer = styled(Paper)(({ theme }) => ({
  height: '600px',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  boxShadow: theme.shadows[8]
}));

const ChatHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  padding: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
}));

const MessagesContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  overflowY: 'auto',
  padding: theme.spacing(1),
  backgroundColor: theme.palette.grey[50],
  '&::-webkit-scrollbar': {
    width: '6px',
  },
  '&::-webkit-scrollbar-track': {
    background: theme.palette.grey[100],
  },
  '&::-webkit-scrollbar-thumb': {
    background: theme.palette.grey[400],
    borderRadius: '3px',
  },
}));

const MessageBubble = styled(motion.div, {
  shouldForwardProp: (prop) => prop !== 'isUser'
})(({ theme, isUser }) => ({
  maxWidth: '80%',
  margin: theme.spacing(1, 0),
  alignSelf: isUser ? 'flex-end' : 'flex-start',
  display: 'flex',
  flexDirection: isUser ? 'row-reverse' : 'row',
  alignItems: 'flex-start',
  gap: theme.spacing(1)
}));

const MessageContent = styled(Paper, {
  shouldForwardProp: (prop) => prop !== 'isUser'
})(({ theme, isUser }) => ({
  padding: theme.spacing(1.5, 2),
  borderRadius: theme.spacing(2),
  backgroundColor: isUser ? theme.palette.primary.main : 'white',
  color: isUser ? 'white' : theme.palette.text.primary,
  boxShadow: theme.shadows[2],
  wordBreak: 'break-word',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: theme.spacing(1),
    [isUser ? 'right' : 'left']: '-6px',
    width: 0,
    height: 0,
    borderTop: `6px solid ${isUser ? theme.palette.primary.main : 'white'}`,
    borderLeft: isUser ? '6px solid transparent' : 'none',
    borderRight: isUser ? 'none' : '6px solid transparent',
    borderBottom: '6px solid transparent'
  }
}));

const InputContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: 'white',
  borderTop: `1px solid ${theme.palette.divider}`,
  display: 'flex',
  gap: theme.spacing(1),
  alignItems: 'flex-end'
}));

const SuggestionChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  cursor: 'pointer',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
    color: 'white'
  }
}));

const ChatInterface = ({ open, onClose, initialMessage = null }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [isTyping, setIsTyping] = useState(false);

  // Refs
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize chat with welcome message
  useEffect(() => {
    if (open && messages.length === 0) {
      const welcomeMessage = {
        id: 'welcome',
        role: 'assistant',
        content: "👋 Hello! I'm your HR assistant. I can help you with leave requests, task management, attendance, and HR policies. What would you like to know?",
        timestamp: new Date(),
        type: 'text'
      };

      setMessages([welcomeMessage]);
      setSuggestions([]); // Removed hardcoded suggestions - handled by context-aware agent

      // Send initial message if provided
      if (initialMessage) {
        setTimeout(() => {
          sendMessage(initialMessage);
        }, 1000);
      }
    }
  }, [open, initialMessage]);

  // Send message to AI
  const sendMessage = async (messageText = inputValue) => {
    if (!messageText.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: messageText.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setIsTyping(true);
    setSuggestions([]);

    try {
      const response = await api.post('/ai/chat/message', {
        conversationId,
        message: messageText.trim(),
        context: {}
      });

      if (response.data.success) {
        const { assistantMessage, suggestions: newSuggestions } = response.data.data;

        // Set conversation ID if this is the first message
        if (!conversationId && response.data.data.conversationId) {
          setConversationId(response.data.data.conversationId);
        }

        // Add assistant message
        const aiMessage = {
          id: assistantMessage.messageId,
          role: 'assistant',
          content: assistantMessage.content,
          timestamp: new Date(assistantMessage.timestamp),
          type: assistantMessage.type || 'text',
          metadata: assistantMessage.metadata
        };

        // Simulate typing delay
        setTimeout(() => {
          setMessages(prev => [...prev, aiMessage]);
          setSuggestions([]); // Removed suggestions - handled by context-aware agent
          setIsTyping(false);
          setIsLoading(false);
        }, 1000);
      }
    } catch (error) {
      console.error('Error sending message:', error);

      const errorMessage = {
        id: Date.now(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again or contact support if the issue persists.',
        timestamp: new Date(),
        type: 'error'
      };

      setMessages(prev => [...prev, errorMessage]);
      setIsTyping(false);
      setIsLoading(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    sendMessage(suggestion);
  };

  // Handle key press
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  // Clear chat
  const clearChat = () => {
    setMessages([]);
    setConversationId(null);
    setSuggestions([]);

    // Add welcome message
    const welcomeMessage = {
      id: 'welcome-' + Date.now(),
      role: 'assistant',
      content: "Chat cleared! How can I help you today?",
      timestamp: new Date(),
      type: 'text'
    };

    setMessages([welcomeMessage]);
    setSuggestions([]); // Removed suggestions - handled by context-aware agent
  };

  // Render message
  const renderMessage = (message) => {
    const isUser = message.role === 'user';

    return (
      <MessageBubble
        key={message.id}
        isUser={isUser}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Avatar
          sx={{
            width: 32,
            height: 32,
            bgcolor: isUser ? theme.palette.primary.main : theme.palette.secondary.main
          }}
        >
          {isUser ? <PersonIcon /> : <BotIcon />}
        </Avatar>

        <MessageContent isUser={isUser}>
          <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
            {message.content}
          </Typography>

          {message.metadata?.confidence && (
            <Typography variant="caption" sx={{ opacity: 0.7, mt: 1, display: 'block' }}>
              Confidence: {Math.round(message.metadata.confidence * 100)}%
            </Typography>
          )}
        </MessageContent>
      </MessageBubble>
    );
  };

  // Chat content
  const chatContent = (
    <ChatContainer>
      <ChatHeader>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ bgcolor: 'white', color: theme.palette.primary.main }}>
            <BotIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
              Jarvis Intelligence Agent
            </Typography>
            <Typography variant="caption" sx={{ opacity: 0.9 }}>
              {isTyping ? 'Processing with advanced intelligence...' : 'Advanced HR Intelligence • Complete System Knowledge'}
            </Typography>
          </Box>
        </Box>

        <Box>
          <IconButton color="inherit" onClick={clearChat} size="small">
            <RefreshIcon />
          </IconButton>
          {isMobile && (
            <IconButton color="inherit" onClick={onClose} size="small">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </ChatHeader>

      <MessagesContainer>
        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
          {messages.map(renderMessage)}

          {isTyping && (
            <MessageBubble
              isUser={false}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.secondary.main }}>
                <BotIcon />
              </Avatar>
              <MessageContent isUser={false}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CircularProgress size={16} />
                  <Typography variant="body2">Thinking...</Typography>
                </Box>
              </MessageContent>
            </MessageBubble>
          )}
        </Box>
        <div ref={messagesEndRef} />
      </MessagesContainer>

      {suggestions.length > 0 && (
        <Box sx={{ p: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Typography variant="caption" color="textSecondary" sx={{ mb: 1, display: 'block' }}>
            Quick suggestions:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {suggestions.map((suggestion, index) => (
              <SuggestionChip
                key={index}
                label={suggestion}
                size="small"
                onClick={() => handleSuggestionClick(suggestion)}
                variant="outlined"
              />
            ))}
          </Box>
        </Box>
      )}

      <InputContainer>
        <TextField
          ref={inputRef}
          fullWidth
          multiline
          maxRows={3}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Type your message..."
          variant="outlined"
          size="small"
          disabled={isLoading}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 3
            }
          }}
        />
        <IconButton
          color="primary"
          onClick={() => sendMessage()}
          disabled={!inputValue.trim() || isLoading}
          sx={{
            bgcolor: theme.palette.primary.main,
            color: 'white',
            '&:hover': {
              bgcolor: theme.palette.primary.dark
            },
            '&:disabled': {
              bgcolor: theme.palette.grey[300]
            }
          }}
        >
          {isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
        </IconButton>
      </InputContainer>
    </ChatContainer>
  );

  if (isMobile) {
    return (
      <Drawer
        anchor="bottom"
        open={open}
        onClose={onClose}
        PaperProps={{
          sx: {
            height: '90vh',
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16
          }
        }}
      >
        {chatContent}
      </Drawer>
    );
  }

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: 400,
          p: 2
        }
      }}
    >
      {chatContent}
    </Drawer>
  );
};

export default ChatInterface;
