import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
import BusinessCenterIcon from '@mui/icons-material/BusinessCenter';

const LogoWrapper = styled(Box)(({ theme, size = 'medium', variant = 'default' }) => {
  const sizes = {
    small: {
      iconSize: 24,
      fontSize: '1.2rem',
      spacing: 1,
    },
    medium: {
      iconSize: 32,
      fontSize: '1.5rem',
      spacing: 1.5,
    },
    large: {
      iconSize: 40,
      fontSize: '1.8rem',
      spacing: 2,
    },
  };

  const variants = {
    default: {
      color: theme.palette.primary.main,
      textColor: theme.palette.text.primary,
    },
    light: {
      color: theme.palette.primary.main,
      textColor: theme.palette.common.white,
    },
    dark: {
      color: theme.palette.common.white,
      textColor: theme.palette.common.white,
    },
  };

  return {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(sizes[size].spacing),
    '& .logo-icon': {
      color: variants[variant].color,
      fontSize: sizes[size].iconSize,
    },
    '& .logo-text': {
      color: variants[variant].textColor,
      fontWeight: 700,
      fontSize: sizes[size].fontSize,
      letterSpacing: '0.5px',
    },
  };
});

const Logo = ({ size = 'medium', variant = 'default', showIcon = true, showText = true }) => {
  return (
    <LogoWrapper size={size} variant={variant}>
      {showIcon && (
        <BusinessCenterIcon className="logo-icon" />
      )}
      {showText && (
        <Typography variant="h6" component="span" className="logo-text">
          MjayTrack
        </Typography>
      )}
    </LogoWrapper>
  );
};

export default Logo;
