import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  useMediaQuery,
  Tooltip,
  Badge,
  useTheme,
  Collapse,
  Popover
} from '@mui/material';
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  Notifications as NotificationsIcon,
  AccountCircle,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  ExpandLess,
  ExpandMore
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import Logo from '../Logo';
import NotificationList from '../notifications/NotificationList';
import ChatButton from '../ai-assistant/ChatButton';
import api from '../../Services/ApiService';
import NavigationLink from '../navigation/NavigationLink';
import '../../Styles/Transitions.css';

const drawerWidth = 260;

// Create a motion component version of main
const MotionMain = motion('main');

const Main = styled(MotionMain, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => ({
    flexGrow: 1,
    padding: theme.spacing(3),
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: `-${drawerWidth}px`,
    height: '100vh',
    overflow: 'auto',
    ...(open && {
      transition: theme.transitions.create('margin', {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
      marginLeft: 0,
    }),
    [theme.breakpoints.down('md')]: {
      marginLeft: 0,
      padding: theme.spacing(2),
    },
  }),
);

const StyledAppBar = styled(AppBar, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => ({
    transition: theme.transitions.create(['margin', 'width'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    backgroundColor: theme.palette.background.paper,
    color: theme.palette.text.primary,
    boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.08)',
    ...(open && {
      width: `calc(100% - ${drawerWidth}px)`,
      marginLeft: `${drawerWidth}px`,
      transition: theme.transitions.create(['margin', 'width'], {
        easing: theme.transitions.easing.easeOut,
        duration: theme.transitions.duration.enteringScreen,
      }),
    }),
  }),
);

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(0, 1),
  ...theme.mixins.toolbar,
  justifyContent: 'space-between',
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
}));

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  width: drawerWidth,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: drawerWidth,
    boxSizing: 'border-box',
    borderRight: 'none',
    boxShadow: '4px 0px 10px rgba(0, 0, 0, 0.05)',
  },
}));

const DashboardLayout = ({
  children,
  title,
  menuItems = [],
  userName = 'User',
  userRole = 'User',
  userAvatar = null,
  onLogout
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [open, setOpen] = useState(!isMobile);
  const [anchorEl, setAnchorEl] = useState(null);
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState(null);
  const [expandedMenus, setExpandedMenus] = useState({});


  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationsOpen = (event) => {
    setNotificationsAnchorEl(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };

  const handleMenuToggle = (text) => {
    setExpandedMenus(prev => ({
      ...prev,
      [text]: !prev[text]
    }));
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <StyledAppBar position="fixed" open={open}>
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            sx={{ mr: 2, ...(open && { display: 'none' }) }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title="Notifications">
              <IconButton
                color="inherit"
                onClick={handleNotificationsOpen}
                size="large"
              >
                <NotificationsIcon />
              </IconButton>
            </Tooltip>

            <Tooltip title="Account">
              <IconButton
                onClick={handleProfileMenuOpen}
                size="large"
                edge="end"
                color="inherit"
                sx={{ ml: 1 }}
              >
                {userAvatar ? (
                  <Avatar
                    src={userAvatar}
                    alt={userName}
                    sx={{ width: 32, height: 32 }}
                  />
                ) : (
                  <AccountCircle />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </StyledAppBar>

      <StyledDrawer
        variant={isMobile ? "temporary" : "persistent"}
        anchor="left"
        open={open}
        onClose={handleDrawerClose}
      >
        <DrawerHeader>
          <Box sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
            <Logo variant="dark" />
          </Box>
          <IconButton onClick={handleDrawerClose} sx={{ color: 'white' }}>
            <ChevronLeftIcon />
          </IconButton>
        </DrawerHeader>

        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {userAvatar ? (
              <Avatar
                src={userAvatar}
                alt={userName}
                sx={{ width: 40, height: 40, mr: 2 }}
              />
            ) : (
              <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2 }}>
                {userName.charAt(0).toUpperCase()}
              </Avatar>
            )}
            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {userName}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {userRole}
              </Typography>
            </Box>
          </Box>
        </Box>

        <Divider />

        <List>
          {menuItems.map((item) => (
            <React.Fragment key={item.text}>
              <ListItem disablePadding>
                <motion.div
                  whileHover={{ x: 2 }}
                  transition={{ duration: 0.1 }}
                >
                  <ListItemButton
                    onClick={item.items ? () => handleMenuToggle(item.text) : item.onClick}
                    selected={item.active}
                    className="nav-item"
                    sx={{
                      borderRadius: '0 24px 24px 0',
                      mr: 1,
                      mb: 0.5,
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: item.active ? '4px' : '0px',
                        height: '100%',
                        backgroundColor: theme.palette.primary.main,
                        transition: 'width 0.3s ease',
                      },
                      '&:hover::before': {
                        width: '4px',
                      },
                      '&.Mui-selected': {
                        backgroundColor: 'rgba(211, 47, 47, 0.08)',
                        '&:hover': {
                          backgroundColor: 'rgba(211, 47, 47, 0.12)',
                        },
                        '& .MuiListItemIcon-root': {
                          color: theme.palette.primary.main,
                        },
                      },
                    }}
                  >
                    <ListItemIcon>
                      {item.icon}
                    </ListItemIcon>
                    <ListItemText primary={item.text} />
                    {item.items && (
                      expandedMenus[item.text] ? <ExpandLess /> : <ExpandMore />
                    )}
                  </ListItemButton>
                </motion.div>
              </ListItem>

              {item.items && (
                <Collapse in={expandedMenus[item.text]} timeout="auto" unmountOnExit>
                  <List component="div" disablePadding>
                    {item.items.map((subItem) => (
                      <ListItem key={subItem.text} disablePadding>
                        <motion.div
                          whileHover={{ x: 2 }}
                          transition={{ duration: 0.1 }}
                        >
                          <ListItemButton
                            onClick={subItem.onClick}
                            selected={subItem.active}
                            className="nav-item"
                            sx={{
                              pl: 4,
                              borderRadius: '0 24px 24px 0',
                              mr: 1,
                              mb: 0.5,
                              position: 'relative',
                              overflow: 'hidden',
                              '&::before': {
                                content: '""',
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: subItem.active ? '4px' : '0px',
                                height: '100%',
                                backgroundColor: theme.palette.primary.main,
                                transition: 'width 0.3s ease',
                              },
                              '&:hover::before': {
                                width: '4px',
                              },
                              '&.Mui-selected': {
                                backgroundColor: 'rgba(211, 47, 47, 0.08)',
                                '&:hover': {
                                  backgroundColor: 'rgba(211, 47, 47, 0.12)',
                                },
                                '& .MuiListItemIcon-root': {
                                  color: theme.palette.primary.main,
                                },
                              },
                            }}
                          >
                            <ListItemIcon>
                              {subItem.icon}
                            </ListItemIcon>
                            <ListItemText primary={subItem.text} />
                          </ListItemButton>
                        </motion.div>
                      </ListItem>
                    ))}
                  </List>
                </Collapse>
              )}
            </React.Fragment>
          ))}
        </List>

        <Box sx={{ mt: 'auto', mb: 2, mx: 2 }}>
          <Divider sx={{ mb: 2 }} />
          <motion.div
            whileHover={{ scale: 1.01 }}
            whileTap={{ scale: 0.99 }}
            transition={{ duration: 0.1 }}
          >
            <ListItemButton
              onClick={onLogout}
              sx={{
                borderRadius: 2,
                backgroundColor: theme.palette.primary.main,
                color: theme.palette.primary.contrastText,
                '&:hover': {
                  backgroundColor: theme.palette.primary.dark,
                  boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                },
                transition: 'all 0.15s ease-out',
              }}
            >
              <ListItemIcon sx={{ color: 'inherit' }}>
                <LogoutIcon />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </ListItemButton>
          </motion.div>
        </Box>
      </StyledDrawer>

      <Main
        open={open}
        initial={{ opacity: 0.95 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.15 }}
      >
        <DrawerHeader />
        {/* Add a dialog container div for accessibility */}
        <div id="dialog-container" />
        <motion.div
          initial={{ opacity: 0.95, y: 3 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.1 }}
          style={{ minHeight: '100%' }}
        >
          {children}
        </motion.div>
      </Main>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          onClick={() => {
            handleProfileMenuClose();
            navigate('/profile');
          }}
        >
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Profile</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={onLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Logout</ListItemText>
        </MenuItem>
      </Menu>

      <Popover
        anchorEl={notificationsAnchorEl}
        open={Boolean(notificationsAnchorEl)}
        onClose={handleNotificationsClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        PaperProps={{
          sx: { boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.15)' }
        }}
      >
        <NotificationList
          onClose={handleNotificationsClose}
        />
      </Popover>

      {/* AI Assistant Chat Button */}
      <ChatButton
        showNotification={false}
        onChatOpen={() => console.log('Chat opened')}
        onChatClose={() => console.log('Chat closed')}
      />
    </Box>
  );
};

export default DashboardLayout;
