/**
 * Smart Assistant API Routes
 * Handles proactive AI suggestions and behavioral analysis
 */

const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middleware/authmiddleware');
const smartAssistantEngine = require('../services/smartAssistantEngine');
const AIInsight = require('../../models/AIInsight');

/**
 * Trigger behavioral analysis and get suggestions
 */
router.post('/analyze', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeWindow } = req.body;

    console.log(`🔍 Triggering analysis for user ${userId}`);

    const result = await smartAssistantEngine.analyzeAndSuggest(userId);

    res.json({
      success: true,
      data: {
        patterns: result.patterns,
        suggestions: result.suggestions,
        confidence: result.confidence,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in smart assistant analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Error analyzing user behavior',
      error: error.message
    });
  }
});

/**
 * Get user's AI insights and suggestions
 */
router.get('/insights', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, category, limit = 10 } = req.query;

    const query = { userId };
    if (status) query.status = status;
    if (category) query.category = category;

    const insights = await AIInsight.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: {
        insights,
        count: insights.length
      }
    });

  } catch (error) {
    console.error('Error fetching insights:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching AI insights',
      error: error.message
    });
  }
});

/**
 * Provide feedback on a suggestion
 */
router.post('/feedback', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { insightId, rating, comment } = req.body;

    if (!insightId || !rating) {
      return res.status(400).json({
        success: false,
        message: 'Insight ID and rating are required'
      });
    }

    // Store feedback
    await smartAssistantEngine.storeSuggestionFeedback(userId, insightId, {
      rating, // 'up' or 'down'
      comment: comment || ''
    });

    // Update insight status based on feedback
    const insight = await AIInsight.findById(insightId);
    if (insight) {
      if (rating === 'up') {
        insight.status = 'acted_upon';
      } else if (rating === 'down') {
        insight.status = 'dismissed';
      }
      await insight.save();
    }

    res.json({
      success: true,
      message: 'Feedback recorded successfully'
    });

  } catch (error) {
    console.error('Error recording feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Error recording feedback',
      error: error.message
    });
  }
});

/**
 * Get user preferences for AI suggestions
 */
router.get('/preferences', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const preferences = smartAssistantEngine.getUserPreferences(userId);

    res.json({
      success: true,
      data: preferences
    });

  } catch (error) {
    console.error('Error fetching preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching user preferences',
      error: error.message
    });
  }
});

/**
 * Update user preferences for AI suggestions
 */
router.put('/preferences', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { frequency, categories, tone } = req.body;

    const updatedPreferences = smartAssistantEngine.updateUserPreferences(userId, {
      frequency,
      categories,
      tone
    });

    res.json({
      success: true,
      data: updatedPreferences,
      message: 'Preferences updated successfully'
    });

  } catch (error) {
    console.error('Error updating preferences:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating preferences',
      error: error.message
    });
  }
});

/**
 * Get analytics data for user dashboard
 */
router.get('/analytics', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const analyticsData = await smartAssistantEngine.getAnalyticsData(userId);

    if (!analyticsData) {
      return res.status(404).json({
        success: false,
        message: 'No analytics data available'
      });
    }

    res.json({
      success: true,
      data: analyticsData
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching analytics data',
      error: error.message
    });
  }
});

/**
 * Process CRUD operation for behavioral tracking
 */
router.post('/track-operation', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { operation, resourceType, resourceId, metadata } = req.body;

    if (!operation || !resourceType) {
      return res.status(400).json({
        success: false,
        message: 'Operation and resource type are required'
      });
    }

    const auditLog = await smartAssistantEngine.processCRUDOperation(
      userId,
      operation,
      resourceType,
      resourceId,
      metadata
    );

    res.json({
      success: true,
      data: {
        auditLogId: auditLog?._id,
        message: 'Operation tracked successfully'
      }
    });

  } catch (error) {
    console.error('Error tracking operation:', error);
    res.status(500).json({
      success: false,
      message: 'Error tracking operation',
      error: error.message
    });
  }
});

/**
 * Dismiss an insight
 */
router.post('/insights/:id/dismiss', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const insightId = req.params.id;

    const insight = await AIInsight.findOne({ _id: insightId, userId });
    
    if (!insight) {
      return res.status(404).json({
        success: false,
        message: 'Insight not found'
      });
    }

    await insight.dismiss();

    res.json({
      success: true,
      message: 'Insight dismissed successfully'
    });

  } catch (error) {
    console.error('Error dismissing insight:', error);
    res.status(500).json({
      success: false,
      message: 'Error dismissing insight',
      error: error.message
    });
  }
});

/**
 * Mark insight as viewed
 */
router.post('/insights/:id/view', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const insightId = req.params.id;
    const { readTime = 0 } = req.body;

    const insight = await AIInsight.findOne({ _id: insightId, userId });
    
    if (!insight) {
      return res.status(404).json({
        success: false,
        message: 'Insight not found'
      });
    }

    await insight.markAsViewed(readTime);

    res.json({
      success: true,
      message: 'Insight marked as viewed'
    });

  } catch (error) {
    console.error('Error marking insight as viewed:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating insight',
      error: error.message
    });
  }
});

/**
 * Get suggestion statistics
 */
router.get('/stats', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeRange = 30 } = req.query; // days

    const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);

    const stats = await AIInsight.aggregate([
      {
        $match: {
          userId: userId,
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          avgConfidence: { $avg: '$confidence' }
        }
      }
    ]);

    const categoryStats = await AIInsight.aggregate([
      {
        $match: {
          userId: userId,
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          avgConfidence: { $avg: '$confidence' }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        statusStats: stats,
        categoryStats,
        timeRange: `${timeRange} days`
      }
    });

  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching statistics',
      error: error.message
    });
  }
});

module.exports = router;
