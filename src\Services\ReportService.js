import api from './ApiService';

// Report types
export const REPORT_TYPES = {
  NLP: 'nlp',
  ATTENDANCE: 'attendance',
  USER: 'user',
  JOB: 'job',
  LEAVE: 'leave',
  TASK: 'task'
};

/**
 * Save a report to the backend and localStorage
 * @param {Object} reportData - The report data to save
 * @param {string} reportType - The type of report
 * @returns {Promise<Object>} - The saved report object
 */
export const saveReport = async (reportData, reportType) => {
  try {
    // Create the report object
    const report = {
      title: getReportTitle(reportData, reportType),
      reportType: reportType,
      reportData: reportData,
      timestamp: new Date().toISOString()
    };

    // If this is an NLP report for an application, include the application ID
    if (reportType === REPORT_TYPES.NLP && reportData.applicationId) {
      report.applicationId = reportData.applicationId;
    }

    console.log('Saving report to backend:', report);

    // Save to backend API
    try {
      const response = await api.post('/reports', report);
      console.log('Report saved to backend successfully:', response.data);

      // Use the data from the backend
      const savedReport = {
        ...report,
        id: response.data.report.id,
        timestamp: response.data.report.timestamp || report.timestamp
      };

      // Also save to localStorage as a backup/cache
      saveReportToLocalStorage(savedReport);

      return savedReport;
    } catch (apiError) {
      console.error('Error saving report to backend:', apiError);

      // Fallback to localStorage only if API fails
      const localReport = saveReportToLocalStorage({
        ...report,
        id: generateReportId()
      });

      return localReport;
    }
  } catch (error) {
    console.error('Error in saveReport:', error);
    throw error;
  }
};

/**
 * Helper function to save a report to localStorage
 * @param {Object} report - The report object to save
 * @returns {Object} - The saved report object
 */
const saveReportToLocalStorage = (report) => {
  try {
    // Get existing reports from localStorage
    const allReports = JSON.parse(localStorage.getItem('allReports') || '[]');

    // Add the new report
    allReports.push(report);

    // Save back to localStorage
    localStorage.setItem('allReports', JSON.stringify(allReports));

    console.log('Report saved to localStorage successfully:', report);

    return report;
  } catch (error) {
    console.error('Error saving report to localStorage:', error);
    throw error;
  }
};

/**
 * Get all reports from backend API with fallback to localStorage
 * @param {string} reportType - Optional filter by report type
 * @returns {Promise<Array>} - Array of report objects
 */
export const getReports = async (reportType = null) => {
  try {
    // Try to get reports from backend API
    try {
      const params = reportType ? { reportType } : {};
      const response = await api.get('/reports', { params });
      console.log('Reports fetched from backend successfully:', response.data);

      // Process the reports to ensure they have the expected format
      const processedReports = response.data.map(report => ({
        ...report,
        id: report._id || report.id, // Use _id as id if available
        timestamp: report.timestamp || report.createdAt // Use timestamp or fall back to createdAt
      }));

      // Update localStorage with the latest reports from the backend
      localStorage.setItem('allReports', JSON.stringify(processedReports));

      return processedReports;
    } catch (apiError) {
      console.error('Error fetching reports from backend:', apiError);
      console.log('Falling back to localStorage for reports');

      // Fallback to localStorage if API fails
      const allReports = JSON.parse(localStorage.getItem('allReports') || '[]');

      // Filter by report type if specified
      if (reportType) {
        return allReports.filter(report => report.reportType === reportType);
      }

      return allReports;
    }
  } catch (error) {
    console.error('Error in getReports:', error);
    return [];
  }
};

/**
 * Delete a report from backend and localStorage
 * @param {string} reportId - The ID of the report to delete
 * @returns {Promise<boolean>} - Success status
 */
export const deleteReport = async (reportId) => {
  try {
    // Try to delete from backend API
    try {
      await api.delete(`/reports/${reportId}`);
      console.log('Report deleted from backend successfully');
    } catch (apiError) {
      console.error('Error deleting report from backend:', apiError);
      // Continue to delete from localStorage even if API fails
    }

    // Always delete from localStorage
    const allReports = JSON.parse(localStorage.getItem('allReports') || '[]');
    const updatedReports = allReports.filter(report => report.id !== reportId);
    localStorage.setItem('allReports', JSON.stringify(updatedReports));

    return true;
  } catch (error) {
    console.error('Error in deleteReport:', error);
    return false;
  }
};

/**
 * Get a specific report by ID from backend with fallback to localStorage
 * @param {string} reportId - The ID of the report to get
 * @returns {Promise<Object|null>} - The report object or null if not found
 */
export const getReportById = async (reportId) => {
  try {
    console.log('Getting report by ID:', reportId);

    // Try multiple approaches to get the report

    // 1. First try the direct API call
    try {
      console.log('Attempting to fetch report from backend API...');
      const response = await api.get(`/reports/${reportId}`);
      console.log('Report fetched from backend successfully:', response.data);

      // Process the report to ensure it has the expected format
      const processedReport = {
        ...response.data,
        id: response.data._id || response.data.id, // Use _id as id if available
        timestamp: response.data.timestamp || response.data.createdAt // Use timestamp or fall back to createdAt
      };

      return processedReport;
    } catch (apiError) {
      console.error('Error fetching report from backend:', apiError);

      // 2. Try localStorage as fallback
      console.log('Falling back to localStorage for report');
      try {
        const allReports = JSON.parse(localStorage.getItem('allReports') || '[]');
        console.log('Reports in localStorage:', allReports.length);

        // Try to find by id or _id
        const localReport = allReports.find(report =>
          report.id === reportId ||
          report._id === reportId ||
          String(report.id) === String(reportId) ||
          String(report._id) === String(reportId)
        );

        if (localReport) {
          console.log('Found report in localStorage:', localReport);
          return localReport;
        }

        console.log('Report not found in localStorage');
      } catch (localStorageError) {
        console.error('Error accessing localStorage:', localStorageError);
      }

      // 3. If we get here, we couldn't find the report
      return null;
    }
  } catch (error) {
    console.error('Error in getReportById:', error);
    return null;
  }
};

/**
 * Generate a title for a report based on its type and data
 * @param {Object} reportData - The report data
 * @param {string} reportType - The type of report
 * @returns {string} - A title for the report
 */
const getReportTitle = (reportData, reportType) => {
  switch (reportType) {
    case REPORT_TYPES.NLP:
      return `NLP Analysis: ${reportData.candidateName || 'Unknown Candidate'}`;
    case REPORT_TYPES.ATTENDANCE:
      return `Attendance Report: ${reportData.period || new Date().toLocaleDateString()}`;
    case REPORT_TYPES.USER:
      return `User Report: ${reportData.title || 'All Users'}`;
    case REPORT_TYPES.JOB:
      return `Job Report: ${reportData.title || 'All Jobs'}`;
    case REPORT_TYPES.LEAVE:
      return `Leave Report: ${reportData.title || 'All Leaves'}`;
    case REPORT_TYPES.TASK:
      return `Task Report: ${reportData.title || 'All Tasks'}`;
    default:
      return `Report: ${new Date().toLocaleDateString()}`;
  }
};

/**
 * Generate a unique ID for reports
 * @returns {string} - A unique ID
 */
export const generateReportId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};
