const natural = require('natural');
const emotionAnalyzer = require('./emotionAnalyzer');
const spellCorrector = require('./spellCorrector');
const contextAnalyzer = require('./contextAnalyzer');
const systemKnowledge = require('./systemKnowledge');

class IntentClassifier {
  constructor() {
    this.classifier = new natural.LogisticRegressionClassifier();
    this.isTrained = false;
    this.confidenceThreshold = 0.6;

    // Initialize with training data and advanced features
    this.initializeTrainingData();
    this.initializeAdvancedFeatures();
    this.trainClassifier();
  }

  /**
   * Initialize advanced NLP features
   */
  initializeAdvancedFeatures() {
    // Emotional intelligence patterns
    this.emotionalIntents = {
      'wellness_support': ['stressed', 'overwhelmed', 'anxious', 'depressed', 'burnout'],
      'gratitude': ['thank', 'grateful', 'appreciate'],
      'frustration': ['frustrated', 'angry', 'annoyed', 'upset'],
      'confusion': ['confused', 'unclear', 'dont understand', 'lost']
    };

    // Context-aware intent modifiers
    this.contextModifiers = {
      urgent: ['urgent', 'asap', 'immediately', 'emergency', 'critical'],
      polite: ['please', 'could you', 'would you mind', 'if possible'],
      casual: ['hey', 'hi', 'yo', 'sup', 'whats up'],
      formal: ['good morning', 'good afternoon', 'i would like to', 'i am writing to']
    };

    // Typo-tolerant keywords for better matching
    this.typoTolerantKeywords = {
      'leave': ['leav', 'leve', 'leavve', 'leaev'],
      'task': ['taks', 'taask', 'tsak'],
      'attendance': ['attendence', 'atendance', 'attendanse'],
      'schedule': ['shedule', 'schedual', 'scedule'],
      'vacation': ['vacaton', 'vacasion', 'vakation']
    };
  }

  /**
   * Initialize training data for intent classification
   */
  initializeTrainingData() {
    this.trainingData = [
      // User Management Intents
      { text: 'Create a new user account', intent: 'user_create' },
      { text: 'Add new employee', intent: 'user_create' },
      { text: 'Register new user', intent: 'user_create' },
      { text: 'Show all users', intent: 'user_list' },
      { text: 'List employees', intent: 'user_list' },
      { text: 'Find user by name', intent: 'user_search' },
      { text: 'Search for employee', intent: 'user_search' },
      { text: 'Update user information', intent: 'user_update' },
      { text: 'Edit employee details', intent: 'user_update' },
      { text: 'Delete user account', intent: 'user_delete' },
      { text: 'Remove employee', intent: 'user_delete' },
      { text: 'Change my password', intent: 'password_change' },
      { text: 'Update password', intent: 'password_change' },

      // Leave Management Intents
      { text: 'I want to request leave', intent: 'leave_request' },
      { text: 'Can I take time off next week', intent: 'leave_request' },
      { text: 'I need vacation days', intent: 'leave_request' },
      { text: 'Submit leave application', intent: 'leave_request' },
      { text: 'How many leave days do I have', intent: 'leave_balance' },
      { text: 'Check my leave balance', intent: 'leave_balance' },
      { text: 'What is my remaining vacation time', intent: 'leave_balance' },
      { text: 'Show all leave requests', intent: 'leave_list' },
      { text: 'View pending leave applications', intent: 'leave_list' },
      { text: 'Approve leave request', intent: 'leave_approve' },
      { text: 'Reject leave application', intent: 'leave_reject' },
      { text: 'Update leave status', intent: 'leave_status_update' },
      { text: 'When is the best time to take leave', intent: 'leave_suggestion' },
      { text: 'Suggest optimal leave dates', intent: 'leave_suggestion' },

      // Task Management Intents
      { text: 'Show my tasks', intent: 'task_list' },
      { text: 'What are my assignments', intent: 'task_list' },
      { text: 'List all tasks', intent: 'task_list' },
      { text: 'Create new task', intent: 'task_create' },
      { text: 'Add task for employee', intent: 'task_create' },
      { text: 'Assign task to team member', intent: 'task_create' },
      { text: 'Update task status', intent: 'task_update' },
      { text: 'Mark task as complete', intent: 'task_update' },
      { text: 'I finished my assignment', intent: 'task_update' },
      { text: 'Change task priority', intent: 'task_update' },
      { text: 'Delete task', intent: 'task_delete' },
      { text: 'Remove assignment', intent: 'task_delete' },
      { text: 'Search tasks', intent: 'task_search' },
      { text: 'Find tasks by priority', intent: 'task_search' },

      // Job Management Intents
      { text: 'Create new job posting', intent: 'job_create' },
      { text: 'Add job position', intent: 'job_create' },
      { text: 'Post new vacancy', intent: 'job_create' },
      { text: 'Show available jobs', intent: 'job_list' },
      { text: 'List job openings', intent: 'job_list' },
      { text: 'View job positions', intent: 'job_list' },
      { text: 'Update job description', intent: 'job_update' },
      { text: 'Edit job posting', intent: 'job_update' },
      { text: 'Delete job position', intent: 'job_delete' },
      { text: 'Remove job posting', intent: 'job_delete' },
      { text: 'Search for jobs', intent: 'job_search' },
      { text: 'Find job by title', intent: 'job_search' },

      // Application Management Intents
      { text: 'View job applications', intent: 'application_list' },
      { text: 'Show all applications', intent: 'application_list' },
      { text: 'List candidates', intent: 'application_list' },
      { text: 'Check application status', intent: 'application_status' },
      { text: 'Approve application', intent: 'application_approve' },
      { text: 'Reject candidate', intent: 'application_reject' },
      { text: 'Update application status', intent: 'application_status_update' },
      { text: 'Delete application', intent: 'application_delete' },
      { text: 'Search applications', intent: 'application_search' },
      { text: 'Find candidate by name', intent: 'application_search' },

      // Attendance and Time Tracking
      { text: 'Check in for work', intent: 'attendance_checkin' },
      { text: 'I want to check in for work', intent: 'attendance_checkin' },
      { text: 'I need to check in', intent: 'attendance_checkin' },
      { text: 'Clock in', intent: 'attendance_checkin' },
      { text: 'Start work day', intent: 'attendance_checkin' },
      { text: 'Mark my attendance', intent: 'attendance_checkin' },
      { text: 'Check in now', intent: 'attendance_checkin' },
      { text: 'Check out from work', intent: 'attendance_checkout' },
      { text: 'I want to check out', intent: 'attendance_checkout' },
      { text: 'Clock out', intent: 'attendance_checkout' },
      { text: 'End work day', intent: 'attendance_checkout' },
      { text: 'Check out now', intent: 'attendance_checkout' },
      { text: 'My attendance record', intent: 'attendance_view' },
      { text: 'Show attendance history', intent: 'attendance_view' },
      { text: 'Working hours summary', intent: 'attendance_view' },
      { text: 'View team attendance', intent: 'attendance_view' },
      { text: 'Show my attendance', intent: 'attendance_view' },
      { text: 'View attendance records', intent: 'attendance_view' },

      // Evaluation Management
      { text: 'Create employee evaluation', intent: 'evaluation_create' },
      { text: 'Add performance review', intent: 'evaluation_create' },
      { text: 'Show my evaluations', intent: 'evaluation_view' },
      { text: 'View performance reviews', intent: 'evaluation_view' },
      { text: 'List all evaluations', intent: 'evaluation_list' },
      { text: 'Update evaluation', intent: 'evaluation_update' },
      { text: 'Edit performance review', intent: 'evaluation_update' },

      // Notification Management
      { text: 'Show my notifications', intent: 'notification_list' },
      { text: 'View alerts', intent: 'notification_list' },
      { text: 'Check messages', intent: 'notification_list' },
      { text: 'Mark notification as read', intent: 'notification_read' },
      { text: 'Delete notification', intent: 'notification_delete' },
      { text: 'Clear alerts', intent: 'notification_delete' },

      // Search and Analytics
      { text: 'Search for employees', intent: 'search_users' },
      { text: 'Find user information', intent: 'search_users' },
      { text: 'Generate report', intent: 'report_generate' },
      { text: 'Show statistics', intent: 'analytics_view' },
      { text: 'Department metrics', intent: 'analytics_view' },
      { text: 'Performance analytics', intent: 'analytics_view' },

      // HR Policy and Information
      { text: 'What is the sick leave policy', intent: 'policy_query' },
      { text: 'How do I apply for maternity leave', intent: 'policy_query' },
      { text: 'Company handbook information', intent: 'policy_query' },
      { text: 'HR policies and procedures', intent: 'policy_query' },
      { text: 'Benefits information', intent: 'benefits_info' },
      { text: 'Health insurance details', intent: 'benefits_info' },

      // General Help and Navigation
      { text: 'Help me with something', intent: 'help_general' },
      { text: 'How do I use this system', intent: 'help_general' },
      { text: 'What can you do', intent: 'capabilities' },
      { text: 'Show me around', intent: 'help_navigation' },
      { text: 'I need assistance', intent: 'help_general' },
      { text: 'List all available commands', intent: 'capabilities' },

      // Wellness and Support
      { text: 'I am feeling overwhelmed', intent: 'wellness_support' },
      { text: 'Work life balance tips', intent: 'wellness_support' },
      { text: 'Stress management help', intent: 'wellness_support' },
      { text: 'Mental health resources', intent: 'wellness_support' },

      // Greetings and Casual Conversation
      { text: 'Hello', intent: 'greeting' },
      { text: 'Hi there', intent: 'greeting' },
      { text: 'Hey', intent: 'greeting' },
      { text: 'Good morning', intent: 'greeting' },
      { text: 'Good afternoon', intent: 'greeting' },
      { text: 'Good evening', intent: 'greeting' },
      { text: 'Whats up', intent: 'casual_conversation' },
      { text: 'How are you', intent: 'casual_conversation' },
      { text: 'How are you doing', intent: 'casual_conversation' },
      { text: 'How is your day', intent: 'casual_conversation' },
      { text: 'How are things', intent: 'casual_conversation' },
      { text: 'What are you up to', intent: 'casual_conversation' },
      { text: 'How have you been', intent: 'casual_conversation' },

      // Gratitude and Appreciation
      { text: 'Thank you', intent: 'gratitude' },
      { text: 'Thanks', intent: 'gratitude' },
      { text: 'Thanks for your help', intent: 'gratitude' },
      { text: 'I appreciate it', intent: 'gratitude' },
      { text: 'You are amazing', intent: 'gratitude' },
      { text: 'You are awesome', intent: 'gratitude' },
      { text: 'Great job', intent: 'gratitude' },
      { text: 'Well done', intent: 'gratitude' },

      // Farewells
      { text: 'Goodbye', intent: 'farewell' },
      { text: 'Bye', intent: 'farewell' },
      { text: 'See you later', intent: 'farewell' },
      { text: 'Talk to you later', intent: 'farewell' },
      { text: 'Have a good day', intent: 'farewell' },
      { text: 'Take care', intent: 'farewell' },

      // System Capabilities and Features
      { text: 'What can you do', intent: 'capabilities' },
      { text: 'What are your features', intent: 'capabilities' },
      { text: 'Show me system capabilities', intent: 'capabilities' },
      { text: 'What features are available', intent: 'capabilities' },
      { text: 'Tell me about the system', intent: 'capabilities' },
      { text: 'What modules do you have', intent: 'capabilities' },
      { text: 'Show me all features', intent: 'capabilities' },

      // Enhanced Help Requests
      { text: 'Help me', intent: 'help_general' },
      { text: 'I need help', intent: 'help_general' },
      { text: 'Can you help me', intent: 'help_general' },
      { text: 'I need assistance', intent: 'help_general' },
      { text: 'How do I use this system', intent: 'help_general' },
      { text: 'Show me how to', intent: 'help_general' },
      { text: 'Guide me through', intent: 'help_general' },
      { text: 'I dont know how to', intent: 'help_general' },

      // System Overview and Explanations
      { text: 'Explain the system', intent: 'system_overview' },
      { text: 'How does the HR system work', intent: 'system_overview' },
      { text: 'Tell me about the features', intent: 'system_overview' },
      { text: 'What modules are available', intent: 'system_overview' },
      { text: 'System overview', intent: 'system_overview' },
      { text: 'How does this platform work', intent: 'system_overview' },

      // Advanced Feature Inquiries
      { text: 'How does GEK work', intent: 'gek_explanation' },
      { text: 'What is the GEK system', intent: 'gek_explanation' },
      { text: 'Explain fit scores', intent: 'gek_explanation' },
      { text: 'What are fit scores', intent: 'gek_explanation' },
      { text: 'How are employees ranked', intent: 'gek_explanation' },
      { text: 'Task assignment algorithm', intent: 'gek_explanation' },

      // Task Assignment Help
      { text: 'How are tasks assigned', intent: 'task_assignment_help' },
      { text: 'Task assignment process', intent: 'task_assignment_help' },
      { text: 'How do I assign tasks', intent: 'task_assignment_help' },
      { text: 'Task creation workflow', intent: 'task_assignment_help' },

      // Recruitment and CV Processing
      { text: 'How does CV matching work', intent: 'recruitment_help' },
      { text: 'Explain the recruitment process', intent: 'recruitment_help' },
      { text: 'How does NLP analysis work', intent: 'nlp_explanation' },
      { text: 'What is OCR processing', intent: 'nlp_explanation' },
      { text: 'CV processing explanation', intent: 'nlp_explanation' },
      { text: 'How are CVs analyzed', intent: 'nlp_explanation' },

      // Workflow Explanations
      { text: 'How do I request leave', intent: 'leave_workflow_help' },
      { text: 'Leave request process', intent: 'leave_workflow_help' },
      { text: 'How are leave requests approved', intent: 'leave_workflow_help' },
      { text: 'Leave approval workflow', intent: 'leave_workflow_help' },

      // Evaluation System Help
      { text: 'How do evaluations work', intent: 'evaluation_workflow_help' },
      { text: 'Performance review process', intent: 'evaluation_workflow_help' },
      { text: 'Evaluation system explanation', intent: 'evaluation_workflow_help' },
      { text: 'How are employees evaluated', intent: 'evaluation_workflow_help' },

      // Notification System Help
      { text: 'How do notifications work', intent: 'notification_help' },
      { text: 'Notification system explanation', intent: 'notification_help' },
      { text: 'How do I manage notifications', intent: 'notification_help' },

      // Analytics and Reporting
      { text: 'How do I generate reports', intent: 'analytics_help' },
      { text: 'Analytics explanation', intent: 'analytics_help' },
      { text: 'Dashboard features', intent: 'analytics_help' },
      { text: 'What reports are available', intent: 'analytics_help' },

      // Department and Role Specific
      { text: 'What can HR do', intent: 'role_capabilities' },
      { text: 'Admin features', intent: 'role_capabilities' },
      { text: 'Employee capabilities', intent: 'role_capabilities' },
      { text: 'Role permissions', intent: 'role_capabilities' }
    ];
  }

  /**
   * Train the intent classifier
   */
  trainClassifier() {
    try {
      // Add training data to classifier
      this.trainingData.forEach(({ text, intent }) => {
        this.classifier.addDocument(text.toLowerCase(), intent);
      });

      // Train the classifier
      this.classifier.train();
      this.isTrained = true;

      console.log('Intent classifier trained successfully');
    } catch (error) {
      console.error('Error training intent classifier:', error);
      this.isTrained = false;
    }
  }

  /**
   * Advanced intent classification with emotion detection, spell correction, and context awareness
   * @param {string} text - User input text
   * @param {Array} conversationHistory - Previous conversation messages
   * @param {Object} userContext - User context information
   * @returns {Object} - Enhanced classification result
   */
  classifyIntent(text, conversationHistory = [], userContext = {}) {
    if (!this.isTrained) {
      return {
        intent: 'unknown',
        confidence: 0,
        error: 'Classifier not trained'
      };
    }

    try {
      // Step 1: Spell correction and text preprocessing
      const spellAnalysis = spellCorrector.analyzeTypos(text);
      const correctedText = spellAnalysis.correctedText;

      // Step 2: Emotion and sentiment analysis
      const emotionalAnalysis = emotionAnalyzer.analyzeComplete(text);

      // Step 3: Context analysis
      const contextAnalysis = contextAnalyzer.analyzeContext(text, conversationHistory, userContext);

      // Step 4: Enhanced text processing for better classification
      const processedText = this.preprocessTextForClassification(correctedText, emotionalAnalysis, contextAnalysis);

      // Step 5: Get base classification
      const classifications = this.classifier.getClassifications(processedText.toLowerCase());

      if (classifications.length === 0) {
        return this.handleUnknownIntent(text, emotionalAnalysis, contextAnalysis);
      }

      // Step 6: Apply advanced scoring with emotional and contextual factors
      const enhancedClassifications = this.enhanceClassificationScores(
        classifications,
        emotionalAnalysis,
        contextAnalysis,
        spellAnalysis
      );

      const topClassification = enhancedClassifications[0];

      // Step 7: Determine final intent with confidence adjustment
      const finalResult = this.determineFinalIntent(
        topClassification,
        enhancedClassifications,
        emotionalAnalysis,
        contextAnalysis
      );

      return {
        ...finalResult,
        analysis: {
          emotional: emotionalAnalysis,
          context: contextAnalysis,
          spelling: spellAnalysis,
          processedText: processedText
        },
        metadata: {
          originalText: text,
          correctedText: correctedText,
          hasTypos: spellAnalysis.hasCorrections,
          emotionalState: emotionalAnalysis.emotions.primaryEmotion,
          urgency: contextAnalysis.temporal?.urgency || 'normal',
          conversationStage: contextAnalysis.conversationStage
        }
      };
    } catch (error) {
      console.error('Error in advanced intent classification:', error);
      return {
        intent: 'error',
        confidence: 0,
        error: error.message
      };
    }
  }

  /**
   * Preprocess text for better classification
   * @param {string} text - Corrected text
   * @param {Object} emotionalAnalysis - Emotional analysis results
   * @param {Object} contextAnalysis - Context analysis results
   * @returns {string} - Processed text
   */
  preprocessTextForClassification(text, emotionalAnalysis, contextAnalysis) {
    let processedText = text.toLowerCase();

    // Add emotional context keywords for better classification
    if (emotionalAnalysis.emotions.primaryEmotion) {
      const emotionKeywords = this.emotionalIntents[emotionalAnalysis.emotions.primaryEmotion];
      if (emotionKeywords) {
        processedText += ' ' + emotionKeywords.join(' ');
      }
    }

    // Add context keywords based on topic analysis
    if (contextAnalysis.topic && contextAnalysis.topic.primaryTopics) {
      contextAnalysis.topic.primaryTopics.forEach(topic => {
        processedText += ' ' + topic.replace('_', ' ');
      });
    }

    // Handle typo-tolerant keywords
    Object.entries(this.typoTolerantKeywords).forEach(([correct, variations]) => {
      variations.forEach(variation => {
        if (processedText.includes(variation)) {
          processedText = processedText.replace(new RegExp(variation, 'g'), correct);
        }
      });
    });

    return processedText;
  }

  /**
   * Enhance classification scores with emotional and contextual factors
   * @param {Array} classifications - Base classifications
   * @param {Object} emotionalAnalysis - Emotional analysis
   * @param {Object} contextAnalysis - Context analysis
   * @param {Object} spellAnalysis - Spell analysis
   * @returns {Array} - Enhanced classifications
   */
  enhanceClassificationScores(classifications, emotionalAnalysis, contextAnalysis, spellAnalysis) {
    return classifications.map(classification => {
      let enhancedScore = classification.value;
      let confidenceBoost = 0;

      // Boost confidence for emotional intents when emotions are detected
      if (emotionalAnalysis.emotions.hasEmotionalContent) {
        const emotionalIntentMatch = this.getEmotionalIntentMatch(
          classification.label,
          emotionalAnalysis.emotions.primaryEmotion
        );
        if (emotionalIntentMatch) {
          confidenceBoost += 0.2;
        }
      }

      // Boost confidence for contextually relevant intents
      if (contextAnalysis.topic && contextAnalysis.topic.primaryTopics) {
        const topicMatch = this.getTopicIntentMatch(
          classification.label,
          contextAnalysis.topic.primaryTopics
        );
        if (topicMatch) {
          confidenceBoost += 0.15;
        }
      }

      // Boost confidence for conversation continuation
      if (contextAnalysis.reference && contextAnalysis.reference.topicContinuation) {
        confidenceBoost += 0.1;
      }

      // Reduce confidence if many typos (might indicate confusion)
      if (spellAnalysis.statistics && spellAnalysis.statistics.typoRate > 0.3) {
        confidenceBoost -= 0.1;
      }

      // Apply urgency boost for time-sensitive intents
      if (contextAnalysis.temporal && contextAnalysis.temporal.urgency === 'high') {
        const urgentIntents = ['leave_request', 'task_update', 'attendance_checkin', 'wellness_support'];
        if (urgentIntents.includes(classification.label)) {
          confidenceBoost += 0.1;
        }
      }

      return {
        ...classification,
        value: Math.min(enhancedScore + confidenceBoost, 1.0),
        originalScore: classification.value,
        confidenceBoost: confidenceBoost
      };
    }).sort((a, b) => b.value - a.value);
  }

  /**
   * Determine final intent with advanced logic
   * @param {Object} topClassification - Top classification result
   * @param {Array} allClassifications - All classification results
   * @param {Object} emotionalAnalysis - Emotional analysis
   * @param {Object} contextAnalysis - Context analysis
   * @returns {Object} - Final intent determination
   */
  determineFinalIntent(topClassification, allClassifications, emotionalAnalysis, contextAnalysis) {
    // Handle emotional support needs
    if (emotionalAnalysis.summary && emotionalAnalysis.summary.needsEmotionalSupport) {
      return {
        intent: 'wellness_support',
        confidence: 0.9,
        reason: 'emotional_support_needed',
        alternatives: allClassifications.slice(0, 2),
        empathyResponse: emotionalAnalysis.empathy
      };
    }

    // Handle high urgency situations
    if (contextAnalysis.temporal && contextAnalysis.temporal.urgency === 'high') {
      const urgentIntents = ['leave_request', 'task_update', 'attendance_checkin', 'wellness_support'];
      const urgentMatch = allClassifications.find(c => urgentIntents.includes(c.label));
      if (urgentMatch && urgentMatch.value > 0.4) {
        return {
          intent: urgentMatch.label,
          confidence: Math.min(urgentMatch.value + 0.2, 1.0),
          reason: 'urgency_detected',
          alternatives: allClassifications.slice(0, 2),
          urgency: 'high'
        };
      }
    }

    // Standard confidence threshold check
    if (topClassification.value < this.confidenceThreshold) {
      return {
        intent: 'unclear',
        confidence: topClassification.value,
        reason: 'low_confidence',
        alternatives: allClassifications.slice(0, 3),
        suggestions: this.generateClarificationSuggestions(allClassifications, contextAnalysis)
      };
    }

    // High confidence result
    return {
      intent: topClassification.label,
      confidence: topClassification.value,
      reason: 'high_confidence',
      alternatives: allClassifications.slice(1, 3),
      originalScore: topClassification.originalScore,
      confidenceBoost: topClassification.confidenceBoost
    };
  }

  /**
   * Handle unknown intents with intelligent fallback using system knowledge
   * @param {string} text - Original text
   * @param {Object} emotionalAnalysis - Emotional analysis
   * @param {Object} contextAnalysis - Context analysis
   * @returns {Object} - Fallback intent result
   */
  handleUnknownIntent(text, emotionalAnalysis, contextAnalysis) {
    // Check for emotional support needs
    if (emotionalAnalysis.summary && emotionalAnalysis.summary.needsEmotionalSupport) {
      return {
        intent: 'wellness_support',
        confidence: 0.8,
        reason: 'emotional_fallback'
      };
    }

    // Use system knowledge to identify relevant modules
    const relevantModules = systemKnowledge.identifyRelevantModules(text);
    if (relevantModules.length > 0) {
      const topModule = relevantModules[0];

      // Map modules to likely intents
      const moduleIntentMapping = {
        'user_management': 'user_list',
        'leave_management': 'leave_balance',
        'task_management': 'task_list',
        'attendance_system': 'attendance_view',
        'evaluation_system': 'evaluation_view',
        'recruitment_system': 'job_list',
        'notification_system': 'notification_list',
        'analytics_system': 'analytics_view',
        'gek_system': 'help_general'
      };

      const suggestedIntent = moduleIntentMapping[topModule.module];
      if (suggestedIntent) {
        return {
          intent: suggestedIntent,
          confidence: 0.6,
          reason: 'system_knowledge_fallback',
          systemModule: topModule.module,
          moduleInfo: topModule.info,
          suggestions: this.generateSystemAwareSuggestions(topModule)
        };
      }
    }

    // Check for help-seeking patterns
    const helpPatterns = ['help', 'how', 'what', 'can you', 'assist', 'support'];
    if (helpPatterns.some(pattern => text.toLowerCase().includes(pattern))) {
      return {
        intent: 'help_general',
        confidence: 0.7,
        reason: 'help_pattern_detected',
        suggestions: this.generateGeneralHelpSuggestions()
      };
    }

    // Default unknown with system-aware suggestions
    return {
      intent: 'unknown',
      confidence: 0,
      reason: 'no_pattern_match',
      suggestions: this.generateSystemAwareSuggestions()
    };
  }

  /**
   * Generate system-aware suggestions based on modules
   */
  generateSystemAwareSuggestions(moduleInfo = null) {
    if (moduleInfo) {
      const module = moduleInfo.info;
      return module.features.slice(0, 4).map(feature =>
        `Try: "${feature}"`
      );
    }

    // General system suggestions
    return [
      'Ask about "leave balance" or "time off"',
      'Try "show my tasks" or "task assignments"',
      'Ask about "attendance" or "check in"',
      'Try "job openings" or "applications"',
      'Ask "what can you help me with?"'
    ];
  }

  /**
   * Generate general help suggestions
   */
  generateGeneralHelpSuggestions() {
    const systemOverview = systemKnowledge.getSystemOverview();
    return [
      `I can help with ${systemOverview.keyCapabilities.slice(0, 3).join(', ')}`,
      'Try asking about leave, tasks, attendance, or evaluations',
      'Ask "what features are available?" for a complete overview',
      'Say "show me system capabilities" to learn more'
    ];
  }

  /**
   * Helper methods for enhanced classification
   */
  getEmotionalIntentMatch(intent, emotion) {
    const emotionalMappings = {
      'wellness_support': ['sadness', 'anxiety', 'anger', 'fatigue'],
      'gratitude': ['gratitude', 'joy'],
      'help_general': ['confusion'],
      'greeting': ['joy', 'excitement']
    };

    return emotionalMappings[intent] && emotionalMappings[intent].includes(emotion);
  }

  getTopicIntentMatch(intent, topics) {
    const topicMappings = {
      'leave_request': ['leave_management'],
      'leave_balance': ['leave_management'],
      'task_list': ['task_management'],
      'task_create': ['task_management'],
      'attendance_checkin': ['attendance'],
      'attendance_checkout': ['attendance'],
      'policy_query': ['support'],
      'benefits_info': ['benefits']
    };

    return topicMappings[intent] &&
           topicMappings[intent].some(topic => topics.includes(topic));
  }

  generateClarificationSuggestions(classifications, contextAnalysis) {
    const suggestions = [];

    classifications.slice(0, 3).forEach(classification => {
      const description = this.getIntentDescription(classification.label);
      suggestions.push(`Did you mean: ${description}?`);
    });

    return suggestions;
  }

  /**
   * Get human-readable description for an intent
   * @param {string} intent - Intent name
   * @returns {string} - Human-readable description
   */
  getIntentDescription(intent) {
    const descriptions = {
      'greeting': 'Say hello or start a conversation',
      'leave_request': 'Request time off or vacation',
      'leave_balance': 'Check your leave balance',
      'task_list': 'View your tasks and assignments',
      'task_create': 'Create a new task',
      'task_update': 'Update task status',
      'attendance_checkin': 'Check in for work',
      'attendance_checkout': 'Check out from work',
      'user_create': 'Create a new user account',
      'user_list': 'View all users',
      'job_create': 'Create a job posting',
      'job_list': 'View available jobs',
      'application_list': 'View job applications',
      'policy_query': 'Ask about HR policies',
      'wellness_support': 'Get emotional or wellness support',
      'help_general': 'Get general help and assistance'
    };

    return descriptions[intent] || 'Perform an action';
  }

  /**
   * Extract entities from text using pattern matching
   * @param {string} text - Input text
   * @param {string} intent - Classified intent
   * @returns {Array} - Extracted entities
   */
  extractEntities(text, intent) {
    const entities = [];
    const lowerText = text.toLowerCase();

    // Date patterns
    const datePatterns = [
      /\b(next week|this week|tomorrow|today|yesterday)\b/g,
      /\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/g,
      /\b(\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})\b/g,
      /\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}\b/g
    ];

    datePatterns.forEach(pattern => {
      const matches = lowerText.match(pattern);
      if (matches) {
        matches.forEach(match => {
          entities.push({
            type: 'date',
            value: match,
            confidence: 0.8
          });
        });
      }
    });

    // Duration patterns
    const durationPattern = /\b(\d+)\s+(day|days|week|weeks|month|months)\b/g;
    const durationMatches = lowerText.match(durationPattern);
    if (durationMatches) {
      durationMatches.forEach(match => {
        entities.push({
          type: 'duration',
          value: match,
          confidence: 0.8
        });
      });
    }

    // Leave type patterns
    const leaveTypes = ['vacation', 'sick', 'personal', 'maternity', 'paternity', 'emergency'];
    leaveTypes.forEach(type => {
      if (lowerText.includes(type)) {
        entities.push({
          type: 'leave_type',
          value: type,
          confidence: 0.9
        });
      }
    });

    // Task status patterns
    const taskStatuses = ['complete', 'completed', 'done', 'finished', 'in progress', 'pending', 'started'];
    taskStatuses.forEach(status => {
      if (lowerText.includes(status)) {
        entities.push({
          type: 'task_status',
          value: status,
          confidence: 0.8
        });
      }
    });

    // Priority patterns
    const priorities = ['high', 'medium', 'low', 'urgent', 'critical'];
    priorities.forEach(priority => {
      if (lowerText.includes(priority)) {
        entities.push({
          type: 'priority',
          value: priority,
          confidence: 0.8
        });
      }
    });

    // Person name patterns (simple detection)
    const namePattern = /\b([A-Z][a-z]+ [A-Z][a-z]+)\b/g;
    const nameMatches = text.match(namePattern);
    if (nameMatches) {
      nameMatches.forEach(match => {
        entities.push({
          type: 'person_name',
          value: match,
          confidence: 0.7
        });
      });
    }

    // Email patterns
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const emailMatches = text.match(emailPattern);
    if (emailMatches) {
      emailMatches.forEach(match => {
        entities.push({
          type: 'email',
          value: match,
          confidence: 0.9
        });
      });
    }

    // Role patterns
    const roles = ['admin', 'hr', 'user', 'manager', 'employee'];
    roles.forEach(role => {
      if (lowerText.includes(role)) {
        entities.push({
          type: 'role',
          value: role,
          confidence: 0.8
        });
      }
    });

    // Status patterns
    const statuses = ['pending', 'approved', 'rejected', 'completed', 'in progress', 'on hold'];
    statuses.forEach(status => {
      if (lowerText.includes(status)) {
        entities.push({
          type: 'status',
          value: status,
          confidence: 0.8
        });
      }
    });

    // Search term extraction (words after "search", "find", "show")
    const searchPattern = /(?:search|find|show|list|view)\s+(?:for\s+)?([a-zA-Z\s]+)/gi;
    const searchMatches = text.match(searchPattern);
    if (searchMatches) {
      searchMatches.forEach(match => {
        const term = match.replace(/(?:search|find|show|list|view)\s+(?:for\s+)?/gi, '').trim();
        if (term.length > 2) {
          entities.push({
            type: 'search_term',
            value: term,
            confidence: 0.6
          });
        }
      });
    }

    // Task title extraction (words after "task", "create task", "assign")
    const taskPattern = /(?:task|create task|assign)\s+([^.!?]+)/gi;
    const taskMatches = text.match(taskPattern);
    if (taskMatches) {
      taskMatches.forEach(match => {
        const title = match.replace(/(?:task|create task|assign)\s+/gi, '').trim();
        if (title.length > 3) {
          entities.push({
            type: 'task_title',
            value: title,
            confidence: 0.7
          });
        }
      });
    }

    return entities;
  }

  /**
   * Get intent suggestions based on partial input
   * @param {string} partialText - Partial user input
   * @returns {Array} - Suggested intents
   */
  getSuggestions(partialText) {
    if (!partialText || partialText.length < 2) {
      return [];
    }

    const suggestions = [];
    const lowerPartial = partialText.toLowerCase();

    // Find training examples that match partial input
    this.trainingData.forEach(({ text, intent }) => {
      if (text.toLowerCase().includes(lowerPartial)) {
        const existingSuggestion = suggestions.find(s => s.intent === intent);
        if (!existingSuggestion) {
          suggestions.push({
            intent,
            example: text,
            confidence: 0.7
          });
        }
      }
    });

    return suggestions.slice(0, 5); // Return top 5 suggestions
  }

  /**
   * Add new training example
   * @param {string} text - Training text
   * @param {string} intent - Intent label
   */
  addTrainingExample(text, intent) {
    this.trainingData.push({ text, intent });

    // Retrain classifier with new data
    this.classifier.addDocument(text.toLowerCase(), intent);
    this.classifier.retrain();
  }

  /**
   * Get all available intents
   * @returns {Array} - List of available intents
   */
  getAvailableIntents() {
    const intents = [...new Set(this.trainingData.map(item => item.intent))];
    return intents.sort();
  }

  /**
   * Get intent description
   * @param {string} intent - Intent name
   * @returns {string} - Intent description
   */
  getIntentDescription(intent) {
    const descriptions = {
      'leave_request': 'Request time off or vacation',
      'leave_balance': 'Check remaining leave days',
      'leave_suggestion': 'Get optimal leave timing suggestions',
      'leave_conflict_check': 'Check for conflicts with leave dates',
      'task_list': 'View assigned tasks',
      'task_update': 'Update task status or progress',
      'task_create': 'Create new task',
      'task_assign': 'Assign task to team member',
      'task_deadline': 'Check task deadlines',
      'task_info': 'Get task information',
      'policy_query': 'Ask about HR policies',
      'benefits_info': 'Get benefits information',
      'evaluation_view': 'View performance evaluations',
      'evaluation_schedule': 'Check evaluation schedule',
      'goals_view': 'View goals and objectives',
      'career_guidance': 'Get career development advice',
      'attendance_checkin': 'Clock in for work',
      'attendance_checkout': 'Clock out from work',
      'attendance_view': 'View attendance records',
      'application_view': 'View job applications',
      'application_status': 'Check application status',
      'job_search': 'Search for job positions',
      'job_apply': 'Apply for job position',
      'help_general': 'Get general help',
      'capabilities': 'Learn about AI assistant capabilities',
      'help_navigation': 'Get help with navigation',
      'wellness_support': 'Get wellness and mental health support',
      'form_assistance': 'Get help filling forms',
      'profile_update': 'Update profile information',
      'report_generate': 'Generate reports',
      'analytics_view': 'View analytics and metrics',
      'greeting': 'Greeting or hello',
      'gratitude': 'Thank you or appreciation',
      'farewell': 'Goodbye or farewell'
    };

    return descriptions[intent] || 'Unknown intent';
  }
}

// Singleton instance
const intentClassifier = new IntentClassifier();

module.exports = intentClassifier;
