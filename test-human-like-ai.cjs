const axios = require('axios');

async function testHumanLikeAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test human-like conversation features
    console.log('\n🤖 Testing Human-Like Conversational AI...\n');
    
    const conversationTests = [
      // Test 1: Casual Greeting
      {
        message: "Hey! How are you doing today?",
        description: "Testing casual conversation and personality"
      },
      
      // Test 2: Emotional Expression - Sadness
      {
        message: "I'm feeling really down today. Work has been overwhelming and I don't know what to do 😢",
        description: "Testing emotional intelligence and empathy for sadness"
      },
      
      // Test 3: Follow-up conversation
      {
        message: "Thanks for understanding. What kind of support is available?",
        description: "Testing conversation memory and context awareness"
      },
      
      // Test 4: Casual Language with Typos
      {
        message: "hey whats up? can u help me w/ my tasks plz? i dont no how 2 do this",
        description: "Testing casual language understanding and spell correction"
      },
      
      // Test 5: Emotional Expression - Excitement
      {
        message: "OMG I just got promoted! I'm so excited and happy! 🎉🎊",
        description: "Testing positive emotion detection and celebratory response"
      },
      
      // Test 6: Frustration and Anger
      {
        message: "I'm so frustrated with this system! Nothing works properly and I'm getting really angry!",
        description: "Testing anger detection and conflict resolution support"
      },
      
      // Test 7: Gratitude
      {
        message: "Thank you so much for all your help! You're amazing and I really appreciate everything! 🙏💖",
        description: "Testing gratitude recognition and warm response"
      },
      
      // Test 8: Casual Check-in
      {
        message: "What's up? How's your day going?",
        description: "Testing natural conversation flow and personality"
      },
      
      // Test 9: Anxiety and Stress
      {
        message: "I'm really anxious about my performance review tomorrow. I can't sleep and I'm so worried 😰",
        description: "Testing anxiety detection and supportive response"
      },
      
      // Test 10: Memory Reference
      {
        message: "Remember when we talked about my promotion earlier? I wanted to follow up on that",
        description: "Testing conversation memory and reference handling"
      }
    ];

    for (let i = 0; i < conversationTests.length; i++) {
      const test = conversationTests[i];
      console.log(`\n📝 Test ${i + 1}: ${test.description}`);
      console.log(`👤 User: "${test.message}"`);
      
      try {
        const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
          message: test.message
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = response.data.data;
        const assistantMessage = data.assistantMessage;
        const classification = data.classification;

        console.log(`🤖 Alex: ${assistantMessage.content}`);
        console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
        
        // Show advanced analysis if available
        if (classification.metadata) {
          console.log(`🧠 Analysis:`);
          if (classification.metadata.hasTypos) {
            console.log(`   📝 Corrected: "${classification.metadata.correctedText}"`);
          }
          if (classification.metadata.emotionalState || classification.metadata.primaryEmotion) {
            console.log(`   💭 Emotion: ${classification.metadata.emotionalState || classification.metadata.primaryEmotion}`);
          }
          if (classification.metadata.urgency !== 'normal') {
            console.log(`   ⚡ Urgency: ${classification.metadata.urgency}`);
          }
          if (classification.reason) {
            console.log(`   🔍 Reason: ${classification.reason}`);
          }
        }

        // Show suggestions if available
        if (data.suggestions && data.suggestions.length > 0) {
          console.log(`💡 Suggestions: ${data.suggestions.slice(0, 3).join(', ')}`);
        }

        // Show response metadata
        if (assistantMessage.metadata) {
          console.log(`⚡ Response time: ${assistantMessage.metadata.responseTime}ms`);
          if (assistantMessage.metadata.model) {
            console.log(`🔧 Model: ${assistantMessage.metadata.model}`);
          }
        }
        
      } catch (error) {
        console.error(`❌ Error in test ${i + 1}:`, error.response?.data?.message || error.message);
      }
      
      // Small delay between tests to simulate natural conversation
      await new Promise(resolve => setTimeout(resolve, 1500));
    }

    console.log('\n🎉 Human-Like AI Testing Complete!');
    console.log('\n📊 Summary of Enhanced Features Tested:');
    console.log('✅ Natural Conversation Flow');
    console.log('✅ Emotional Intelligence & Empathy');
    console.log('✅ Personality & Warmth');
    console.log('✅ Conversation Memory & Context');
    console.log('✅ Casual Language Understanding');
    console.log('✅ Spell Correction & Typo Tolerance');
    console.log('✅ Mood-Based Response Adaptation');
    console.log('✅ Supportive & Caring Responses');
    console.log('✅ Reference Handling');
    console.log('✅ Contextual Suggestions');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testHumanLikeAI();
