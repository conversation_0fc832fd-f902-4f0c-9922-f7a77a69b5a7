/**
 * AI-Powered Task Assignment Component
 * Uses GEK analytics and AI to recommend the best user for task assignment
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Button,
  LinearProgress,
  Alert,
  Divider,
  Grid,
  IconButton,
  Tooltip,
  CircularProgress,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Psychology as AIIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Person as PersonIcon,
  Speed as SpeedIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

const AITaskAssignment = ({ taskData, onUserSelected, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [recommendations, setRecommendations] = useState([]);
  const [analyzing, setAnalyzing] = useState(false);

  useEffect(() => {
    if (taskData) {
      generateRecommendations();
    }
  }, [taskData]);

  const generateRecommendations = async () => {
    try {
      setLoading(true);
      setAnalyzing(true);

      // Fetch all users and their performance data
      const [usersResponse, tasksResponse, evaluationsResponse, attendanceResponse] = await Promise.all([
        api.get('/hr/users', { params: { role: 'user' } }),
        api.get('/tasks/hr'),
        api.get('/hr/evaluations'),
        api.get('/normaluser/attendance')
      ]);

      const users = usersResponse.data || [];
      const tasks = tasksResponse.data || [];
      const evaluations = evaluationsResponse.data || [];
      const attendance = attendanceResponse.data || [];

      // Use AI to analyze and recommend best candidates
      const aiRecommendations = await analyzeTaskAssignment(users, tasks, evaluations, attendance, taskData);
      
      setRecommendations(aiRecommendations);
      setAnalyzing(false);

    } catch (error) {
      console.error('Error generating recommendations:', error);
      toast.error('Failed to generate AI recommendations');
      setAnalyzing(false);
    } finally {
      setLoading(false);
    }
  };

  const analyzeTaskAssignment = async (users, tasks, evaluations, attendance, task) => {
    // Calculate comprehensive user metrics
    const userAnalytics = users.map(user => {
      const userTasks = tasks.filter(t => 
        t.assignedTo === user._id || t.assignedTo === user.email
      );
      const userEvaluations = evaluations.filter(e => e.userId === user._id);
      const userAttendance = attendance.filter(a => a.userId === user._id);

      // Task performance metrics
      const completedTasks = userTasks.filter(t => t.status === 'Completed').length;
      const totalTasks = userTasks.length;
      const taskCompletionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 50;

      // Priority task handling
      const highPriorityTasks = userTasks.filter(t => t.priority === 'High');
      const highPriorityCompleted = highPriorityTasks.filter(t => t.status === 'Completed').length;
      const highPriorityRate = highPriorityTasks.length > 0 ? 
        (highPriorityCompleted / highPriorityTasks.length) * 100 : 50;

      // Category expertise
      const categoryTasks = userTasks.filter(t => t.category === task.category);
      const categoryCompleted = categoryTasks.filter(t => t.status === 'Completed').length;
      const categoryExpertise = categoryTasks.length > 0 ? 
        (categoryCompleted / categoryTasks.length) * 100 : 30;

      // Current workload
      const activeTasks = userTasks.filter(t => 
        t.status === 'In Progress' || t.status === 'Pending'
      ).length;
      const workloadScore = Math.max(0, 100 - (activeTasks * 15)); // Penalty for high workload

      // Evaluation scores
      const latestEvaluation = userEvaluations.sort((a, b) => 
        new Date(b.createdAt) - new Date(a.createdAt)
      )[0];
      
      const evaluationScore = latestEvaluation ? 
        ((latestEvaluation.performanceRating + 
          latestEvaluation.attitudeRating + 
          latestEvaluation.communicationRating + 
          latestEvaluation.teamworkRating + 
          latestEvaluation.initiativeRating) / 5) * 20 : 50;

      // Attendance reliability
      const attendanceRate = userAttendance.length > 0 ? 
        (userAttendance.filter(a => a.checkIn && a.checkOut).length / userAttendance.length) * 100 : 70;

      // AI-powered scoring algorithm
      let aiScore = 0;
      let reasons = [];

      // Base performance weight (30%)
      aiScore += taskCompletionRate * 0.3;
      if (taskCompletionRate >= 80) {
        reasons.push(`High task completion rate (${Math.round(taskCompletionRate)}%)`);
      }

      // Priority handling weight (25%)
      if (task.priority === 'High') {
        aiScore += highPriorityRate * 0.25;
        if (highPriorityRate >= 75) {
          reasons.push(`Excellent with high-priority tasks (${Math.round(highPriorityRate)}%)`);
        }
      } else {
        aiScore += taskCompletionRate * 0.25;
      }

      // Category expertise weight (20%)
      aiScore += categoryExpertise * 0.2;
      if (categoryExpertise >= 70) {
        reasons.push(`Strong expertise in ${task.category} category`);
      }

      // Workload availability weight (15%)
      aiScore += workloadScore * 0.15;
      if (activeTasks <= 2) {
        reasons.push(`Low current workload (${activeTasks} active tasks)`);
      } else if (activeTasks >= 5) {
        reasons.push(`High workload warning (${activeTasks} active tasks)`);
      }

      // Evaluation performance weight (10%)
      aiScore += evaluationScore * 0.1;
      if (evaluationScore >= 80) {
        reasons.push(`Excellent performance evaluations`);
      }

      // Bonus factors
      if (attendanceRate >= 90) {
        aiScore += 5;
        reasons.push(`Excellent attendance record`);
      }

      if (user.role === 'hr' && task.category === 'HR') {
        aiScore += 10;
        reasons.push(`HR role matches task category`);
      }

      // Deadline urgency factor
      if (task.deadline) {
        const daysUntilDeadline = Math.ceil((new Date(task.deadline) - new Date()) / (1000 * 60 * 60 * 24));
        if (daysUntilDeadline <= 1 && taskCompletionRate >= 85) {
          aiScore += 8;
          reasons.push(`Reliable for urgent deadlines`);
        }
      }

      return {
        ...user,
        aiScore: Math.min(100, Math.round(aiScore)),
        taskCompletionRate: Math.round(taskCompletionRate),
        highPriorityRate: Math.round(highPriorityRate),
        categoryExpertise: Math.round(categoryExpertise),
        workloadScore: Math.round(workloadScore),
        evaluationScore: Math.round(evaluationScore),
        attendanceRate: Math.round(attendanceRate),
        activeTasks,
        totalTasks,
        completedTasks,
        reasons: reasons.slice(0, 3), // Top 3 reasons
        recommendation: aiScore >= 80 ? 'Highly Recommended' : 
                       aiScore >= 60 ? 'Recommended' : 
                       aiScore >= 40 ? 'Consider' : 'Not Recommended'
      };
    });

    // Sort by AI score and return top candidates
    return userAnalytics
      .sort((a, b) => b.aiScore - a.aiScore)
      .slice(0, 5); // Top 5 recommendations
  };

  const handleSelectUser = (user) => {
    if (onUserSelected) {
      onUserSelected(user);
    }
    toast.success(`Selected ${user.name} for task assignment`);
  };

  const getRecommendationColor = (recommendation) => {
    switch (recommendation) {
      case 'Highly Recommended': return 'success';
      case 'Recommended': return 'primary';
      case 'Consider': return 'warning';
      default: return 'error';
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return '#4caf50';
    if (score >= 60) return '#2196f3';
    if (score >= 40) return '#ff9800';
    return '#f44336';
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="center" py={4}>
            <CircularProgress size={40} />
            <Typography variant="h6" sx={{ ml: 2 }}>
              {analyzing ? 'AI Analyzing Best Candidates...' : 'Loading...'}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card elevation={3}>
      <CardContent>
        {/* Header */}
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <AIIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              AI Task Assignment Recommendations
            </Typography>
          </Box>
          <Box>
            <IconButton onClick={generateRecommendations} disabled={analyzing}>
              {analyzing ? <CircularProgress size={20} /> : <RefreshIcon />}
            </IconButton>
            {onClose && (
              <Button onClick={onClose} size="small">
                Close
              </Button>
            )}
          </Box>
        </Box>

        {/* Task Info */}
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="subtitle2" fontWeight="bold">
            Task: {taskData.title}
          </Typography>
          <Typography variant="body2">
            Priority: {taskData.priority} | Category: {taskData.category} | 
            {taskData.deadline && ` Deadline: ${new Date(taskData.deadline).toLocaleDateString()}`}
          </Typography>
        </Alert>

        {/* Recommendations List */}
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Top Candidates (AI-Ranked)
        </Typography>

        <List>
          {recommendations.map((user, index) => (
            <React.Fragment key={user._id}>
              <ListItem alignItems="flex-start">
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: getScoreColor(user.aiScore) }}>
                    {index + 1}
                  </Avatar>
                </ListItemAvatar>
                
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        {user.name}
                      </Typography>
                      <Chip 
                        label={user.recommendation} 
                        color={getRecommendationColor(user.recommendation)}
                        size="small"
                      />
                      <Chip 
                        label={`AI Score: ${user.aiScore}%`} 
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {user.job} • {user.department} • {user.activeTasks} active tasks
                      </Typography>
                      
                      {/* Performance Metrics */}
                      <Grid container spacing={1} sx={{ mb: 1 }}>
                        <Grid item xs={6} sm={3}>
                          <Typography variant="caption" display="block">
                            Task Completion
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={user.taskCompletionRate} 
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                          <Typography variant="caption">
                            {user.taskCompletionRate}%
                          </Typography>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Typography variant="caption" display="block">
                            Category Expertise
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={user.categoryExpertise} 
                            sx={{ height: 6, borderRadius: 3 }}
                            color="secondary"
                          />
                          <Typography variant="caption">
                            {user.categoryExpertise}%
                          </Typography>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Typography variant="caption" display="block">
                            Availability
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={user.workloadScore} 
                            sx={{ height: 6, borderRadius: 3 }}
                            color="success"
                          />
                          <Typography variant="caption">
                            {user.workloadScore}%
                          </Typography>
                        </Grid>
                        <Grid item xs={6} sm={3}>
                          <Typography variant="caption" display="block">
                            Performance
                          </Typography>
                          <LinearProgress 
                            variant="determinate" 
                            value={user.evaluationScore} 
                            sx={{ height: 6, borderRadius: 3 }}
                            color="warning"
                          />
                          <Typography variant="caption">
                            {user.evaluationScore}%
                          </Typography>
                        </Grid>
                      </Grid>

                      {/* AI Reasons */}
                      <Typography variant="caption" color="primary" fontWeight="bold">
                        AI Analysis:
                      </Typography>
                      <Box sx={{ mt: 0.5 }}>
                        {user.reasons.map((reason, idx) => (
                          <Chip
                            key={idx}
                            label={reason}
                            size="small"
                            variant="outlined"
                            sx={{ mr: 0.5, mb: 0.5, fontSize: '0.7rem' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <Button
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={() => handleSelectUser(user)}
                    startIcon={<CheckCircleIcon />}
                  >
                    Select
                  </Button>
                </ListItemSecondaryAction>
              </ListItem>
              
              {index < recommendations.length - 1 && <Divider variant="inset" component="li" />}
            </React.Fragment>
          ))}
        </List>

        {recommendations.length === 0 && (
          <Alert severity="warning">
            No suitable candidates found. Consider adjusting task requirements or deadline.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default AITaskAssignment;
