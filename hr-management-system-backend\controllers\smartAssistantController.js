/**
 * Smart Assistant Controller
 * Handles AI insights, user activity tracking, and smart suggestions
 */

const AIInsight = require('../models/AIInsight');
const UserActivity = require('../models/UserActivity');
const User = require('../models/User');
const Task = require('../models/Task');
const LeaveRequest = require('../models/LeaveRequest');
const Application = require('../models/Application');
const Job = require('../models/Job');

// Track user activity
const trackActivity = async (req, res) => {
  try {
    const { activityType, category, resourceType, resourceId, metadata, duration } = req.body;
    const userId = req.user.id;

    const activity = new UserActivity({
      userId,
      activityType,
      category,
      resourceType,
      resourceId,
      metadata: metadata || {},
      duration: duration || 0,
      sessionId: req.sessionID || req.headers['x-session-id'],
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip || req.connection.remoteAddress
    });

    await activity.save();

    // Generate insights based on activity patterns
    await generateInsightsFromActivity(userId, activity);

    res.status(201).json({
      success: true,
      message: 'Activity tracked successfully',
      activityId: activity._id
    });
  } catch (error) {
    console.error('Error tracking activity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track activity',
      error: error.message
    });
  }
};

// Get user insights
const getUserInsights = async (req, res) => {
  try {
    const userId = req.user.id;
    const { category, priority, limit = 10 } = req.query;

    const options = { limit: parseInt(limit) };
    if (category) options.category = category;
    if (priority) options.priority = priority;

    const insights = await AIInsight.findActiveForUser(userId, options);

    res.json({
      success: true,
      insights,
      count: insights.length
    });
  } catch (error) {
    console.error('Error fetching insights:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch insights',
      error: error.message
    });
  }
};

// Get user activity statistics
const getActivityStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const { timeRange = 30 } = req.query;

    const stats = await UserActivity.getUserActivityStats(userId, parseInt(timeRange));
    const patterns = await UserActivity.getActivityPatterns(userId, parseInt(timeRange));
    const productivity = await UserActivity.getProductivityMetrics(userId, 7);

    res.json({
      success: true,
      stats,
      patterns,
      productivity
    });
  } catch (error) {
    console.error('Error fetching activity stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch activity statistics',
      error: error.message
    });
  }
};

// Provide feedback on insight
const provideFeedback = async (req, res) => {
  try {
    const { insightId } = req.params;
    const { rating, comment } = req.body;
    const userId = req.user.id;

    const insight = await AIInsight.findById(insightId);
    if (!insight) {
      return res.status(404).json({
        success: false,
        message: 'Insight not found'
      });
    }

    // Check if user owns this insight
    if (insight.userId.toString() !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to provide feedback on this insight'
      });
    }

    await insight.addFeedback({
      rating,
      comment: comment || '',
      userId
    });

    res.json({
      success: true,
      message: 'Feedback recorded successfully'
    });
  } catch (error) {
    console.error('Error providing feedback:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record feedback',
      error: error.message
    });
  }
};

// Mark insight as viewed
const markInsightViewed = async (req, res) => {
  try {
    const { insightId } = req.params;
    const { readTime = 0 } = req.body;
    const userId = req.user.id;

    const insight = await AIInsight.findById(insightId);
    if (!insight) {
      return res.status(404).json({
        success: false,
        message: 'Insight not found'
      });
    }

    // Check if user owns this insight
    if (insight.userId.toString() !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to mark this insight as viewed'
      });
    }

    await insight.markAsViewed(readTime);

    res.json({
      success: true,
      message: 'Insight marked as viewed'
    });
  } catch (error) {
    console.error('Error marking insight as viewed:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark insight as viewed',
      error: error.message
    });
  }
};

// Dismiss insight
const dismissInsight = async (req, res) => {
  try {
    const { insightId } = req.params;
    const userId = req.user.id;

    const insight = await AIInsight.findById(insightId);
    if (!insight) {
      return res.status(404).json({
        success: false,
        message: 'Insight not found'
      });
    }

    // Check if user owns this insight
    if (insight.userId.toString() !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to dismiss this insight'
      });
    }

    await insight.dismiss();

    res.json({
      success: true,
      message: 'Insight dismissed successfully'
    });
  } catch (error) {
    console.error('Error dismissing insight:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to dismiss insight',
      error: error.message
    });
  }
};

// Generate insights from activity patterns
const generateInsightsFromActivity = async (userId, activity) => {
  try {
    // Check for similar recent activities to avoid duplicates
    const similarActivities = await UserActivity.findSimilarActivities(
      userId,
      activity.activityType,
      activity.resourceType,
      24 // 24 hours
    );

    // Generate insights based on activity type and patterns
    const insights = [];

    switch (activity.activityType) {
      case 'leave_request':
        insights.push(...await generateLeaveInsights(userId, activity, similarActivities));
        break;
      case 'task_creation':
        insights.push(...await generateTaskInsights(userId, activity, similarActivities));
        break;
      case 'application_review':
        insights.push(...await generateRecruitmentInsights(userId, activity, similarActivities));
        break;
      case 'job_posting':
        insights.push(...await generateJobPostingInsights(userId, activity, similarActivities));
        break;
    }

    // Save insights to database
    for (const insightData of insights) {
      const existingSimilar = await AIInsight.findSimilar(
        userId,
        insightData.actionType,
        24 // 24 hours
      );

      // Only create if no similar insight exists
      if (existingSimilar.length === 0) {
        const insight = new AIInsight({
          userId,
          ...insightData
        });
        await insight.save();
      }
    }
  } catch (error) {
    console.error('Error generating insights from activity:', error);
  }
};

// Generate leave-related insights
const generateLeaveInsights = async (userId, activity, similarActivities) => {
  const insights = [];

  // If user has submitted multiple leave requests recently
  if (similarActivities.length >= 2) {
    insights.push({
      type: 'pattern_alert',
      category: 'leave_management',
      title: 'Frequent Leave Requests Detected',
      description: 'You have submitted multiple leave requests recently. Consider planning longer breaks or discussing workload with your manager.',
      actionType: 'investigate_leave_patterns',
      priority: 'medium',
      confidence: 0.8,
      metadata: {
        recentRequestsCount: similarActivities.length,
        timeframe: '24 hours'
      }
    });
  }

  return insights;
};

// Generate task-related insights
const generateTaskInsights = async (userId, activity, similarActivities) => {
  const insights = [];

  // If user is creating many tasks
  if (similarActivities.length >= 5) {
    insights.push({
      type: 'proactive_suggestion',
      category: 'task_management',
      title: 'High Task Creation Activity',
      description: 'You are creating many tasks. Consider using task templates or bulk creation features to improve efficiency.',
      actionType: 'optimize_workflow',
      priority: 'low',
      confidence: 0.7,
      metadata: {
        tasksCreated: similarActivities.length,
        timeframe: '24 hours'
      }
    });
  }

  return insights;
};

// Generate recruitment insights
const generateRecruitmentInsights = async (userId, activity, similarActivities) => {
  const insights = [];

  // If reviewing many applications
  if (similarActivities.length >= 10) {
    insights.push({
      type: 'proactive_suggestion',
      category: 'recruitment',
      title: 'High Application Review Volume',
      description: 'You are reviewing many applications. Consider using batch processing or automated screening to improve efficiency.',
      actionType: 'review_applications',
      priority: 'medium',
      confidence: 0.8,
      metadata: {
        applicationsReviewed: similarActivities.length,
        timeframe: '24 hours'
      }
    });
  }

  return insights;
};

// Generate job posting insights
const generateJobPostingInsights = async (userId, activity, similarActivities) => {
  const insights = [];

  // If posting multiple jobs
  if (similarActivities.length >= 3) {
    insights.push({
      type: 'proactive_suggestion',
      category: 'recruitment',
      title: 'Multiple Job Postings',
      description: 'You are posting multiple jobs. Ensure job descriptions are optimized and consider using templates for consistency.',
      actionType: 'improve_job_posting',
      priority: 'low',
      confidence: 0.6,
      metadata: {
        jobsPosted: similarActivities.length,
        timeframe: '24 hours'
      }
    });
  }

  return insights;
};

module.exports = {
  trackActivity,
  getUserInsights,
  getActivityStats,
  provideFeedback,
  markInsightViewed,
  dismissInsight
};
