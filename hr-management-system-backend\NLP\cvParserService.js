const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

const runCVParser = (filePath, job = null) => {
  return new Promise((resolve, reject) => {
    // Use the advanced NLP processor for better accuracy
    const scriptPath = path.join(__dirname, 'processors/advanced_nlp.py');

    // Check if the script exists
    if (!fs.existsSync(scriptPath)) {
      console.error(`NLP processor script not found at: ${scriptPath}`);
      return reject(`NLP processor script not found at: ${scriptPath}`);
    }

    // Prepare command with job details if available
    let command = `python "${scriptPath}" "${filePath}"`;

    if (job) {
      try {
        // Prepare job data for command line
        // Replace newlines with spaces to avoid command line issues
        let jobStr = JSON.stringify(job).replace(/\\n/g, ' ');

        // Use a temporary file to pass job data to avoid command line length limitations
        const tempJobFile = path.join(__dirname, 'temp_job_data.json');
        fs.writeFileSync(tempJobFile, jobStr);

        command = `python "${scriptPath}" "${filePath}" "${tempJobFile}" --job-file`;
        console.log('Using temp file for job data');
      } catch (err) {
        console.error('Error preparing job data:', err);
        // Fall back to direct command if temp file fails
        command = `python "${scriptPath}" "${filePath}"`;
      }
    }

    console.log('Executing command:', command);

    // Set a timeout to kill the process if it takes too long
    const timeoutMs = 60000; // 60 seconds
    const timeout = setTimeout(() => {
      console.error(`Parser execution timed out after ${timeoutMs/1000} seconds`);
      // Create a graceful error response instead of rejecting
      resolve({
        error: 'NLP processing timed out',
        errorDetails: `The NLP process took longer than ${timeoutMs/1000} seconds and was terminated.`,
        name: "Timeout Error",
        email: null,
        phone: null,
        skills: [],
        education: [],
        experience: [],
        matchScore: 0,
        matchedSkills: [],
        missingSkills: [],
        jobTitle: "Unknown Job"
      });
    }, timeoutMs);

    exec(command, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
      // Clear the timeout since the process completed
      clearTimeout(timeout);

      if (error) {
        console.error('Parser execution error:', error);
        console.error('Parser stderr:', stderr);

        // Instead of rejecting, return a graceful error response
        return resolve({
          error: 'NLP processing error',
          errorDetails: stderr || error.message,
          name: "Error",
          email: null,
          phone: null,
          skills: [],
          education: [],
          experience: [],
          matchScore: 0,
          matchedSkills: [],
          missingSkills: [],
          jobTitle: "Unknown Job"
        });
      }

      try {
        console.log('Parser output received, length:', stdout.length);

        // Clean the output in case there's debug info before the JSON
        let jsonStart = stdout.indexOf('{');
        let jsonOutput = stdout;

        if (jsonStart > 0) {
          console.log(`Found JSON start at position ${jsonStart}, trimming output`);
          jsonOutput = stdout.substring(jsonStart);
        }

        try {
          const data = JSON.parse(jsonOutput);

          // Ensure required fields exist to prevent frontend errors
          if (!data.jobTitle && data.matchScore !== undefined) {
            data.jobTitle = 'Unknown Job';
          }

          if (data.matchScore !== undefined && !data.matchedSkills) {
            data.matchedSkills = [];
          }

          if (data.matchScore !== undefined && !data.missingSkills) {
            data.missingSkills = [];
          }

          resolve(data);
        } catch (jsonErr) {
          console.error('Failed to parse JSON output:', jsonErr);
          console.error('Raw output:', stdout.substring(0, 500) + '...');

          // Try to extract JSON from the output
          try {
            const jsonRegex = /{[\s\S]*}/;
            const match = stdout.match(jsonRegex);

            if (match) {
              console.log('Found JSON using regex');
              const extractedJson = match[0];
              const data = JSON.parse(extractedJson);

              // Ensure required fields exist
              if (!data.jobTitle && data.matchScore !== undefined) {
                data.jobTitle = 'Unknown Job';
              }

              if (data.matchScore !== undefined && !data.matchedSkills) {
                data.matchedSkills = [];
              }

              if (data.matchScore !== undefined && !data.missingSkills) {
                data.missingSkills = [];
              }

              resolve(data);
            } else {
              // If no JSON found, create a basic error response
              resolve({
                error: 'Failed to parse output',
                errorDetails: jsonErr.message,
                name: "Error",
                email: null,
                phone: null,
                skills: [],
                education: [],
                experience: [],
                matchScore: 0,
                matchedSkills: [],
                missingSkills: [],
                jobTitle: "Unknown Job",
                extracted_text: stdout.substring(0, 1000) + '...'
              });
            }
          } catch (regexErr) {
            console.error('Regex extraction failed:', regexErr);
            resolve({
              error: 'Failed to parse output',
              errorDetails: jsonErr.message + ' | ' + regexErr.message,
              name: "Error",
              email: null,
              phone: null,
              skills: [],
              education: [],
              experience: [],
              matchScore: 0,
              matchedSkills: [],
              missingSkills: [],
              jobTitle: "Unknown Job",
              extracted_text: stdout.substring(0, 1000) + '...'
            });
          }
        }
      } catch (err) {
        console.error('General error processing parser output:', err);
        resolve({
          error: 'Error processing parser output',
          errorDetails: err.message,
          name: "Error",
          email: null,
          phone: null,
          skills: [],
          education: [],
          experience: [],
          matchScore: 0,
          matchedSkills: [],
          missingSkills: [],
          jobTitle: "Unknown Job",
          extracted_text: stdout.substring(0, 1000) + '...'
        });
      }
    });
  });
};

module.exports = runCVParser;
