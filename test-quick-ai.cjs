const axios = require('axios');

async function testQuickAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test simple message
    console.log('\n🤖 Testing enhanced AI...');
    
    try {
      const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'Hey! How are you doing today? 😊'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const data = response.data.data;
      const assistantMessage = data.assistantMessage;
      const classification = data.classification;

      console.log(`🤖 Alex: ${assistantMessage.content}`);
      console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
      
      if (classification.metadata?.emotionalState) {
        console.log(`💭 Emotion: ${classification.metadata.emotionalState}`);
      }

      if (data.suggestions && data.suggestions.length > 0) {
        console.log(`💡 Suggestions: ${data.suggestions.slice(0, 3).join(', ')}`);
      }

      console.log(`⚡ Response time: ${assistantMessage.metadata.responseTime}ms`);
      
    } catch (error) {
      console.error('❌ Chat error:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testQuickAI();
