require('dotenv').config();
const openaiService = require('./ai-assistant/services/openaiService');

// Direct test of OpenAI service with DeepSeek
async function testOpenAIServiceDirect() {
  try {
    console.log('🧪 Testing OpenAI Service with DeepSeek Directly...\n');
    
    // Check service status
    console.log('1️⃣ Checking service status...');
    const status = openaiService.getStatus();
    console.log('Service Status:', status);
    console.log('Available:', openaiService.isAvailable());
    console.log('');
    
    if (!openaiService.isAvailable()) {
      console.log('❌ OpenAI service is not available');
      return;
    }
    
    // Test 1: Simple question
    console.log('2️⃣ Testing simple question...');
    const simpleResponse = await openaiService.generateResponse(
      'What is the capital of France?',
      { userRole: 'user', userName: 'Test User' },
      'general',
      []
    );
    
    console.log('🗼 Simple Response:', simpleResponse.content);
    console.log('📊 Metadata:', simpleResponse.metadata);
    console.log('');
    
    // Test 2: HR-related question
    console.log('3️⃣ Testing HR-related question...');
    const hrResponse = await openaiService.generateResponse(
      'How should I plan my vacation leave to maximize work-life balance?',
      { 
        userRole: 'user', 
        userName: 'Test User',
        department: 'IT',
        conversationHistory: 'User: Hello\nAssistant: Hi there!'
      },
      'leave_request',
      [{ type: 'leave_type', value: 'vacation' }]
    );
    
    console.log('🏖️ HR Response:', hrResponse.content);
    console.log('📊 Metadata:', hrResponse.metadata);
    console.log('');
    
    // Test 3: Complex reasoning
    console.log('4️⃣ Testing complex reasoning...');
    const complexResponse = await openaiService.generateResponse(
      'Explain the mathematical concept of machine learning and how it applies to HR analytics.',
      { 
        userRole: 'admin', 
        userName: 'Admin User',
        department: 'HR'
      },
      'analytics_help',
      []
    );
    
    console.log('🧠 Complex Response:', complexResponse.content);
    console.log('📊 Metadata:', complexResponse.metadata);
    console.log('');
    
    console.log('🎉 Direct OpenAI service test completed!');
    console.log('✅ DeepSeek Prover v2 is working through OpenAI service!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testOpenAIServiceDirect();
