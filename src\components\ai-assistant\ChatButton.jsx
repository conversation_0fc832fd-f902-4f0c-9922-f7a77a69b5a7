import React, { useState } from 'react';
import {
  Fab,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Zoom
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { motion } from 'framer-motion';
import ChatInterface from './ChatInterface';
import ContextAwarePopup from './ContextAwarePopup';

// Styled components
const FloatingButton = styled(motion.div)(({ theme }) => ({
  position: 'fixed',
  bottom: theme.spacing(3),
  right: theme.spacing(3),
  zIndex: theme.zIndex.speedDial,
  [theme.breakpoints.down('md')]: {
    bottom: theme.spacing(2),
    right: theme.spacing(2)
  }
}));

const StyledFab = styled(Fab, {
  shouldForwardProp: (prop) => prop !== 'isOpen'
})(({ theme, isOpen }) => ({
  background: isOpen
    ? `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  boxShadow: theme.shadows[8],
  '&:hover': {
    background: isOpen
      ? `linear-gradient(135deg, ${theme.palette.secondary.dark} 0%, ${theme.palette.secondary.main} 100%)`
      : `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
    transform: 'scale(1.1)',
    boxShadow: theme.shadows[12]
  },
  transition: 'all 0.3s ease-in-out',
  width: 64,
  height: 64,
  [theme.breakpoints.down('md')]: {
    width: 56,
    height: 56
  }
}));

const NotificationDot = styled('div')(({ theme }) => ({
  position: 'absolute',
  top: 8,
  right: 8,
  width: 12,
  height: 12,
  borderRadius: '50%',
  backgroundColor: theme.palette.error.main,
  border: `2px solid white`,
  animation: 'pulse 2s infinite'
}));

const ChatButton = ({
  initialMessage = null,
  showNotification = false,
  onChatOpen = () => {},
  onChatClose = () => {}
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(showNotification);

  const handleToggleChat = () => {
    if (isOpen) {
      setIsOpen(false);
      onChatClose();
    } else {
      setIsOpen(true);
      setHasNewMessage(false);
      onChatOpen();
    }
  };

  const handleChatClose = () => {
    setIsOpen(false);
    onChatClose();
  };

  return (
    <>
      <FloatingButton
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          delay: 0.5
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Tooltip
          title={isOpen ? "Close Jarvis Intelligence Agent" : "Open Jarvis Intelligence Agent"}
          placement="left"
          arrow
        >
          <StyledFab
            isOpen={isOpen}
            onClick={handleToggleChat}
            aria-label="AI Assistant"
          >
            <Zoom in={!isOpen} timeout={200}>
              <BotIcon sx={{ fontSize: isMobile ? 28 : 32 }} />
            </Zoom>
            <Zoom in={isOpen} timeout={200}>
              <CloseIcon
                sx={{
                  fontSize: isMobile ? 28 : 32,
                  position: 'absolute'
                }}
              />
            </Zoom>

            {hasNewMessage && !isOpen && (
              <NotificationDot />
            )}
          </StyledFab>
        </Tooltip>
      </FloatingButton>

      <ChatInterface
        open={isOpen}
        onClose={handleChatClose}
        initialMessage={initialMessage}
      />

      {/* Context-Aware Popup */}
      <ContextAwarePopup
        onOpenChat={() => {
          if (!isOpen) {
            handleToggleChat();
          }
        }}
        onDismiss={() => {
          console.log('Context popup dismissed');
        }}
      />

      <style jsx>{`
        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.2);
            opacity: 0.7;
          }
          100% {
            transform: scale(1);
            opacity: 1;
          }
        }
      `}</style>
    </>
  );
};

export default ChatButton;
