import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Rating,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Divider,
  Grid,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  Star as StarIcon,
  Visibility as VisibilityIcon,
  Check as CheckIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import api from '../Services/ApiService';
import ContextualSuggestions from './ai-assistant/ContextualSuggestions';
import { useContextualSuggestions } from '../hooks/useContextualSuggestions';

const UserEvaluations = () => {
  const [loading, setLoading] = useState(true);
  const [evaluations, setEvaluations] = useState([]);
  const [filteredEvaluations, setFilteredEvaluations] = useState([]);
  const [selectedEvaluation, setSelectedEvaluation] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [acknowledging, setAcknowledging] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searching, setSearching] = useState(false);

  // Contextual AI suggestions
  const { generateSuggestions, activeSuggestions, clearSuggestions } = useContextualSuggestions();

  // Fetch evaluations on component mount
  useEffect(() => {
    fetchEvaluations();
  }, []);

  // Component-specific search timeout reference
  const evalSearchTimeoutRef = useRef(null);

  // Cleanup function to clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (evalSearchTimeoutRef.current) {
        clearTimeout(evalSearchTimeoutRef.current);
      }
    };
  }, []);

  // Enhanced real-time search with debouncing for server-side search
  useEffect(() => {
    // Clear any existing timeout
    if (evalSearchTimeoutRef.current) {
      clearTimeout(evalSearchTimeoutRef.current);
    }

    if (searchQuery.trim() === '') {
      setFilteredEvaluations(evaluations);
      return;
    }

    // For immediate client-side filtering
    const query = searchQuery.toLowerCase();
    const filtered = evaluations.filter(evaluation =>
      (evaluation.evaluationPeriod && evaluation.evaluationPeriod.toLowerCase().includes(query)) ||
      (evaluation.strengths && evaluation.strengths.toLowerCase().includes(query)) ||
      (evaluation.areasForImprovement && evaluation.areasForImprovement.toLowerCase().includes(query)) ||
      (evaluation.goals && evaluation.goals.toLowerCase().includes(query)) ||
      (evaluation.comments && evaluation.comments.toLowerCase().includes(query)) ||
      (evaluation.status && evaluation.status.toLowerCase().includes(query))
    );
    setFilteredEvaluations(filtered);

    // Debounced server-side search for more accurate results
    evalSearchTimeoutRef.current = setTimeout(() => {
      if (searchQuery.trim() !== '') {
        handleServerSearch();
      }
    }, 300); // 300ms delay for more responsive feel
  }, [searchQuery, evaluations]);

  const fetchEvaluations = async () => {
    try {
      setLoading(true);
      const response = await api.get('/user/evaluations');
      setEvaluations(response.data);
      setFilteredEvaluations(response.data);
    } catch (error) {
      console.error('Error fetching evaluations:', error);
      toast.error('Failed to load evaluations');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    fetchEvaluations();
  };

  const handleServerSearch = async () => {
    if (searchQuery.trim() === '') {
      fetchEvaluations();
      return;
    }

    try {
      setSearching(true);
      setLoading(true);

      const response = await api.get('/user/evaluations', {
        params: { search: searchQuery }
      });

      setEvaluations(response.data);
      setFilteredEvaluations(response.data);
    } catch (error) {
      console.error('Error searching evaluations:', error);
      toast.error('Failed to search evaluations');
    } finally {
      setSearching(false);
      setLoading(false);
    }
  };

  const handleViewEvaluation = async (evaluationId) => {
    try {
      setLoading(true);
      const response = await api.get(`/user/evaluations/${evaluationId}`);
      setSelectedEvaluation(response.data);
      setOpenDialog(true);
    } catch (error) {
      console.error('Error fetching evaluation details:', error);
      toast.error('Failed to load evaluation details');
    } finally {
      setLoading(false);
    }
  };

  const handleAcknowledge = async () => {
    if (!selectedEvaluation) return;

    try {
      setAcknowledging(true);
      await api.post(`/user/evaluations/${selectedEvaluation._id}/acknowledge`);
      toast.success('Evaluation acknowledged successfully');

      // Update the evaluation in the list
      setEvaluations(evaluations.map(evaluation =>
        evaluation._id === selectedEvaluation._id
          ? { ...evaluation, status: 'Acknowledged' }
          : evaluation
      ));

      // Update the selected evaluation
      setSelectedEvaluation({ ...selectedEvaluation, status: 'Acknowledged' });

      // Generate AI suggestions for evaluation acknowledgment
      generateSuggestions('evaluation', 'acknowledge', {
        overallRating: selectedEvaluation.overallRating,
        performanceRating: selectedEvaluation.performanceRating,
        evaluationPeriod: selectedEvaluation.evaluationPeriod,
        strengths: selectedEvaluation.strengths?.substring(0, 100),
        areasForImprovement: selectedEvaluation.areasForImprovement?.substring(0, 100)
      });
    } catch (error) {
      console.error('Error acknowledging evaluation:', error);
      toast.error('Failed to acknowledge evaluation');
    } finally {
      setAcknowledging(false);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedEvaluation(null);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Draft': return 'default';
      case 'Completed': return 'primary';
      case 'Acknowledged': return 'success';
      default: return 'default';
    }
  };



  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" gutterBottom={false} fontWeight={600}>
          My Performance Evaluations
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            placeholder="Search evaluations..."
            size="small"
            value={searchQuery}
            onChange={handleSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
              endAdornment: searchQuery ? (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={handleClearSearch}
                    aria-label="clear search"
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null
            }}
            sx={{ width: 280 }}
          />
          <Tooltip title="Refresh Evaluations">
            <IconButton
              onClick={fetchEvaluations}
              disabled={loading}
              color="primary"
              size="small"
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {loading && filteredEvaluations.length === 0 ? (
        <Box sx={{ width: '100%', mt: 4 }}>
          <LinearProgress />
          <Typography align="center" sx={{ mt: 2 }}>Loading evaluations...</Typography>
        </Box>
      ) : filteredEvaluations.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography>
            {searchQuery ? 'No evaluations match your search criteria.' : 'No evaluations found.'}
          </Typography>
        </Paper>
      ) : (
        <Box sx={{ mt: 3 }}>
          <Grid container spacing={3}>
            {filteredEvaluations.map((evaluation) => (
              <Grid item xs={12} sm={6} md={4} key={evaluation._id}>
                <Paper
                  elevation={2}
                  sx={{
                    borderRadius: 2,
                    overflow: 'hidden',
                    height: '100%',
                    transition: 'all 0.2s',
                    '&:hover': {
                      boxShadow: 6,
                      transform: 'translateY(-3px)'
                    }
                  }}
                >
                  {/* Card Header */}
                  <Box
                    sx={{
                      bgcolor: 'primary.main',
                      color: 'white',
                      p: 2,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}
                  >
                    <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                      {evaluation.evaluationPeriod}
                    </Typography>
                    <Chip
                      label={evaluation.status}
                      color={getStatusColor(evaluation.status)}
                      size="small"
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: evaluation.status === 'Acknowledged' ? 'success.main' : 'primary.light',
                        color: 'white'
                      }}
                    />
                  </Box>

                  {/* Card Content */}
                  <Box sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(evaluation.evaluationDate)}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="body2" sx={{ mr: 1, fontWeight: 'medium' }}>
                          Overall:
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                          {evaluation.overallRating}
                        </Typography>
                      </Box>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    {/* Ratings Section */}
                    <Typography variant="subtitle2" sx={{ mb: 1.5, color: 'text.secondary', fontWeight: 'bold' }}>
                      PERFORMANCE RATINGS
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                      <Grid container spacing={1}>
                        <Grid item xs={6}>
                          <Box sx={{ mb: 1 }}>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Performance
                            </Typography>
                            <Rating value={evaluation.performanceRating} readOnly size="small" />
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Attitude
                            </Typography>
                            <Rating value={evaluation.attitudeRating} readOnly size="small" />
                          </Box>
                        </Grid>
                        <Grid item xs={6}>
                          <Box sx={{ mb: 1 }}>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Communication
                            </Typography>
                            <Rating value={evaluation.communicationRating} readOnly size="small" />
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              Teamwork
                            </Typography>
                            <Rating value={evaluation.teamworkRating} readOnly size="small" />
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Box>

                  {/* Card Actions */}
                  <Box sx={{ p: 2, pt: 0, display: 'flex', justifyContent: 'flex-end' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      size="medium"
                      startIcon={<VisibilityIcon />}
                      onClick={() => handleViewEvaluation(evaluation._id)}
                      sx={{ borderRadius: 1.5 }}
                    >
                      View Details
                    </Button>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}

      {/* Evaluation Details Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth={false}
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxHeight: '90vh',
            overflow: 'hidden',
            width: '99%',
            maxWidth: '100vw',
            margin: 'auto'
          }
        }}
      >
        {selectedEvaluation && (
          <>
            <DialogTitle
              sx={{
                bgcolor: 'primary.main',
                color: 'white',
                py: 1.5,
                px: 2,
                borderBottom: '1px solid rgba(255,255,255,0.1)'
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  {selectedEvaluation.evaluationPeriod} Performance Evaluation
                </Typography>
                <Chip
                  label={selectedEvaluation.status}
                  color={getStatusColor(selectedEvaluation.status)}
                  size="small"
                  sx={{
                    fontWeight: 'bold',
                    bgcolor: selectedEvaluation.status === 'Acknowledged' ? 'success.main' : 'primary.light',
                    color: 'white'
                  }}
                />
              </Box>
            </DialogTitle>

            <DialogContent sx={{ p: 3 }}>
              {/* Simple Header with Date and Evaluator */}
              <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Evaluation Date: <strong>{formatDate(selectedEvaluation.evaluationDate)}</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Evaluator: <strong>{selectedEvaluation.evaluatorId?.name || 'HR Department'}</strong>
                  </Typography>
                </Box>

                {/* Overall Rating */}
                <Box sx={{ textAlign: 'center', bgcolor: 'primary.main', color: 'white', p: 2, borderRadius: 2 }}>
                  <Typography variant="body2" sx={{ mb: 0.5 }}>
                    OVERALL RATING
                  </Typography>
                  <Typography variant="h3" sx={{ fontWeight: 'bold' }}>
                    {selectedEvaluation.overallRating}
                  </Typography>
                </Box>
              </Box>

              {/* Ratings Section */}
              <Paper elevation={1} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
                <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 'bold' }}>
                  Performance Ratings
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <table style={{ width: '100%', borderCollapse: 'separate', borderSpacing: '0 10px' }}>
                    <tbody>
                      <tr>
                        <td width="20%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px 0 0 4px' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>Performance</Typography>
                        </td>
                        <td width="10%" align="center" style={{ padding: '10px', backgroundColor: '#f5f5f5' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {selectedEvaluation.performanceRating}
                          </Typography>
                        </td>
                        <td width="70%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '0 4px 4px 0' }}>
                          <Rating
                            value={selectedEvaluation.performanceRating}
                            readOnly
                            size="small"
                            sx={{ '& .MuiRating-icon': { fontSize: '1rem' } }}
                          />
                        </td>
                      </tr>

                      <tr>
                        <td width="20%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px 0 0 4px' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>Attitude</Typography>
                        </td>
                        <td width="10%" align="center" style={{ padding: '10px', backgroundColor: '#f5f5f5' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {selectedEvaluation.attitudeRating}
                          </Typography>
                        </td>
                        <td width="70%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '0 4px 4px 0' }}>
                          <Rating
                            value={selectedEvaluation.attitudeRating}
                            readOnly
                            size="small"
                            sx={{ '& .MuiRating-icon': { fontSize: '1rem' } }}
                          />
                        </td>
                      </tr>

                      <tr>
                        <td width="20%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px 0 0 4px' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>Communication</Typography>
                        </td>
                        <td width="10%" align="center" style={{ padding: '10px', backgroundColor: '#f5f5f5' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {selectedEvaluation.communicationRating}
                          </Typography>
                        </td>
                        <td width="70%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '0 4px 4px 0' }}>
                          <Rating
                            value={selectedEvaluation.communicationRating}
                            readOnly
                            size="small"
                            sx={{ '& .MuiRating-icon': { fontSize: '1rem' } }}
                          />
                        </td>
                      </tr>

                      <tr>
                        <td width="20%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px 0 0 4px' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>Teamwork</Typography>
                        </td>
                        <td width="10%" align="center" style={{ padding: '10px', backgroundColor: '#f5f5f5' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {selectedEvaluation.teamworkRating}
                          </Typography>
                        </td>
                        <td width="70%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '0 4px 4px 0' }}>
                          <Rating
                            value={selectedEvaluation.teamworkRating}
                            readOnly
                            size="small"
                            sx={{ '& .MuiRating-icon': { fontSize: '1rem' } }}
                          />
                        </td>
                      </tr>

                      <tr>
                        <td width="20%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px 0 0 4px' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>Initiative</Typography>
                        </td>
                        <td width="10%" align="center" style={{ padding: '10px', backgroundColor: '#f5f5f5' }}>
                          <Typography variant="body1" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {selectedEvaluation.initiativeRating}
                          </Typography>
                        </td>
                        <td width="70%" style={{ padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '0 4px 4px 0' }}>
                          <Rating
                            value={selectedEvaluation.initiativeRating}
                            readOnly
                            size="small"
                            sx={{ '& .MuiRating-icon': { fontSize: '1rem' } }}
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </Box>
              </Paper>

              {/* Feedback Section */}
              <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
                <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 'bold' }}>
                  Feedback & Comments
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        mb: 2,
                        borderRadius: 1,
                        borderColor: 'success.main',
                        borderLeft: '4px solid',
                        borderLeftColor: 'success.main'
                      }}
                    >
                      <Typography variant="subtitle2" sx={{ color: 'success.main', mb: 1 }}>
                        Strengths
                      </Typography>
                      <Typography>
                        {selectedEvaluation.strengths || 'No strengths specified.'}
                      </Typography>
                    </Paper>

                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        borderColor: 'error.main',
                        borderLeft: '4px solid',
                        borderLeftColor: 'error.main'
                      }}
                    >
                      <Typography variant="subtitle2" sx={{ color: 'error.main', mb: 1 }}>
                        Areas for Improvement
                      </Typography>
                      <Typography>
                        {selectedEvaluation.areasForImprovement || 'No areas for improvement specified.'}
                      </Typography>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        mb: 2,
                        borderRadius: 1,
                        borderColor: 'info.main',
                        borderLeft: '4px solid',
                        borderLeftColor: 'info.main'
                      }}
                    >
                      <Typography variant="subtitle2" sx={{ color: 'info.main', mb: 1 }}>
                        Goals for Next Period
                      </Typography>
                      <Typography>
                        {selectedEvaluation.goals || 'No goals specified.'}
                      </Typography>
                    </Paper>

                    <Paper
                      variant="outlined"
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        borderColor: 'secondary.main',
                        borderLeft: '4px solid',
                        borderLeftColor: 'secondary.main'
                      }}
                    >
                      <Typography variant="subtitle2" sx={{ color: 'secondary.main', mb: 1 }}>
                        Additional Comments
                      </Typography>
                      <Typography>
                        {selectedEvaluation.comments || 'No additional comments.'}
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Paper>

              {/* AI Suggestions for Evaluation - Automatic Display */}
              {selectedEvaluation && (
                <Box sx={{ mt: 2 }}>
                  <ContextualSuggestions
                    context="evaluation"
                    actionType={selectedEvaluation.status === 'Acknowledged' ? 'view' : 'acknowledge'}
                    data={{
                      overallRating: selectedEvaluation.overallRating,
                      performanceRating: selectedEvaluation.performanceRating,
                      attitudeRating: selectedEvaluation.attitudeRating,
                      communicationRating: selectedEvaluation.communicationRating,
                      teamworkRating: selectedEvaluation.teamworkRating,
                      evaluationPeriod: selectedEvaluation.evaluationPeriod,
                      status: selectedEvaluation.status,
                      strengths: selectedEvaluation.strengths?.substring(0, 100),
                      areasForImprovement: selectedEvaluation.areasForImprovement?.substring(0, 100)
                    }}
                    position="inline"
                    onSuggestionApplied={(suggestion) => {
                      console.log('Applied evaluation suggestion:', suggestion);
                      // Handle suggestion application if needed
                    }}
                  />
                </Box>
              )}
            </DialogContent>

            <DialogActions
              sx={{
                px: 2,
                py: 1.5,
                borderTop: '1px solid',
                borderColor: 'divider',
                bgcolor: 'grey.50'
              }}
            >
              <Button
                onClick={handleCloseDialog}
                variant="outlined"
                size="small"
                sx={{ borderRadius: 1.5, px: 2 }}
              >
                Close
              </Button>
              {selectedEvaluation.status !== 'Acknowledged' && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<CheckIcon fontSize="small" />}
                  onClick={handleAcknowledge}
                  disabled={acknowledging}
                  size="small"
                  sx={{ borderRadius: 1.5, px: 2 }}
                >
                  {acknowledging ? 'Processing...' : 'Acknowledge Evaluation'}
                </Button>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default UserEvaluations;
