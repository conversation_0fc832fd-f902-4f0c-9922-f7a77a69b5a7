import sys
import json
import os
import re

# Import with error handling for better robustness
try:
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    nltk_available = True
except ImportError:
    print("NLTK not available, using fallback mode", file=sys.stderr)
    nltk_available = False

try:
    import spacy
    spacy_available = True
except ImportError:
    print("spaCy not available, using fallback mode", file=sys.stderr)
    spacy_available = False

try:
    import numpy as np
    numpy_available = True
except ImportError:
    print("NumPy not available, using fallback mode", file=sys.stderr)
    numpy_available = False

try:
    from sentence_transformers import SentenceTransformer, util
    transformers_available = True
except ImportError:
    print("sentence-transformers not available, using fallback mode", file=sys.stderr)
    transformers_available = False

# Add parent directory to path so we can import from utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.text_extraction import extract_text_from_pdf
from utils.text_processing import extract_basic_info, extract_skills, extract_education, extract_experience

# Initialize models as None first
nlp = None
sentence_model = None

# Load models only if dependencies are available
if spacy_available:
    try:
        # Try to load spaCy model
        nlp = spacy.load("en_core_web_sm")
        print("spaCy model loaded successfully", file=sys.stderr)
    except Exception as e:
        print(f"Error loading spaCy model: {str(e)}", file=sys.stderr)
        try:
            # Create blank model as fallback
            nlp = spacy.blank("en")
            print("Using fallback blank spaCy model", file=sys.stderr)
        except Exception as e2:
            print(f"Could not create fallback spaCy model: {str(e2)}", file=sys.stderr)

if transformers_available:
    try:
        # Try to load sentence transformer model
        sentence_model = SentenceTransformer('paraphrase-MiniLM-L6-v2')
        print("Sentence transformer model loaded successfully", file=sys.stderr)
    except Exception as e:
        print(f"Error loading sentence transformer model: {str(e)}", file=sys.stderr)
        try:
            # Try to load a smaller model as fallback
            sentence_model = SentenceTransformer('distilbert-base-nli-mean-tokens')
            print("Using fallback sentence-transformer model", file=sys.stderr)
        except Exception as e2:
            print(f"Could not create fallback sentence-transformer model: {str(e2)}", file=sys.stderr)

# Ensure NLTK resources are downloaded if NLTK is available
if nltk_available:
    try:
        # Check if required NLTK data is available
        try:
            nltk.data.find('tokenizers/punkt')
            print("NLTK punkt tokenizer found", file=sys.stderr)
        except LookupError:
            print("Downloading NLTK punkt tokenizer", file=sys.stderr)
            nltk.download('punkt')

        try:
            nltk.data.find('corpora/stopwords')
            print("NLTK stopwords found", file=sys.stderr)
        except LookupError:
            print("Downloading NLTK stopwords", file=sys.stderr)
            nltk.download('stopwords')
    except Exception as e:
        print(f"Error checking/downloading NLTK resources: {str(e)}", file=sys.stderr)

# Define skill categories for better matching
SKILL_CATEGORIES = {
    'programming_languages': [
        'python', 'java', 'javascript', 'c++', 'c#', 'ruby', 'php', 'swift', 'kotlin', 'go',
        'typescript', 'scala', 'perl', 'r', 'matlab', 'bash', 'shell', 'powershell', 'rust'
    ],
    'web_technologies': [
        'html', 'css', 'react', 'angular', 'vue', 'node.js', 'express', 'django', 'flask',
        'spring', 'asp.net', 'jquery', 'bootstrap', 'tailwind', 'sass', 'less', 'webpack'
    ],
    'databases': [
        'sql', 'nosql', 'mongodb', 'mysql', 'postgresql', 'oracle', 'sqlite', 'redis',
        'cassandra', 'dynamodb', 'firebase', 'elasticsearch', 'neo4j', 'graphql'
    ],
    'cloud_platforms': [
        'aws', 'azure', 'google cloud', 'gcp', 'heroku', 'digitalocean', 'kubernetes',
        'docker', 'terraform', 'jenkins', 'ci/cd', 'devops', 'serverless'
    ],
    'data_science': [
        'machine learning', 'deep learning', 'ai', 'data science', 'big data', 'data analysis',
        'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy', 'matplotlib',
        'tableau', 'power bi', 'statistics', 'nlp', 'computer vision'
    ],
    'soft_skills': [
        'communication', 'teamwork', 'leadership', 'problem solving', 'critical thinking',
        'time management', 'project management', 'agile', 'scrum', 'kanban', 'presentation'
    ]
}

def preprocess_text(text):
    """
    Preprocess text for NLP analysis

    Args:
        text (str): Text to preprocess

    Returns:
        str: Preprocessed text
    """
    if not text:
        return ""

    # Convert to lowercase
    text = text.lower()

    # Remove special characters and extra whitespace
    text = re.sub(r'[^\w\s]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()

    return text

def extract_entities(text):
    """
    Extract named entities from text using spaCy or fallback to regex

    Args:
        text (str): Text to extract entities from

    Returns:
        dict: Dictionary of entities by type
    """
    entities = {
        'PERSON': [],
        'ORG': [],
        'GPE': [],  # Geopolitical entity (countries, cities)
        'DATE': [],
        'SKILL': []  # Custom entity for skills
    }

    # Extract skills using custom patterns (works without spaCy)
    try:
        for category, skills in SKILL_CATEGORIES.items():
            for skill in skills:
                if re.search(r'\b' + re.escape(skill) + r'\b', text, re.IGNORECASE):
                    entities['SKILL'].append(skill)
    except Exception as e:
        print(f"Error extracting skills with regex: {str(e)}", file=sys.stderr)

    # Use spaCy for entity extraction if available
    if spacy_available and nlp is not None:
        try:
            # Limit text length to avoid memory issues
            max_length = 100000
            truncated_text = text[:max_length] if len(text) > max_length else text

            # Process text with spaCy
            doc = nlp(truncated_text)

            # Extract standard entities
            for ent in doc.ents:
                if ent.label_ in entities:
                    entities[ent.label_].append(ent.text)
        except Exception as e:
            print(f"Error extracting entities with spaCy: {str(e)}", file=sys.stderr)
    else:
        # Fallback to simple regex patterns for basic entity extraction
        try:
            # Simple regex for emails (potential contacts/persons)
            emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
            for email in emails:
                # Extract potential name from email
                name_part = email.split('@')[0].replace('.', ' ').replace('_', ' ').title()
                if len(name_part) > 3:  # Avoid very short names
                    entities['PERSON'].append(name_part)

            # Simple regex for dates
            dates = re.findall(r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b', text)
            entities['DATE'].extend(dates)

            # Simple regex for organizations (capitalized multi-word phrases)
            orgs = re.findall(r'\b(?:[A-Z][a-z]+ ){1,5}(?:Inc|LLC|Ltd|Corporation|Corp|Company|Co)\b', text)
            entities['ORG'].extend(orgs)

            print("Used regex fallback for entity extraction", file=sys.stderr)
        except Exception as e:
            print(f"Error with fallback regex entity extraction: {str(e)}", file=sys.stderr)

    # Remove duplicates
    for entity_type in entities:
        entities[entity_type] = list(set(entities[entity_type]))

    return entities

def calculate_semantic_similarity(text1, text2):
    """
    Calculate semantic similarity between two texts using sentence transformers
    or fallback to simple word overlap

    Args:
        text1 (str): First text
        text2 (str): Second text

    Returns:
        float: Similarity score between 0 and 1
    """
    # Check for empty inputs
    if not text1 or not text2:
        return 0.0

    # Try using sentence transformers if available
    if transformers_available and sentence_model is not None:
        try:
            # Limit text length to avoid memory issues
            max_length = 5000
            text1_truncated = text1[:max_length] if len(text1) > max_length else text1
            text2_truncated = text2[:max_length] if len(text2) > max_length else text2

            # Encode texts
            embedding1 = sentence_model.encode(text1_truncated, convert_to_tensor=True)
            embedding2 = sentence_model.encode(text2_truncated, convert_to_tensor=True)

            # Calculate cosine similarity
            similarity = util.pytorch_cos_sim(embedding1, embedding2).item()

            return max(0.0, min(1.0, similarity))  # Ensure value is between 0 and 1
        except Exception as e:
            print(f"Error calculating semantic similarity with transformers: {str(e)}", file=sys.stderr)
            # Fall back to simple word overlap

    # Fallback: Calculate similarity based on word overlap
    try:
        print("Using fallback word overlap for semantic similarity", file=sys.stderr)

        # Tokenize texts into words
        if nltk_available:
            try:
                words1 = set(word_tokenize(text1.lower()))
                words2 = set(word_tokenize(text2.lower()))
            except Exception:
                # Even simpler fallback if tokenization fails
                words1 = set(text1.lower().split())
                words2 = set(text2.lower().split())
        else:
            # Simple word splitting if NLTK is not available
            words1 = set(text1.lower().split())
            words2 = set(text2.lower().split())

        # Remove very common words if stopwords are available
        if nltk_available:
            try:
                stop_words = set(stopwords.words('english'))
                words1 = words1.difference(stop_words)
                words2 = words2.difference(stop_words)
            except Exception:
                pass

        # Calculate Jaccard similarity (intersection over union)
        if not words1 or not words2:
            return 0.0

        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))

        similarity = intersection / union if union > 0 else 0.0

        return similarity
    except Exception as e:
        print(f"Error calculating fallback similarity: {str(e)}", file=sys.stderr)
        return 0.0

def calculate_job_match(cv_data, job_data):
    """
    Calculate how well a CV matches a job using advanced NLP techniques

    Args:
        cv_data (dict): CV data with skills, education, experience
        job_data (dict): Job data with requirements, responsibilities

    Returns:
        dict: Match results with score, matched skills, missing skills
    """
    # Check if job_data is valid
    if not job_data or not isinstance(job_data, dict):
        print(f"Invalid job data for matching: {type(job_data)}", file=sys.stderr)
        return {
            "score": 0,
            "matched_skills": [],
            "missing_skills": [],
            "job_requirements": [],
            "analysis": "No valid job data provided for comparison."
        }

    # Extract job requirements and responsibilities
    requirements = []
    if 'requirements' in job_data:
        if isinstance(job_data['requirements'], list):
            requirements.extend(job_data['requirements'])
        elif isinstance(job_data['requirements'], str):
            requirements.append(job_data['requirements'])

    responsibilities = []
    if 'responsibilities' in job_data:
        if isinstance(job_data['responsibilities'], list):
            responsibilities.extend(job_data['responsibilities'])
        elif isinstance(job_data['responsibilities'], str):
            responsibilities.append(job_data['responsibilities'])

    # Get job title
    job_title = job_data.get('title', 'Unknown Job')

    # Combine all job text for semantic matching
    job_text = f"{job_title}. "
    job_text += " ".join(requirements) + " " + " ".join(responsibilities)
    job_text = preprocess_text(job_text)

    # Extract key skills from job description using NLP
    job_entities = extract_entities(job_text)
    job_skills = job_entities['SKILL']

    # Add explicitly mentioned skills from requirements
    for req in requirements:
        # Look for skill keywords in requirements
        for category, skills in SKILL_CATEGORIES.items():
            for skill in skills:
                if re.search(r'\b' + re.escape(skill) + r'\b', req, re.IGNORECASE):
                    job_skills.append(skill)

    # Remove duplicates and normalize
    job_skills = [skill.lower() for skill in job_skills]
    job_skills = list(set(job_skills))

    # Get candidate skills and other relevant information
    candidate_skills = [skill.lower() for skill in cv_data.get('skills', [])]
    candidate_education = cv_data.get('education', [])
    candidate_experience = cv_data.get('experience', [])

    # Combine all candidate text for semantic matching
    candidate_text = " ".join(candidate_skills) + " " + " ".join(candidate_education) + " " + " ".join(candidate_experience)
    candidate_text = preprocess_text(candidate_text)

    # Calculate direct skill matches
    matched_skills = []
    missing_skills = []

    for skill in job_skills:
        if any(skill in s.lower() for s in candidate_skills):
            matched_skills.append(skill)
        else:
            missing_skills.append(skill)

    # Calculate semantic similarity between job and candidate
    semantic_score = calculate_semantic_similarity(job_text, candidate_text)
    print(f"Semantic similarity score: {semantic_score}", file=sys.stderr)

    # Calculate skill match percentage
    if not job_skills:
        skill_match_percentage = 0
    else:
        skill_match_percentage = (len(matched_skills) / len(job_skills)) * 100

    # Calculate weighted score (60% skill match, 40% semantic similarity)
    weighted_score = (skill_match_percentage * 0.6) + (semantic_score * 100 * 0.4)
    match_percentage = min(100, int(weighted_score))

    # Generate analysis text
    if match_percentage >= 80:
        analysis = f"Excellent match for {job_title}! The candidate has most of the required skills and qualifications."
    elif match_percentage >= 60:
        analysis = f"Good match for {job_title}. The candidate has many of the required skills but is missing some key qualifications."
    elif match_percentage >= 40:
        analysis = f"Moderate match for {job_title}. The candidate has some relevant skills but lacks several important requirements."
    else:
        analysis = f"Poor match for {job_title}. The candidate's profile does not align well with the job requirements."

    # Add specific details about strengths and weaknesses
    if matched_skills:
        analysis += f" Strengths include: {', '.join(matched_skills[:5])}"
        if len(matched_skills) > 5:
            analysis += f" and {len(matched_skills) - 5} more."
        else:
            analysis += "."

    if missing_skills:
        analysis += f" Missing qualifications include: {', '.join(missing_skills[:5])}"
        if len(missing_skills) > 5:
            analysis += f" and {len(missing_skills) - 5} more."
        else:
            analysis += "."

    return {
        "score": match_percentage,
        "matched_skills": matched_skills,
        "missing_skills": missing_skills,
        "job_requirements": requirements,
        "analysis": analysis,
        "semantic_score": round(semantic_score, 2)
    }

# Parse CV
def parse_cv(text, job_data=None):
    """
    Parse CV text and extract information with advanced NLP

    Args:
        text (str): CV text
        job_data (dict, optional): Job data for matching

    Returns:
        dict: Parsed CV data with extracted information
    """
    # Check if text is an error message
    if text.startswith("Error") or text.startswith("File not") or text.startswith("No text"):
        return {
            "error": text,
            "name": "Unknown",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": [],
            "extracted_text": text
        }

    try:
        print(f"Parsing CV text of length: {len(text)}", file=sys.stderr)

        # Extract basic information
        basic_info = extract_basic_info(text)
        print(f"Extracted basic info: {basic_info}", file=sys.stderr)

        # Extract skills
        skills = extract_skills(text)
        print(f"Extracted {len(skills)} skills", file=sys.stderr)

        # Extract education
        education = extract_education(text)
        print(f"Extracted {len(education)} education entries", file=sys.stderr)

        # Extract experience
        experience = extract_experience(text)
        print(f"Extracted {len(experience)} experience entries", file=sys.stderr)

        # Use spaCy for entity extraction
        entities = extract_entities(text)
        print(f"Extracted entities: {len(entities['SKILL'])} skills, {len(entities['ORG'])} organizations", file=sys.stderr)

        # Add any skills found by spaCy that weren't in the basic extraction
        for skill in entities['SKILL']:
            if skill not in [s.lower() for s in skills]:
                skills.append(skill.capitalize())

        # Create result dictionary
        result = {
            "name": basic_info["name"],
            "email": basic_info["email"],
            "phone": basic_info["phone"],
            "skills": skills,
            "education": education,
            "experience": experience,
            "organizations": entities['ORG'][:10],  # Include top organizations
            "extracted_text": text[:1000] + "..." if len(text) > 1000 else text  # Include a preview of the extracted text
        }

        # Calculate job match if job data is provided
        if job_data:
            try:
                # Ensure job_data is a dictionary
                if not isinstance(job_data, dict):
                    print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                    job_data = {"title": "Unknown Job"}

                # Ensure job_data has a title
                if "title" not in job_data:
                    job_data["title"] = "Unknown Job"

                print(f"Calculating job match with job: {job_data.get('title', 'Unknown')}", file=sys.stderr)
                match_result = calculate_job_match(result, job_data)

                # Safely add match results to the result dictionary
                result["matchScore"] = match_result["score"]
                result["matchedSkills"] = match_result["matched_skills"]
                result["missingSkills"] = match_result["missing_skills"]
                result["jobRequirements"] = match_result["job_requirements"]
                result["matchAnalysis"] = match_result["analysis"]
                result["jobTitle"] = job_data.get('title', 'Unknown Job')
                result["semanticScore"] = match_result.get("semantic_score", 0)

                print(f"Match score: {match_result['score']}%", file=sys.stderr)
                print(f"Matched {len(match_result['matched_skills'])} skills, missing {len(match_result['missing_skills'])} skills", file=sys.stderr)
            except Exception as e:
                print(f"Error calculating job match: {str(e)}", file=sys.stderr)
                import traceback
                traceback.print_exc(file=sys.stderr)

                # Add default values for match results
                result["matchScore"] = 0
                result["matchedSkills"] = []
                result["missingSkills"] = []
                result["jobRequirements"] = []
                result["jobTitle"] = "Unknown Job"
                result["matchError"] = str(e)
                result["matchAnalysis"] = "Error analyzing job match: " + str(e)

        # Generate a summary report
        summary = []
        summary.append(f"Candidate Name: {basic_info['name']}")

        if basic_info['email']:
            summary.append(f"Contact: {basic_info['email']}")
            if basic_info['phone']:
                summary.append(f", {basic_info['phone']}")

        summary.append("\n")

        if skills:
            summary.append("Skills Summary:")
            for skill in skills[:10]:  # Show top 10 skills
                summary.append(f"- {skill}")
            if len(skills) > 10:
                summary.append(f"- And {len(skills) - 10} more skills")
            summary.append("\n")

        if education:
            summary.append("Education Highlights:")
            for edu in education[:3]:  # Show top 3 education entries
                summary.append(f"- {edu}")
            if len(education) > 3:
                summary.append(f"- And {len(education) - 3} more education entries")
            summary.append("\n")

        if experience:
            summary.append("Experience Highlights:")
            for exp in experience[:3]:  # Show top 3 experience entries
                summary.append(f"- {exp}")
            if len(experience) > 3:
                summary.append(f"- And {len(experience) - 3} more experience entries")
            summary.append("\n")

        if "matchScore" in result:
            # Ensure jobTitle exists
            job_title = result.get("jobTitle", "Unknown Job")
            summary.append(f"Job Match: {result['matchScore']}% for {job_title}")
            if "semanticScore" in result:
                summary.append(f"Semantic Similarity: {result['semanticScore']}")
            summary.append(result.get("matchAnalysis", ""))
            summary.append("\n")

            if "matchedSkills" in result and result["matchedSkills"]:
                summary.append("Matching Qualifications:")
                for skill in result["matchedSkills"][:5]:
                    summary.append(f"- {skill}")
                if len(result["matchedSkills"]) > 5:
                    summary.append(f"- And {len(result['matchedSkills']) - 5} more")
                summary.append("\n")

            if "missingSkills" in result and result["missingSkills"]:
                summary.append("Missing Qualifications:")
                for skill in result["missingSkills"][:5]:
                    summary.append(f"- {skill}")
                if len(result["missingSkills"]) > 5:
                    summary.append(f"- And {len(result['missingSkills']) - 5} more")

        result["summary"] = "\n".join(summary)

        return result
    except Exception as e:
        print(f"Error parsing CV: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)

        return {
            "error": f"Error parsing CV: {str(e)}",
            "name": "Error",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": [],
            "extracted_text": text[:1000] + "..." if len(text) > 1000 else text
        }

# Main function to process command line arguments
if __name__ == "__main__":
    try:
        # Get file path from command line arguments
        file_path = sys.argv[1]

        # Check if job data is provided
        job_data = None
        if len(sys.argv) > 2:
            # Check if we're using a job data file
            is_job_file = False
            if len(sys.argv) > 3 and sys.argv[3] == '--job-file':
                is_job_file = True
                print(f"Using job data from file", file=sys.stderr)

            if is_job_file:
                # Read job data from file
                try:
                    job_file_path = sys.argv[2]
                    print(f"Reading job data from file: {job_file_path}", file=sys.stderr)

                    # Check if file exists
                    if not os.path.exists(job_file_path):
                        print(f"Job data file not found: {job_file_path}", file=sys.stderr)
                    else:
                        with open(job_file_path, 'r') as f:
                            job_data_str = f.read()

                        # Check if file is empty
                        if not job_data_str.strip():
                            print("Job data file is empty", file=sys.stderr)
                        else:
                            # Try to parse the JSON
                            job_data = json.loads(job_data_str)

                            # Verify job_data is a dictionary
                            if not isinstance(job_data, dict):
                                print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                                job_data = {"title": "Unknown Job"}
                            else:
                                print(f"Successfully parsed job data from file", file=sys.stderr)
                except Exception as e:
                    print(f"Error reading job data from file: {str(e)}", file=sys.stderr)
                    job_data = {"title": "Unknown Job"}
            else:
                # Parse job data from command line argument
                try:
                    # Clean the job data string to ensure it's valid JSON
                    job_data_str = sys.argv[2].strip('"\'')

                    # Fix common JSON issues
                    # Replace single quotes with double quotes
                    job_data_str = job_data_str.replace("'", '"')

                    # Fix newlines in JSON strings
                    job_data_str = job_data_str.replace('\n', '\\n')

                    # Try to parse the JSON
                    job_data = json.loads(job_data_str)

                    # Verify job_data is a dictionary
                    if not isinstance(job_data, dict):
                        print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                        job_data = {"title": "Unknown Job"}
                    else:
                        print(f"Successfully parsed job data", file=sys.stderr)
                except json.JSONDecodeError as e:
                    print(f"Error parsing job data: {str(e)}", file=sys.stderr)

                    # Try a different approach - use a more lenient parser
                    try:
                        import ast
                        # Convert string representation of dict to actual dict
                        job_data = ast.literal_eval(sys.argv[2])

                        # Verify job_data is a dictionary
                        if not isinstance(job_data, dict):
                            print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                            job_data = {"title": "Unknown Job"}
                        else:
                            print(f"Successfully parsed job data using ast.literal_eval", file=sys.stderr)
                    except Exception as e2:
                        print(f"Second parsing attempt failed: {str(e2)}", file=sys.stderr)
                        job_data = {"title": "Unknown Job"}

        # Extract text from PDF
        text = extract_text_from_pdf(file_path)

        # Parse the CV
        result = parse_cv(text, job_data)

        # Output the result as JSON
        print(json.dumps(result))

    except Exception as e:
        print(json.dumps({
            "error": f"Error in main function: {str(e)}",
            "name": "Error",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": []
        }))
