/**
 * Context-Aware Service for Jarvis
 * Provides intelligent suggestions based on user context and actions
 */

const User = require('../../models/user');
const Task = require('../../models/Task');
const LeaveRequest = require('../../models/LeaveRequest');
const contextualSuggestionEngine = require('./contextualSuggestionEngine');
const realTimeEngine = require('./realTimeEngine');

class ContextAwareService {
  constructor() {
    this.contextRules = new Map();
    this.userContextHistory = new Map();
    this.initializeContextRules();
  }

  /**
   * Initialize context-aware rules and suggestions
   */
  initializeContextRules() {
    // Leave Request Context Rules
    this.contextRules.set('leave_request_form', {
      triggers: ['form_focus', 'dialog_open'],
      suggestions: this.getLeaveRequestSuggestions.bind(this),
      priority: 5,
      cooldown: 300000 // 5 minutes
    });

    // User Management Context Rules
    this.contextRules.set('user_management_form', {
      triggers: ['form_focus', 'dialog_open'],
      suggestions: this.getUserManagementSuggestions.bind(this),
      priority: 4,
      cooldown: 300000
    });

    // Task Management Context Rules
    this.contextRules.set('task_management_form', {
      triggers: ['form_focus', 'dialog_open'],
      suggestions: this.getTaskManagementSuggestions.bind(this),
      priority: 4,
      cooldown: 300000
    });

    // Job Application Context Rules
    this.contextRules.set('job_application_form', {
      triggers: ['form_focus', 'page_visit'],
      suggestions: this.getJobApplicationSuggestions.bind(this),
      priority: 3,
      cooldown: 600000 // 10 minutes
    });

    // Dashboard Navigation Context Rules
    this.contextRules.set('dashboard_navigation', {
      triggers: ['page_visit'],
      suggestions: this.getDashboardSuggestions.bind(this),
      priority: 2,
      cooldown: 1800000 // 30 minutes
    });

    console.log('🧠 Jarvis Context-Aware Service initialized with', this.contextRules.size, 'rule sets');
  }

  /**
   * Process context and generate suggestions
   */
  async processContext(userId, contextData) {
    try {
      const { type, action, data, element } = contextData;
      
      // Check if user has context history
      if (!this.userContextHistory.has(userId)) {
        this.userContextHistory.set(userId, []);
      }

      const userHistory = this.userContextHistory.get(userId);
      
      // Check cooldown for this context type
      if (this.isInCooldown(userHistory, type)) {
        return null;
      }

      // Get context rule
      const rule = this.contextRules.get(type);
      if (!rule || !rule.triggers.includes(action)) {
        // Use contextual suggestion engine for unknown contexts
        const suggestions = await contextualSuggestionEngine.generateSuggestions(userId, type, action, data);
        if (suggestions && suggestions.length > 0) {
          return {
            contextType: type,
            suggestions,
            priority: 3,
            timestamp: Date.now(),
            metadata: {
              action,
              source: 'contextual_engine'
            }
          };
        }
        return null;
      }

      // Generate suggestions using both rule-based and AI-powered methods
      let suggestions = [];

      // Get rule-based suggestions
      const ruleSuggestions = await rule.suggestions(userId, data);
      suggestions.push(...ruleSuggestions);

      // Get AI-powered contextual suggestions for enhanced intelligence
      try {
        const aiSuggestions = await contextualSuggestionEngine.generateSuggestions(userId, type, action, data);

        // Merge and deduplicate suggestions
        const allSuggestions = [...suggestions, ...aiSuggestions];
        const uniqueSuggestions = [...new Set(allSuggestions)];
        suggestions = uniqueSuggestions.slice(0, 8); // Limit to 8 suggestions
      } catch (error) {
        console.error('Error getting AI suggestions:', error);
        // Continue with rule-based suggestions only
      }
      
      // Add to user history
      userHistory.push({
        type,
        action,
        timestamp: Date.now(),
        suggestions: suggestions.length
      });

      // Keep only last 20 context entries
      if (userHistory.length > 20) {
        userHistory.splice(0, userHistory.length - 20);
      }

      return {
        contextType: type,
        suggestions,
        priority: rule.priority,
        timestamp: Date.now(),
        metadata: {
          action,
          userHistory: userHistory.length,
          source: 'hybrid_engine',
          suggestionCount: suggestions.length
        }
      };

    } catch (error) {
      console.error('Error processing context:', error);
      return null;
    }
  }

  /**
   * Get leave request suggestions
   */
  async getLeaveRequestSuggestions(userId, data) {
    try {
      const user = await User.findById(userId).select('name role department');
      const currentYear = new Date().getFullYear();
      
      // Get user's leave history
      const leaveHistory = await LeaveRequest.find({
        userId,
        createdAt: { $gte: new Date(currentYear, 0, 1) }
      }).sort({ createdAt: -1 }).limit(5);

      const suggestions = [
        '📊 Check your current leave balance before submitting',
        '📅 Ensure your leave dates don\'t conflict with important deadlines',
        '✍️ Provide a clear and specific reason for your leave request',
        '⏰ Submit your request at least 2 weeks in advance for better approval chances'
      ];

      // Add personalized suggestions based on history
      if (leaveHistory.length === 0) {
        suggestions.unshift('🎯 This appears to be your first leave request this year');
      } else {
        const lastLeave = leaveHistory[0];
        const daysSinceLastLeave = Math.floor((Date.now() - lastLeave.createdAt) / (1000 * 60 * 60 * 24));
        
        if (daysSinceLastLeave < 30) {
          suggestions.push('⚠️ You recently submitted a leave request. Consider spacing out your requests');
        }
      }

      // Add role-specific suggestions
      if (user.role === 'HR') {
        suggestions.push('👥 As HR, ensure you have coverage for your responsibilities');
      }

      return suggestions;

    } catch (error) {
      console.error('Error getting leave request suggestions:', error);
      return [
        '📊 Check your leave balance',
        '📅 Plan your leave dates carefully',
        '✍️ Provide clear reasons for your request'
      ];
    }
  }

  /**
   * Get user management suggestions
   */
  async getUserManagementSuggestions(userId, data) {
    try {
      const user = await User.findById(userId).select('role');
      
      const suggestions = [
        '📧 Use official work email addresses for new users',
        '🔐 Assign appropriate roles based on job responsibilities',
        '✅ Verify all required fields are completed accurately',
        '🏢 Ensure department assignment matches organizational structure'
      ];

      // Add role-specific suggestions
      if (user.role === 'admin') {
        suggestions.push('⚡ As admin, you can assign any role including HR and admin privileges');
        suggestions.push('🔒 Consider security implications when assigning elevated permissions');
      } else if (user.role === 'HR') {
        suggestions.push('👥 You can create user accounts but cannot assign admin roles');
        suggestions.push('📋 Ensure new employees complete onboarding documentation');
      }

      // Add context-specific suggestions based on action
      if (data?.action === 'editing_user') {
        suggestions.push('⚠️ Changes to user roles take effect immediately');
        suggestions.push('📝 Document any significant changes for audit purposes');
      }

      return suggestions;

    } catch (error) {
      console.error('Error getting user management suggestions:', error);
      return [
        '📧 Use work email addresses',
        '🔐 Assign appropriate roles',
        '✅ Complete all required fields'
      ];
    }
  }

  /**
   * Get task management suggestions
   */
  async getTaskManagementSuggestions(userId, data) {
    try {
      const user = await User.findById(userId).select('name role');
      
      // Get user's recent tasks
      const recentTasks = await Task.find({
        $or: [
          { assignedTo: userId },
          { createdBy: userId }
        ]
      }).sort({ createdAt: -1 }).limit(10);

      const suggestions = [
        '🎯 Set realistic and achievable deadlines',
        '📝 Provide clear, detailed task descriptions',
        '⚡ Assign appropriate priority levels based on urgency',
        '👥 Consider team member workload when assigning tasks'
      ];

      // Add suggestions based on recent task patterns
      if (recentTasks.length > 0) {
        const overdueTasks = recentTasks.filter(task => 
          task.deadline < new Date() && task.status !== 'Completed'
        );
        
        if (overdueTasks.length > 0) {
          suggestions.push('⚠️ You have overdue tasks. Consider adjusting deadlines or priorities');
        }

        const highPriorityTasks = recentTasks.filter(task => 
          task.priority === 'High' && task.status !== 'Completed'
        );
        
        if (highPriorityTasks.length > 3) {
          suggestions.push('🔥 You have many high-priority tasks. Consider redistributing workload');
        }
      }

      // Add role-specific suggestions
      if (user.role === 'HR') {
        suggestions.push('📊 Use GEK system for optimal task assignment based on employee skills');
        suggestions.push('📈 Track task completion rates for performance evaluations');
      }

      return suggestions;

    } catch (error) {
      console.error('Error getting task management suggestions:', error);
      return [
        '🎯 Set realistic deadlines',
        '📝 Provide clear descriptions',
        '⚡ Assign appropriate priorities'
      ];
    }
  }

  /**
   * Get job application suggestions
   */
  async getJobApplicationSuggestions(userId, data) {
    const suggestions = [
      '📄 Upload your CV in PDF format for best compatibility',
      '✅ Fill out all required personal information accurately',
      '🎯 Review job requirements carefully before applying',
      '📞 Double-check your contact information',
      '💼 Tailor your application to the specific position',
      '🕒 Submit your application well before the deadline'
    ];

    // Add context-specific suggestions
    if (data?.hasCV === false) {
      suggestions.unshift('📎 Don\'t forget to upload your CV - it\'s required for most positions');
    }

    if (data?.formProgress && data.formProgress < 0.5) {
      suggestions.push('📋 Complete all sections of the application form');
    }

    return suggestions;
  }

  /**
   * Get dashboard suggestions
   */
  async getDashboardSuggestions(userId, data) {
    try {
      const user = await User.findById(userId).select('role lastLogin');
      
      const suggestions = [
        '📊 Check your dashboard analytics for insights',
        '🔔 Review any pending notifications',
        '📋 Update your task status if you\'ve made progress'
      ];

      // Add role-specific suggestions
      if (user.role === 'HR') {
        suggestions.push('👥 Review pending leave requests and applications');
        suggestions.push('📈 Check team performance metrics');
        suggestions.push('🎯 Use GEK system for employee evaluations');
      } else if (user.role === 'admin') {
        suggestions.push('🔧 Review system health and user activity');
        suggestions.push('📊 Generate reports for management insights');
        suggestions.push('👤 Monitor user account status and permissions');
      } else {
        suggestions.push('📅 Check your upcoming deadlines');
        suggestions.push('🏖️ Review your leave balance');
        suggestions.push('✅ Complete any pending tasks');
      }

      // Add time-based suggestions
      const hour = new Date().getHours();
      if (hour < 10) {
        suggestions.push('🌅 Good morning! Start your day by reviewing priorities');
      } else if (hour > 17) {
        suggestions.push('🌆 End of day - consider updating task progress');
      }

      return suggestions;

    } catch (error) {
      console.error('Error getting dashboard suggestions:', error);
      return [
        '📊 Check your dashboard',
        '🔔 Review notifications',
        '📋 Update task status'
      ];
    }
  }

  /**
   * Check if context type is in cooldown period
   */
  isInCooldown(userHistory, contextType) {
    const rule = this.contextRules.get(contextType);
    if (!rule || !rule.cooldown) return false;

    const lastOccurrence = userHistory
      .filter(entry => entry.type === contextType)
      .sort((a, b) => b.timestamp - a.timestamp)[0];

    if (!lastOccurrence) return false;

    return (Date.now() - lastOccurrence.timestamp) < rule.cooldown;
  }

  /**
   * Get context suggestions for specific user action
   */
  async getContextSuggestions(userId, contextType, actionData = {}) {
    return await this.processContext(userId, {
      type: contextType,
      action: 'manual_request',
      data: actionData
    });
  }

  /**
   * Update context rules dynamically
   */
  updateContextRule(contextType, rule) {
    this.contextRules.set(contextType, rule);
  }

  /**
   * Get user context history
   */
  getUserContextHistory(userId) {
    return this.userContextHistory.get(userId) || [];
  }

  /**
   * Clear user context history
   */
  clearUserContextHistory(userId) {
    this.userContextHistory.delete(userId);
  }

  /**
   * Get context statistics
   */
  getContextStats() {
    const stats = {
      totalRules: this.contextRules.size,
      activeUsers: this.userContextHistory.size,
      totalContextEntries: 0
    };

    for (const history of this.userContextHistory.values()) {
      stats.totalContextEntries += history.length;
    }

    return stats;
  }
}

// Singleton instance
const contextAwareService = new ContextAwareService();

module.exports = contextAwareService;
