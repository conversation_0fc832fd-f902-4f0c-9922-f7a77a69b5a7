const axios = require('axios');

async function testEmotionalAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test emotional intelligence
    console.log('\n💭 Testing Emotional Intelligence...\n');
    
    const emotionalTests = [
      {
        message: "I'm feeling really sad and overwhelmed today 😢",
        emotion: "Sadness"
      },
      {
        message: "I'm so frustrated with my workload! This is ridiculous and I'm angry!",
        emotion: "Anger"
      },
      {
        message: "Hello! I'm excited about my new project! 🎉",
        emotion: "Joy/Excitement"
      },
      {
        message: "Thank you so much! You're amazing and I really appreciate your help! 🙏",
        emotion: "Gratitude"
      }
    ];

    for (let i = 0; i < emotionalTests.length; i++) {
      const test = emotionalTests[i];
      console.log(`\n📝 Test ${i + 1}: ${test.emotion} Detection`);
      console.log(`👤 User: "${test.message}"`);
      
      try {
        const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
          message: test.message
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = response.data.data;
        const assistantMessage = data.assistantMessage;
        const classification = data.classification;

        console.log(`🤖 Assistant: ${assistantMessage.content}`);
        console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
        
        if (classification.metadata?.emotionalState) {
          console.log(`💭 Detected Emotion: ${classification.metadata.emotionalState}`);
        }
        
        if (classification.metadata?.hasTypos) {
          console.log(`📝 Corrected Text: "${classification.metadata.correctedText}"`);
        }

        if (data.suggestions && data.suggestions.length > 0) {
          console.log(`💡 Suggestions: ${data.suggestions.slice(0, 2).join(', ')}`);
        }

        console.log(`⚡ Response time: ${assistantMessage.metadata.responseTime}ms`);
        
      } catch (error) {
        console.error(`❌ Error in test ${i + 1}:`, error.response?.data?.message || error.message);
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n🎉 Emotional Intelligence Testing Complete!');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testEmotionalAI();
