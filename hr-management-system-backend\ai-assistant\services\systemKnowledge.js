/**
 * Complete HR Management System Knowledge Base
 * Comprehensive understanding of all system features, models, and capabilities
 */

class SystemKnowledge {
  constructor() {
    this.initializeSystemStructure();
    this.initializeUserRoles();
    this.initializeFeatureMapping();
    this.initializeWorkflows();
  }

  /**
   * Initialize complete system structure knowledge
   */
  initializeSystemStructure() {
    this.systemModules = {
      // Core User Management
      user_management: {
        description: "Complete user lifecycle management",
        features: [
          "User registration and authentication",
          "Role-based access control (Admin, HR, User)",
          "Profile management and password changes",
          "Department and job assignment",
          "User search and filtering"
        ],
        models: ["User", "LoginHistory", "AuditLog"],
        permissions: {
          admin: ["create", "read", "update", "delete", "manage_roles"],
          hr: ["create", "read", "update", "search"],
          user: ["read_own", "update_own"]
        }
      },

      // Leave Management System
      leave_management: {
        description: "Comprehensive leave request and approval system",
        features: [
          "Leave request submission with multiple types",
          "Leave balance tracking and calculation",
          "Approval workflow (HR/Manager approval)",
          "Leave history and reporting",
          "Automatic leave balance updates",
          "Leave policy enforcement"
        ],
        models: ["LeaveRequest"],
        leave_types: ["Annual", "Sick", "Personal", "Maternity", "Paternity", "Emergency"],
        workflow: ["Submit → Pending → Approved/Rejected → Applied"],
        permissions: {
          admin: ["view_all", "approve", "reject", "modify"],
          hr: ["view_all", "approve", "reject", "generate_reports"],
          user: ["create", "view_own", "cancel_pending"]
        }
      },

      // Task Management System
      task_management: {
        description: "Advanced task assignment and tracking system",
        features: [
          "Task creation and assignment",
          "Priority and deadline management",
          "Task status tracking and updates",
          "Team collaboration and comments",
          "Task categorization and filtering",
          "Performance analytics and reporting",
          "Automated notifications and reminders"
        ],
        models: ["Task"],
        statuses: ["Pending", "In Progress", "Completed", "On Hold", "Cancelled"],
        priorities: ["Low", "Medium", "High", "Urgent"],
        categories: ["Development", "Design", "Testing", "Documentation", "Meeting", "Research", "Other"],
        permissions: {
          admin: ["create", "assign", "view_all", "modify", "delete"],
          hr: ["create", "assign", "view_all", "modify"],
          user: ["view_assigned", "update_status", "add_comments"]
        }
      },

      // Attendance Tracking
      attendance_system: {
        description: "Real-time attendance monitoring and reporting",
        features: [
          "Check-in/Check-out functionality",
          "Location-based attendance tracking",
          "Hours worked calculation",
          "Attendance status monitoring",
          "Late arrival and early departure tracking",
          "Attendance reports and analytics",
          "Integration with leave management"
        ],
        models: ["Attendance"],
        statuses: ["Present", "Late", "Absent", "Half-Day", "On Leave"],
        permissions: {
          admin: ["view_all", "modify", "generate_reports"],
          hr: ["view_all", "generate_reports", "modify"],
          user: ["check_in", "check_out", "view_own"]
        }
      },

      // Performance Evaluation System
      evaluation_system: {
        description: "Comprehensive employee performance evaluation",
        features: [
          "Multi-criteria performance ratings",
          "AI-powered evaluation insights",
          "Goal setting and tracking",
          "360-degree feedback collection",
          "Performance trend analysis",
          "Evaluation scheduling and reminders",
          "Self-evaluation capabilities"
        ],
        models: ["Evaluation"],
        rating_criteria: [
          "Performance Rating", "Attitude Rating", "Communication Rating",
          "Teamwork Rating", "Initiative Rating"
        ],
        periods: ["Monthly", "Quarterly", "Semi-Annual", "Annual"],
        statuses: ["Draft", "Completed", "Acknowledged"],
        permissions: {
          admin: ["create", "view_all", "modify", "delete"],
          hr: ["create", "view_all", "modify", "generate_insights"],
          user: ["view_own", "acknowledge", "self_evaluate"]
        }
      },

      // Job & Application Management
      recruitment_system: {
        description: "End-to-end recruitment and application management",
        features: [
          "Job posting creation and management",
          "Application submission and tracking",
          "CV/Resume processing with OCR",
          "AI-powered candidate matching",
          "NLP analysis for job-candidate fit",
          "Application status workflow",
          "Interview scheduling and feedback",
          "Candidate communication"
        ],
        models: ["Job", "Application"],
        job_types: ["Internship", "Full-Time", "Part-Time", "Contract"],
        academic_levels: ["Bachelor", "Engineer", "Master", "PhD", "Other"],
        application_statuses: ["Pending", "Under Review", "Interview", "Approved", "Rejected"],
        permissions: {
          admin: ["create_jobs", "view_all", "modify", "delete"],
          hr: ["create_jobs", "view_all", "process_applications", "schedule_interviews"],
          user: ["view_jobs", "apply", "view_own_applications"]
        }
      },

      // Notification System
      notification_system: {
        description: "Real-time notification and communication system",
        features: [
          "Real-time push notifications",
          "Email notification integration",
          "Notification categorization and filtering",
          "Read/unread status tracking",
          "Notification history and archiving",
          "Custom notification preferences",
          "System-wide announcements"
        ],
        models: ["Notification"],
        types: [
          "TASK_ASSIGNED", "TASK_UPDATED", "TASK_COMPLETED", "TASK_DEADLINE_APPROACHING",
          "LEAVE_REQUEST_SUBMITTED", "LEAVE_REQUEST_APPROVED", "LEAVE_REQUEST_REJECTED",
          "NEW_APPLICATION", "APPLICATION_STATUS_CHANGED",
          "EVALUATION_CREATED", "EVALUATION_UPDATED",
          "NEW_USER", "ATTENDANCE_RECORDED"
        ],
        permissions: {
          admin: ["send_system_wide", "view_all", "manage"],
          hr: ["send_to_users", "view_relevant", "manage_own"],
          user: ["view_own", "mark_read", "delete_own"]
        }
      },

      // Analytics & Reporting
      analytics_system: {
        description: "Comprehensive analytics and business intelligence",
        features: [
          "Dashboard with key metrics",
          "Custom report generation",
          "Data visualization and charts",
          "Performance trend analysis",
          "Attendance and leave analytics",
          "Task completion metrics",
          "User activity monitoring",
          "Export capabilities (PDF, Excel)"
        ],
        models: ["Statistics", "Reports"],
        report_types: [
          "User Activity", "Leave Summary", "Task Performance",
          "Attendance Report", "Evaluation Summary", "Application Analytics"
        ],
        permissions: {
          admin: ["view_all_analytics", "generate_all_reports", "export"],
          hr: ["view_hr_analytics", "generate_hr_reports", "export"],
          user: ["view_own_analytics", "generate_own_reports"]
        }
      },

      // GEK (General Estimating Knowledge) System
      gek_system: {
        description: "AI-powered employee performance estimation and task assignment optimization",
        features: [
          "Employee fit score calculation",
          "Task completion time estimation",
          "Performance prediction modeling",
          "Optimal task assignment recommendations",
          "Skill gap analysis",
          "Performance benchmarking",
          "Predictive analytics for HR decisions"
        ],
        models: ["GEKEstimate"],
        metrics: [
          "Completion Rate", "On-Time Rate", "Quality Score",
          "Efficiency Rating", "Collaboration Score"
        ],
        permissions: {
          admin: ["view_all", "configure", "generate_estimates"],
          hr: ["view_all", "generate_estimates", "use_recommendations"],
          user: ["view_own_metrics"]
        }
      }
    };
  }

  /**
   * Initialize user roles and their capabilities
   */
  initializeUserRoles() {
    this.userRoles = {
      admin: {
        title: "System Administrator",
        description: "Full system access with administrative privileges",
        capabilities: [
          "Complete user management (create, edit, delete users)",
          "System configuration and settings",
          "Access to all modules and features",
          "Audit trail and security monitoring",
          "System backup and maintenance",
          "Role and permission management",
          "Global analytics and reporting"
        ],
        dashboard_features: [
          "System overview and health metrics",
          "User activity monitoring",
          "Security and audit logs",
          "System performance analytics",
          "Global statistics and trends"
        ]
      },

      hr: {
        title: "Human Resources",
        description: "HR management with employee-focused capabilities",
        capabilities: [
          "Employee lifecycle management",
          "Leave request approval and management",
          "Task assignment and monitoring",
          "Performance evaluation creation and review",
          "Job posting and application management",
          "Attendance monitoring and reporting",
          "Employee analytics and insights",
          "Notification management"
        ],
        dashboard_features: [
          "HR metrics and KPIs",
          "Pending approvals and tasks",
          "Employee performance overview",
          "Leave and attendance summaries",
          "Recent applications and evaluations"
        ]
      },

      user: {
        title: "Employee",
        description: "Standard employee access with self-service capabilities",
        capabilities: [
          "Personal profile management",
          "Leave request submission and tracking",
          "Task viewing and status updates",
          "Attendance check-in/check-out",
          "Performance evaluation viewing",
          "Job application submission",
          "Notification management",
          "Personal analytics and reports"
        ],
        dashboard_features: [
          "Personal task overview",
          "Leave balance and history",
          "Attendance summary",
          "Recent notifications",
          "Performance metrics",
          "Upcoming deadlines"
        ]
      }
    };
  }

  /**
   * Initialize feature mapping for intelligent responses
   */
  initializeFeatureMapping() {
    this.featureKeywords = {
      // User Management
      user_management: [
        "user", "users", "employee", "employees", "staff", "team member",
        "profile", "account", "registration", "login", "password",
        "role", "permission", "access", "department", "job title"
      ],

      // Leave Management
      leave_management: [
        "leave", "vacation", "holiday", "time off", "pto", "absence",
        "sick leave", "personal leave", "annual leave", "maternity",
        "paternity", "emergency leave", "leave balance", "leave request"
      ],

      // Task Management
      task_management: [
        "task", "tasks", "assignment", "project", "work", "deadline",
        "priority", "status", "progress", "completion", "todo",
        "assignment", "responsibility", "deliverable"
      ],

      // Attendance
      attendance_system: [
        "attendance", "check in", "check out", "clock in", "clock out",
        "hours", "time tracking", "present", "absent", "late",
        "working hours", "timesheet", "schedule"
      ],

      // Evaluations
      evaluation_system: [
        "evaluation", "performance", "review", "rating", "assessment",
        "feedback", "appraisal", "goals", "objectives", "improvement",
        "strengths", "weaknesses", "development"
      ],

      // Jobs & Applications
      recruitment_system: [
        "job", "jobs", "position", "opening", "vacancy", "career",
        "application", "apply", "candidate", "resume", "cv",
        "interview", "hiring", "recruitment", "onboarding"
      ],

      // Notifications
      notification_system: [
        "notification", "notifications", "alert", "alerts", "message",
        "announcement", "update", "reminder", "news", "communication"
      ],

      // Analytics
      analytics_system: [
        "analytics", "report", "reports", "statistics", "metrics",
        "dashboard", "chart", "graph", "data", "insights",
        "summary", "overview", "trends", "analysis"
      ],

      // GEK System
      gek_system: [
        "gek", "estimation", "fit score", "performance prediction",
        "task assignment", "optimization", "recommendation",
        "skill analysis", "benchmarking", "efficiency"
      ]
    };
  }

  /**
   * Initialize common workflows
   */
  initializeWorkflows() {
    this.workflows = {
      leave_request: {
        steps: [
          "Employee submits leave request",
          "System validates leave balance",
          "HR receives notification",
          "HR reviews and approves/rejects",
          "Employee receives notification",
          "Leave balance updated (if approved)"
        ],
        typical_questions: [
          "How do I request leave?",
          "What's my leave balance?",
          "When will my leave be approved?",
          "Can I cancel my leave request?"
        ]
      },

      task_assignment: {
        steps: [
          "HR/Manager creates task",
          "Task assigned to employee",
          "Employee receives notification",
          "Employee updates task status",
          "Manager monitors progress",
          "Task completed and reviewed"
        ],
        typical_questions: [
          "What tasks are assigned to me?",
          "How do I update task status?",
          "When is my task deadline?",
          "Who assigned this task to me?"
        ]
      },

      job_application: {
        steps: [
          "HR posts job opening",
          "Candidate submits application",
          "System processes CV with OCR/NLP",
          "HR reviews applications",
          "Interview scheduling",
          "Final decision and notification"
        ],
        typical_questions: [
          "What jobs are available?",
          "How do I apply for a job?",
          "What's the status of my application?",
          "When will I hear back?"
        ]
      },

      performance_evaluation: {
        steps: [
          "HR schedules evaluation period",
          "Employee completes self-evaluation",
          "Manager/HR conducts evaluation",
          "AI generates insights",
          "Results shared with employee",
          "Goals set for next period"
        ],
        typical_questions: [
          "When is my next evaluation?",
          "How can I view my performance?",
          "What are my performance goals?",
          "How is my rating calculated?"
        ]
      }
    };
  }

  /**
   * Get system capabilities for a specific user role
   */
  getCapabilitiesForRole(userRole) {
    return this.userRoles[userRole] || this.userRoles.user;
  }

  /**
   * Identify relevant system modules based on user query
   */
  identifyRelevantModules(query) {
    const lowerQuery = query.toLowerCase();
    const relevantModules = [];

    Object.entries(this.featureKeywords).forEach(([module, keywords]) => {
      const matchCount = keywords.filter(keyword =>
        lowerQuery.includes(keyword)
      ).length;

      if (matchCount > 0) {
        relevantModules.push({
          module: module,
          relevance: matchCount,
          info: this.systemModules[module]
        });
      }
    });

    return relevantModules.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * Get comprehensive system overview
   */
  getSystemOverview() {
    return {
      name: "Advanced HR Management System",
      description: "Comprehensive human resources management platform with AI-powered features",
      modules: Object.keys(this.systemModules),
      userRoles: Object.keys(this.userRoles),
      totalFeatures: Object.values(this.systemModules).reduce(
        (total, module) => total + module.features.length, 0
      ),
      keyCapabilities: [
        "Complete employee lifecycle management",
        "AI-powered performance insights with GEK system",
        "Real-time attendance tracking with location support",
        "Intelligent task assignment with fit score optimization",
        "Automated leave management with conflict detection",
        "Advanced analytics and reporting with data visualization",
        "Smart recruitment with OCR and NLP-powered CV matching",
        "Comprehensive notification system with real-time updates",
        "Performance evaluation system with AI insights",
        "Predictive analytics for HR decision making"
      ],
      technicalFeatures: [
        "OpenAI GPT-4 integration for natural language processing",
        "Python-based NLP pipeline for CV analysis",
        "OCR technology for document processing",
        "Machine learning algorithms for performance prediction",
        "Real-time WebSocket notifications",
        "Advanced spell correction and typo tolerance",
        "Emotional intelligence and sentiment analysis",
        "Context-aware conversation memory",
        "Multi-language support capabilities",
        "Secure role-based access control"
      ],
      apiEndpoints: {
        authentication: ["/api/auth/login", "/api/auth/register", "/api/forgot-password"],
        userManagement: ["/api/admin/users", "/api/hr/users", "/api/user/profile"],
        leaveManagement: ["/api/leaves", "/api/user/leave-requests", "/api/hr/leave-requests"],
        taskManagement: ["/api/tasks", "/api/hr/tasks", "/api/user/tasks"],
        attendance: ["/api/user/attendance", "/api/hr/attendance"],
        recruitment: ["/api/jobs", "/api/applications", "/api/hr/applications"],
        notifications: ["/api/notifications"],
        analytics: ["/api/statistics", "/api/reports"],
        gek: ["/api/gek/fit-score", "/api/gek/rankings", "/api/gek/task-metrics"],
        aiAssistant: ["/api/ai/chat/message", "/api/ai/health"]
      }
    };
  }

  /**
   * Get detailed feature explanations for each module
   */
  getDetailedFeatureExplanations() {
    return {
      user_management: {
        createUser: "Create new employee accounts with role assignment and department allocation",
        roleManagement: "Assign and manage user roles (Admin, HR, Employee) with specific permissions",
        profileManagement: "Update personal information, contact details, and job information",
        passwordSecurity: "Secure password management with encryption and change tracking",
        accountSecurity: "Account locking, failed login tracking, and security monitoring"
      },
      leave_management: {
        leaveTypes: "Support for Annual, Sick, Personal, Maternity/Paternity, Bereavement, and Unpaid leave",
        balanceTracking: "Automatic calculation of leave balances with carry-over rules",
        approvalWorkflow: "Multi-level approval process with HR and manager review",
        conflictDetection: "AI-powered detection of scheduling conflicts and team availability",
        policyEnforcement: "Automatic enforcement of company leave policies and restrictions"
      },
      task_management: {
        taskCategories: "Support for General, Development, Design, Marketing, HR, Finance, Operations, Project, Administrative, Training, Evaluation tasks",
        priorityLevels: "Four priority levels: Low, Medium, High, Urgent with color coding",
        progressTracking: "Real-time progress updates with percentage completion",
        deadlineManagement: "Automatic deadline reminders and escalation",
        collaborativeFeatures: "Task comments, feedback, and file attachments"
      },
      attendance_system: {
        realTimeTracking: "Live check-in/check-out with timestamp recording",
        locationTracking: "GPS-based location verification for remote work",
        hoursCalculation: "Automatic calculation of work hours, overtime, and breaks",
        statusMonitoring: "Track Present, Late, Absent, Half-Day, On Leave statuses",
        reportGeneration: "Comprehensive attendance reports with analytics"
      },
      evaluation_system: {
        evaluationTypes: "Monthly, Quarterly, Semi-Annual, and Annual performance reviews",
        aiInsights: "AI-powered performance analysis and recommendations",
        selfEvaluation: "Employee self-assessment capabilities",
        goalTracking: "Set and track performance goals and objectives",
        skillAssessment: "Comprehensive skill evaluation and gap analysis"
      },
      recruitment_system: {
        jobPosting: "Create and manage job postings with detailed requirements",
        applicationTracking: "Full application lifecycle management",
        cvProcessing: "OCR-powered CV text extraction and parsing",
        nlpMatching: "Advanced NLP algorithms for job-candidate matching",
        interviewScheduling: "Automated interview scheduling and feedback collection"
      },
      notification_system: {
        realTimeNotifications: "Instant push notifications for important events",
        emailIntegration: "Automatic email notifications for critical updates",
        notificationTypes: "Task assignments, leave approvals, application updates, evaluations",
        customPreferences: "User-configurable notification preferences",
        notificationHistory: "Complete history with read/unread tracking"
      },
      analytics_system: {
        dashboardMetrics: "Real-time KPIs and performance indicators",
        customReports: "Generate custom reports with filtering and sorting",
        dataVisualization: "Interactive charts and graphs for data analysis",
        trendAnalysis: "Historical trend analysis and forecasting",
        exportCapabilities: "Export reports in PDF, Excel, and CSV formats"
      },
      gek_system: {
        fitScoreCalculation: "AI-powered employee-task fit score calculation",
        performancePrediction: "Predict task completion times and success rates",
        optimalAssignment: "Recommend optimal task assignments based on skills and availability",
        skillGapAnalysis: "Identify skill gaps and training opportunities",
        benchmarking: "Performance benchmarking against team and company averages"
      }
    };
  }

  /**
   * Get workflow explanations for complex processes
   */
  getWorkflowExplanations() {
    return {
      leaveRequestWorkflow: {
        steps: [
          "Employee submits leave request with dates and reason",
          "System checks leave balance and policy compliance",
          "AI analyzes potential conflicts with team schedules",
          "Request routed to appropriate approver (HR/Manager)",
          "Approver reviews request with AI recommendations",
          "Decision made and employee notified",
          "If approved, leave balance updated and calendar blocked"
        ],
        automatedChecks: [
          "Leave balance sufficiency",
          "Policy compliance verification",
          "Team availability analysis",
          "Project deadline conflicts",
          "Historical leave pattern analysis"
        ]
      },
      taskAssignmentWorkflow: {
        steps: [
          "HR creates task with requirements and deadline",
          "GEK system calculates fit scores for potential assignees",
          "System recommends optimal assignee based on skills and availability",
          "Task assigned with automatic notification",
          "Employee receives task with context and resources",
          "Progress tracked with regular updates",
          "Completion verified and feedback collected"
        ],
        aiOptimization: [
          "Skill matching analysis",
          "Workload balancing",
          "Completion time estimation",
          "Performance prediction",
          "Resource allocation optimization"
        ]
      },
      recruitmentWorkflow: {
        steps: [
          "HR creates job posting with requirements",
          "Candidates submit applications with CVs",
          "OCR extracts text from CV documents",
          "NLP analyzes CV content and extracts skills",
          "AI calculates job-candidate match scores",
          "Applications ranked by relevance",
          "HR reviews top candidates",
          "Interview process initiated"
        ],
        aiFeatures: [
          "Automatic CV parsing and skill extraction",
          "Job-candidate compatibility scoring",
          "Skill gap identification",
          "Experience relevance analysis",
          "Education background matching"
        ]
      }
    };
  }

  /**
   * Generate contextual help based on user role and query
   */
  generateContextualHelp(userRole, query, modules) {
    const roleCapabilities = this.getCapabilitiesForRole(userRole);
    const help = {
      role: roleCapabilities.title,
      availableActions: [],
      relevantFeatures: [],
      suggestedWorkflows: []
    };

    // Add role-specific actions
    modules.forEach(moduleInfo => {
      const module = moduleInfo.info;
      if (module.permissions[userRole]) {
        help.availableActions.push(...module.permissions[userRole]);
        help.relevantFeatures.push(...module.features);
      }
    });

    // Add relevant workflows
    Object.entries(this.workflows).forEach(([workflow, info]) => {
      if (modules.some(m => m.module.includes(workflow.split('_')[0]))) {
        help.suggestedWorkflows.push({
          name: workflow,
          steps: info.steps,
          questions: info.typical_questions
        });
      }
    });

    return help;
  }

  /**
   * Get smart suggestions based on context
   */
  getSmartSuggestions(userRole, currentContext) {
    const suggestions = [];
    const roleInfo = this.userRoles[userRole];

    // Role-specific suggestions
    switch (userRole) {
      case 'admin':
        suggestions.push(
          "View system analytics and user activity",
          "Manage user accounts and permissions",
          "Generate comprehensive reports",
          "Monitor system performance"
        );
        break;

      case 'hr':
        suggestions.push(
          "Review pending leave requests",
          "Check recent job applications",
          "Create performance evaluations",
          "Assign tasks to team members",
          "View attendance reports"
        );
        break;

      case 'user':
        suggestions.push(
          "Check my task assignments",
          "View my leave balance",
          "Submit a leave request",
          "Check in for work",
          "View my performance evaluation"
        );
        break;
    }

    return suggestions;
  }
}

// Singleton instance
const systemKnowledge = new SystemKnowledge();

module.exports = systemKnowledge;
