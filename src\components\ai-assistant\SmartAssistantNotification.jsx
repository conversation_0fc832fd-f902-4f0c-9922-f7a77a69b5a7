/**
 * Smart Assistant Notification Component
 * Displays proactive AI suggestions with feedback capabilities
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Button,
  Box,
  Chip,
  <PERSON>lapse,
  Rating,
  TextField,
  Tooltip,
  Avatar
} from '@mui/material';
import {
  Close as CloseIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  AutoAwesome as AIIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import smartAssistantService from '../../Services/SmartAssistantService';
import { toast } from 'react-toastify';

const StyledCard = styled(Card)(({ theme, priority }) => ({
  margin: theme.spacing(1),
  borderLeft: `4px solid ${smartAssistantService.getPriorityColor(priority)}`,
  boxShadow: theme.shadows[3],
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: theme.shadows[6],
    transform: 'translateY(-2px)'
  }
}));

const PriorityChip = styled(Chip)(({ theme, priority }) => ({
  backgroundColor: smartAssistantService.getPriorityColor(priority),
  color: 'white',
  fontWeight: 'bold',
  fontSize: '0.75rem'
}));

const SmartAssistantNotification = ({ 
  insight, 
  onDismiss, 
  onFeedback, 
  autoHide = false,
  hideDelay = 10000 
}) => {
  const [expanded, setExpanded] = useState(false);
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackComment, setFeedbackComment] = useState('');
  const [isVisible, setIsVisible] = useState(true);
  const [viewStartTime] = useState(Date.now());

  useEffect(() => {
    // Mark as viewed when component mounts
    smartAssistantService.markInsightAsViewed(insight.id, 0);

    // Auto-hide if enabled
    if (autoHide) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, hideDelay);

      return () => clearTimeout(timer);
    }
  }, [insight.id, autoHide, hideDelay]);

  const handleDismiss = async () => {
    try {
      const readTime = Date.now() - viewStartTime;
      await smartAssistantService.markInsightAsViewed(insight.id, readTime);
      await smartAssistantService.dismissInsight(insight.id);
      
      setIsVisible(false);
      if (onDismiss) {
        onDismiss(insight.id);
      }
    } catch (error) {
      console.error('Error dismissing insight:', error);
      toast.error('Failed to dismiss suggestion');
    }
  };

  const handleFeedback = async (rating) => {
    try {
      await smartAssistantService.provideFeedback(
        insight.id, 
        rating, 
        feedbackComment
      );

      if (onFeedback) {
        onFeedback(insight.id, rating, feedbackComment);
      }

      toast.success(
        rating === 'up' 
          ? 'Thanks for the positive feedback!' 
          : 'Thanks for the feedback. I\'ll learn from this.'
      );

      setShowFeedback(false);
      setFeedbackComment('');
      
      // Auto-dismiss after feedback
      setTimeout(() => {
        handleDismiss();
      }, 2000);

    } catch (error) {
      console.error('Error providing feedback:', error);
      toast.error('Failed to submit feedback');
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now - time) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const getCategoryColor = (category) => {
    const colors = {
      user_management: '#2196f3',
      recruitment: '#4caf50',
      workflow: '#ff9800',
      leave_management: '#9c27b0',
      productivity: '#f44336',
      task_management: '#00bcd4',
      analytics: '#795548'
    };
    return colors[category] || '#757575';
  };

  if (!isVisible) {
    return null;
  }

  return (
    <StyledCard priority={insight.priority}>
      <CardContent>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between">
          <Box display="flex" alignItems="center" flex={1}>
            <Avatar 
              sx={{ 
                bgcolor: getCategoryColor(insight.category),
                width: 40,
                height: 40,
                mr: 2
              }}
            >
              {smartAssistantService.getCategoryIcon(insight.category)}
            </Avatar>
            
            <Box flex={1}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <Typography variant="h6" component="h3" sx={{ fontWeight: 600 }}>
                  {insight.title}
                </Typography>
                <PriorityChip 
                  label={insight.priority.toUpperCase()} 
                  priority={insight.priority}
                  size="small"
                />
                <Chip 
                  label={insight.category.replace('_', ' ')}
                  size="small"
                  sx={{ 
                    backgroundColor: getCategoryColor(insight.category),
                    color: 'white',
                    textTransform: 'capitalize'
                  }}
                />
              </Box>

              <Typography variant="body1" color="text.primary" sx={{ mb: 1 }}>
                {smartAssistantService.formatSuggestionMessage(
                  insight.description, 
                  insight.metadata
                )}
              </Typography>

              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <ScheduleIcon fontSize="small" color="action" />
                <Typography variant="caption" color="text.secondary">
                  {formatTimeAgo(insight.createdAt)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  • Confidence: {Math.round(insight.confidence * 100)}%
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            <Tooltip title="Expand details">
              <IconButton 
                size="small" 
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Dismiss">
              <IconButton size="small" onClick={handleDismiss}>
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Box mt={2} pt={2} borderTop={1} borderColor="divider">
            {insight.metadata && Object.keys(insight.metadata).length > 0 && (
              <Box mb={2}>
                <Typography variant="subtitle2" gutterBottom>
                  Additional Details:
                </Typography>
                {Object.entries(insight.metadata).map(([key, value]) => (
                  <Typography key={key} variant="body2" color="text.secondary">
                    <strong>{key.replace(/([A-Z])/g, ' $1').toLowerCase()}:</strong> {value}
                  </Typography>
                ))}
              </Box>
            )}

            <Box display="flex" alignItems="center" gap={2}>
              <Typography variant="body2" color="text.secondary">
                Was this suggestion helpful?
              </Typography>
              
              <Tooltip title="Helpful">
                <IconButton 
                  size="small" 
                  color="success"
                  onClick={() => handleFeedback('up')}
                >
                  <ThumbUpIcon />
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Not helpful">
                <IconButton 
                  size="small" 
                  color="error"
                  onClick={() => setShowFeedback(true)}
                >
                  <ThumbDownIcon />
                </IconButton>
              </Tooltip>
            </Box>

            <Collapse in={showFeedback}>
              <Box mt={2} p={2} bgcolor="grey.50" borderRadius={1}>
                <Typography variant="body2" gutterBottom>
                  Help us improve by telling us why this wasn't helpful:
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={2}
                  placeholder="Optional feedback..."
                  value={feedbackComment}
                  onChange={(e) => setFeedbackComment(e.target.value)}
                  size="small"
                  sx={{ mb: 1 }}
                />
                <Box display="flex" gap={1}>
                  <Button 
                    size="small" 
                    variant="contained" 
                    color="primary"
                    onClick={() => handleFeedback('down')}
                  >
                    Submit
                  </Button>
                  <Button 
                    size="small" 
                    onClick={() => setShowFeedback(false)}
                  >
                    Cancel
                  </Button>
                </Box>
              </Box>
            </Collapse>
          </Box>
        </Collapse>
      </CardContent>
    </StyledCard>
  );
};

export default SmartAssistantNotification;
