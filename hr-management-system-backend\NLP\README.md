# NLP Module for HR Management System

This directory contains the Natural Language Processing (NLP) functionality for the HR Management System.

## Directory Structure

- `processors/` - Contains the NLP processing scripts
  - `simple_nlp.py` - Simple NLP processor using NLTK
  - `advanced_nlp.py` - Advanced NLP processor (future implementation)
- `utils/` - Utility functions for NLP processing
  - `text_extraction.py` - Functions for extracting text from PDFs
  - `text_processing.py` - Functions for processing and analyzing text
- `models/` - Pre-trained models and model-related code
- `install/` - Installation scripts for NLP dependencies
  - `install_nlp_packages.py` - Script to install required packages

## Usage

To use the NLP functionality, call the `cvParserService.js` which will use the appropriate NLP processor based on configuration.

## Installation

Run the installation script to install all required dependencies:

```
python install/install_nlp_packages.py
```

## Configuration

The NLP module can be configured in the `config.js` file to use different processors and models.
