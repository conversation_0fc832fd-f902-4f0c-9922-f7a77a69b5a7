import React from 'react';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';

// Animation variants for page transitions - minimal for real-time feel
const pageVariants = {
  initial: {
    opacity: 0.9,
    y: 5,
  },
  in: {
    opacity: 1,
    y: 0,
  },
  out: {
    opacity: 0.9,
    y: -5,
  },
};

// Transition settings - very fast for real-time experience
const pageTransition = {
  type: 'tween',
  ease: 'easeOut',
  duration: 0.15,
};

/**
 * PageTransition component that wraps page content with smooth transitions
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to be wrapped
 * @param {Object} props.variants - Custom animation variants (optional)
 * @param {Object} props.transition - Custom transition settings (optional)
 * @returns {JSX.Element} Animated page component
 */
const PageTransition = ({
  children,
  variants = pageVariants,
  transition = pageTransition,
  ...props
}) => {
  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={variants}
      transition={transition}
      style={{ width: '100%', height: '100%' }}
      {...props}
    >
      {children}
    </motion.div>
  );
};

PageTransition.propTypes = {
  children: PropTypes.node.isRequired,
  variants: PropTypes.object,
  transition: PropTypes.object,
};

export default PageTransition;
