/**
 * Smart Assistant Dashboard Component
 * Analytics panel for AI suggestions and user preferences
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Chip,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControlLabel,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Psychology as PsychologyIcon,
  Settings as SettingsIcon,
  Analytics as AnalyticsIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  Visibility as VisibilityIcon,
  AutoAwesome as AIIcon
} from '@mui/icons-material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import smartAssistantService from '../../Services/SmartAssistantService';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

const SmartAssistantDashboard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [preferences, setPreferences] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [insights, setInsights] = useState([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [analyticsData, preferencesData, statsData, insightsData] = await Promise.all([
        smartAssistantService.getAnalytics(),
        smartAssistantService.getPreferences(),
        smartAssistantService.getStatistics(30),
        smartAssistantService.getInsights({ limit: 5 })
      ]);

      setAnalytics(analyticsData.data);
      setPreferences(preferencesData.data);
      setStatistics(statsData.data);
      setInsights(insightsData.data.insights);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = async (key, value) => {
    try {
      setUpdating(true);
      const updatedPrefs = { ...preferences, [key]: value };
      
      await smartAssistantService.updatePreferences(updatedPrefs);
      setPreferences(updatedPrefs);
      
      toast.success('Preferences updated successfully');
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast.error('Failed to update preferences');
    } finally {
      setUpdating(false);
    }
  };

  const triggerAnalysis = async () => {
    try {
      setUpdating(true);
      await smartAssistantService.analyzeAndSuggest();
      await loadDashboardData();
      toast.success('Analysis completed! Check for new suggestions.');
    } catch (error) {
      console.error('Error triggering analysis:', error);
      toast.error('Failed to trigger analysis');
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      active: '#2196f3',
      acted_upon: '#4caf50',
      dismissed: '#f44336'
    };
    return colors[status] || '#757575';
  };

  const formatCategoryName = (category) => {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  const statusData = statistics?.statusStats?.map(stat => ({
    name: stat._id,
    value: stat.count,
    color: getStatusColor(stat._id)
  })) || [];

  const categoryData = statistics?.categoryStats?.map(stat => ({
    name: formatCategoryName(stat._id),
    count: stat.count,
    confidence: Math.round(stat.avgConfidence * 100)
  })) || [];

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <AIIcon color="primary" />
        Smart Assistant Dashboard
      </Typography>

      <Grid container spacing={3}>
        {/* Statistics Overview */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <AnalyticsIcon />
                Suggestion Analytics
              </Typography>

              {analytics && (
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="primary">
                        {analytics.statistics.totalSuggestions}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Suggestions
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="success.main">
                        {analytics.statistics.acceptedSuggestions}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Accepted
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="error.main">
                        {analytics.statistics.dismissedSuggestions}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Dismissed
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={3}>
                    <Box textAlign="center">
                      <Typography variant="h4" color="info.main">
                        {analytics.statistics.acceptanceRate}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Acceptance Rate
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              )}

              {/* Status Distribution Chart */}
              {statusData.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Suggestion Status Distribution
                  </Typography>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={statusData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {statusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              )}

              {/* Category Performance Chart */}
              {categoryData.length > 0 && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    Category Performance
                  </Typography>
                  <ResponsiveContainer width="100%" height={250}>
                    <BarChart data={categoryData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#2196f3" name="Suggestions" />
                      <Bar dataKey="confidence" fill="#4caf50" name="Avg Confidence %" />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Preferences Panel */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <SettingsIcon />
                Preferences
              </Typography>

              {preferences && (
                <Box>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Suggestion Frequency</InputLabel>
                    <Select
                      value={preferences.frequency || 'normal'}
                      label="Suggestion Frequency"
                      onChange={(e) => handlePreferenceChange('frequency', e.target.value)}
                      disabled={updating}
                    >
                      <MenuItem value="low">Low (High priority only)</MenuItem>
                      <MenuItem value="normal">Normal</MenuItem>
                      <MenuItem value="high">High (All suggestions)</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Communication Tone</InputLabel>
                    <Select
                      value={preferences.tone || 'professional'}
                      label="Communication Tone"
                      onChange={(e) => handlePreferenceChange('tone', e.target.value)}
                      disabled={updating}
                    >
                      <MenuItem value="casual">Casual</MenuItem>
                      <MenuItem value="professional">Professional</MenuItem>
                      <MenuItem value="friendly">Friendly</MenuItem>
                    </Select>
                  </FormControl>

                  <Typography variant="subtitle2" gutterBottom>
                    Suggestion Categories
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    {[
                      'user_management',
                      'recruitment', 
                      'workflow',
                      'leave_management',
                      'productivity',
                      'task_management'
                    ].map(category => (
                      <Chip
                        key={category}
                        label={formatCategoryName(category)}
                        variant={
                          preferences.categories?.includes('all') || 
                          preferences.categories?.includes(category) 
                            ? 'filled' 
                            : 'outlined'
                        }
                        color="primary"
                        size="small"
                        sx={{ m: 0.5 }}
                        onClick={() => {
                          const currentCategories = preferences.categories || ['all'];
                          let newCategories;
                          
                          if (currentCategories.includes('all')) {
                            newCategories = [category];
                          } else if (currentCategories.includes(category)) {
                            newCategories = currentCategories.filter(c => c !== category);
                            if (newCategories.length === 0) newCategories = ['all'];
                          } else {
                            newCategories = [...currentCategories, category];
                          }
                          
                          handlePreferenceChange('categories', newCategories);
                        }}
                      />
                    ))}
                  </Box>

                  <Button
                    variant="contained"
                    fullWidth
                    onClick={triggerAnalysis}
                    disabled={updating}
                    startIcon={<PsychologyIcon />}
                    sx={{ mt: 2 }}
                  >
                    {updating ? 'Analyzing...' : 'Trigger New Analysis'}
                  </Button>

                  <Button
                    variant="outlined"
                    fullWidth
                    onClick={async () => {
                      try {
                        const response = await api.post('/api/smart-assistant/test/create-insights');
                        console.log('Test insights created:', response.data);
                        toast.success('Test insights created! Refreshing data...');
                        await loadDashboardData();
                      } catch (error) {
                        console.error('Error creating test insights:', error);
                        toast.error('Failed to create test insights: ' + (error.response?.data?.message || error.message));
                      }
                    }}
                    sx={{ mt: 1 }}
                  >
                    Create Test Insights
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Recent Insights */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Insights
              </Typography>
              
              {insights.length > 0 ? (
                <List dense>
                  {insights.map((insight) => (
                    <ListItem key={insight.id} divider>
                      <ListItemIcon>
                        {insight.status === 'acted_upon' ? (
                          <ThumbUpIcon color="success" fontSize="small" />
                        ) : insight.status === 'dismissed' ? (
                          <ThumbDownIcon color="error" fontSize="small" />
                        ) : (
                          <VisibilityIcon color="primary" fontSize="small" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={insight.title}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {formatCategoryName(insight.category)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(insight.createdAt).toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Alert severity="info">
                  No recent insights. Trigger an analysis to get personalized suggestions!
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SmartAssistantDashboard;
