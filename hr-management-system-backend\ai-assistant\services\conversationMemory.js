/**
 * Advanced Conversation Memory System
 * Maintains context, remembers user preferences, and enables natural dialogue flow
 */

class ConversationMemory {
  constructor() {
    this.initializeMemoryStructures();
    this.initializeContextTracking();
  }

  /**
   * Initialize memory data structures
   */
  initializeMemoryStructures() {
    // Short-term memory (current conversation)
    this.shortTermMemory = new Map();

    // Long-term memory (persistent user data)
    this.longTermMemory = new Map();

    // Context memory (conversation flow and topics)
    this.contextMemory = new Map();

    // Emotional memory (user's emotional patterns)
    this.emotionalMemory = new Map();
  }

  /**
   * Initialize context tracking patterns
   */
  initializeContextTracking() {
    this.contextPatterns = {
      // Topic transitions
      topicShifts: {
        'work_to_personal': ['anyway', 'by the way', 'speaking of', 'on another note'],
        'problem_to_solution': ['so', 'now', 'what should', 'how can'],
        'past_to_present': ['now', 'currently', 'these days', 'lately'],
        'general_to_specific': ['specifically', 'in particular', 'for example', 'like']
      },

      // Reference patterns
      references: {
        'previous_conversation': ['like we talked about', 'as I mentioned', 'remember when', 'last time'],
        'current_topic': ['this', 'that', 'it', 'the issue', 'the problem'],
        'future_plans': ['next', 'later', 'tomorrow', 'soon', 'planning to']
      },

      // Emotional continuity
      emotionalContinuity: {
        'mood_check': ['how are you feeling', 'still feeling', 'feeling better', 'mood'],
        'emotional_reference': ['like before', 'still upset', 'much happier', 'same way']
      }
    };
  }

  /**
   * Store conversation turn in memory
   */
  storeConversationTurn(userId, userMessage, assistantResponse, metadata = {}) {
    if (!userId) return;

    // Initialize user memory if not exists
    if (!this.shortTermMemory.has(userId)) {
      this.initializeUserMemory(userId);
    }

    const timestamp = new Date();
    const turn = {
      id: this.generateTurnId(),
      timestamp: timestamp,
      userMessage: userMessage,
      assistantResponse: assistantResponse,
      metadata: {
        emotion: metadata.emotion,
        intent: metadata.intent,
        topics: metadata.topics || [],
        entities: metadata.entities || [],
        confidence: metadata.confidence,
        ...metadata
      }
    };

    // Store in short-term memory
    const shortTerm = this.shortTermMemory.get(userId);
    shortTerm.conversationTurns.push(turn);

    // Keep only last 20 turns in short-term memory
    if (shortTerm.conversationTurns.length > 20) {
      shortTerm.conversationTurns = shortTerm.conversationTurns.slice(-20);
    }

    // Update context tracking
    this.updateContextTracking(userId, turn);

    // Update emotional memory
    this.updateEmotionalMemory(userId, metadata.emotion, userMessage);

    // Update long-term memory patterns
    this.updateLongTermMemoryPatterns(userId, turn);

    this.shortTermMemory.set(userId, shortTerm);
  }

  /**
   * Initialize memory structures for a new user
   */
  initializeUserMemory(userId) {
    // Short-term memory
    this.shortTermMemory.set(userId, {
      conversationTurns: [],
      currentTopics: [],
      activeContext: {},
      sessionStart: new Date(),
      lastInteraction: new Date()
    });

    // Context memory
    this.contextMemory.set(userId, {
      topicHistory: [],
      entityMentions: new Map(),
      conversationFlow: [],
      unresolved_questions: [],
      pending_followups: []
    });

    // Emotional memory
    this.emotionalMemory.set(userId, {
      emotionalHistory: [],
      dominantEmotions: {},
      emotionalTriggers: {},
      supportNeeds: [],
      positivePatterns: [],
      concernPatterns: []
    });

    // Long-term memory
    if (!this.longTermMemory.has(userId)) {
      this.longTermMemory.set(userId, {
        preferences: {},
        personalInfo: {},
        workPatterns: {},
        communicationStyle: 'neutral',
        frequentTopics: {},
        relationshipBuilding: {
          rapport: 0,
          trust: 0,
          familiarity: 0
        }
      });
    }
  }

  /**
   * Get conversation context for response generation
   */
  getConversationContext(userId, lookbackTurns = 5) {
    if (!userId || !this.shortTermMemory.has(userId)) {
      return this.getDefaultContext();
    }

    const shortTerm = this.shortTermMemory.get(userId);
    const contextMem = this.contextMemory.get(userId);
    const emotionalMem = this.emotionalMemory.get(userId);
    const longTerm = this.longTermMemory.get(userId);

    // Get recent conversation turns
    const recentTurns = shortTerm.conversationTurns.slice(-lookbackTurns);

    // Extract current topics and entities
    const currentTopics = this.extractCurrentTopics(recentTurns);
    const mentionedEntities = this.extractMentionedEntities(recentTurns);

    // Analyze conversation flow
    const conversationFlow = this.analyzeConversationFlow(recentTurns);

    // Get emotional context
    const emotionalContext = this.getEmotionalContext(userId);

    // Check for unresolved items
    const unresolvedItems = this.getUnresolvedItems(userId);

    return {
      // Recent conversation
      recentTurns: recentTurns,
      currentTopics: currentTopics,
      mentionedEntities: mentionedEntities,

      // Conversation flow
      conversationFlow: conversationFlow,
      sessionDuration: Date.now() - shortTerm.sessionStart.getTime(),
      turnCount: shortTerm.conversationTurns.length,

      // Emotional context
      emotionalContext: emotionalContext,

      // User patterns
      communicationStyle: longTerm.communicationStyle,
      preferences: longTerm.preferences,
      relationshipLevel: longTerm.relationshipBuilding,

      // Pending items
      unresolvedItems: unresolvedItems,
      pendingFollowups: contextMem.pending_followups,

      // Memory insights
      memoryInsights: this.generateMemoryInsights(userId)
    };
  }

  /**
   * Update context tracking based on conversation turn
   */
  updateContextTracking(userId, turn) {
    const contextMem = this.contextMemory.get(userId);

    // Track topic progression
    if (turn.metadata.topics && turn.metadata.topics.length > 0) {
      contextMem.topicHistory.push({
        topics: turn.metadata.topics,
        timestamp: turn.timestamp,
        transition: this.detectTopicTransition(userId, turn.metadata.topics)
      });
    }

    // Track entity mentions
    if (turn.metadata.entities) {
      turn.metadata.entities.forEach(entity => {
        if (!contextMem.entityMentions.has(entity.type)) {
          contextMem.entityMentions.set(entity.type, []);
        }
        contextMem.entityMentions.get(entity.type).push({
          value: entity.value,
          timestamp: turn.timestamp,
          context: turn.userMessage.substring(0, 100)
        });
      });
    }

    // Track conversation flow patterns
    contextMem.conversationFlow.push({
      turnId: turn.id,
      userIntent: turn.metadata.intent,
      emotion: turn.metadata.emotion,
      responseType: this.classifyResponseType(turn.assistantResponse),
      timestamp: turn.timestamp
    });

    // Detect unresolved questions or concerns
    if (this.hasUnresolvedQuestion(turn.userMessage)) {
      contextMem.unresolved_questions.push({
        question: turn.userMessage,
        timestamp: turn.timestamp,
        turnId: turn.id
      });
    }

    this.contextMemory.set(userId, contextMem);
  }

  /**
   * Update emotional memory patterns
   */
  updateEmotionalMemory(userId, emotion, message) {
    if (!emotion) return;

    const emotionalMem = this.emotionalMemory.get(userId);

    // Add to emotional history
    emotionalMem.emotionalHistory.push({
      emotion: emotion,
      timestamp: new Date(),
      context: message.substring(0, 100),
      intensity: this.estimateEmotionalIntensity(message, emotion)
    });

    // Keep only last 50 emotional events
    if (emotionalMem.emotionalHistory.length > 50) {
      emotionalMem.emotionalHistory = emotionalMem.emotionalHistory.slice(-50);
    }

    // Update dominant emotions
    emotionalMem.dominantEmotions[emotion] = (emotionalMem.dominantEmotions[emotion] || 0) + 1;

    // Detect emotional triggers
    this.detectEmotionalTriggers(userId, emotion, message);

    // Track support needs
    if (['sadness', 'anxiety', 'frustration'].includes(emotion)) {
      emotionalMem.supportNeeds.push({
        emotion: emotion,
        timestamp: new Date(),
        context: message.substring(0, 100)
      });
    }

    // Track positive patterns
    if (['joy', 'gratitude', 'excitement'].includes(emotion)) {
      emotionalMem.positivePatterns.push({
        emotion: emotion,
        timestamp: new Date(),
        context: message.substring(0, 100)
      });
    }

    this.emotionalMemory.set(userId, emotionalMem);
  }

  /**
   * Generate contextual suggestions based on memory
   */
  generateContextualSuggestions(userId, currentEmotion, currentIntent) {
    const context = this.getConversationContext(userId, 3);
    const suggestions = [];

    // Based on emotional context
    if (currentEmotion === 'sadness' && context.emotionalContext.recentSupport) {
      suggestions.push("Continue our conversation about how you're feeling");
      suggestions.push("Explore additional support resources");
    }

    // Based on unresolved items
    if (context.unresolvedItems.length > 0) {
      suggestions.push(`Follow up on: ${context.unresolvedItems[0].topic}`);
    }

    // Based on conversation flow
    if (context.conversationFlow.pattern === 'problem_solving') {
      suggestions.push("Let's continue working on this together");
      suggestions.push("Explore alternative solutions");
    }

    // Based on user patterns
    const longTerm = this.longTermMemory.get(userId);
    if (longTerm && longTerm.frequentTopics) {
      const topTopics = Object.entries(longTerm.frequentTopics)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 2);

      topTopics.forEach(([topic]) => {
        suggestions.push(`Check on your ${topic.replace('_', ' ')}`);
      });
    }

    return suggestions.slice(0, 4); // Return top 4 suggestions
  }

  /**
   * Check if user is referring to previous conversation
   */
  isReferencingPrevious(message, userId) {
    const lowerMessage = message.toLowerCase();

    // Check for reference patterns
    const referencePatterns = [
      'like we talked about', 'as we discussed', 'remember when',
      'last time', 'earlier', 'before', 'previously',
      'that thing', 'the issue', 'what we said'
    ];

    const hasReference = referencePatterns.some(pattern =>
      lowerMessage.includes(pattern)
    );

    if (hasReference && userId) {
      return this.findReferencedContent(userId, message);
    }

    return null;
  }

  /**
   * Find what the user is referencing from previous conversation
   */
  findReferencedContent(userId, message) {
    const shortTerm = this.shortTermMemory.get(userId);
    if (!shortTerm) return null;

    const recentTurns = shortTerm.conversationTurns.slice(-10);

    // Simple keyword matching for now
    const messageWords = message.toLowerCase().split(' ');
    let bestMatch = null;
    let bestScore = 0;

    recentTurns.forEach(turn => {
      const turnWords = turn.userMessage.toLowerCase().split(' ');
      const commonWords = messageWords.filter(word =>
        turnWords.includes(word) && word.length > 3
      );

      const score = commonWords.length / Math.max(messageWords.length, turnWords.length);

      if (score > bestScore && score > 0.2) {
        bestScore = score;
        bestMatch = {
          turn: turn,
          score: score,
          commonWords: commonWords
        };
      }
    });

    return bestMatch;
  }

  /**
   * Helper methods
   */
  generateTurnId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  getDefaultContext() {
    return {
      recentTurns: [],
      currentTopics: [],
      mentionedEntities: [],
      conversationFlow: { pattern: 'new', stage: 'initiation' },
      emotionalContext: { current: 'neutral', recent: [] },
      unresolvedItems: [],
      memoryInsights: { isNewUser: true }
    };
  }

  extractCurrentTopics(turns) {
    const topics = [];
    turns.forEach(turn => {
      if (turn.metadata.topics) {
        topics.push(...turn.metadata.topics);
      }
    });
    return [...new Set(topics)]; // Remove duplicates
  }

  extractMentionedEntities(turns) {
    const entities = new Map();
    turns.forEach(turn => {
      if (turn.metadata.entities) {
        turn.metadata.entities.forEach(entity => {
          if (!entities.has(entity.type)) {
            entities.set(entity.type, []);
          }
          entities.get(entity.type).push(entity.value);
        });
      }
    });
    return entities;
  }

  analyzeConversationFlow(turns) {
    if (turns.length === 0) {
      return { pattern: 'new', stage: 'initiation' };
    }

    const intents = turns.map(turn => turn.metadata.intent).filter(Boolean);
    const emotions = turns.map(turn => turn.metadata.emotion).filter(Boolean);

    // Determine conversation pattern
    let pattern = 'exploratory';
    if (intents.length > 0) {
      const uniqueIntents = new Set(intents);
      if (uniqueIntents.size === 1) {
        pattern = 'focused';
      } else if (this.hasProgressiveIntents(intents)) {
        pattern = 'problem_solving';
      }
    }

    // Determine conversation stage
    let stage = 'exploration';
    if (turns.length > 5) {
      stage = 'deep_conversation';
    } else if (turns.length > 2) {
      stage = 'engagement';
    }

    return { pattern, stage, intents, emotions };
  }

  getEmotionalContext(userId) {
    const emotionalMem = this.emotionalMemory.get(userId);
    if (!emotionalMem) {
      return { current: 'neutral', recent: [], needsSupport: false };
    }

    const recentEmotions = emotionalMem.emotionalHistory.slice(-5);
    const currentEmotion = recentEmotions.length > 0 ?
      recentEmotions[recentEmotions.length - 1].emotion : 'neutral';

    const needsSupport = emotionalMem.supportNeeds.length > 0 &&
      (Date.now() - emotionalMem.supportNeeds[emotionalMem.supportNeeds.length - 1].timestamp) < 3600000; // 1 hour

    return {
      current: currentEmotion,
      recent: recentEmotions,
      needsSupport: needsSupport,
      recentSupport: needsSupport
    };
  }

  getUnresolvedItems(userId) {
    const contextMem = this.contextMemory.get(userId);
    if (!contextMem) return [];

    // Return recent unresolved questions
    return contextMem.unresolved_questions
      .filter(item => (Date.now() - item.timestamp) < 3600000) // Last hour
      .map(item => ({
        topic: this.extractTopicFromQuestion(item.question),
        timestamp: item.timestamp
      }));
  }

  generateMemoryInsights(userId) {
    const longTerm = this.longTermMemory.get(userId);
    const shortTerm = this.shortTermMemory.get(userId);

    if (!longTerm || !shortTerm) {
      return { isNewUser: true };
    }

    return {
      isNewUser: shortTerm.conversationTurns.length < 3,
      isReturningUser: longTerm.relationshipBuilding.familiarity > 0.5,
      conversationCount: shortTerm.conversationTurns.length,
      relationshipLevel: longTerm.relationshipBuilding.rapport,
      preferredStyle: longTerm.communicationStyle
    };
  }

  // Additional helper methods
  detectTopicTransition(userId, newTopics) {
    const contextMem = this.contextMemory.get(userId);
    if (!contextMem || contextMem.topicHistory.length === 0) {
      return 'initial';
    }

    const lastTopics = contextMem.topicHistory[contextMem.topicHistory.length - 1].topics;
    const hasCommonTopics = newTopics.some(topic => lastTopics.includes(topic));

    return hasCommonTopics ? 'continuation' : 'shift';
  }

  classifyResponseType(response) {
    if (response.includes('?')) return 'question';
    if (response.includes('I understand') || response.includes('I can see')) return 'empathetic';
    if (response.includes('Let me help') || response.includes('I can help')) return 'supportive';
    return 'informative';
  }

  hasUnresolvedQuestion(message) {
    const questionPatterns = [
      /how do i/i, /what should/i, /can you help/i, /i need to/i,
      /how can/i, /what if/i, /is there a way/i
    ];
    return questionPatterns.some(pattern => pattern.test(message));
  }

  estimateEmotionalIntensity(message, emotion) {
    const intensityMarkers = {
      high: ['very', 'extremely', 'really', 'so', 'totally', '!!!', 'CAPS'],
      medium: ['quite', 'pretty', 'somewhat'],
      low: ['a bit', 'slightly', 'kind of']
    };

    const lowerMessage = message.toLowerCase();

    if (intensityMarkers.high.some(marker =>
      lowerMessage.includes(marker) || message.includes('!!!')
    )) {
      return 'high';
    }

    if (intensityMarkers.medium.some(marker => lowerMessage.includes(marker))) {
      return 'medium';
    }

    return 'low';
  }

  detectEmotionalTriggers(userId, emotion, message) {
    // Simple trigger detection - could be enhanced with ML
    const emotionalMem = this.emotionalMemory.get(userId);

    if (['sadness', 'anxiety', 'frustration'].includes(emotion)) {
      const keywords = message.toLowerCase().split(' ').filter(word => word.length > 3);
      keywords.forEach(keyword => {
        if (!emotionalMem.emotionalTriggers[keyword]) {
          emotionalMem.emotionalTriggers[keyword] = 0;
        }
        emotionalMem.emotionalTriggers[keyword]++;
      });
    }
  }

  hasProgressiveIntents(intents) {
    const progressivePatterns = [
      ['help_general', 'task_list', 'task_create'],
      ['greeting', 'leave_balance', 'leave_request'],
      ['policy_query', 'help_general', 'task_create']
    ];

    return progressivePatterns.some(pattern =>
      pattern.every(intent => intents.includes(intent))
    );
  }

  extractTopicFromQuestion(question) {
    // Simple topic extraction
    const topicKeywords = {
      'leave': ['leave', 'vacation', 'time off'],
      'tasks': ['task', 'assignment', 'work'],
      'attendance': ['attendance', 'check in', 'hours'],
      'benefits': ['benefits', 'insurance', 'health']
    };

    const lowerQuestion = question.toLowerCase();

    for (const [topic, keywords] of Object.entries(topicKeywords)) {
      if (keywords.some(keyword => lowerQuestion.includes(keyword))) {
        return topic;
      }
    }

    return 'general';
  }

  /**
   * Update long-term memory patterns
   * @param {string} userId - User ID
   * @param {Object} turn - Conversation turn
   */
  updateLongTermMemoryPatterns(userId, turn) {
    if (!this.longTermMemory.has(userId)) {
      this.initializeUserMemory(userId);
    }

    const longTerm = this.longTermMemory.get(userId);

    // Update frequent topics
    if (turn.metadata.topics) {
      turn.metadata.topics.forEach(topic => {
        longTerm.frequentTopics[topic] = (longTerm.frequentTopics[topic] || 0) + 1;
      });
    }

    // Update communication style
    if (turn.userMessage) {
      longTerm.communicationStyle = this.detectCommunicationStyleFromMessage(turn.userMessage);
    }

    // Update relationship building
    longTerm.relationshipBuilding.familiarity += 0.1;
    if (turn.metadata.emotion === 'gratitude') {
      longTerm.relationshipBuilding.rapport += 0.2;
    }
    if (turn.metadata.confidence > 0.8) {
      longTerm.relationshipBuilding.trust += 0.1;
    }

    // Cap values at 1.0
    Object.keys(longTerm.relationshipBuilding).forEach(key => {
      longTerm.relationshipBuilding[key] = Math.min(longTerm.relationshipBuilding[key], 1.0);
    });

    this.longTermMemory.set(userId, longTerm);
  }

  /**
   * Detect communication style from message
   * @param {string} message - User message
   * @returns {string} - Communication style
   */
  detectCommunicationStyleFromMessage(message) {
    const formalIndicators = ['please', 'thank you', 'could you', 'would you', 'i would like'];
    const casualIndicators = ['hey', 'hi', 'thanks', 'ok', 'yeah', 'whats up'];

    const lowerMessage = message.toLowerCase();
    const formalCount = formalIndicators.filter(indicator => lowerMessage.includes(indicator)).length;
    const casualCount = casualIndicators.filter(indicator => lowerMessage.includes(indicator)).length;

    if (formalCount > casualCount) return 'formal';
    if (casualCount > formalCount) return 'casual';
    return 'neutral';
  }
}

// Singleton instance
const conversationMemory = new ConversationMemory();

module.exports = conversationMemory;
