const express = require('express');
const router = express.Router();
const Report = require('../models/Report');
const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');

// Create a new report
router.post('/', authenticate, async (req, res) => {
  try {
    const { title, reportType, reportData, applicationId, timestamp } = req.body;

    // Validate required fields
    if (!title || !reportType || !reportData) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Check if a similar report already exists
    let existingReport = null;

    // If this is an NLP report with an applicationId, check if we already have a report for this application
    if (reportType === 'nlp' && applicationId) {
      existingReport = await Report.findOne({
        reportType: 'nlp',
        applicationId: applicationId
      });

      if (existingReport) {
        console.log('Found existing NLP report for application:', applicationId);

        // Update the existing report instead of creating a new one
        existingReport.reportData = reportData;
        existingReport.timestamp = timestamp || new Date().toISOString();

        // Save the updated report
        await existingReport.save();

        return res.status(200).json({
          message: 'Report updated successfully',
          report: {
            id: existingReport._id,
            title: existingReport.title,
            reportType: existingReport.reportType,
            createdAt: existingReport.createdAt,
            timestamp: existingReport.timestamp
          }
        });
      }
    }

    // Create a new report if no existing report was found
    const report = new Report({
      title,
      reportType,
      reportData,
      applicationId,
      timestamp: timestamp || new Date().toISOString(),
      createdBy: req.user.id // Get the user ID from the auth middleware
    });

    // Save the report
    await report.save();

    res.status(201).json({
      message: 'Report saved successfully',
      report: {
        id: report._id,
        title: report.title,
        reportType: report.reportType,
        createdAt: report.createdAt,
        timestamp: report.timestamp
      }
    });
  } catch (error) {
    console.error('Error saving report:', error);
    res.status(500).json({
      message: 'Server error while saving report',
      error: error.message
    });
  }
});

// Get all reports (with optional filtering by type)
router.get('/', authenticate, async (req, res) => {
  try {
    const { reportType } = req.query;

    // Build query based on filters
    const query = {};

    // Filter by report type if provided
    if (reportType) {
      query.reportType = reportType;
    }

    // Get reports
    const reports = await Report.find(query)
      .sort({ createdAt: -1 }) // Sort by creation date, newest first
      .limit(100); // Limit to prevent excessive data transfer

    res.status(200).json(reports);
  } catch (error) {
    console.error('Error fetching reports:', error);
    res.status(500).json({
      message: 'Server error while fetching reports',
      error: error.message
    });
  }
});

// Get a specific report by ID - this must come AFTER any other GET routes with specific paths
// but BEFORE the /:id route to avoid conflicts
router.get('/by-application/:applicationId', authenticate, async (req, res) => {
  try {
    console.log('Fetching report by application ID:', req.params.applicationId);
    const { applicationId } = req.params;

    if (!applicationId) {
      return res.status(400).json({ message: 'Application ID is required' });
    }

    // Find the most recent NLP report for this application
    const report = await Report.findOne({
      applicationId: applicationId,
      reportType: 'nlp'
    }).sort({ createdAt: -1 });

    if (!report) {
      console.log('No report found for application ID:', applicationId);
      return res.status(404).json({ message: 'No report found for this application' });
    }

    console.log('Found report for application ID:', applicationId, 'Report ID:', report._id);
    res.status(200).json(report);
  } catch (error) {
    console.error('Error fetching report by application ID:', error);
    res.status(500).json({
      message: 'Server error while fetching report by application ID',
      error: error.message
    });
  }
});

// Get a specific report by ID - this must come AFTER any other GET routes with specific paths
router.get('/:id', authenticate, async (req, res) => {
  try {
    console.log('Fetching report by ID:', req.params.id);
    const report = await Report.findById(req.params.id);

    if (!report) {
      console.log('No report found for ID:', req.params.id);
      return res.status(404).json({ message: 'Report not found' });
    }

    console.log('Found report with ID:', req.params.id);
    res.status(200).json(report);
  } catch (error) {
    console.error('Error fetching report:', error);
    res.status(500).json({
      message: 'Server error while fetching report',
      error: error.message
    });
  }
});

// Delete a report
router.delete('/:id', authenticate, authorizeRoles('hr', 'admin'), async (req, res) => {
  try {
    const report = await Report.findById(req.params.id);

    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }

    await Report.deleteOne({ _id: req.params.id });

    res.status(200).json({ message: 'Report deleted successfully' });
  } catch (error) {
    console.error('Error deleting report:', error);
    res.status(500).json({
      message: 'Server error while deleting report',
      error: error.message
    });
  }
});

module.exports = router;
