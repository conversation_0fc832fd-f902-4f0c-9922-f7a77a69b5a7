const axios = require('axios');

async function debugAI() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test AI chat with detailed logging
    console.log('\n🤖 Testing AI Assistant...');
    
    try {
      const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'Hello!'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📦 Full Response:', JSON.stringify(response.data, null, 2));
      
    } catch (error) {
      console.error('❌ Chat error details:');
      console.error('Status:', error.response?.status);
      console.error('Status Text:', error.response?.statusText);
      console.error('Data:', error.response?.data);
      console.error('Message:', error.message);
    }

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

debugAI();
