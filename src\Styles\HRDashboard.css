/* File: src/styles/HRDashboard.css */

body, html, #root {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  font-family: Arial, sans-serif;
}

.dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

.dashboard-header {
  background-color: #2c3e50;
  color: white;
  padding: 20px;
  text-align: center;
}

.dashboard-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 80px);
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background-color: #34495e;
  color: white;
  padding: 15px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sidebar h2 {
  margin-bottom: 20px;
}

.sidebar button {
  background-color: #2c3e50;
  color: white;
  border: none;
  padding: 10px;
  text-align: left;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sidebar button:hover {
  background-color: #1a252f;
}

.logout-btn {
  background-color: #c0392b;
  margin-top: auto;
}

.logout-btn:hover {
  background-color: #a93226;
}

.main-section {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f6fa;
  box-sizing: content-box;
  width: 80%;
  margin-left: 270px;
}

.main-section h2 {
  margin-bottom: 20px;
}

.table-container {
  overflow-x: auto;
  max-width: 100%;
}

table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  table-layout: auto;
}

th, td {
  padding: 8px;
  border: 1px solid #ddd;
  text-align: left;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

/* Actions column should have more space and wrap content */
td:last-child {
  white-space: normal;
  max-width: 250px;
  min-width: 200px;
}

th {
  background-color: #ecf0f1;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Special column widths */
table tr th:nth-child(5),
table tr td:nth-child(5),
table tr th:nth-child(6),
table tr td:nth-child(6),
table tr th:nth-child(7),
table tr td:nth-child(7) {
  max-width: 100px;
}

/* Reason column can be wider */
table tr th:nth-child(7),
table tr td:nth-child(7) {
  max-width: 200px;
}

.edit-btn {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.edit-btn:hover {
  background-color: #1e8449;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.delete-btn:hover {
  background-color: #c0392b;
}

/* Evaluate button */
.evaluate-btn {
  background-color: #f39c12;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.evaluate-btn:hover {
  background-color: #d35400;
}

/* Attendance button */
.attendance-btn {
  background-color: #1abc9c;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.attendance-btn:hover {
  background-color: #16a085;
}

/* NLP button */
.nlp-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.nlp-btn:hover {
  background-color: #2980b9;
}

/* View button */
.view-btn {
  background-color: #9b59b6;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.view-btn:hover {
  background-color: #8e44ad;
}

/* Approve button */
.approve-btn {
  background-color: #2ecc71;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.approve-btn:hover {
  background-color: #27ae60;
}

/* Reject button */
.reject-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
}

.reject-btn:hover {
  background-color: #c0392b;
}

/* ✅ Added Jobs Section Styles */

.jobs-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.add-job-btn {
  background-color: #2980b9;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-job-btn:hover {
  background-color: #1f618d;
}

.add-job-form {
  background-color: white;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 5px;
  max-width: 600px;
  margin-bottom: 20px;
}

.add-job-form label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
}

.add-job-form input,
.add-job-form textarea {
  width: 100%;
  padding: 10px;
  margin-top: 4px;
  margin-bottom: 15px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
}

.add-job-form button[type="submit"] {
  background-color: #27ae60;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-job-form button[type="submit"]:hover {
  background-color: #1e8449;
}

/* Updated Job Form Layout */
.add-job-form input, .add-job-form textarea {
  min-height: 40px;
}

.add-job-form button[type="submit"]:focus {
  outline: none;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.modal-content h2 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.form-buttons button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-buttons button[type="submit"] {
  background-color: #27ae60;
  color: white;
}

.form-buttons button[type="submit"]:hover {
  background-color: #1e8449;
}

.form-buttons button[type="button"] {
  background-color: #7f8c8d;
  color: white;
}

.form-buttons button[type="button"]:hover {
  background-color: #6c7a7d;
}

/* CV Modal Styles */
.cv-modal {
  width: 90%;
  max-width: 800px;
}

.cv-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.cv-path-info {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
  padding: 5px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  word-break: break-all;
}

.iframe-container {
  position: relative;
  width: 100%;
  height: 500px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.pdf-viewer {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pdf-fallback {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
  text-align: center;
  background-color: #f8f8f8;
}

.iframe-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.iframe-error {
  background-color: white;
  padding: 20px;
  border-radius: 5px;
  text-align: center;
  max-width: 80%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.iframe-error h3 {
  color: #e74c3c;
  margin-top: 0;
}

.iframe-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

.iframe-action-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.iframe-action-btn:hover {
  background-color: #2980b9;
}

.cv-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.download-btn {
  background-color: #3498db;
  color: white;
  padding: 8px 15px;
  text-decoration: none;
  border-radius: 4px;
  display: inline-block;
  font-size: 14px;
}

.download-btn:hover {
  background-color: #2980b9;
  text-decoration: none;
  color: white;
}

/* NLP Results Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f8f9fa;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Processing indicator styles */
.processing-indicator {
  text-align: center;
  padding: 40px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin: 20px auto;
  max-width: 600px;
}

.processing-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #e53935;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 2s linear infinite;
  margin: 0 auto 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-steps {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.step:not(:last-child):after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.step.active:not(:last-child):after {
  background-color: #e53935;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

.step.active .step-number {
  background-color: #e53935;
}

.step-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.step.active .step-label {
  color: #e53935;
  font-weight: bold;
}

/* NLP Results Content Styles */
.nlp-results h3 {
  font-size: 22px;
  margin-top: 30px;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 2px solid #e53935;
  padding-bottom: 8px;
}

.nlp-results .match-score {
  font-size: 48px;
  font-weight: bold;
  color: #e53935;
}

.nlp-results .match-label {
  font-size: 16px;
  color: #666;
  margin-top: 5px;
}

.nlp-results .section {
  margin-bottom: 30px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.nlp-results .section-title {
  font-size: 20px;
  font-weight: bold;
  color: #e53935;
  margin-bottom: 15px;
}

.nlp-results .skill-item {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: inline-block;
  font-size: 14px;
}

.nlp-results .skill-match {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.nlp-results .skill-missing {
  background-color: #ffebee;
  color: #c62828;
}

/* Print styles for NLP report */
@media print {
  body * {
    visibility: hidden;
  }

  .modal-overlay,
  .nlp-results,
  .nlp-results * {
    visibility: visible;
  }

  .modal-overlay {
    position: absolute;
    left: 0;
    top: 0;
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
    background: white !important;
    z-index: 9999;
  }

  .modal-overlay > div:first-child,
  .modal-overlay > div:last-child {
    display: none !important;
  }

  .nlp-results {
    padding: 0 !important;
    overflow: visible !important;
    max-height: none !important;
    margin-bottom: 50px !important;
  }
}

/* Processing indicator */
.processing-indicator {
  text-align: center;
  padding: 40px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.processing-indicator h3 {
  color: #3498db;
  margin-bottom: 20px;
  font-size: 22px;
}

.processing-indicator p {
  color: #7f8c8d;
  margin-bottom: 30px;
  font-size: 16px;
}

.processing-spinner {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 2s linear infinite;
  margin: 0 auto 30px;
}

/* Processing steps */
.processing-steps {
  display: flex;
  justify-content: space-between;
  max-width: 700px;
  margin: 30px auto 0;
  position: relative;
}

.processing-steps::before {
  content: "";
  position: absolute;
  top: 25px;
  left: 50px;
  right: 50px;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  width: 100px;
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #7f8c8d;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 14px;
  color: #7f8c8d;
  text-align: center;
}

.step.active .step-number {
  background-color: #3498db;
  color: white;
  box-shadow: 0 0 0 5px rgba(52, 152, 219, 0.2);
}

.step.active .step-label {
  color: #3498db;
  font-weight: bold;
}

.nlp-results {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.nlp-results ul {
  margin-top: 5px;
  padding-left: 20px;
}

.nlp-results li {
  margin-bottom: 5px;
}

/* Error container styles */
.error-container {
  background-color: #ffebee;
  border-left: 4px solid #f44336;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.error-container p {
  margin: 5px 0;
  color: #d32f2f;
}

/* Processing container styles */
.processing-container {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.processing-container p {
  margin: 5px 0;
  color: #0d47a1;
  text-align: center;
}

/* Spinner animation */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #2196f3;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* NLP Results Enhanced Styles */
.section-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.section-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #2c3e50;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.section-container h4 {
  margin-top: 15px;
  margin-bottom: 10px;
  color: #34495e;
}

/* Summary Report Styles */
.summary-report {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.summary-report h3 {
  margin-top: 0;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 15px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.report-content {
  line-height: 1.6;
}

.report-list-item {
  margin: 5px 0 5px 20px;
  position: relative;
}

.report-list-item::before {
  content: "•";
  position: absolute;
  left: -15px;
  color: #3498db;
}

/* Job Match Styles */
.job-match {
  background-color: #f5f9ff;
}

.match-score-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.match-score-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.match-score {
  font-size: 24px;
  font-weight: bold;
  color: #2c3e50;
}

/* Report Header Styles */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.candidate-info {
  flex: 1;
}

.candidate-name {
  font-size: 24px;
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.contact-details {
  display: flex;
  gap: 15px;
  color: #7f8c8d;
  font-size: 14px;
}

.contact-details p {
  margin: 0;
  display: flex;
  align-items: center;
}

.contact-details i {
  margin-right: 5px;
  color: #3498db;
}

.match-score-card {
  text-align: center;
  padding: 10px;
}

.match-label {
  margin-top: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #7f8c8d;
}

/* Section Container Styles */
.section-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 18px;
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ecf0f1;
}

/* Executive Summary Styles */
.executive-summary {
  background-color: #f8f9fa;
}

.summary-content {
  line-height: 1.6;
}

.match-analysis-box {
  background-color: #f1f8e9;
  border-left: 4px solid #8bc34a;
  padding: 15px;
  margin-top: 15px;
  border-radius: 4px;
}

.match-analysis {
  margin: 0;
  line-height: 1.6;
  color: #37474f;
  font-style: italic;
}

.semantic-score {
  font-weight: bold;
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* Qualifications Analysis Styles */
.qualifications-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.qualifications-column {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.column-title {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 15px;
  text-align: center;
}

.matched-title {
  color: #2e7d32;
}

.missing-title {
  color: #c62828;
}

.no-skills {
  text-align: center;
  color: #9e9e9e;
  font-style: italic;
}

/* Candidate Profile Styles */
.profile-section {
  margin-bottom: 20px;
}

.profile-section:last-child {
  margin-bottom: 0;
}

.profile-subtitle {
  font-size: 16px;
  color: #3498db;
  margin-top: 0;
  margin-bottom: 10px;
}

.no-data {
  color: #9e9e9e;
  font-style: italic;
}

/* Skills Tags Styles */
.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.skills-list.categorized {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 6px;
}

.skill-tag {
  background-color: #e0e0e0;
  color: #333;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 13px;
  display: inline-block;
  transition: all 0.2s ease;
}

.skill-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.matched-skill {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.missing-skill {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ef9a9a;
}

/* Education and Experience Lists */
.education-list, .experience-list {
  margin: 0;
  padding-left: 20px;
}

.education-item, .experience-item {
  margin-bottom: 10px;
  line-height: 1.6;
  position: relative;
}

.education-item:last-child, .experience-item:last-child {
  margin-bottom: 0;
}

/* Extracted Text Preview */
.extracted-text {
  background-color: #f5f5f5;
}

.text-preview {
  max-height: 200px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

/* Match score badge styles */
.match-score-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 24px;
  border-radius: 12px;
  color: white;
  font-weight: bold;
  font-size: 12px;
  padding: 0 8px;
  margin: 0 5px;
}

.status-pending {
  background-color: #fff3e0;
  color: #e65100;
}

.status-approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-rejected {
  background-color: #ffebee;
  color: #c62828;
}

/* Additional Button Styles */
.view-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 0;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  min-width: 70px;
  text-align: center;
}

.view-btn:hover {
  background-color: #2980b9;
}

.nlp-btn {
  background-color: #9b59b6;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 0;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  min-width: 70px;
  text-align: center;
}

.nlp-btn:hover {
  background-color: #8e44ad;
}

/* Report button */
.report-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 0;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  min-width: 70px;
  text-align: center;
}

.report-btn:hover {
  background-color: #2980b9;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  margin-bottom: 5px;
}

/* Approve and Reject buttons */
.approve-btn {
  background-color: #2ecc71;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 0;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  min-width: 70px;
  text-align: center;
}

.approve-btn:hover {
  background-color: #27ae60;
}

.reject-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 0;
  border-radius: 3px;
  cursor: pointer;
  display: inline-block;
  min-width: 70px;
  text-align: center;
}

.reject-btn:hover {
  background-color: #c0392b;
}

/* Attendance button */
.attendance-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.attendance-btn:hover {
  background-color: #2980b9;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .dashboard-content {
    flex-direction: column;
  }

  .sidebar {
    width: 200px;
    margin-bottom: 15px;
  }

  .main-section {
    width: 90%;
    margin-left: 0;
  }

  table {
    font-size: 14px;
  }

  th, td {
    padding: 10px;
  }

  .modal-content {
    width: 95%;
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    padding: 10px;
  }

  .main-section {
    padding: 15px;
    width: 100%;
    margin-left: 0;
  }

  table {
    font-size: 12px;
  }

  .qualifications-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Print styles */
@media print {
  body * {
    visibility: hidden;
  }

  .nlp-results-modal,
  .nlp-results-modal * {
    visibility: visible;
  }

  .nlp-results-modal {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: white;
    z-index: 9999;
  }

  .nlp-results-modal .modal-content {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
    box-shadow: none;
    border: none;
  }

  .nlp-results-modal .form-buttons,
  .modal-overlay {
    display: none !important;
  }

  .report-header {
    border: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .section-container {
    page-break-inside: avoid;
    border: 1px solid #ddd;
    margin-bottom: 15px;
  }

  .extracted-text {
    display: none;
  }

  .candidate-name {
    font-size: 24px;
    margin-bottom: 10px;
  }

  .match-score-circle {
    border: 3px solid #ddd;
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }

  .skill-tag {
    border: 1px solid #ddd;
    margin: 2px;
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }
}
