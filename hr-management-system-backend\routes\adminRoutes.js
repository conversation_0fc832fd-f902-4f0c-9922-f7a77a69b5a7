const express = require('express');
const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');
const { body, validationResult } = require('express-validator');
const User = require('../models/user');
const AuditLog = require('../models/AuditLog');
const LoginHistory = require('../models/LoginHistory');
const { sendPasswordChangeEmail } = require('../utils/emailNotifications');

const router = express.Router();

// Admin Init Route
router.get('/init', authenticate, authorizeRoles('admin'), (req, res) => {
  res.json({ message: 'Hello Admin' });
});

// Create User
router.post(
  '/users/create',
  authenticate,
  authorizeRoles('admin'),
  [
    body('name').notEmpty().withMessage('Name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters long'),
    body('role').notEmpty().withMessage('Role is required'),
  ],
  async (req, res) => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { name, email, password, role, job, department } = req.body;

    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(409).json({ message: 'User already exists' });
      }

      // Create new user with plain password
      // The pre-save hook will detect if it needs to be hashed
      const newUser = new User({
        name,
        email,
        password, // Plain password - will be hashed by the pre-save hook if needed
        role,
        creationDate: new Date(),
        job,
        department: department || 'Other', // Use the provided department or default to 'Other'
        birthdate: req.body.birthdate ? new Date(req.body.birthdate) : undefined,
      });

      await newUser.save();
      res.status(201).json({ message: 'User created successfully', user: newUser });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'Server error' });
    }
  }
);



// Delete User
router.delete('/users/delete/:id', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const userId = req.params.id;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const user = await User.findByIdAndDelete(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});
//get all users
router.get('/users/all', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const users = await User.find().select('-password');
    res.status(200).json(users);
  } catch (error) {
    console.error('Failed to fetch users:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Search users
router.get('/users/search', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const { search } = req.query;

    let query = {};

    // Add search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { role: { $regex: search, $options: 'i' } },
        { job: { $regex: search, $options: 'i' } },
        { department: { $regex: search, $options: 'i' } }
      ];
    }

    const users = await User.find(query).select('-password');
    res.status(200).json(users);
  } catch (error) {
    console.error('Failed to search users:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update User
router.put('/users/update/:id', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const userId = req.params.id;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const { name, email, password, role, job, department, birthdate } = req.body;

    if (name) user.name = name;
    if (email) user.email = email;
    // Only update password if it's provided and not empty
    if (password && password.trim() !== '') {
      console.log('Updating password for user:', user.email);
      console.log('New password (unhashed):', password);

      // Set the plain password - the pre-save hook will handle hashing
      user.password = password;

      // Log the update
      console.log('Password will be hashed during save if needed');

      // Send email notification about password change
      try {
        await sendPasswordChangeEmail({
          email: user.email,
          to_name: user.name,
          date: new Date().toLocaleString(),
          byAdmin: true
        });
        console.log('Password change email notification sent to user:', user.email);
      } catch (emailError) {
        console.error('Error sending password change email notification:', emailError);
        // Continue with the flow even if email fails
      }
    }
    if (role) user.role = role;
    if (job) user.job = job;
    if (department) user.department = department;
    if (birthdate) user.birthdate = new Date(birthdate);

    await user.save();
    res.status(200).json({ message: 'User updated successfully' });
  } catch (err) {
    console.error('Update Error:', err);

    // Check for duplicate email error
    if (err.code === 11000 && err.keyPattern && err.keyPattern.email) {
      return res.status(409).json({ message: 'Email already in use by another user' });
    }

    res.status(500).json({ message: 'Server error occurred while updating user' });
  }
});

// Get audit logs with pagination and filtering
router.get('/audit-logs', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      userId,
      action,
      resourceType,
      startDate,
      endDate,
      sortBy = 'timestamp',
      sortOrder = 'desc',
      search
    } = req.query;

    // Build query based on filters
    const query = {};

    if (userId) {
      query.userId = userId;
    }

    if (action) {
      query.action = action;
    }

    if (resourceType) {
      query.resourceType = resourceType;
    }

    // Date range filter
    if (startDate || endDate) {
      query.timestamp = {};

      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        query.timestamp.$lte = endDateObj;
      }
    }

    // Add search functionality
    if (search) {
      // Get user IDs that match the search term
      const users = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      // Build search query
      const searchQuery = {
        $or: [
          { description: { $regex: search, $options: 'i' } },
          { action: { $regex: search, $options: 'i' } },
          { resourceType: { $regex: search, $options: 'i' } },
          { ipAddress: { $regex: search, $options: 'i' } }
        ]
      };

      // Add user IDs to the search query if any were found
      if (userIds.length > 0) {
        searchQuery.$or.push({ userId: { $in: userIds } });
      }

      // Combine with existing query using $and
      query.$and = query.$and || [];
      query.$and.push(searchQuery);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Determine sort direction
    const sortDirection = sortOrder.toLowerCase() === 'asc' ? 1 : -1;

    // Execute query with pagination
    const auditLogs = await AuditLog.find(query)
      .sort({ [sortBy]: sortDirection })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('userId', 'name email role');

    // Get total count for pagination
    const totalLogs = await AuditLog.countDocuments(query);

    res.status(200).json({
      logs: auditLogs,
      pagination: {
        total: totalLogs,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalLogs / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({ message: 'Server error while fetching audit logs' });
  }
});

// Get login history with pagination and filtering
router.get('/login-history', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      userId,
      status,
      ipAddress,
      startDate,
      endDate,
      sortBy = 'loginTime',
      sortOrder = 'desc',
      search
    } = req.query;

    // Build query based on filters
    const query = {};

    if (userId) {
      query.userId = userId;
    }

    if (status) {
      query.status = status;
    }

    if (ipAddress) {
      query.ipAddress = ipAddress;
    }

    // Date range filter
    if (startDate || endDate) {
      query.loginTime = {};

      if (startDate) {
        query.loginTime.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        query.loginTime.$lte = endDateObj;
      }
    }

    // Add search functionality
    if (search) {
      // Get user IDs that match the search term
      const users = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      const userIds = users.map(user => user._id);

      // Build search query
      const searchQuery = {
        $or: [
          { ipAddress: { $regex: search, $options: 'i' } },
          { 'browser.name': { $regex: search, $options: 'i' } },
          { 'operatingSystem.name': { $regex: search, $options: 'i' } },
          { device: { $regex: search, $options: 'i' } },
          { 'userInfo.name': { $regex: search, $options: 'i' } },
          { 'userInfo.email': { $regex: search, $options: 'i' } }
        ]
      };

      // Add user IDs to the search query if any were found
      if (userIds.length > 0) {
        searchQuery.$or.push({ userId: { $in: userIds } });
      }

      // Combine with existing query using $and
      query.$and = query.$and || [];
      query.$and.push(searchQuery);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Determine sort direction
    const sortDirection = sortOrder.toLowerCase() === 'asc' ? 1 : -1;

    // Execute query with pagination
    const loginHistory = await LoginHistory.find(query)
      .sort({ [sortBy]: sortDirection })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('userId', 'name email role');

    // Get total count for pagination
    const totalEntries = await LoginHistory.countDocuments(query);

    res.status(200).json({
      history: loginHistory,
      pagination: {
        total: totalEntries,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalEntries / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching login history:', error);
    res.status(500).json({ message: 'Server error while fetching login history' });
  }
});

// Get login statistics
router.get('/login-statistics', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Date range filter
    const dateFilter = {};
    if (startDate || endDate) {
      dateFilter.loginTime = {};

      if (startDate) {
        dateFilter.loginTime.$gte = new Date(startDate);
      }

      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        dateFilter.loginTime.$lte = endDateObj;
      }
    }

    console.log('Fetching login statistics with filter:', JSON.stringify(dateFilter));

    // Get total login attempts
    const totalLogins = await LoginHistory.countDocuments(dateFilter);
    console.log('Total logins:', totalLogins);

    // Get successful logins
    const successfulLogins = await LoginHistory.countDocuments({
      ...dateFilter,
      status: 'SUCCESS'
    });
    console.log('Successful logins:', successfulLogins);

    // Get failed logins
    const failedLogins = await LoginHistory.countDocuments({
      ...dateFilter,
      status: 'FAILED'
    });
    console.log('Failed logins:', failedLogins);

    // Get unique users who logged in
    const uniqueUsers = await LoginHistory.distinct('userId', {
      ...dateFilter,
      status: 'SUCCESS'
    });
    console.log('Unique users count:', uniqueUsers.length);

    // Get unique IP addresses
    const uniqueIPs = await LoginHistory.distinct('ipAddress', dateFilter);
    console.log('Unique IPs count:', uniqueIPs.length);

    // Get login counts by role
    const loginsByRole = await LoginHistory.aggregate([
      { $match: { ...dateFilter, status: 'SUCCESS' } },
      { $group: { _id: '$userInfo.role', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Logins by role:', loginsByRole);

    // Get login counts by browser
    const loginsByBrowser = await LoginHistory.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$browser.name', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    console.log('Logins by browser:', loginsByBrowser);

    // Get login counts by device
    const loginsByDevice = await LoginHistory.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$device', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get login counts by OS
    const loginsByOS = await LoginHistory.aggregate([
      { $match: dateFilter },
      { $group: { _id: '$operatingSystem.name', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Get login counts by day
    const loginsByDay = await LoginHistory.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            year: { $year: '$loginTime' },
            month: { $month: '$loginTime' },
            day: { $dayOfMonth: '$loginTime' }
          },
          count: { $sum: 1 },
          successCount: {
            $sum: { $cond: [{ $eq: ['$status', 'SUCCESS'] }, 1, 0] }
          },
          failCount: {
            $sum: { $cond: [{ $eq: ['$status', 'FAILED'] }, 1, 0] }
          }
        }
      },
      {
        $project: {
          _id: 0,
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          total: '$count',
          successful: '$successCount',
          failed: '$failCount'
        }
      },
      { $sort: { date: 1 } }
    ]);

    res.status(200).json({
      summary: {
        totalLogins,
        successfulLogins,
        failedLogins,
        uniqueUserCount: uniqueUsers.length,
        uniqueIPCount: uniqueIPs.length,
        successRate: totalLogins > 0 ? (successfulLogins / totalLogins) * 100 : 0
      },
      loginsByRole: loginsByRole.map(item => ({
        role: item._id || 'Unknown',
        count: item.count
      })),
      loginsByBrowser: loginsByBrowser.map(item => ({
        browser: item._id || 'Unknown',
        count: item.count
      })),
      loginsByDevice: loginsByDevice.map(item => ({
        device: item._id || 'Unknown',
        count: item.count
      })),
      loginsByOS: loginsByOS.map(item => ({
        os: item._id || 'Unknown',
        count: item.count
      })),
      loginsByDay
    });
  } catch (error) {
    console.error('Error fetching login statistics:', error);
    res.status(500).json({ message: 'Server error while fetching login statistics' });
  }
});

// Get user account status
router.get('/users/:id/status', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const userId = req.params.id;

    const user = await User.findById(userId).select('-password');

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get user's last login
    const lastLogin = await LoginHistory.findOne({
      userId: userId,
      status: 'SUCCESS'
    }).sort({ loginTime: -1 });

    // Get user's login count
    const loginCount = await LoginHistory.countDocuments({
      userId: userId,
      status: 'SUCCESS'
    });

    // Get user's failed login attempts
    const failedLoginCount = await LoginHistory.countDocuments({
      userId: userId,
      status: 'FAILED'
    });

    // Get user's recent audit logs
    const recentActivity = await AuditLog.find({
      userId: userId
    }).sort({ timestamp: -1 }).limit(5);

    res.status(200).json({
      user,
      accountStatus: {
        lastLogin: lastLogin ? lastLogin.loginTime : null,
        loginCount,
        failedLoginCount,
        isActive: true, // This will be updated when we implement account activation/deactivation
        lastActivity: recentActivity.length > 0 ? recentActivity[0].timestamp : null
      },
      recentActivity
    });
  } catch (error) {
    console.error('Error fetching user account status:', error);
    res.status(500).json({ message: 'Server error while fetching user account status' });
  }
});

// Activate/deactivate user account
router.put('/users/:id/status', authenticate, authorizeRoles('admin'), async (req, res) => {
  try {
    const userId = req.params.id;
    const { active } = req.body;

    if (active === undefined) {
      return res.status(400).json({ message: 'Active status is required' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update user's active status
    user.active = active;
    await user.save();

    // Create audit log entry
    await new AuditLog({
      userId: req.user.id, // Admin who performed the action
      userInfo: {
        name: req.user.name,
        email: req.user.email,
        role: req.user.role
      },
      action: active ? 'USER_ACTIVATE' : 'USER_DEACTIVATE',
      resourceType: 'USER',
      resourceId: userId,
      description: `User account ${active ? 'activated' : 'deactivated'}: ${user.name} (${user.email})`,
      ipAddress: req.ip || req.connection.remoteAddress || 'Unknown',
      userAgent: req.headers['user-agent'] || 'Unknown'
    }).save();

    res.status(200).json({
      message: `User account ${active ? 'activated' : 'deactivated'} successfully`,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        active: user.active
      }
    });
  } catch (error) {
    console.error('Error updating user account status:', error);
    res.status(500).json({ message: 'Server error while updating user account status' });
  }
});

module.exports = router;
