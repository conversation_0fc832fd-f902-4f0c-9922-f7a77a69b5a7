/**
 * Context API Service - DISABLED
 * Context-aware functionality removed - chatbot should not appear automatically
 */

// import api from './ApiService';

class ContextApiService {
  constructor() {
    // Context API disabled - chatbot should not appear automatically
    console.log('⚠️ ContextApiService disabled - context-aware functionality removed');
  }

  /**
   * Process context and get suggestions - DISABLED
   */
  async processContext(contextType, action, data = {}) {
    console.log('⚠️ processContext disabled - context-aware functionality removed');
    return { success: false, message: 'Context-aware functionality disabled' };
  }

  /**
   * Get suggestions for specific context type - DISABLED
   */
  async getContextSuggestions(contextType, actionData = {}) {
    console.log('⚠️ getContextSuggestions disabled - context-aware functionality removed');
    return { success: false, suggestions: [] };
  }

  // All context methods disabled - chatbot should not appear automatically
  async getContextHistory() { return { success: false, data: [] }; }
  async clearContextHistory() { return { success: false }; }
  async getContextStats() { return { success: false, data: {} }; }

  // All processing methods disabled - chatbot should not appear automatically
  async processFormContext(formType, formData = {}) { return { success: false }; }
  async processDialogContext(dialogType, dialogData = {}) { return { success: false }; }
  async processNavigationContext(path, pageData = {}) { return null; }
  async processInputContext(inputType, inputData = {}) { return { success: false }; }

  // Mapping methods disabled but kept as stubs
  mapFormTypeToContext(formType) { return 'disabled'; }
  mapDialogTypeToContext(dialogType) { return 'disabled'; }
  mapPathToContext(path) { return null; }

  // All suggestion methods disabled - chatbot should not appear automatically
  async getLeaveRequestSuggestions(formData = {}) { return { success: false, suggestions: [] }; }
  async getUserManagementSuggestions(formData = {}) { return { success: false, suggestions: [] }; }
  async getTaskManagementSuggestions(formData = {}) { return { success: false, suggestions: [] }; }
  async getJobApplicationSuggestions(formData = {}) { return { success: false, suggestions: [] }; }
  async getDashboardSuggestions(pageData = {}) { return { success: false, suggestions: [] }; }

  // All utility methods disabled - chatbot should not appear automatically
  async batchProcessContexts(contexts) { return []; }
  clearCache() { /* disabled */ }
  getCacheSize() { return 0; }
  cleanCache() { /* disabled */ }
  setCacheTimeout(timeout) { /* disabled */ }
}

// Create singleton instance
const contextApiService = new ContextApiService();

// Clean cache every 10 minutes
setInterval(() => {
  contextApiService.cleanCache();
}, 10 * 60 * 1000);

export default contextApiService;
