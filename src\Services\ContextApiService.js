/**
 * Context API Service
 * Handles communication with backend context-aware services
 */

import api from './ApiService';

class ContextApiService {
  constructor() {
    this.baseUrl = '/api/ai/context';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Process context and get suggestions
   */
  async processContext(contextType, action, data = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/process`, {
        contextType,
        action,
        data
      });

      return response.data;
    } catch (error) {
      console.error('Error processing context:', error);
      throw error;
    }
  }

  /**
   * Get suggestions for specific context type
   */
  async getContextSuggestions(contextType, actionData = {}) {
    try {
      // Check cache first
      const cacheKey = `${contextType}_${JSON.stringify(actionData)}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.data;
      }

      const response = await api.get(`${this.baseUrl}/suggestions/${contextType}`, {
        params: actionData
      });

      // Cache the result
      this.cache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now()
      });

      return response.data;
    } catch (error) {
      console.error('Error getting context suggestions:', error);
      throw error;
    }
  }

  /**
   * Get user context history
   */
  async getContextHistory() {
    try {
      const response = await api.get(`${this.baseUrl}/history`);
      return response.data;
    } catch (error) {
      console.error('Error getting context history:', error);
      throw error;
    }
  }

  /**
   * Clear user context history
   */
  async clearContextHistory() {
    try {
      const response = await api.delete(`${this.baseUrl}/history`);
      this.clearCache();
      return response.data;
    } catch (error) {
      console.error('Error clearing context history:', error);
      throw error;
    }
  }

  /**
   * Get context statistics
   */
  async getContextStats() {
    try {
      const response = await api.get(`${this.baseUrl}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error getting context stats:', error);
      throw error;
    }
  }

  /**
   * Process form context
   */
  async processFormContext(formType, formData = {}) {
    const contextType = this.mapFormTypeToContext(formType);
    
    return await this.processContext(contextType, 'form_focus', {
      formType,
      formData,
      timestamp: Date.now()
    });
  }

  /**
   * Process dialog context
   */
  async processDialogContext(dialogType, dialogData = {}) {
    const contextType = this.mapDialogTypeToContext(dialogType);
    
    return await this.processContext(contextType, 'dialog_open', {
      dialogType,
      dialogData,
      timestamp: Date.now()
    });
  }

  /**
   * Process navigation context
   */
  async processNavigationContext(path, pageData = {}) {
    const contextType = this.mapPathToContext(path);
    
    if (!contextType) return null;
    
    return await this.processContext('dashboard_navigation', 'page_visit', {
      path,
      contextType,
      pageData,
      timestamp: Date.now()
    });
  }

  /**
   * Process input focus context
   */
  async processInputContext(inputType, inputData = {}) {
    return await this.processContext('input_focus', 'input_focus', {
      inputType,
      inputData,
      timestamp: Date.now()
    });
  }

  /**
   * Map form types to context types
   */
  mapFormTypeToContext(formType) {
    const mapping = {
      'leave-request': 'leave_request_form',
      'user-management': 'user_management_form',
      'task-management': 'task_management_form',
      'job-application': 'job_application_form',
      'user-form': 'user_management_form',
      'task-form': 'task_management_form',
      'leave-form': 'leave_request_form',
      'application-form': 'job_application_form'
    };
    
    return mapping[formType] || 'form_interaction';
  }

  /**
   * Map dialog types to context types
   */
  mapDialogTypeToContext(dialogType) {
    const mapping = {
      'leave-request': 'leave_request_form',
      'user-management': 'user_management_form',
      'task-management': 'task_management_form',
      'add-user': 'user_management_form',
      'edit-user': 'user_management_form',
      'create-task': 'task_management_form',
      'request-leave': 'leave_request_form'
    };
    
    return mapping[dialogType] || 'dialog_interaction';
  }

  /**
   * Map paths to context types
   */
  mapPathToContext(path) {
    if (path.includes('dashboard')) return 'dashboard';
    if (path.includes('leave')) return 'leave';
    if (path.includes('user')) return 'users';
    if (path.includes('task')) return 'tasks';
    if (path.includes('report')) return 'reports';
    if (path.includes('apply')) return 'application';
    if (path.includes('hr-dashboard')) return 'hr_dashboard';
    if (path.includes('admin')) return 'admin_dashboard';
    return null;
  }

  /**
   * Get context suggestions for leave request
   */
  async getLeaveRequestSuggestions(formData = {}) {
    return await this.getContextSuggestions('leave_request_form', formData);
  }

  /**
   * Get context suggestions for user management
   */
  async getUserManagementSuggestions(formData = {}) {
    return await this.getContextSuggestions('user_management_form', formData);
  }

  /**
   * Get context suggestions for task management
   */
  async getTaskManagementSuggestions(formData = {}) {
    return await this.getContextSuggestions('task_management_form', formData);
  }

  /**
   * Get context suggestions for job application
   */
  async getJobApplicationSuggestions(formData = {}) {
    return await this.getContextSuggestions('job_application_form', formData);
  }

  /**
   * Get context suggestions for dashboard
   */
  async getDashboardSuggestions(pageData = {}) {
    return await this.getContextSuggestions('dashboard_navigation', pageData);
  }

  /**
   * Batch process multiple contexts
   */
  async batchProcessContexts(contexts) {
    try {
      const promises = contexts.map(context => 
        this.processContext(context.type, context.action, context.data)
      );
      
      const results = await Promise.allSettled(promises);
      
      return results.map((result, index) => ({
        context: contexts[index],
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }));
    } catch (error) {
      console.error('Error batch processing contexts:', error);
      throw error;
    }
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize() {
    return this.cache.size;
  }

  /**
   * Clean expired cache entries
   */
  cleanCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Set cache timeout
   */
  setCacheTimeout(timeout) {
    this.cacheTimeout = timeout;
  }
}

// Create singleton instance
const contextApiService = new ContextApiService();

// Clean cache every 10 minutes
setInterval(() => {
  contextApiService.cleanCache();
}, 10 * 60 * 1000);

export default contextApiService;
