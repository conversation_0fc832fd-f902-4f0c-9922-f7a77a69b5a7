/* Task List Styles */
.task-list-container {
  margin-top: 20px;
}

.task-card {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.task-title {
  font-weight: 500;
  margin: 0;
}

.task-content {
  padding: 16px;
}

.task-description {
  margin-bottom: 16px;
  color: #555;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.task-meta-item {
  display: flex;
  flex-direction: column;
}

.task-meta-label {
  font-size: 12px;
  color: #777;
  margin-bottom: 4px;
}

.task-meta-value {
  font-weight: 500;
}

.task-progress {
  margin-bottom: 16px;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Task Dialog Styles */
.task-dialog-title {
  background-color: #f5f5f5;
  padding: 16px;
}

.task-dialog-content {
  padding: 24px;
}

.task-dialog-section {
  margin-bottom: 24px;
}

.task-dialog-section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.task-updates-list {
  max-height: 300px;
  overflow-y: auto;
}

.task-update-item {
  padding: 12px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.task-update-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.task-update-author {
  font-weight: 500;
}

.task-update-time {
  font-size: 12px;
  color: #777;
}

.task-update-message {
  color: #333;
}

/* Priority Colors */
.priority-low {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.priority-medium {
  background-color: #fff8e1;
  color: #ff8f00;
}

.priority-high {
  background-color: #ffebee;
  color: #c62828;
}

.priority-urgent {
  background-color: #7f0000;
  color: white;
}

/* Status Colors */
.status-assigned {
  background-color: #e3f2fd;
  color: #1565c0;
}

.status-in-progress {
  background-color: #fff8e1;
  color: #ff8f00;
}

.status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-needs-revision {
  background-color: #ffebee;
  color: #c62828;
}

/* Responsive Styles */
@media (max-width: 600px) {
  .task-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .task-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .task-actions button {
    margin-bottom: 8px;
  }
}
