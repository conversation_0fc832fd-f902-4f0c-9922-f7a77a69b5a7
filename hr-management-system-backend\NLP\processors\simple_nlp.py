import sys
import json
import os
import re
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# Add parent directory to path so we can import from utils
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.text_extraction import extract_text_from_pdf
from utils.text_processing import extract_basic_info, extract_skills, extract_education, extract_experience

# Calculate job match
def calculate_job_match(cv_data, job_data):
    """
    Calculate how well a CV matches a job

    Args:
        cv_data (dict): CV data with skills, education, experience
        job_data (dict): Job data with requirements, responsibilities

    Returns:
        dict: Match results with score, matched skills, missing skills
    """
    # Check if job_data is valid
    if not job_data or not isinstance(job_data, dict):
        print(f"Invalid job data for matching: {type(job_data)}", file=sys.stderr)
        return {
            "score": 0,
            "matched_skills": [],
            "missing_skills": [],
            "job_requirements": [],
            "analysis": "No valid job data provided for comparison."
        }

    # Extract job requirements
    requirements = []
    if 'requirements' in job_data and isinstance(job_data['requirements'], list):
        requirements.extend(job_data['requirements'])
    elif 'requirements' in job_data and isinstance(job_data['requirements'], str):
        requirements.append(job_data['requirements'])

    # Extract job responsibilities
    responsibilities = []
    if 'responsibilities' in job_data and isinstance(job_data['responsibilities'], list):
        responsibilities.extend(job_data['responsibilities'])
    elif 'responsibilities' in job_data and isinstance(job_data['responsibilities'], str):
        responsibilities.append(job_data['responsibilities'])

    # Process requirements and responsibilities to extract key skills
    job_keywords = []
    processed_requirements = []

    # Process requirements
    for req in requirements:
        if not isinstance(req, str):
            continue

        # Clean and normalize the requirement
        req = req.strip()
        processed_requirements.append(req)

        # Split by commas if it's a comma-separated string
        if ',' in req:
            parts = [k.strip().lower() for k in req.split(',')]
            job_keywords.extend(parts)
        else:
            # Extract key terms using simple NLP
            words = word_tokenize(req.lower())
            # Remove stopwords
            stop_words = set(stopwords.words('english'))
            filtered_words = [w for w in words if w.isalnum() and w not in stop_words]

            # Add important phrases
            noun_phrases = re.findall(r'\b(?:(?:\w+\s+){0,2}(?:knowledge|experience|skills|ability|proficiency)(?:\s+\w+){0,3})\b', req.lower())
            job_keywords.extend(noun_phrases)

            # Add the whole requirement as a keyword if it's short
            if len(req.split()) <= 5:
                job_keywords.append(req.lower())

    # Process responsibilities similarly
    for resp in responsibilities:
        if not isinstance(resp, str):
            continue

        # Split by commas if it's a comma-separated string
        if ',' in resp:
            parts = [k.strip().lower() for k in resp.split(',')]
            job_keywords.extend(parts)
        else:
            # Extract key terms using simple NLP
            words = word_tokenize(resp.lower())
            # Remove stopwords
            stop_words = set(stopwords.words('english'))
            filtered_words = [w for w in words if w.isalnum() and w not in stop_words]

            # Add important phrases
            verb_phrases = re.findall(r'\b(?:(?:\w+\s+){0,2}(?:develop|create|manage|design|implement|analyze|maintain|support)(?:\s+\w+){0,3})\b', resp.lower())
            job_keywords.extend(verb_phrases)

    # Clean up keywords
    cleaned_keywords = []
    for keyword in job_keywords:
        # Remove very short keywords
        if len(keyword.split()) == 1 and len(keyword) < 3:
            continue

        # Remove common stop words if they're standalone
        if keyword in stopwords.words('english'):
            continue

        cleaned_keywords.append(keyword)

    # Remove duplicates and very similar terms
    unique_keywords = []
    for keyword in cleaned_keywords:
        # Check if this keyword is a subset of any existing keyword
        is_subset = False
        for existing in unique_keywords:
            if keyword in existing and len(keyword) < len(existing):
                is_subset = True
                break

        if not is_subset:
            unique_keywords.append(keyword)

    # Get candidate skills and other relevant information
    candidate_skills = cv_data.get('skills', [])
    candidate_education = cv_data.get('education', [])
    candidate_experience = cv_data.get('experience', [])

    # Combine all candidate text for comprehensive matching
    all_candidate_text = ' '.join(candidate_skills + candidate_education + candidate_experience).lower()

    # Calculate match score with detailed analysis
    matched_skills = []
    missing_skills = []

    for keyword in unique_keywords:
        if keyword in all_candidate_text or any(keyword in skill.lower() for skill in candidate_skills):
            matched_skills.append(keyword)
        else:
            missing_skills.append(keyword)

    # Calculate percentage match
    if not unique_keywords:
        match_percentage = 0
    else:
        match_percentage = min(100, int((len(matched_skills) / len(unique_keywords)) * 100))

    # Generate analysis text
    if match_percentage >= 80:
        analysis = "Excellent match! The candidate has most of the required skills and qualifications."
    elif match_percentage >= 60:
        analysis = "Good match. The candidate has many of the required skills but is missing some key qualifications."
    elif match_percentage >= 40:
        analysis = "Moderate match. The candidate has some relevant skills but lacks several important requirements."
    else:
        analysis = "Poor match. The candidate's profile does not align well with the job requirements."

    # Add specific details about strengths and weaknesses
    if matched_skills:
        analysis += f" Strengths include: {', '.join(matched_skills[:5])}"
        if len(matched_skills) > 5:
            analysis += f" and {len(matched_skills) - 5} more."
        else:
            analysis += "."

    if missing_skills:
        analysis += f" Missing qualifications include: {', '.join(missing_skills[:5])}"
        if len(missing_skills) > 5:
            analysis += f" and {len(missing_skills) - 5} more."
        else:
            analysis += "."

    return {
        "score": match_percentage,
        "matched_skills": matched_skills,
        "missing_skills": missing_skills,
        "job_requirements": processed_requirements,
        "analysis": analysis
    }

# Parse CV
def parse_cv(text, job_data=None):
    """
    Parse CV text and extract information

    Args:
        text (str): CV text
        job_data (dict, optional): Job data for matching

    Returns:
        dict: Parsed CV data with extracted information
    """
    # Check if text is an error message
    if text.startswith("Error") or text.startswith("File not") or text.startswith("No text"):
        return {
            "error": text,
            "name": "Unknown",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": [],
            "extracted_text": text
        }

    try:
        print(f"Parsing CV text of length: {len(text)}", file=sys.stderr)

        # Extract basic information
        basic_info = extract_basic_info(text)
        print(f"Extracted basic info: {basic_info}", file=sys.stderr)

        # Extract skills
        skills = extract_skills(text)
        print(f"Extracted {len(skills)} skills", file=sys.stderr)

        # Extract education
        education = extract_education(text)
        print(f"Extracted {len(education)} education entries", file=sys.stderr)

        # Extract experience
        experience = extract_experience(text)
        print(f"Extracted {len(experience)} experience entries", file=sys.stderr)

        # Create result dictionary
        result = {
            "name": basic_info["name"],
            "email": basic_info["email"],
            "phone": basic_info["phone"],
            "skills": skills,
            "education": education,
            "experience": experience,
            "extracted_text": text[:1000] + "..." if len(text) > 1000 else text  # Include a preview of the extracted text
        }

        # Calculate job match if job data is provided
        if job_data:
            try:
                # Ensure job_data is a dictionary
                if not isinstance(job_data, dict):
                    print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                    job_data = {"title": "Unknown Job"}

                # Ensure job_data has a title
                if "title" not in job_data:
                    job_data["title"] = "Unknown Job"

                print(f"Calculating job match with job: {job_data.get('title', 'Unknown')}", file=sys.stderr)
                match_result = calculate_job_match(result, job_data)

                # Safely add match results to the result dictionary
                result["matchScore"] = match_result["score"]
                result["matchedSkills"] = match_result["matched_skills"]
                result["missingSkills"] = match_result["missing_skills"]
                result["jobRequirements"] = match_result["job_requirements"]
                result["matchAnalysis"] = match_result["analysis"]
                result["jobTitle"] = job_data.get('title', 'Unknown Job')

                print(f"Match score: {match_result['score']}%", file=sys.stderr)
                print(f"Matched {len(match_result['matched_skills'])} skills, missing {len(match_result['missing_skills'])} skills", file=sys.stderr)
            except Exception as e:
                print(f"Error calculating job match: {str(e)}", file=sys.stderr)
                import traceback
                traceback.print_exc(file=sys.stderr)

                # Add default values for match results
                result["matchScore"] = 0
                result["matchedSkills"] = []
                result["missingSkills"] = []
                result["jobRequirements"] = []
                result["jobTitle"] = "Unknown Job"
                result["matchError"] = str(e)
                result["matchAnalysis"] = "Error analyzing job match: " + str(e)

        # Generate a summary report
        summary = []
        summary.append(f"Candidate Name: {basic_info['name']}")

        if basic_info['email']:
            summary.append(f"Contact: {basic_info['email']}")
            if basic_info['phone']:
                summary.append(f", {basic_info['phone']}")

        summary.append("\n")

        if skills:
            summary.append("Skills Summary:")
            for skill in skills[:10]:  # Show top 10 skills
                summary.append(f"- {skill}")
            if len(skills) > 10:
                summary.append(f"- And {len(skills) - 10} more skills")
            summary.append("\n")

        if education:
            summary.append("Education Highlights:")
            for edu in education[:3]:  # Show top 3 education entries
                summary.append(f"- {edu}")
            if len(education) > 3:
                summary.append(f"- And {len(education) - 3} more education entries")
            summary.append("\n")

        if experience:
            summary.append("Experience Highlights:")
            for exp in experience[:3]:  # Show top 3 experience entries
                summary.append(f"- {exp}")
            if len(experience) > 3:
                summary.append(f"- And {len(experience) - 3} more experience entries")
            summary.append("\n")

        if "matchScore" in result:
            # Ensure jobTitle exists
            job_title = result.get("jobTitle", "Unknown Job")
            summary.append(f"Job Match: {result['matchScore']}% for {job_title}")
            summary.append(result.get("matchAnalysis", ""))
            summary.append("\n")

            if "matchedSkills" in result and result["matchedSkills"]:
                summary.append("Matching Qualifications:")
                for skill in result["matchedSkills"][:5]:
                    summary.append(f"- {skill}")
                if len(result["matchedSkills"]) > 5:
                    summary.append(f"- And {len(result['matchedSkills']) - 5} more")
                summary.append("\n")

            if "missingSkills" in result and result["missingSkills"]:
                summary.append("Missing Qualifications:")
                for skill in result["missingSkills"][:5]:
                    summary.append(f"- {skill}")
                if len(result["missingSkills"]) > 5:
                    summary.append(f"- And {len(result['missingSkills']) - 5} more")

        result["summary"] = "\n".join(summary)

        return result
    except Exception as e:
        print(f"Error parsing CV: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)

        return {
            "error": f"Error parsing CV: {str(e)}",
            "name": "Error",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": [],
            "extracted_text": text[:1000] + "..." if len(text) > 1000 else text
        }

# Main function to process command line arguments
if __name__ == "__main__":
    try:
        # Get file path from command line arguments
        file_path = sys.argv[1]

        # Check if job data is provided
        job_data = None
        if len(sys.argv) > 2:
            # Check if we're using a job data file
            is_job_file = False
            if len(sys.argv) > 3 and sys.argv[3] == '--job-file':
                is_job_file = True
                print(f"Using job data from file", file=sys.stderr)

            if is_job_file:
                # Read job data from file
                try:
                    job_file_path = sys.argv[2]
                    print(f"Reading job data from file: {job_file_path}", file=sys.stderr)

                    # Check if file exists
                    if not os.path.exists(job_file_path):
                        print(f"Job data file not found: {job_file_path}", file=sys.stderr)
                    else:
                        with open(job_file_path, 'r') as f:
                            job_data_str = f.read()

                        # Check if file is empty
                        if not job_data_str.strip():
                            print("Job data file is empty", file=sys.stderr)
                        else:
                            # Try to parse the JSON
                            job_data = json.loads(job_data_str)

                            # Verify job_data is a dictionary
                            if not isinstance(job_data, dict):
                                print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                                job_data = {"title": "Unknown Job"}
                            else:
                                print(f"Successfully parsed job data from file", file=sys.stderr)
                except Exception as e:
                    print(f"Error reading job data from file: {str(e)}", file=sys.stderr)
                    job_data = {"title": "Unknown Job"}
            else:
                # Parse job data from command line argument
                try:
                    # Clean the job data string to ensure it's valid JSON
                    job_data_str = sys.argv[2].strip('"\'')

                    # Fix common JSON issues
                    # Replace single quotes with double quotes
                    job_data_str = job_data_str.replace("'", '"')

                    # Fix newlines in JSON strings
                    job_data_str = job_data_str.replace('\n', '\\n')

                    # Try to parse the JSON
                    job_data = json.loads(job_data_str)

                    # Verify job_data is a dictionary
                    if not isinstance(job_data, dict):
                        print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                        job_data = {"title": "Unknown Job"}
                    else:
                        print(f"Successfully parsed job data", file=sys.stderr)
                except json.JSONDecodeError as e:
                    print(f"Error parsing job data: {str(e)}", file=sys.stderr)

                    # Try a different approach - use a more lenient parser
                    try:
                        import ast
                        # Convert string representation of dict to actual dict
                        job_data = ast.literal_eval(sys.argv[2])

                        # Verify job_data is a dictionary
                        if not isinstance(job_data, dict):
                            print(f"Job data is not a dictionary: {type(job_data)}", file=sys.stderr)
                            job_data = {"title": "Unknown Job"}
                        else:
                            print(f"Successfully parsed job data using ast.literal_eval", file=sys.stderr)
                    except Exception as e2:
                        print(f"Second parsing attempt failed: {str(e2)}", file=sys.stderr)
                        job_data = {"title": "Unknown Job"}

        # Extract text from PDF
        text = extract_text_from_pdf(file_path)

        # Parse the CV
        result = parse_cv(text, job_data)

        # Output the result as JSON
        print(json.dumps(result))

    except Exception as e:
        print(json.dumps({
            "error": f"Error in main function: {str(e)}",
            "name": "Error",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": []
        }))
