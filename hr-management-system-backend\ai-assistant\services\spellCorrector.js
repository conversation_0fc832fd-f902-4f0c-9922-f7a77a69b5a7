/**
 * Advanced Spell Correction and Typo Detection Service
 * Handles common typos, phonetic similarities, and context-aware corrections
 */

class SpellCorrector {
  constructor() {
    this.initializeCommonTypos();
    this.initializePhoneticMappings();
    this.initializeHRVocabulary();
    this.initializeKeyboardLayout();
  }

  /**
   * Initialize common typos and their corrections
   */
  initializeCommonTypos() {
    this.commonTypos = {
      // Common misspellings
      'recieve': 'receive',
      'seperate': 'separate',
      'definately': 'definitely',
      'occured': 'occurred',
      'begining': 'beginning',
      'accomodate': 'accommodate',
      'neccessary': 'necessary',
      'embarass': 'embarrass',
      'maintainance': 'maintenance',
      'existance': 'existence',
      'independant': 'independent',
      'appearence': 'appearance',
      'occassion': 'occasion',
      'recomend': 'recommend',
      'sucessful': 'successful',
      'beleive': 'believe',
      'acheive': 'achieve',
      'recieved': 'received',
      'untill': 'until',
      'alot': 'a lot',
      'aswell': 'as well',
      'incase': 'in case',
      'eachother': 'each other',
      'thankyou': 'thank you',
      'atleast': 'at least',
      'alright': 'all right',
      'everyday': 'every day',
      'maybe': 'may be',
      'cannot': 'can not',
      'wont': 'won\'t',
      'dont': 'don\'t',
      'cant': 'can\'t',
      'shouldnt': 'shouldn\'t',
      'wouldnt': 'wouldn\'t',
      'couldnt': 'couldn\'t',
      'didnt': 'didn\'t',
      'doesnt': 'doesn\'t',
      'isnt': 'isn\'t',
      'arent': 'aren\'t',
      'wasnt': 'wasn\'t',
      'werent': 'weren\'t',
      'hasnt': 'hasn\'t',
      'havent': 'haven\'t',
      'hadnt': 'hadn\'t',

      // Casual/informal to formal
      'ur': 'your',
      'u': 'you',
      'r': 'are',
      'n': 'and',
      'w/': 'with',
      'w/o': 'without',
      'b4': 'before',
      '2': 'to',
      '4': 'for',
      'thru': 'through',
      'tho': 'though',
      'gonna': 'going to',
      'wanna': 'want to',
      'gotta': 'got to',
      'kinda': 'kind of',
      'sorta': 'sort of',
      'dunno': 'don\'t know',
      'lemme': 'let me',
      'gimme': 'give me',
      'yeah': 'yes',
      'yep': 'yes',
      'nope': 'no',
      'ok': 'okay',
      'thx': 'thanks',
      'pls': 'please',
      'plz': 'please',
      'msg': 'message',
      'info': 'information',
      'asap': 'as soon as possible',
      'fyi': 'for your information',
      'btw': 'by the way',
      'imo': 'in my opinion',
      'imho': 'in my humble opinion',
      'lol': 'laugh out loud',
      'omg': 'oh my god',
      'wtf': 'what the f***',
      'idk': 'I don\'t know',
      'tbh': 'to be honest',
      'rn': 'right now',
      'atm': 'at the moment'
    };
  }

  /**
   * Initialize phonetic mappings for sound-alike corrections
   */
  initializePhoneticMappings() {
    this.phoneticMappings = {
      // Common phonetic confusions
      'ph': 'f',
      'gh': 'f',
      'ck': 'k',
      'qu': 'kw',
      'x': 'ks',
      'c': 'k',
      'z': 's',
      'j': 'g',
      'y': 'i',
      'w': 'u',
      'oo': 'u',
      'ee': 'i',
      'ea': 'i',
      'ou': 'ow',
      'ough': 'uff',
      'augh': 'aff',
      'tion': 'shun',
      'sion': 'shun',
      'cian': 'shun',
      'tial': 'shal',
      'cial': 'shal'
    };
  }

  /**
   * Initialize HR-specific vocabulary and common terms
   */
  initializeHRVocabulary() {
    this.hrVocabulary = {
      // HR terms and their common misspellings
      'employee': ['employe', 'emploi', 'employe', 'emplyee', 'employie'],
      'attendance': ['attendence', 'atendance', 'attendanse'],
      'performance': ['performence', 'preformance', 'performanse'],
      'evaluation': ['evalution', 'evaluaton', 'evalation'],
      'schedule': ['shedule', 'schedual', 'scedule'],
      'vacation': ['vacaton', 'vacasion', 'vakation'],
      'salary': ['salery', 'salari', 'sallary'],
      'benefits': ['benifits', 'benfits', 'benafits'],
      'interview': ['intervew', 'interwiew', 'interveiw'],
      'application': ['aplication', 'aplications', 'aplicaton'],
      'resignation': ['resegnation', 'resignaton', 'resination'],
      'promotion': ['promosion', 'promtion', 'promoshun'],
      'department': ['departement', 'deparment', 'departmant'],
      'supervisor': ['superviser', 'supervisar', 'supervizor'],
      'colleague': ['collegue', 'coleague', 'colege'],
      'meeting': ['meting', 'meating', 'meetng'],
      'deadline': ['dedline', 'deadlne', 'deadlin'],
      'project': ['projet', 'projct', 'projeckt'],
      'training': ['traning', 'trainng', 'trainin'],
      'policy': ['polisy', 'polici', 'polocy'],
      'procedure': ['procedur', 'prosedure', 'procedyre'],
      'overtime': ['overime', 'ovetime', 'overtme'],
      'timesheet': ['timeshet', 'timesheet', 'timesheat'],
      'payroll': ['payrol', 'payrolle', 'payrole'],
      'insurance': ['insurence', 'insuranse', 'insuranse'],
      'retirement': ['retirment', 'retiremnt', 'retiremen'],
      'harassment': ['harasment', 'harassmnt', 'harrasment'],
      'discrimination': ['discriminaton', 'discrimation', 'discriminashun'],
      'grievance': ['grevance', 'grievanse', 'grivance'],
      'disciplinary': ['disciplinry', 'disciplanary', 'disciplinery'],
      'termination': ['terminaton', 'terminashun', 'terminstion'],
      'onboarding': ['onbording', 'onboarding', 'on-boarding'],
      'offboarding': ['offbording', 'off-boarding', 'ofboarding']
    };

    // Create reverse mapping for quick lookup
    this.hrCorrections = {};
    Object.entries(this.hrVocabulary).forEach(([correct, misspellings]) => {
      misspellings.forEach(misspelling => {
        this.hrCorrections[misspelling.toLowerCase()] = correct;
      });
    });
  }

  /**
   * Initialize keyboard layout for detecting adjacent key typos
   */
  initializeKeyboardLayout() {
    this.keyboardLayout = {
      'q': ['w', 'a'],
      'w': ['q', 'e', 'a', 's'],
      'e': ['w', 'r', 's', 'd'],
      'r': ['e', 't', 'd', 'f'],
      't': ['r', 'y', 'f', 'g'],
      'y': ['t', 'u', 'g', 'h'],
      'u': ['y', 'i', 'h', 'j'],
      'i': ['u', 'o', 'j', 'k'],
      'o': ['i', 'p', 'k', 'l'],
      'p': ['o', 'l'],
      'a': ['q', 'w', 's', 'z'],
      's': ['a', 'w', 'e', 'd', 'z', 'x'],
      'd': ['s', 'e', 'r', 'f', 'x', 'c'],
      'f': ['d', 'r', 't', 'g', 'c', 'v'],
      'g': ['f', 't', 'y', 'h', 'v', 'b'],
      'h': ['g', 'y', 'u', 'j', 'b', 'n'],
      'j': ['h', 'u', 'i', 'k', 'n', 'm'],
      'k': ['j', 'i', 'o', 'l', 'm'],
      'l': ['k', 'o', 'p'],
      'z': ['a', 's', 'x'],
      'x': ['z', 's', 'd', 'c'],
      'c': ['x', 'd', 'f', 'v'],
      'v': ['c', 'f', 'g', 'b'],
      'b': ['v', 'g', 'h', 'n'],
      'n': ['b', 'h', 'j', 'm'],
      'm': ['n', 'j', 'k']
    };
  }

  /**
   * Calculate Levenshtein distance between two strings
   * @param {string} str1 - First string
   * @param {string} str2 - Second string
   * @returns {number} - Edit distance
   */
  levenshteinDistance(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len2; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= len1; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= len2; i++) {
      for (let j = 1; j <= len1; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          );
        }
      }
    }

    return matrix[len2][len1];
  }

  /**
   * Check if two characters are adjacent on keyboard
   * @param {string} char1 - First character
   * @param {string} char2 - Second character
   * @returns {boolean} - True if adjacent
   */
  areAdjacentKeys(char1, char2) {
    const adjacent = this.keyboardLayout[char1.toLowerCase()];
    return adjacent && adjacent.includes(char2.toLowerCase());
  }

  /**
   * Generate phonetic representation of a word
   * @param {string} word - Input word
   * @returns {string} - Phonetic representation
   */
  getPhoneticRepresentation(word) {
    let phonetic = word.toLowerCase();
    
    Object.entries(this.phoneticMappings).forEach(([pattern, replacement]) => {
      phonetic = phonetic.replace(new RegExp(pattern, 'g'), replacement);
    });

    return phonetic;
  }

  /**
   * Correct a single word
   * @param {string} word - Word to correct
   * @returns {Object} - Correction result
   */
  correctWord(word) {
    const lowerWord = word.toLowerCase();
    
    // Check direct typo mapping
    if (this.commonTypos[lowerWord]) {
      return {
        original: word,
        corrected: this.commonTypos[lowerWord],
        confidence: 0.95,
        type: 'common_typo'
      };
    }

    // Check HR vocabulary corrections
    if (this.hrCorrections[lowerWord]) {
      return {
        original: word,
        corrected: this.hrCorrections[lowerWord],
        confidence: 0.9,
        type: 'hr_vocabulary'
      };
    }

    // Find best match using edit distance
    let bestMatch = null;
    let minDistance = Infinity;
    let bestConfidence = 0;

    // Check against HR vocabulary
    Object.keys(this.hrVocabulary).forEach(correctWord => {
      const distance = this.levenshteinDistance(lowerWord, correctWord);
      const maxLen = Math.max(lowerWord.length, correctWord.length);
      const similarity = 1 - (distance / maxLen);
      
      if (distance < minDistance && similarity > 0.6) {
        minDistance = distance;
        bestMatch = correctWord;
        bestConfidence = similarity;
      }
    });

    // Check against common words
    const commonWords = Object.values(this.commonTypos);
    commonWords.forEach(correctWord => {
      const distance = this.levenshteinDistance(lowerWord, correctWord);
      const maxLen = Math.max(lowerWord.length, correctWord.length);
      const similarity = 1 - (distance / maxLen);
      
      if (distance < minDistance && similarity > 0.7) {
        minDistance = distance;
        bestMatch = correctWord;
        bestConfidence = similarity;
      }
    });

    if (bestMatch && bestConfidence > 0.6) {
      return {
        original: word,
        corrected: bestMatch,
        confidence: bestConfidence,
        type: 'similarity_match',
        editDistance: minDistance
      };
    }

    // No correction found
    return {
      original: word,
      corrected: word,
      confidence: 1.0,
      type: 'no_correction'
    };
  }

  /**
   * Correct entire text
   * @param {string} text - Text to correct
   * @returns {Object} - Correction results
   */
  correctText(text) {
    const words = text.split(/(\s+|[^\w\s])/);
    const corrections = [];
    let correctedText = '';
    let hasCorrections = false;

    words.forEach(word => {
      if (/^\w+$/.test(word)) {
        const correction = this.correctWord(word);
        corrections.push(correction);
        
        if (correction.corrected !== correction.original) {
          hasCorrections = true;
        }
        
        correctedText += correction.corrected;
      } else {
        correctedText += word;
      }
    });

    return {
      originalText: text,
      correctedText: correctedText,
      hasCorrections: hasCorrections,
      corrections: corrections.filter(c => c.type !== 'no_correction'),
      confidence: corrections.length > 0 ? 
        corrections.reduce((sum, c) => sum + c.confidence, 0) / corrections.length : 1.0
    };
  }

  /**
   * Suggest alternative spellings
   * @param {string} word - Word to get suggestions for
   * @param {number} maxSuggestions - Maximum number of suggestions
   * @returns {Array} - Array of suggestions
   */
  getSuggestions(word, maxSuggestions = 5) {
    const suggestions = [];
    const lowerWord = word.toLowerCase();

    // Get all possible corrections
    const allWords = [
      ...Object.keys(this.commonTypos),
      ...Object.values(this.commonTypos),
      ...Object.keys(this.hrVocabulary),
      ...Object.values(this.hrVocabulary).flat()
    ];

    allWords.forEach(candidateWord => {
      const distance = this.levenshteinDistance(lowerWord, candidateWord.toLowerCase());
      const maxLen = Math.max(lowerWord.length, candidateWord.length);
      const similarity = 1 - (distance / maxLen);
      
      if (similarity > 0.5 && candidateWord.toLowerCase() !== lowerWord) {
        suggestions.push({
          word: candidateWord,
          similarity: similarity,
          editDistance: distance
        });
      }
    });

    // Sort by similarity and return top suggestions
    return suggestions
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxSuggestions)
      .map(s => s.word);
  }

  /**
   * Detect and analyze typos in text
   * @param {string} text - Text to analyze
   * @returns {Object} - Typo analysis
   */
  analyzeTypos(text) {
    const correction = this.correctText(text);
    const words = text.split(/\s+/);
    const typoCount = correction.corrections.length;
    const totalWords = words.filter(w => /^\w+$/.test(w)).length;
    const typoRate = totalWords > 0 ? typoCount / totalWords : 0;

    return {
      ...correction,
      statistics: {
        totalWords: totalWords,
        typoCount: typoCount,
        typoRate: typoRate,
        qualityScore: 1 - typoRate
      },
      needsCorrection: typoRate > 0.1, // More than 10% typos
      severity: typoRate > 0.3 ? 'high' : typoRate > 0.1 ? 'medium' : 'low'
    };
  }
}

// Singleton instance
const spellCorrector = new SpellCorrector();

module.exports = spellCorrector;
