# Sprint 1: MVC Sequence Diagram

## Authentication Flow Sequence Diagram

```
+--------+     +----------------+     +----------------+     +----------------+     +---------------+
| Client |     | View (React)   |     | Controller     |     | Model (User)   |     | Database      |
+--------+     +----------------+     +----------------+     +----------------+     +---------------+
    |                 |                      |                      |                     |
    | 1. Enter Login  |                      |                      |                     |
    | Credentials     |                      |                      |                     |
    |---------------->|                      |                      |                     |
    |                 |                      |                      |                     |
    |                 | 2. Submit Login      |                      |                     |
    |                 | Request (email, pwd) |                      |                     |
    |                 |--------------------->|                      |                     |
    |                 |                      |                      |                     |
    |                 |                      | 3. Find User by Email|                     |
    |                 |                      |--------------------->|                     |
    |                 |                      |                      |                     |
    |                 |                      |                      | 4. Query Database   |
    |                 |                      |                      |-------------------->|
    |                 |                      |                      |                     |
    |                 |                      |                      | 5. Return User Data |
    |                 |                      |                      |<--------------------|
    |                 |                      |                      |                     |
    |                 |                      | 6. Return User       |                     |
    |                 |                      |<---------------------|                     |
    |                 |                      |                      |                     |
    |                 |                      | 7. Compare Password  |                     |
    |                 |                      |--------------------->|                     |
    |                 |                      |                      |                     |
    |                 |                      | 8. Return Result     |                     |
    |                 |                      |<---------------------|                     |
    |                 |                      |                      |                     |
    |                 |                      | 9. Generate JWT Token|                     |
    |                 |                      |----------------------|                     |
    |                 |                      |                      |                     |
    |                 | 10. Return Token     |                      |                     |
    |                 |<---------------------|                      |                     |
    |                 |                      |                      |                     |
    | 11. Store Token |                      |                      |                     |
    | & Redirect      |                      |                      |                     |
    |<----------------|                      |                      |                     |
    |                 |                      |                      |                     |
```

## User Registration Sequence Diagram (Admin Creating a User)

```
+--------+     +----------------+     +----------------+     +----------------+     +---------------+
| Admin  |     | View (React)   |     | Controller     |     | Model (User)   |     | Database      |
+--------+     +----------------+     +----------------+     +----------------+     +---------------+
    |                 |                      |                      |                     |
    | 1. Enter User   |                      |                      |                     |
    | Details         |                      |                      |                     |
    |---------------->|                      |                      |                     |
    |                 |                      |                      |                     |
    |                 | 2. Submit Create     |                      |                     |
    |                 | User Request         |                      |                     |
    |                 |--------------------->|                      |                     |
    |                 |                      |                      |                     |
    |                 |                      | 3. Validate Input    |                     |
    |                 |                      |----------------------|                     |
    |                 |                      |                      |                     |
    |                 |                      | 4. Check Email Exists|                     |
    |                 |                      |--------------------->|                     |
    |                 |                      |                      |                     |
    |                 |                      |                      | 5. Query Database   |
    |                 |                      |                      |-------------------->|
    |                 |                      |                      |                     |
    |                 |                      |                      | 6. Return Result    |
    |                 |                      |                      |<--------------------|
    |                 |                      |                      |                     |
    |                 |                      | 7. Return Result     |                     |
    |                 |                      |<---------------------|                     |
    |                 |                      |                      |                     |
    |                 |                      | 8. Hash Password     |                     |
    |                 |                      |--------------------->|                     |
    |                 |                      |                      |                     |
    |                 |                      | 9. Return Hashed Pwd |                     |
    |                 |                      |<---------------------|                     |
    |                 |                      |                      |                     |
    |                 |                      | 10. Create User      |                     |
    |                 |                      |--------------------->|                     |
    |                 |                      |                      |                     |
    |                 |                      |                      | 11. Save to Database|
    |                 |                      |                      |-------------------->|
    |                 |                      |                      |                     |
    |                 |                      |                      | 12. Return Success  |
    |                 |                      |                      |<--------------------|
    |                 |                      |                      |                     |
    |                 |                      | 13. Return User Data |                     |
    |                 |                      |<---------------------|                     |
    |                 |                      |                      |                     |
    |                 | 14. Return Success   |                      |                     |
    |                 |<---------------------|                      |                     |
    |                 |                      |                      |                     |
    | 15. Display     |                      |                      |                     |
    | Success Message |                      |                      |                     |
    |<----------------|                      |                      |                     |
    |                 |                      |                      |                     |
```

## Password Reset Sequence Diagram

```
+--------+     +----------------+     +----------------+     +----------------+     +---------------+     +----------+
| User   |     | View (React)   |     | Controller     |     | Model (User)   |     | Database      |     | Email.js |
+--------+     +----------------+     +----------------+     +----------------+     +---------------+     +----------+
    |                 |                      |                      |                     |                    |
    | 1. Request      |                      |                      |                     |                    |
    | Password Reset  |                      |                      |                     |                    |
    |---------------->|                      |                      |                     |                    |
    |                 |                      |                      |                     |                    |
    |                 | 2. Submit Email      |                      |                     |                    |
    |                 |--------------------->|                      |                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 3. Find User by Email|                     |                    |
    |                 |                      |--------------------->|                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      |                      | 4. Query Database   |                    |
    |                 |                      |                      |-------------------->|                    |
    |                 |                      |                      |                     |                    |
    |                 |                      |                      | 5. Return User Data |                    |
    |                 |                      |                      |<--------------------|                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 6. Return User       |                     |                    |
    |                 |                      |<---------------------|                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 7. Generate Reset    |                     |                    |
    |                 |                      | Token                |                     |                    |
    |                 |                      |----------------------|                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 8. Send Email with   |                     |                    |
    |                 |                      | Reset Link           |                     |                    |
    |                 |                      |---------------------------------------------------------->|    |
    |                 |                      |                      |                     |                    |
    |                 |                      |                      |                     |                    |
    |                 | 9. Return Success    |                      |                     |                    |
    |                 |<---------------------|                      |                     |                    |
    |                 |                      |                      |                     |                    |
    | 10. Receive     |                      |                      |                     |                    |
    | Email & Click   |                      |                      |                     |                    |
    | Reset Link      |                      |                      |                     |                    |
    |<------------------------------------------------------------ |                     |                    |
    |                 |                      |                      |                     |                    |
    | 11. Enter New   |                      |                      |                     |                    |
    | Password        |                      |                      |                     |                    |
    |---------------->|                      |                      |                     |                    |
    |                 |                      |                      |                     |                    |
    |                 | 12. Submit New       |                      |                     |                    |
    |                 | Password & Token     |                      |                     |                    |
    |                 |--------------------->|                      |                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 13. Verify Token     |                     |                    |
    |                 |                      |----------------------|                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 14. Hash New Password|                     |                    |
    |                 |                      |--------------------->|                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      | 15. Update User      |                     |                    |
    |                 |                      |--------------------->|                     |                    |
    |                 |                      |                      |                     |                    |
    |                 |                      |                      | 16. Update Database |                    |
    |                 |                      |                      |-------------------->|                    |
    |                 |                      |                      |                     |                    |
    |                 |                      |                      | 17. Return Success  |                    |
    |                 |                      |                      |<--------------------|                    |
    |                 |                      |                      |                     |                    |
    |                 | 18. Return Success   |                      |                     |                    |
    |                 |<---------------------|                      |                     |                    |
    |                 |                      |                      |                     |                    |
    | 19. Redirect to |                      |                      |                     |                    |
    | Login Page      |                      |                      |                     |                    |
    |<----------------|                      |                      |                     |                    |
    |                 |                      |                      |                     |                    |
```
