import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  useMediaQuery,
  useTheme,
  Divider,
  Paper,
  Avatar,
} from '@mui/material';
import {
  Work as WorkIcon,
  LocationOn as LocationOnIcon,
  School as SchoolIcon,
  Check as CheckIcon,
  ArrowForward as ArrowForwardIcon,
  Close as CloseIcon,
  Menu as MenuIcon,
  BusinessCenter as BusinessCenterIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  CalendarToday as CalendarTodayIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import axios from 'axios';
import Logo from '../components/Logo';
import ApplicationTracker from '../components/ApplicationTracker';

// Styled components
const HeroSection = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
  color: theme.palette.common.white,
  padding: theme.spacing(12, 0),
  position: 'relative',
  overflow: 'hidden',
  [theme.breakpoints.down('md')]: {
    padding: theme.spacing(8, 0),
  },
}));

const HeroPattern = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  opacity: 0.1,
  backgroundImage: 'url(/images/pattern.svg)',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  zIndex: 0,
}));

const JobCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.shape.borderRadius * 2,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
  },
}));

const FeatureCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  textAlign: 'center',
  borderRadius: theme.shape.borderRadius * 2,
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'translateY(-5px)',
  },
}));

const FeatureIcon = styled(Avatar)(({ theme }) => ({
  width: 64,
  height: 64,
  backgroundColor: theme.palette.primary.main,
  marginBottom: theme.spacing(2),
}));

const NewLandingPage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedJob, setSelectedJob] = useState(null);
  const [openJobDialog, setOpenJobDialog] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Fetch jobs on component mount
  useEffect(() => {
    const api = axios.create({
      baseURL: "http://localhost:5000/api",
    });

    api.get('/jobs')
      .then((response) => {
        console.log('Jobs fetched successfully:', response.data);
        setJobs(Array.isArray(response.data) ? response.data : []);
        setLoading(false);
      })
      .catch((error) => {
        console.error('Error fetching jobs:', error);
        setLoading(false);
      });
  }, []);

  const handleLogin = () => {
    navigate('/login');
  };

  const handleApply = () => {
    navigate('/apply');
  };

  const handleJobDetails = (job) => {
    setSelectedJob(job);
    setOpenJobDialog(true);
  };

  const handleCloseJobDialog = () => {
    setOpenJobDialog(false);
  };

  // Format responsibilities and requirements as arrays
  const formatListItems = (items) => {
    if (!items) return [];
    if (Array.isArray(items)) return items;
    return items.split(',').map(item => item.trim()).filter(item => item);
  };

  return (
    <Box sx={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <AppBar position="sticky" color="default" elevation={0} sx={{ bgcolor: 'background.paper' }}>
        <Toolbar>
          <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            <Logo size={isMobile ? 'small' : 'medium'} />
          </Box>

          {isMobile ? (
            <IconButton
              edge="end"
              color="inherit"
              aria-label="menu"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <MenuIcon />
            </IconButton>
          ) : (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button color="inherit" href="#jobs">Jobs</Button>
              <Button color="inherit" href="#track-application">Track Application</Button>
              <Button color="inherit" href="#features">Features</Button>
              <Button color="inherit" href="#about">About</Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleLogin}
                sx={{ ml: 2 }}
              >
                Login
              </Button>
            </Box>
          )}
        </Toolbar>
      </AppBar>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <Box sx={{
          position: 'fixed',
          top: 64,
          left: 0,
          right: 0,
          bgcolor: 'background.paper',
          zIndex: 1000,
          boxShadow: 3,
        }}>
          <List>
            <ListItem button component="a" href="#jobs" onClick={() => setMobileMenuOpen(false)}>
              <ListItemText primary="Jobs" />
            </ListItem>
            <ListItem button component="a" href="#track-application" onClick={() => setMobileMenuOpen(false)}>
              <ListItemText primary="Track Application" />
            </ListItem>
            <ListItem button component="a" href="#features" onClick={() => setMobileMenuOpen(false)}>
              <ListItemText primary="Features" />
            </ListItem>
            <ListItem button component="a" href="#about" onClick={() => setMobileMenuOpen(false)}>
              <ListItemText primary="About" />
            </ListItem>
            <Divider />
            <ListItem button onClick={() => { handleLogin(); setMobileMenuOpen(false); }}>
              <ListItemText primary="Login" primaryTypographyProps={{ color: 'primary' }} />
            </ListItem>
          </List>
        </Box>
      )}

      {/* Hero Section */}
      <HeroSection>
        <HeroPattern />
        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={7}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <Typography
                  variant="h2"
                  component="h1"
                  fontWeight={700}
                  gutterBottom
                  sx={{ fontSize: { xs: '2.5rem', md: '3.5rem' } }}
                >
                  Streamline Your HR Management
                </Typography>
                <Typography
                  variant="h5"
                  sx={{ mb: 4, opacity: 0.9, maxWidth: 600 }}
                >
                  A comprehensive solution for managing your workforce, recruitment, and HR operations.
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    color="secondary"
                    size="large"
                    onClick={handleLogin}
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1rem',
                      fontWeight: 600,
                      bgcolor: 'white',
                      color: 'primary.main',
                      '&:hover': {
                        bgcolor: 'rgba(255,255,255,0.9)',
                      }
                    }}
                  >
                    Get Started
                  </Button>
                  <Button
                    variant="outlined"
                    color="inherit"
                    size="large"
                    href="#jobs"
                    sx={{
                      px: 4,
                      py: 1.5,
                      fontSize: '1rem',
                      fontWeight: 600,
                      borderColor: 'rgba(255,255,255,0.5)',
                      '&:hover': {
                        borderColor: 'white',
                        bgcolor: 'rgba(255,255,255,0.1)',
                      }
                    }}
                  >
                    View Jobs
                  </Button>
                </Box>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={5} sx={{ display: { xs: 'none', md: 'block' } }}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <Box
                  component="img"
                  src="/images/hero-illustration.svg"
                  alt="HR Management"
                  sx={{
                    width: '100%',
                    maxWidth: 500,
                    filter: 'drop-shadow(0 10px 20px rgba(0,0,0,0.2))',
                  }}
                />
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </HeroSection>

      {/* Jobs Section */}
      <Box id="jobs" sx={{ py: 8, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h3"
              component="h2"
              fontWeight={700}
              gutterBottom
              sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}
            >
              Available Positions
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
              Join our team and be part of something great. Check out our current openings.
            </Typography>
          </Box>

          {loading ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography>Loading available positions...</Typography>
            </Box>
          ) : (
            <Grid container spacing={3}>
              {jobs.length > 0 ? (
                jobs.map((job) => (
                  <Grid item xs={12} sm={6} md={4} key={job._id}>
                    <JobCard elevation={2}>
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography variant="h5" component="h3" gutterBottom fontWeight={600}>
                          {job.title}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LocationOnIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            {job.location}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <WorkIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            {job.jobType}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <SchoolIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            {job.academicLevel}
                          </Typography>
                        </Box>
                        {job.endDate && (
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <CalendarTodayIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                            <Typography variant="body2" color="text.secondary">
                              Deadline: {new Date(job.endDate).toLocaleDateString()}
                            </Typography>
                          </Box>
                        )}
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{
                            mb: 2,
                            display: '-webkit-box',
                            WebkitLineClamp: 3,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            height: 60,
                          }}
                        >
                          {job.description}
                        </Typography>
                      </CardContent>
                      <CardActions sx={{ p: 2, pt: 0 }}>
                        <Button
                          size="small"
                          onClick={() => handleJobDetails(job)}
                        >
                          View Details
                        </Button>
                        <Button
                          size="small"
                          variant="contained"
                          color="primary"
                          onClick={handleApply}
                          sx={{ ml: 'auto' }}
                        >
                          Apply Now
                        </Button>
                      </CardActions>
                    </JobCard>
                  </Grid>
                ))
              ) : (
                <Box sx={{ textAlign: 'center', py: 4, width: '100%' }}>
                  <Typography variant="h6" color="text.secondary">
                    No open positions at the moment. Please check back later.
                  </Typography>
                </Box>
              )}
            </Grid>
          )}
        </Container>
      </Box>

      {/* Application Tracker Section */}
      <Box id="track-application" sx={{ py: 8, bgcolor: 'background.default' }}>
        <Container maxWidth="lg">
          <ApplicationTracker />
        </Container>
      </Box>

      {/* Features Section */}
      <Box id="features" sx={{ py: 8, bgcolor: 'background.paper' }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h3"
              component="h2"
              fontWeight={700}
              gutterBottom
              sx={{ fontSize: { xs: '2rem', md: '2.5rem' } }}
            >
              Why Choose MjayTrack
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 700, mx: 'auto' }}>
              Our HR management system offers powerful features to streamline your operations.
            </Typography>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={4}>
              <FeatureCard elevation={1}>
                <FeatureIcon>
                  <PeopleIcon />
                </FeatureIcon>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Employee Management
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Easily manage employee information, attendance, and performance in one place.
                </Typography>
              </FeatureCard>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FeatureCard elevation={1}>
                <FeatureIcon>
                  <BusinessCenterIcon />
                </FeatureIcon>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Recruitment & Hiring
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Streamline your recruitment process with AI-powered CV matching and candidate evaluation.
                </Typography>
              </FeatureCard>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FeatureCard elevation={1}>
                <FeatureIcon>
                  <AssessmentIcon />
                </FeatureIcon>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Performance Tracking
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Set goals, track progress, and evaluate employee performance with comprehensive tools.
                </Typography>
              </FeatureCard>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FeatureCard elevation={1}>
                <FeatureIcon>
                  <SecurityIcon />
                </FeatureIcon>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Secure & Compliant
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Keep your HR data secure with role-based access control and data protection features.
                </Typography>
              </FeatureCard>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FeatureCard elevation={1}>
                <FeatureIcon>
                  <SpeedIcon />
                </FeatureIcon>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Time & Attendance
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Track employee attendance, manage leave requests, and monitor work hours efficiently.
                </Typography>
              </FeatureCard>
            </Grid>
            <Grid item xs={12} sm={6} md={4}>
              <FeatureCard elevation={1}>
                <FeatureIcon>
                  <WorkIcon />
                </FeatureIcon>
                <Typography variant="h6" gutterBottom fontWeight={600}>
                  Task Management
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Assign tasks, track progress, and ensure timely completion of work across your organization.
                </Typography>
              </FeatureCard>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Footer */}
      <Box
        component="footer"
        sx={{
          py: 4,
          bgcolor: 'background.paper',
          borderTop: 1,
          borderColor: 'divider',
          mt: 'auto'
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 2 }}>
                <Logo size="medium" />
              </Box>
              <Typography variant="body2" color="text.secondary">
                A comprehensive HR management solution for modern enterprises.
              </Typography>
            </Grid>
            <Grid item xs={6} md={2}>
              <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                Company
              </Typography>
              <List dense disablePadding>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">About Us</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Careers</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Contact</Button>
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={6} md={2}>
              <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                Resources
              </Typography>
              <List dense disablePadding>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Blog</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Help Center</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Documentation</Button>
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={6} md={2}>
              <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                Legal
              </Typography>
              <List dense disablePadding>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Privacy Policy</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Terms of Service</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Cookie Policy</Button>
                </ListItem>
              </List>
            </Grid>
            <Grid item xs={6} md={2}>
              <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                Connect
              </Typography>
              <List dense disablePadding>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Twitter</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">LinkedIn</Button>
                </ListItem>
                <ListItem disableGutters disablePadding>
                  <Button color="inherit" size="small">Facebook</Button>
                </ListItem>
              </List>
            </Grid>
          </Grid>
          <Divider sx={{ my: 3 }} />
          <Typography variant="body2" color="text.secondary" align="center">
            &copy; {new Date().getFullYear()} MjayTrack. All rights reserved.
          </Typography>
        </Container>
      </Box>

      {/* Job Details Dialog */}
      <Dialog
        open={openJobDialog}
        onClose={handleCloseJobDialog}
        maxWidth="md"
        fullWidth
        scroll="paper"
      >
        {selectedJob && (
          <>
            <DialogTitle>
              <Box sx={{ pr: 6 }}>
                <Typography variant="h5" component="h2" fontWeight={600}>
                  {selectedJob.title}
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                  <Chip
                    icon={<LocationOnIcon fontSize="small" />}
                    label={selectedJob.location}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    icon={<WorkIcon fontSize="small" />}
                    label={selectedJob.jobType}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    icon={<SchoolIcon fontSize="small" />}
                    label={selectedJob.academicLevel}
                    size="small"
                    variant="outlined"
                  />
                  {selectedJob.endDate && (
                    <Chip
                      icon={<CalendarTodayIcon fontSize="small" />}
                      label={`Deadline: ${new Date(selectedJob.endDate).toLocaleDateString()}`}
                      size="small"
                      variant="outlined"
                      color="error"
                    />
                  )}
                </Box>
              </Box>
              <IconButton
                aria-label="close"
                onClick={handleCloseJobDialog}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500],
                }}
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent dividers>
              <Typography variant="h6" gutterBottom fontWeight={600}>
                Job Description
              </Typography>
              <Typography variant="body1" paragraph>
                {selectedJob.description}
              </Typography>

              <Typography variant="h6" gutterBottom fontWeight={600}>
                Responsibilities
              </Typography>
              <List disablePadding>
                {formatListItems(selectedJob.responsibilities).map((item, index) => (
                  <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CheckIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>

              <Typography variant="h6" gutterBottom fontWeight={600} sx={{ mt: 2 }}>
                Requirements
              </Typography>
              <List disablePadding>
                {formatListItems(selectedJob.requirements).map((item, index) => (
                  <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                    <ListItemIcon sx={{ minWidth: 36 }}>
                      <CheckIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={item} />
                  </ListItem>
                ))}
              </List>
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 2 }}>
              <Button onClick={handleCloseJobDialog} color="inherit">
                Close
              </Button>
              <Button
                variant="contained"
                color="primary"
                endIcon={<ArrowForwardIcon />}
                onClick={handleApply}
              >
                Apply for this Position
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default NewLandingPage;
