const Notification = require('../models/Notification');
const User = require('../models/user');
const Task = require('../models/Task');
const LeaveRequest = require('../models/LeaveRequest');
const Application = require('../models/application');

/**
 * Create a new notification
 * @param {Object} notificationData - Notification data
 * @returns {Promise<Object>} Created notification
 */
const createNotification = async (notificationData) => {
  try {
    const notification = new Notification(notificationData);
    await notification.save();
    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
};

/**
 * Create task assignment notification
 * @param {Object} task - Task object
 * @returns {Promise<Object>} Created notification
 */
const createTaskAssignedNotification = async (task) => {
  try {
    // Get the creator and assignee details
    const creator = await User.findById(task.createdBy);
    const assignee = await User.findById(task.assignedTo);
    
    if (!assignee) {
      console.error('Assignee not found for task:', task._id);
      return null;
    }
    
    const notification = await createNotification({
      userId: task.assignedTo,
      type: 'TASK_ASSIGNED',
      title: 'New Task Assigned',
      content: `You have been assigned a new task: ${task.title}`,
      resourceType: 'TASK',
      resourceId: task._id,
      data: {
        taskTitle: task.title,
        priority: task.priority,
        deadline: task.deadline,
        createdBy: creator ? creator.name : 'HR'
      }
    });
    
    return notification;
  } catch (error) {
    console.error('Error creating task assigned notification:', error);
    return null;
  }
};

/**
 * Create task update notification
 * @param {Object} task - Task object
 * @param {String} updateMessage - Update message
 * @returns {Promise<Object>} Created notification
 */
const createTaskUpdateNotification = async (task, updateMessage) => {
  try {
    // Notify the assignee about the update
    const notification = await createNotification({
      userId: task.assignedTo,
      type: 'TASK_UPDATED',
      title: 'Task Updated',
      content: `Your task "${task.title}" has been updated: ${updateMessage}`,
      resourceType: 'TASK',
      resourceId: task._id,
      data: {
        taskTitle: task.title,
        status: task.status,
        progress: task.progress,
        updateMessage
      }
    });
    
    return notification;
  } catch (error) {
    console.error('Error creating task update notification:', error);
    return null;
  }
};

/**
 * Create leave request notification
 * @param {Object} leaveRequest - Leave request object
 * @param {String} type - Notification type
 * @returns {Promise<Object>} Created notification
 */
const createLeaveRequestNotification = async (leaveRequest, type) => {
  try {
    let title, content;
    
    switch (type) {
      case 'LEAVE_REQUEST_SUBMITTED':
        // Notify HR about new leave request
        const user = await User.findById(leaveRequest.userId);
        title = 'New Leave Request';
        content = `${user ? user.name : 'An employee'} has submitted a new leave request`;
        
        // Find all HR users to notify them
        const hrUsers = await User.find({ role: 'hr' });
        
        // Create a notification for each HR user
        const notifications = [];
        for (const hrUser of hrUsers) {
          const notification = await createNotification({
            userId: hrUser._id,
            type,
            title,
            content,
            resourceType: 'LEAVE_REQUEST',
            resourceId: leaveRequest._id,
            data: {
              employeeName: leaveRequest.employeeName,
              leaveType: leaveRequest.leaveType,
              startDate: leaveRequest.startDate,
              endDate: leaveRequest.endDate,
              reason: leaveRequest.reason
            }
          });
          
          notifications.push(notification);
        }
        
        return notifications;
        
      case 'LEAVE_REQUEST_APPROVED':
        title = 'Leave Request Approved';
        content = `Your ${leaveRequest.leaveType} request has been approved`;
        break;
        
      case 'LEAVE_REQUEST_REJECTED':
        title = 'Leave Request Rejected';
        content = `Your ${leaveRequest.leaveType} request has been rejected`;
        break;
        
      default:
        return null;
    }
    
    // For approval/rejection, notify the employee
    const notification = await createNotification({
      userId: leaveRequest.userId,
      type,
      title,
      content,
      resourceType: 'LEAVE_REQUEST',
      resourceId: leaveRequest._id,
      data: {
        leaveType: leaveRequest.leaveType,
        startDate: leaveRequest.startDate,
        endDate: leaveRequest.endDate,
        status: leaveRequest.status
      }
    });
    
    return notification;
  } catch (error) {
    console.error('Error creating leave request notification:', error);
    return null;
  }
};

/**
 * Create application notification
 * @param {Object} application - Application object
 * @param {String} type - Notification type
 * @returns {Promise<Object>} Created notification
 */
const createApplicationNotification = async (application, type) => {
  try {
    // For new applications, notify all HR users
    if (type === 'NEW_APPLICATION') {
      const hrUsers = await User.find({ role: 'hr' });
      
      const notifications = [];
      for (const hrUser of hrUsers) {
        const notification = await createNotification({
          userId: hrUser._id,
          type,
          title: 'New Job Application',
          content: `${application.fullname} has applied for the ${application.jobTitle} position`,
          resourceType: 'APPLICATION',
          resourceId: application._id,
          data: {
            applicantName: application.fullname,
            applicantEmail: application.email,
            jobTitle: application.jobTitle,
            position: application.position
          }
        });
        
        notifications.push(notification);
      }
      
      return notifications;
    }
    
    return null;
  } catch (error) {
    console.error('Error creating application notification:', error);
    return null;
  }
};

module.exports = {
  createNotification,
  createTaskAssignedNotification,
  createTaskUpdateNotification,
  createLeaveRequestNotification,
  createApplicationNotification
};
