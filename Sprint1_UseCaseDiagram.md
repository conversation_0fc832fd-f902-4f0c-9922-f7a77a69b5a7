# Sprint 1: Use Case Diagram (UML)

```
@startuml HR Management System - Sprint 1 Use Cases

skinparam actorStyle awesome
skinparam packageStyle rectangle
skinparam usecaseStyle roundbox

' Actors
actor "Admin" as admin
actor "H<PERSON>" as hr
actor "Normal User" as user
actor "All Users" as allUsers

' Use case packages
rectangle "Authentication System" {
  usecase "Login" as login
  usecase "Logout" as logout
  usecase "Reset Password" as resetPassword
  usecase "Change Password" as changePassword
}

rectangle "User Management" {
  usecase "Create User Account" as createUser
  usecase "Edit User Account" as editUser
  usecase "Deactivate User Account" as deactivateUser
  usecase "View User List" as viewUsers
}

rectangle "Profile Management" {
  usecase "View Profile" as viewProfile
  usecase "Edit Profile" as editProfile
}

rectangle "Dashboard Access" {
  usecase "Access Admin Dashboard" as adminDashboard
  usecase "Access HR Dashboard" as hrDashboard
  usecase "Access User Dashboard" as userDashboard
}

rectangle "Security Management" {
  usecase "View Audit Logs" as viewAuditLogs
  usecase "View Login History" as viewLoginHistory
}

' Relationships
allUsers --> login
allUsers --> logout
allUsers --> resetPassword
allUsers --> changePassword
allUsers --> viewProfile
allUsers --> editProfile

admin --> createUser
admin --> editUser
admin --> deactivateUser
admin --> viewUsers
admin --> viewAuditLogs
admin --> viewLoginHistory
admin --> adminDashboard

hr --> hrDashboard
hr --> viewUsers

user --> userDashboard

' Inheritance
admin --|> allUsers
hr --|> allUsers
user --|> allUsers

@enduml
```

## Use Case Diagram Description

The Use Case Diagram for Sprint 1 illustrates the interactions between different user types (actors) and the system functionalities being implemented in this sprint.

### Actors
- **Admin**: System administrator with full access to all features
- **HR**: Human Resources personnel with access to HR-specific features
- **Normal User**: Regular employees with limited access
- **All Users**: Generic actor representing common functionality for all user types

### Use Case Packages

1. **Authentication System**
   - Login: All users can authenticate into the system
   - Logout: All users can sign out of the system
   - Reset Password: All users can request a password reset
   - Change Password: All users can change their password

2. **User Management**
   - Create User Account: Admin can create new user accounts
   - Edit User Account: Admin can modify existing user accounts
   - Deactivate User Account: Admin can disable user accounts
   - View User List: Admin and HR can view the list of users

3. **Profile Management**
   - View Profile: All users can view their own profile
   - Edit Profile: All users can edit their own profile information

4. **Dashboard Access**
   - Access Admin Dashboard: Admin can access the admin dashboard
   - Access HR Dashboard: HR can access the HR dashboard
   - Access User Dashboard: Normal users can access the user dashboard

5. **Security Management**
   - View Audit Logs: Admin can view system audit logs
   - View Login History: Admin can view login history records

### Relationships
- Inheritance relationships show that Admin, HR, and Normal User inherit from All Users
- Association relationships show which actors can perform which use cases
