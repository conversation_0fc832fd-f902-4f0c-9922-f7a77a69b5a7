const express = require('express');
const multer = require('multer');
const path = require('path');
const runCVParser = require('../NLP/cvParserService');

const router = express.Router();
const upload = multer({ dest: 'uploads/' });

router.post('/parse-cv', upload.single('cv'), async (req, res) => {
  try {
    const filePath = path.resolve(req.file.path);
    const parsedData = await runCVParser(filePath);
    res.json(parsedData);
  } catch (err) {
    res.status(500).json({ error: err.toString() });
  }
});

module.exports = router;
