import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Tooltip,
  alpha
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled ListItemButton with transition effects
const StyledListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  borderRadius: theme.shape.borderRadius,
  marginBottom: theme.spacing(0.5),
  transition: 'all 0.3s ease',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: active ? '4px' : '0px',
    height: '100%',
    backgroundColor: theme.palette.primary.main,
    transition: 'width 0.3s ease',
  },
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    transform: 'translateX(4px)',
    '&::before': {
      width: '4px',
    },
  },
  ...(active && {
    backgroundColor: alpha(theme.palette.primary.main, 0.12),
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
    },
    '& .MuiListItemText-primary': {
      fontWeight: 600,
      color: theme.palette.primary.main,
    },
  }),
}));

/**
 * NavigationLink component for sidebar navigation with transition effects
 * 
 * @param {Object} props - Component props
 * @param {string} props.to - Target route path
 * @param {React.ReactNode} props.icon - Icon component
 * @param {string} props.text - Link text
 * @param {Function} props.onClick - Optional click handler
 * @param {boolean} props.exact - Whether to match route exactly
 * @returns {JSX.Element} Navigation link component
 */
const NavigationLink = ({ to, icon, text, onClick, exact = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Check if link is active
  const isActive = exact 
    ? location.pathname === to 
    : location.pathname.startsWith(to);
  
  const handleClick = () => {
    navigate(to);
    if (onClick) onClick();
  };
  
  return (
    <Tooltip title={text} placement="right" arrow>
      <StyledListItemButton
        onClick={handleClick}
        active={isActive ? 1 : 0}
        className={`nav-item ${isActive ? 'active' : ''}`}
      >
        {icon && (
          <ListItemIcon sx={{ minWidth: 40 }}>
            {icon}
          </ListItemIcon>
        )}
        <ListItemText primary={text} />
      </StyledListItemButton>
    </Tooltip>
  );
};

NavigationLink.propTypes = {
  to: PropTypes.string.isRequired,
  icon: PropTypes.node,
  text: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  exact: PropTypes.bool,
};

export default NavigationLink;
