const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const Application = require('../models/application'); // Ensure this model exists
const LeaveRequest = require('../models/LeaveRequest'); // Ensure this model exists
const User = require('../models/user'); // Import User model
const Job = require('../models/Job'); // Import Job model
const Evaluation = require('../models/Evaluation'); // Import the Evaluation model
const Attendance = require('../models/Attendance'); // Import the Attendance model
const Task = require('../models/Task'); // Import the Task model

const { authenticate, authorizeRoles } = require('../middleware/authmiddleware');
const { createAIEvaluation } = require('../controllers/aiEvaluationController');
const {
  createLeaveRequestNotification,
  createApplicationNotification
} = require('../controllers/notificationController');
const { sendPasswordChangeEmail } = require('../utils/emailNotifications');

// HR Init Route - Used to verify HR authentication
router.get('/init', authenticate, authorizeRoles('hr'), (req, res) => {
  res.json({ message: 'Hello HR' });
});

// GET all applications with job details
router.get('/applications', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { search } = req.query;

    let query = {};

    // Add search functionality
    if (search) {
      // Try to match by ID if the search looks like a MongoDB ObjectId
      if (/^[0-9a-fA-F]{24}$/.test(search)) {
        query._id = search;
      } else {
        query.$or = [
          { fullname: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { position: { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } },
          { status: { $regex: search, $options: 'i' } }
        ];
      }
    }

    // Fetch applications and populate job details
    const applications = await Application.find(query)
      .sort({ createdAt: -1 }) // Sort by creation date, newest first
      .populate('jobId', 'title jobType location'); // Get job details

    res.json(applications);
  } catch (error) {
    console.error('Error fetching applications:', error);
    res.status(500).json({ message: 'Error fetching applications' });
  }
});

// GET all leave requests with user details
router.get('/leave-requests', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { search } = req.query;

    // Build the base query
    let query = {};

    // Add search functionality
    if (search) {
      // First, find users that match the search criteria
      const matchingUsers = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { job: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      // Get the user IDs
      const userIds = matchingUsers.map(user => user._id);

      // Build the query to search in leave requests
      query.$or = [
        { leaveType: { $regex: search, $options: 'i' } },
        { reason: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } }
      ];

      // Add user IDs to the query if any were found
      if (userIds.length > 0) {
        query.$or.push({ userId: { $in: userIds } });
      }
    }

    // Find all leave requests and populate with user details
    const leaveRequests = await LeaveRequest.find(query)
      .sort({ createdAt: -1 }) // Sort by creation date, newest first
      .populate('userId', 'name email job'); // Get user details

    // Format the response
    const formattedRequests = leaveRequests.map(leaveReq => {
      return {
        _id: leaveReq._id,
        employeeName: leaveReq.employeeName || (leaveReq.userId ? leaveReq.userId.name : 'Unknown'),
        email: leaveReq.userId ? leaveReq.userId.email : 'Unknown',
        job: leaveReq.userId ? leaveReq.userId.job : 'Unknown',
        leaveType: leaveReq.leaveType,
        startDate: leaveReq.startDate,
        endDate: leaveReq.endDate,
        reason: leaveReq.reason,
        status: leaveReq.status,
        createdAt: leaveReq.createdAt
      };
    });

    res.json(formattedRequests);
  } catch (error) {
    console.error('Error fetching leave requests:', error);
    res.status(500).json({ message: 'Error fetching leave requests' });
  }
});

// Route to fetch all users or users by role
router.get('/users', authenticate, authorizeRoles('hr'), async (req, res) => {
  const { role, search } = req.query;

  try {
    let query = {};

    // Filter by role if specified
    if (role) {
      query.role = role;
    }

    // Add search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { job: { $regex: search, $options: 'i' } }
      ];
    }

    // Execute the query
    const users = await User.find(query);

    res.json(users);
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

// Route to fetch all available jobs
router.get('/jobs', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { search } = req.query;

    let query = {};

    // Add search functionality
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } },
        { jobType: { $regex: search, $options: 'i' } }
      ];
    }

    const jobs = await Job.find(query);
    res.json(jobs);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching jobs' });
  }
});

// Route to add a new job
router.post('/jobs', authenticate, authorizeRoles('hr'), async (req, res) => {
  const { title, description, responsibilities, requirements, academicLevel, location, jobType, endDate } = req.body;

  if (!title || !description || !responsibilities || !requirements) {
    return res.status(400).json({ message: 'Required fields are missing' });
  }

  try {
    // Format responsibilities and requirements
    const formattedResponsibilities = Array.isArray(responsibilities)
      ? responsibilities
      : responsibilities.split(',').map(item => item.trim());

    const formattedRequirements = Array.isArray(requirements)
      ? requirements
      : requirements.split(',').map(item => item.trim());

    // Handle endDate properly
    let formattedEndDate = null;
    if (endDate) {
      // If it's a valid date string, use it
      if (typeof endDate === 'string' && endDate.trim() !== '') {
        formattedEndDate = new Date(endDate);
        // Check if the date is valid
        if (isNaN(formattedEndDate.getTime())) {
          formattedEndDate = null;
        }
      } else if (endDate instanceof Date) {
        // If it's already a Date object
        formattedEndDate = endDate;
      }
    }

    console.log('Creating job with endDate:', {
      original: endDate,
      formatted: formattedEndDate
    });

    const newJob = new Job({
      title,
      description,
      responsibilities: formattedResponsibilities,
      requirements: formattedRequirements,
      academicLevel,
      location,
      jobType,
      endDate: formattedEndDate
    });

    await newJob.save();
    res.status(201).json({ message: 'Job added successfully!', job: newJob });
  } catch (error) {
    console.error('Error adding job:', error);
    res.status(500).json({ message: 'Error adding job' });
  }
});

// Route to update a job
router.put('/jobs/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const jobId = req.params.id;
    const { title, description, responsibilities, requirements, academicLevel, location, jobType, endDate } = req.body;

    if (!jobId) {
      return res.status(400).json({ message: 'Job ID is required' });
    }

    if (!title || !description || !responsibilities || !requirements) {
      return res.status(400).json({ message: 'Required fields are missing' });
    }

    // Find the job first to check if it exists
    const job = await Job.findById(jobId);

    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }

    // Format responsibilities and requirements
    const formattedResponsibilities = Array.isArray(responsibilities)
      ? responsibilities
      : responsibilities.split(',').map(item => item.trim());

    const formattedRequirements = Array.isArray(requirements)
      ? requirements
      : requirements.split(',').map(item => item.trim());

    // Handle endDate properly
    let formattedEndDate = null;
    if (endDate) {
      // If it's a valid date string, use it
      if (typeof endDate === 'string' && endDate.trim() !== '') {
        formattedEndDate = new Date(endDate);
        // Check if the date is valid
        if (isNaN(formattedEndDate.getTime())) {
          formattedEndDate = null;
        }
      } else if (endDate instanceof Date) {
        // If it's already a Date object
        formattedEndDate = endDate;
      }
    }

    console.log('Updating job with endDate:', {
      original: endDate,
      formatted: formattedEndDate
    });

    // Update the job
    const updatedJob = await Job.findByIdAndUpdate(
      jobId,
      {
        title,
        description,
        responsibilities: formattedResponsibilities,
        requirements: formattedRequirements,
        academicLevel,
        location,
        jobType,
        endDate: formattedEndDate
      },
      { new: true } // Return the updated document
    );

    res.status(200).json({ message: 'Job updated successfully', job: updatedJob });
  } catch (error) {
    console.error('Update Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to delete a job
router.delete('/jobs/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const jobId = req.params.id;

    if (!jobId) {
      return res.status(400).json({ message: 'Job ID is required' });
    }

    // Find the job first to check if it exists
    const job = await Job.findById(jobId);

    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }

    // Delete the job
    await Job.findByIdAndDelete(jobId);

    res.status(200).json({ message: 'Job deleted successfully' });
  } catch (error) {
    console.error('Delete Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to delete a user (HR can only delete normal users)
router.delete('/users/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const userId = req.params.id;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    // Find the user first to check their role
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // HR can only delete normal users, not other HR or admin users
    if (user.role !== 'user') {
      return res.status(403).json({ message: 'HR can only delete normal users' });
    }

    // Delete the user
    await User.findByIdAndDelete(userId);

    res.status(200).json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to update a user (HR can only update normal users)
router.put('/users/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const userId = req.params.id;

    if (!userId) {
      return res.status(400).json({ message: 'User ID is required' });
    }

    // Find the user first to check their role
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // HR can only update normal users, not other HR or admin users
    if (user.role !== 'user') {
      return res.status(403).json({ message: 'HR can only update normal users' });
    }

    const { name, email, password, job, birthdate } = req.body;

    // Update user fields
    if (name) user.name = name;
    if (email) user.email = email;
    if (password && password.trim() !== '') {
      const hashedPassword = await bcrypt.hash(password, 10);
      user.password = hashedPassword;

      // Send email notification about password change
      try {
        await sendPasswordChangeEmail({
          email: user.email,
          to_name: user.name,
          date: new Date().toLocaleString(),
          byAdmin: true
        });
        console.log('Password change email notification sent to user:', user.email);
      } catch (emailError) {
        console.error('Error sending password change email notification:', emailError);
        // Continue with the flow even if email fails
      }
    }
    if (job) user.job = job;
    if (birthdate) user.birthdate = new Date(birthdate);

    await user.save();

    res.status(200).json({ message: 'User updated successfully' });
  } catch (error) {
    console.error('Update Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to delete an application
router.delete('/applications/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const applicationId = req.params.id;

    if (!applicationId) {
      return res.status(400).json({ message: 'Application ID is required' });
    }

    const application = await Application.findById(applicationId);

    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }

    await Application.findByIdAndDelete(applicationId);

    res.status(200).json({ message: 'Application deleted successfully' });
  } catch (error) {
    console.error('Delete Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to update an application
router.put('/applications/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const applicationId = req.params.id;

    if (!applicationId) {
      return res.status(400).json({ message: 'Application ID is required' });
    }

    const application = await Application.findById(applicationId);

    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }

    const { fullname, email, position, phone, status, feedback } = req.body;

    // Update application fields
    if (fullname) application.fullname = fullname;
    if (email) application.email = email;
    if (position) application.position = position;
    if (phone) application.phone = phone;
    if (status && ['Pending', 'Approved', 'Rejected'].includes(status)) {
      application.status = status;
    }
    if (feedback !== undefined) application.feedback = feedback;

    await application.save();

    res.status(200).json({
      message: 'Application updated successfully',
      application
    });
  } catch (error) {
    console.error('Update Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to update application status (approve/reject)
router.put('/applications/:id/status', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const applicationId = req.params.id;
    const { status, feedback } = req.body;

    console.log('Updating application status:', { applicationId, status, feedback });

    if (!applicationId) {
      return res.status(400).json({ message: 'Application ID is required' });
    }

    if (!status || !['Approved', 'Rejected', 'Pending'].includes(status)) {
      return res.status(400).json({
        message: 'Valid status is required (Approved, Rejected, or Pending)',
        receivedStatus: status,
        validOptions: ['Approved', 'Rejected', 'Pending']
      });
    }

    const application = await Application.findById(applicationId);

    if (!application) {
      return res.status(404).json({ message: 'Application not found', id: applicationId });
    }

    console.log('Found application:', {
      id: application._id,
      fullname: application.fullname,
      currentStatus: application.status,
      newStatus: status
    });

    // Update status and feedback
    application.status = status;
    if (feedback !== undefined) {
      application.feedback = feedback;
    }

    const updatedApplication = await application.save();

    console.log('Application updated successfully:', {
      id: updatedApplication._id,
      status: updatedApplication.status,
      feedback: updatedApplication.feedback
    });

    // Create notification for new application
    if (status === 'Approved' || status === 'Rejected') {
      await createApplicationNotification(updatedApplication, 'APPLICATION_STATUS_CHANGED');
    }

    res.status(200).json({
      message: `Application ${status.toLowerCase()} successfully`,
      application: updatedApplication
    });
  } catch (error) {
    console.error('Status Update Error:', error);
    res.status(500).json({
      message: 'Server Error Occurred',
      error: error.toString(),
      stack: error.stack
    });
  }
});

// Route to delete a leave request
router.delete('/leave-requests/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const requestId = req.params.id;

    if (!requestId) {
      return res.status(400).json({ message: 'Leave Request ID is required' });
    }

    const leaveRequest = await LeaveRequest.findById(requestId);

    if (!leaveRequest) {
      return res.status(404).json({ message: 'Leave Request not found' });
    }

    await LeaveRequest.findByIdAndDelete(requestId);

    res.status(200).json({ message: 'Leave Request deleted successfully' });
  } catch (error) {
    console.error('Delete Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Route to update a leave request
router.put('/leave-requests/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const requestId = req.params.id;

    if (!requestId) {
      return res.status(400).json({ message: 'Leave Request ID is required' });
    }

    const leaveRequest = await LeaveRequest.findById(requestId);

    if (!leaveRequest) {
      return res.status(404).json({ message: 'Leave Request not found' });
    }

    const { employeeName, leaveType, startDate, endDate, reason, status } = req.body;

    // Update leave request fields
    if (employeeName) leaveRequest.employeeName = employeeName;
    if (leaveType) leaveRequest.leaveType = leaveType;
    if (startDate) leaveRequest.startDate = new Date(startDate);
    if (endDate) leaveRequest.endDate = new Date(endDate);
    if (reason) leaveRequest.reason = reason;
    if (status) leaveRequest.status = status;

    await leaveRequest.save();

    res.status(200).json({ message: 'Leave Request updated successfully' });
  } catch (error) {
    console.error('Update Error:', error);
    res.status(500).json({ message: 'Server Error Occurred' });
  }
});

// Debug route to check a leave request
router.get('/leave-requests/:id/debug', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const requestId = req.params.id;

    if (!requestId) {
      return res.status(400).json({ message: 'Leave Request ID is required' });
    }

    const leaveRequest = await LeaveRequest.findById(requestId);

    if (!leaveRequest) {
      return res.status(404).json({ message: 'Leave Request not found' });
    }

    res.status(200).json({
      message: 'Leave Request found',
      leaveRequest,
      id: requestId,
      idMatch: leaveRequest._id.toString() === requestId
    });
  } catch (error) {
    console.error('Debug Error:', error);
    res.status(500).json({ message: 'Server Error Occurred', error: error.toString() });
  }
});

// Route to update leave request status (approve/reject)
router.put('/leave-requests/:id/status', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const requestId = req.params.id;
    const { status } = req.body;

    console.log('Updating leave request status:', { requestId, status, body: req.body });

    if (!requestId) {
      return res.status(400).json({ message: 'Leave Request ID is required' });
    }

    if (!status || !['Approved', 'Rejected', 'Pending'].includes(status)) {
      return res.status(400).json({
        message: 'Valid status is required (Approved, Rejected, or Pending)',
        receivedStatus: status,
        validOptions: ['Approved', 'Rejected', 'Pending']
      });
    }

    const leaveRequest = await LeaveRequest.findById(requestId);

    if (!leaveRequest) {
      return res.status(404).json({ message: 'Leave Request not found', id: requestId });
    }

    console.log('Found leave request:', {
      id: leaveRequest._id,
      currentStatus: leaveRequest.status,
      newStatus: status
    });

    // Update status
    leaveRequest.status = status;
    const updatedRequest = await leaveRequest.save();

    console.log('Leave request updated successfully:', {
      id: updatedRequest._id,
      status: updatedRequest.status
    });

    // Create notification for the user about their leave request status
    const notificationType = status === 'Approved'
      ? 'LEAVE_REQUEST_APPROVED'
      : status === 'Rejected'
        ? 'LEAVE_REQUEST_REJECTED'
        : null;

    if (notificationType) {
      await createLeaveRequestNotification(updatedRequest, notificationType);
    }

    res.status(200).json({
      message: `Leave Request ${status.toLowerCase()} successfully`,
      leaveRequest: updatedRequest
    });
  } catch (error) {
    console.error('Status Update Error:', error);
    res.status(500).json({
      message: 'Server Error Occurred',
      error: error.toString(),
      stack: error.stack
    });
  }
});

// Route to parse a CV using advanced NLP
router.post('/parse-cv', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { cvPath, jobId, applicationId } = req.body;

    if (!cvPath) {
      return res.status(400).json({ message: 'CV path is required' });
    }

    console.log('Parsing CV with advanced NLP:', cvPath);
    console.log('Job ID:', jobId);
    console.log('Application ID:', applicationId);

    // Get job details if jobId is provided
    let job = null;
    if (jobId) {
      job = await Job.findById(jobId);
      console.log('Found job:', job ? job.title : 'None');

      // Ensure job data is properly formatted for NLP processing
      if (job) {
        // Make sure requirements and responsibilities are arrays
        if (typeof job.requirements === 'string') {
          job.requirements = job.requirements.split(',').map(item => item.trim());
        }

        if (typeof job.responsibilities === 'string') {
          job.responsibilities = job.responsibilities.split(',').map(item => item.trim());
        }

        // Add academic level and job type to requirements if available
        if (job.academicLevel && !job.requirements.includes(job.academicLevel)) {
          job.requirements.push(`Academic Level: ${job.academicLevel}`);
        }

        if (job.jobType && !job.requirements.includes(job.jobType)) {
          job.requirements.push(`Job Type: ${job.jobType}`);
        }

        // Add location to requirements if available
        if (job.location && !job.requirements.includes(job.location)) {
          job.requirements.push(`Location: ${job.location}`);
        }

        console.log('Prepared job data for NLP:', {
          title: job.title,
          requirements: job.requirements.length,
          responsibilities: job.responsibilities.length
        });
      }
    }

    // Construct the full path to the CV file
    const path = require('path');
    const fullCvPath = path.join(__dirname, '..', cvPath);
    console.log('Full CV path:', fullCvPath);

    // Check if the file exists
    const fs = require('fs');
    if (!fs.existsSync(fullCvPath)) {
      return res.status(404).json({
        error: 'CV file not found',
        errorDetails: `The file at path ${cvPath} does not exist.`
      });
    }

    try {
      console.log('Starting CV parsing with advanced NLP...');

      // Use the CV parser service
      const parsedData = await require('../NLP/cvParserService')(fullCvPath, job);
      console.log('CV parsed successfully with advanced NLP');

      // If applicationId is provided, update the application with NLP results
      if (applicationId) {
        const application = await Application.findById(applicationId);
        if (application) {
          // Store the NLP results
          application.nlpResults = parsedData;

          // Set the nlpProcessed flag to true
          application.nlpProcessed = true;

          // Update match score
          application.matchScore = parsedData.matchScore || 0;

          // Update semantic score if available
          if (parsedData.semanticScore !== undefined) {
            application.semanticScore = parsedData.semanticScore;
          }

          // Store the job ID that was used for matching
          if (jobId) {
            application.matchedJobId = jobId;
          }

          await application.save();
          console.log('Application updated with NLP results:', {
            applicationId,
            matchScore: application.matchScore,
            jobId: application.matchedJobId
          });

          // Automatically save the NLP report to the Reports collection
          try {
            // Import the Report model
            const Report = require('../models/Report');

            // Check if a report already exists for this application
            const existingReport = await Report.findOne({
              reportType: 'nlp',
              applicationId: application._id
            });

            // Create report data
            const reportData = {
              title: `NLP Analysis: ${application.fullname || 'Unknown Candidate'}`,
              reportType: 'nlp',
              reportData: {
                candidateName: application.fullname || "Unknown Candidate",
                position: application.position || (job ? job.title : "Unknown Position"),
                matchScore: parsedData.matchScore || 0,
                timestamp: new Date().toISOString(),
                nlpResults: parsedData
              },
              applicationId: application._id,
              createdBy: req.user.id
            };

            let report;

            if (existingReport) {
              // Update existing report
              console.log('Updating existing NLP report for application:', application._id);
              existingReport.reportData = reportData.reportData;
              existingReport.timestamp = reportData.reportData.timestamp;
              report = existingReport;
              await existingReport.save();
            } else {
              // Create new report
              report = new Report(reportData);
              await report.save();
            }

            console.log('NLP report automatically saved to database:', {
              reportId: report._id,
              title: report.title
            });

            // Add the report ID to the response and to the application's NLP results
            console.log('Setting savedReportId in NLP results:', report._id);
            parsedData.savedReportId = report._id;

            // Update the application with the report ID
            if (!application.nlpResults) {
              application.nlpResults = {};
            }
            application.nlpResults.savedReportId = report._id;

            // Also add the application ID to the NLP results for reference
            parsedData.applicationId = application._id;
          } catch (reportError) {
            console.error('Error saving NLP report to database:', reportError);
            // Continue even if report saving fails
          }
        }
      }

      // Send the final result as a single JSON response
      res.status(200).json(parsedData);
    } catch (parserError) {
      console.error('Parser service error:', parserError);

      // Send error response
      res.status(500).json({
        error: 'Error during NLP processing',
        errorDetails: parserError.toString()
      });
    }
  } catch (error) {
    console.error('CV Parsing Error:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }

    // Send a regular error response
    res.status(500).json({
      message: 'Error parsing CV',
      error: error.toString(),
      stack: error.stack,
      details: 'Check server logs for more information'
    });
  }
});

// Route to run NLP on an application
router.post('/applications/:id/nlp', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const applicationId = req.params.id;
    const { cvPath, jobId } = req.body;

    if (!applicationId) {
      return res.status(400).json({ message: 'Application ID is required' });
    }

    // Find the application
    const application = await Application.findById(applicationId);
    if (!application) {
      return res.status(404).json({ message: 'Application not found' });
    }

    console.log('Application found:', application);

    // Use the CV path from the request or from the application
    let finalCvPath = cvPath || application.cv;
    if (!finalCvPath) {
      return res.status(400).json({ message: 'CV path is required' });
    }

    // Use the job ID from the request or from the application
    const finalJobId = jobId || application.jobId;
    if (!finalJobId) {
      return res.status(400).json({ message: 'Job ID is required' });
    }

    console.log('Running NLP for application:', {
      applicationId,
      cvPath: finalCvPath,
      jobId: finalJobId
    });

    // Normalize the CV path
    // If the path already includes 'uploads/', remove it to avoid duplication
    if (finalCvPath.startsWith('uploads/')) {
      finalCvPath = finalCvPath.substring(8);
    }

    // Get job details
    const job = await Job.findById(finalJobId);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }

    // Construct the full path to the CV file
    const path = require('path');
    const fullCvPath = path.join(__dirname, '..', 'uploads', finalCvPath);
    console.log('Full CV path:', fullCvPath);

    // Check if the file exists
    const fs = require('fs');
    if (!fs.existsSync(fullCvPath)) {
      // Try alternative path
      const altPath = path.join(__dirname, '..', finalCvPath);
      console.log('Trying alternative path:', altPath);

      if (!fs.existsSync(altPath)) {
        return res.status(404).json({
          error: 'CV file not found',
          errorDetails: `The file at path ${finalCvPath} or ${altPath} does not exist.`,
          paths: {
            original: finalCvPath,
            fullPath: fullCvPath,
            altPath: altPath
          }
        });
      } else {
        // Use the alternative path
        console.log('Using alternative path:', altPath);
        fullCvPath = altPath;
      }
    }

    // Use the CV parser service
    const parsedData = await require('../NLP/cvParserService')(fullCvPath, job);
    console.log('CV parsed successfully with advanced NLP');

    // Update the application with NLP results
    application.nlpResults = parsedData;
    application.nlpProcessed = true;
    application.matchScore = parsedData.matchScore || 0;

    // Update semantic score if available
    if (parsedData.semanticScore !== undefined) {
      application.semanticScore = parsedData.semanticScore;
    }

    // Store the job ID that was used for matching
    application.matchedJobId = finalJobId;

    await application.save();
    console.log('Application updated with NLP results:', {
      applicationId,
      matchScore: application.matchScore,
      jobId: application.matchedJobId
    });

    // Automatically save the NLP report to the Reports collection
    try {
      // Import the Report model
      const Report = require('../models/Report');

      // Check if a report already exists for this application
      const existingReport = await Report.findOne({
        reportType: 'nlp',
        applicationId: application._id
      });

      // Create report data
      const reportData = {
        title: `NLP Analysis: ${application.fullname || 'Unknown Candidate'}`,
        reportType: 'nlp',
        reportData: {
          candidateName: application.fullname || "Unknown Candidate",
          position: application.position || job.title || "Unknown Position",
          matchScore: parsedData.matchScore || 0,
          timestamp: new Date().toISOString(),
          nlpResults: parsedData
        },
        applicationId: application._id,
        createdBy: req.user.id
      };

      let report;

      if (existingReport) {
        // Update existing report
        console.log('Updating existing NLP report for application:', application._id);
        existingReport.reportData = reportData.reportData;
        existingReport.timestamp = reportData.reportData.timestamp;
        report = existingReport;
        await existingReport.save();
      } else {
        // Create new report
        report = new Report(reportData);
        await report.save();
      }

      console.log('NLP report automatically saved to database:', {
        reportId: report._id,
        title: report.title
      });

      // Add the report ID to the response and to the application's NLP results
      console.log('Setting savedReportId in NLP results:', report._id);
      parsedData.savedReportId = report._id;

      // Update the application with the report ID
      if (!application.nlpResults) {
        application.nlpResults = {};
      }
      application.nlpResults.savedReportId = report._id;

      // Also add the application ID to the NLP results for reference
      parsedData.applicationId = application._id;
    } catch (reportError) {
      console.error('Error saving NLP report to database:', reportError);
      // Continue even if report saving fails
    }

    // Send the final result as a single JSON response
    res.status(200).json(parsedData);
  } catch (error) {
    console.error('Application NLP Error:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }

    // Send a regular error response
    res.status(500).json({
      message: 'Error running NLP on application',
      error: error.toString(),
      stack: error.stack,
      details: 'Check server logs for more information'
    });
  }
});

// ==================== EMPLOYEE EVALUATION ROUTES ====================

// GET all evaluations
router.get('/evaluations', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { search } = req.query;

    let query = {};

    // Add search functionality
    if (search) {
      // First, find users that match the search criteria
      const matchingUsers = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { job: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      // Get the user IDs
      const userIds = matchingUsers.map(user => user._id);

      // Build the query to search in evaluations
      query.$or = [
        { evaluationPeriod: { $regex: search, $options: 'i' } },
        { strengths: { $regex: search, $options: 'i' } },
        { areasForImprovement: { $regex: search, $options: 'i' } },
        { goals: { $regex: search, $options: 'i' } },
        { comments: { $regex: search, $options: 'i' } },
        { status: { $regex: search, $options: 'i' } }
      ];

      // Add user IDs to the query if any were found
      if (userIds.length > 0) {
        query.$or.push({ userId: { $in: userIds } });
      }
    }

    const evaluations = await Evaluation.find(query)
      .sort({ createdAt: -1 })
      .populate('userId', 'name email job')
      .populate('evaluatorId', 'name');

    res.status(200).json(evaluations);
  } catch (error) {
    console.error('Error fetching evaluations:', error);
    res.status(500).json({ message: 'Error fetching evaluations' });
  }
});

// GET evaluations for a specific user
router.get('/evaluations/user/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;

    const evaluations = await Evaluation.find({ userId })
      .sort({ evaluationDate: -1 })
      .populate('evaluatorId', 'name');

    res.status(200).json(evaluations);
  } catch (error) {
    console.error('Error fetching user evaluations:', error);
    res.status(500).json({ message: 'Error fetching user evaluations' });
  }
});

// GET a specific evaluation by ID
router.get('/evaluations/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const evaluation = await Evaluation.findById(req.params.id)
      .populate('userId', 'name email job')
      .populate('evaluatorId', 'name');

    if (!evaluation) {
      return res.status(404).json({ message: 'Evaluation not found' });
    }

    res.status(200).json(evaluation);
  } catch (error) {
    console.error('Error fetching evaluation:', error);
    res.status(500).json({ message: 'Error fetching evaluation' });
  }
});

// CREATE a new evaluation
router.post('/evaluations', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const {
      userId,
      evaluationPeriod,
      performanceRating,
      attitudeRating,
      communicationRating,
      teamworkRating,
      initiativeRating,
      strengths,
      areasForImprovement,
      goals,
      comments,
      status,
      visibleToUser
    } = req.body;

    // Validate required fields
    if (!userId || !evaluationPeriod || !performanceRating || !attitudeRating ||
        !communicationRating || !teamworkRating || !initiativeRating) {
      return res.status(400).json({ message: 'All rating fields are required' });
    }

    // Create new evaluation
    const newEvaluation = new Evaluation({
      userId,
      evaluatorId: req.user.id, // The HR performing the evaluation
      evaluationPeriod,
      evaluationDate: new Date(),
      performanceRating,
      attitudeRating,
      communicationRating,
      teamworkRating,
      initiativeRating,
      strengths: strengths || '',
      areasForImprovement: areasForImprovement || '',
      goals: goals || '',
      comments: comments || '',
      status: status || 'Draft',
      visibleToUser: visibleToUser || false
    });

    await newEvaluation.save();

    res.status(201).json({
      message: 'Evaluation created successfully',
      evaluation: newEvaluation
    });
  } catch (error) {
    console.error('Error creating evaluation:', error);
    res.status(500).json({ message: 'Error creating evaluation' });
  }
});

// UPDATE an evaluation
router.put('/evaluations/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const {
      evaluationPeriod,
      evaluationDate,
      performanceRating,
      attitudeRating,
      communicationRating,
      teamworkRating,
      initiativeRating,
      strengths,
      areasForImprovement,
      goals,
      comments,
      status,
      visibleToUser
    } = req.body;

    const evaluation = await Evaluation.findById(req.params.id);

    if (!evaluation) {
      return res.status(404).json({ message: 'Evaluation not found' });
    }

    // Update fields
    if (evaluationPeriod) evaluation.evaluationPeriod = evaluationPeriod;
    if (evaluationDate) evaluation.evaluationDate = new Date(evaluationDate);
    if (performanceRating) evaluation.performanceRating = performanceRating;
    if (attitudeRating) evaluation.attitudeRating = attitudeRating;
    if (communicationRating) evaluation.communicationRating = communicationRating;
    if (teamworkRating) evaluation.teamworkRating = teamworkRating;
    if (initiativeRating) evaluation.initiativeRating = initiativeRating;
    if (strengths !== undefined) evaluation.strengths = strengths;
    if (areasForImprovement !== undefined) evaluation.areasForImprovement = areasForImprovement;
    if (goals !== undefined) evaluation.goals = goals;
    if (comments !== undefined) evaluation.comments = comments;
    if (status) evaluation.status = status;
    if (visibleToUser !== undefined) evaluation.visibleToUser = visibleToUser;

    await evaluation.save();

    res.status(200).json({
      message: 'Evaluation updated successfully',
      evaluation
    });
  } catch (error) {
    console.error('Error updating evaluation:', error);
    res.status(500).json({ message: 'Error updating evaluation' });
  }
});

// DELETE an evaluation
router.delete('/evaluations/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const evaluation = await Evaluation.findById(req.params.id);

    if (!evaluation) {
      return res.status(404).json({ message: 'Evaluation not found' });
    }

    await Evaluation.findByIdAndDelete(req.params.id);

    res.status(200).json({ message: 'Evaluation deleted successfully' });
  } catch (error) {
    console.error('Error deleting evaluation:', error);
    res.status(500).json({ message: 'Error deleting evaluation' });
  }
});

// ==================== AI EVALUATION ROUTES ====================

// Generate AI evaluation for a user
router.post('/evaluations/ai/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    console.log('Generating AI evaluation for user ID:', userId);
    console.log('Authenticated user:', req.user);

    // Validate user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Only allow generating AI evaluations for normal users
    if (user.role !== 'user') {
      return res.status(400).json({ message: 'AI evaluations can only be generated for normal users' });
    }

    // Make sure we have a valid evaluator ID
    if (!req.user || !req.user.id) {
      return res.status(401).json({ message: 'Authentication error: Missing evaluator ID' });
    }

    // Generate AI evaluation
    const evaluation = await createAIEvaluation(userId, req.user.id);

    console.log('AI evaluation generated successfully:', evaluation._id);

    res.status(201).json({
      message: 'AI evaluation generated successfully',
      evaluation
    });
  } catch (error) {
    console.error('Error generating AI evaluation:', error);
    res.status(500).json({
      message: 'Error generating AI evaluation',
      error: error.message
    });
  }
});

// ==================== ATTENDANCE MANAGEMENT ROUTES ====================

// Get all attendance records (with pagination and filtering)
router.get('/attendance', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { page = 1, limit = 20, userId, startDate, endDate, status, search } = req.query;

    // Build query based on filters
    let query = {};

    if (userId) {
      query.userId = userId;
    }

    if (startDate && endDate) {
      query.date = { $gte: startDate, $lte: endDate };
    } else if (startDate) {
      query.date = { $gte: startDate };
    } else if (endDate) {
      query.date = { $lte: endDate };
    }

    if (status) {
      query.status = status;
    }

    // Add search functionality
    if (search) {
      // First, find users that match the search criteria
      const matchingUsers = await User.find({
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { job: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      // Get the user IDs
      const userIds = matchingUsers.map(user => user._id);

      // Build the search query
      const searchQuery = {
        $or: [
          { status: { $regex: search, $options: 'i' } },
          { notes: { $regex: search, $options: 'i' } }
        ]
      };

      // Add user IDs to the query if any were found
      if (userIds.length > 0) {
        searchQuery.$or.push({ userId: { $in: userIds } });
      }

      // Combine the search query with the existing query
      query = { ...query, ...searchQuery };
    }

    // Count total documents for pagination
    const total = await Attendance.countDocuments(query);

    // Fetch attendance records with pagination
    const attendanceRecords = await Attendance.find(query)
      .populate('userId', 'name email job')
      .sort({ date: -1, checkIn: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    res.json({
      records: attendanceRecords,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      totalRecords: total
    });
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    res.status(500).json({ message: 'Error fetching attendance records' });
  }
});

// Get attendance records for a specific user
router.get('/attendance/user/:userId', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { userId } = req.params;
    const { startDate, endDate } = req.query;

    // Validate user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Build query
    let query = { userId };

    if (startDate && endDate) {
      query.date = { $gte: startDate, $lte: endDate };
    } else if (startDate) {
      query.date = { $gte: startDate };
    } else if (endDate) {
      query.date = { $lte: endDate };
    }

    // Fetch attendance records
    const attendanceRecords = await Attendance.find(query)
      .sort({ date: -1 })
      .limit(100); // Limit to last 100 records by default

    // Calculate statistics
    const stats = {
      totalDays: attendanceRecords.length,
      presentDays: attendanceRecords.filter(record => record.status === 'Present').length,
      lateDays: attendanceRecords.filter(record => record.status === 'Late').length,
      absentDays: attendanceRecords.filter(record => record.status === 'Absent').length,
      totalHoursWorked: attendanceRecords.reduce((total, record) => total + (record.hoursWorked || 0), 0).toFixed(2)
    };

    res.json({
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        job: user.job
      },
      records: attendanceRecords,
      stats
    });
  } catch (error) {
    console.error('Error fetching user attendance:', error);
    res.status(500).json({ message: 'Error fetching user attendance' });
  }
});

// Update an attendance record (HR can edit records if needed)
router.put('/attendance/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { id } = req.params;
    const { checkIn, checkOut, status, notes } = req.body;

    const attendance = await Attendance.findById(id);

    if (!attendance) {
      return res.status(404).json({ message: 'Attendance record not found' });
    }

    // Update fields if provided
    if (checkIn) attendance.checkIn = new Date(checkIn);
    if (checkOut) attendance.checkOut = new Date(checkOut);
    if (status) attendance.status = status;
    if (notes !== undefined) attendance.notes = notes;

    // Recalculate hours worked if both check-in and check-out are present
    if (attendance.checkIn && attendance.checkOut) {
      attendance.calculateHoursWorked();
    }

    await attendance.save();

    res.json({
      message: 'Attendance record updated successfully',
      attendance
    });
  } catch (error) {
    console.error('Error updating attendance record:', error);
    res.status(500).json({ message: 'Error updating attendance record' });
  }
});

// Delete an attendance record
router.delete('/attendance/:id', authenticate, authorizeRoles('hr'), async (req, res) => {
  try {
    const { id } = req.params;

    const attendance = await Attendance.findById(id);

    if (!attendance) {
      return res.status(404).json({ message: 'Attendance record not found' });
    }

    await Attendance.findByIdAndDelete(id);

    res.json({ message: 'Attendance record deleted successfully' });
  } catch (error) {
    console.error('Error deleting attendance record:', error);
    res.status(500).json({ message: 'Error deleting attendance record' });
  }
});

module.exports = router;
