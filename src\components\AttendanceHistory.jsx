import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Grid,
  Card,
  CardContent,
  Divider,
  TextField,
  MenuItem,
  CircularProgress
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { format } from 'date-fns';
import { toast } from 'react-toastify';
import api from '../Services/ApiService';

const AttendanceHistory = ({ open, onClose, userId, userName, userJob }) => {
  const [loading, setLoading] = useState(true);
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [stats, setStats] = useState({
    totalDays: 0,
    presentDays: 0,
    lateDays: 0,
    absentDays: 0,
    totalHoursWorked: 0
  });
  const [filters, setFilters] = useState({
    startDate: null,
    endDate: null,
    status: ''
  });

  // Fetch attendance records for the user
  useEffect(() => {
    if (open && userId) {
      fetchAttendanceRecords();
    }
  }, [open, userId, filters]);

  const fetchAttendanceRecords = async () => {
    try {
      setLoading(true);

      // Prepare query parameters
      const queryParams = {};
      if (filters.startDate) {
        queryParams.startDate = filters.startDate;
      }
      if (filters.endDate) {
        queryParams.endDate = filters.endDate;
      }
      if (filters.status) {
        queryParams.status = filters.status;
      }

      // Convert query params to URL string
      const queryString = new URLSearchParams(queryParams).toString();

      const response = await api.get(`/hr/attendance/user/${userId}${queryString ? `?${queryString}` : ''}`);

      setAttendanceRecords(response.data.records);
      setStats(response.data.stats);
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      toast.error('Failed to load attendance records');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetFilters = () => {
    setFilters({
      startDate: null,
      endDate: null,
      status: ''
    });
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Format time for display
  const formatTime = (timeString) => {
    if (!timeString) return 'N/A';
    try {
      return format(new Date(timeString), 'hh:mm a');
    } catch (error) {
      return timeString;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            Attendance Records: {userName}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Filters */}
        <Box mb={3}>
          <Typography variant="subtitle1" gutterBottom>Filters</Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <DatePicker
                label="Start Date"
                value={filters.startDate}
                onChange={(date) => handleFilterChange('startDate', date)}
                slotProps={{ textField: { fullWidth: true, size: 'small' } }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <DatePicker
                label="End Date"
                value={filters.endDate}
                onChange={(date) => handleFilterChange('endDate', date)}
                slotProps={{ textField: { fullWidth: true, size: 'small' } }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                select
                label="Status"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                fullWidth
                size="small"
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="present">Present</MenuItem>
                <MenuItem value="absent">Absent</MenuItem>
                <MenuItem value="late">Late</MenuItem>
              </TextField>
            </Grid>
          </Grid>
          <Box mt={1} display="flex" justifyContent="flex-end">
            <Button size="small" onClick={resetFilters}>Reset Filters</Button>
          </Box>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={2} mb={3}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">Total Days</Typography>
                <Typography variant="h5">{stats.totalDays}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">Present Days</Typography>
                <Typography variant="h5">{stats.presentDays}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">Late Days</Typography>
                <Typography variant="h5">{stats.lateDays}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">Absent Days</Typography>
                <Typography variant="h5">{stats.absentDays}</Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Card>
              <CardContent sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="textSecondary">Total Hours Worked</Typography>
                <Typography variant="h5">{stats.totalHoursWorked.toFixed(2)} hrs</Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Attendance Records Table */}
        {loading ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : attendanceRecords.length === 0 ? (
          <Typography align="center" sx={{ py: 4 }}>No attendance records found for this employee.</Typography>
        ) : (
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Check In</TableCell>
                  <TableCell>Check Out</TableCell>
                  <TableCell>Hours Worked</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Notes</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {attendanceRecords.map((record) => (
                  <TableRow key={record._id}>
                    <TableCell>{formatDate(record.date)}</TableCell>
                    <TableCell>{formatTime(record.checkIn)}</TableCell>
                    <TableCell>{formatTime(record.checkOut)}</TableCell>
                    <TableCell>
                      {record.hoursWorked ? record.hoursWorked.toFixed(2) + ' hrs' : 'N/A'}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={record.status}
                        color={
                          record.status === 'Present' ? 'success' :
                          record.status === 'Late' ? 'warning' :
                          'error'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{record.notes || 'No notes'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="primary">Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AttendanceHistory;
