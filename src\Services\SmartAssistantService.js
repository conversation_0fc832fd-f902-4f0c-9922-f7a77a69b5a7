/**
 * Smart Assistant Service
 * Handles communication with the smart AI assistant backend
 */

import api from './ApiService';

class SmartAssistantService {
  constructor() {
    this.baseUrl = '/api/ai/smart-assistant';
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Trigger behavioral analysis and get suggestions
   */
  async analyzeAndSuggest(timeWindow = null) {
    try {
      // For now, just return a success message since we don't have this endpoint yet
      return {
        success: true,
        message: 'Analysis triggered successfully',
        data: {
          patterns: [],
          suggestions: [],
          confidence: 0.8,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Error triggering analysis:', error);
      throw error;
    }
  }

  /**
   * Get user's AI insights and suggestions
   */
  async getInsights(filters = {}) {
    try {
      const { status, category, limit = 10 } = filters;
      const params = new URLSearchParams();
      
      if (status) params.append('status', status);
      if (category) params.append('category', category);
      params.append('limit', limit.toString());

      const response = await api.get(`${this.baseUrl}/insights?${params}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching insights:', error);
      throw error;
    }
  }

  /**
   * Provide feedback on a suggestion
   */
  async provideFeedback(insightId, rating, comment = '') {
    try {
      const response = await api.post(`${this.baseUrl}/feedback`, {
        insightId,
        rating, // 'up' or 'down'
        comment
      });
      return response.data;
    } catch (error) {
      console.error('Error providing feedback:', error);
      throw error;
    }
  }

  /**
   * Get user preferences
   */
  async getPreferences() {
    try {
      const response = await api.get(`${this.baseUrl}/preferences`);
      return response.data;
    } catch (error) {
      console.error('Error fetching preferences:', error);
      throw error;
    }
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences) {
    try {
      const response = await api.put(`${this.baseUrl}/preferences`, preferences);
      return response.data;
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw error;
    }
  }

  /**
   * Get analytics data
   */
  async getAnalytics() {
    try {
      const cacheKey = 'analytics';
      const cached = this.cache.get(cacheKey);
      
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        return cached.data;
      }

      const response = await api.get(`${this.baseUrl}/analytics`);
      
      // Cache the result
      this.cache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now()
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching analytics:', error);
      throw error;
    }
  }

  /**
   * Track CRUD operation for behavioral analysis
   */
  async trackOperation(operation, resourceType, resourceId = null, metadata = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/track-operation`, {
        operation,
        resourceType,
        resourceId,
        metadata
      });
      return response.data;
    } catch (error) {
      console.error('Error tracking operation:', error);
      // Don't throw error for tracking - it's not critical
      return null;
    }
  }

  /**
   * Dismiss an insight
   */
  async dismissInsight(insightId) {
    try {
      const response = await api.post(`${this.baseUrl}/insights/${insightId}/dismiss`);
      return response.data;
    } catch (error) {
      console.error('Error dismissing insight:', error);
      throw error;
    }
  }

  /**
   * Mark insight as viewed
   */
  async markInsightAsViewed(insightId, readTime = 0) {
    try {
      const response = await api.post(`${this.baseUrl}/insights/${insightId}/view`, {
        readTime
      });
      return response.data;
    } catch (error) {
      console.error('Error marking insight as viewed:', error);
      // Don't throw error for view tracking
      return null;
    }
  }

  /**
   * Get suggestion statistics
   */
  async getStatistics(timeRange = 30) {
    try {
      const response = await api.get(`${this.baseUrl}/stats?timeRange=${timeRange}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      throw error;
    }
  }

  /**
   * Helper method to track common activities
   */
  trackUserUpdate(userId, changes = {}) {
    return this.trackActivity('user_update', 'user_management', 'user', userId, { changes });
  }

  trackTaskCreation(taskId, taskData = {}) {
    return this.trackActivity('task_creation', 'task_management', 'task', taskId, { taskData });
  }

  trackLeaveRequest(leaveId, leaveData = {}) {
    return this.trackActivity('leave_request', 'leave_management', 'leave_request', leaveId, { leaveData });
  }

  trackJobPosting(jobId, jobData = {}) {
    return this.trackActivity('job_posting', 'recruitment', 'job', jobId, { jobData });
  }

  trackApplicationUpdate(applicationId, status = '') {
    return this.trackActivity('application_review', 'recruitment', 'application', applicationId, { status });
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Get cache size
   */
  getCacheSize() {
    return this.cache.size;
  }

  /**
   * Clean expired cache entries
   */
  cleanCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Set cache timeout
   */
  setCacheTimeout(timeout) {
    this.cacheTimeout = timeout;
  }

  /**
   * Get insight priority color
   */
  getPriorityColor(priority) {
    const colors = {
      high: '#f44336',
      medium: '#ff9800',
      low: '#4caf50'
    };
    return colors[priority] || colors.medium;
  }

  /**
   * Get category icon
   */
  getCategoryIcon(category) {
    const icons = {
      user_management: '👥',
      recruitment: '📋',
      workflow: '⚡',
      leave_management: '🏖️',
      productivity: '📈',
      task_management: '✅',
      analytics: '📊'
    };
    return icons[category] || '💡';
  }

  /**
   * Format suggestion message for display
   */
  formatSuggestionMessage(message, metadata = {}) {
    let formatted = message;
    
    // Replace placeholders with actual values
    if (metadata.userUpdateCount) {
      formatted = formatted.replace('{count}', metadata.userUpdateCount);
    }
    if (metadata.jobTitle) {
      formatted = formatted.replace('{jobTitle}', metadata.jobTitle);
    }
    if (metadata.daysSincePosted) {
      formatted = formatted.replace('{days}', metadata.daysSincePosted);
    }

    return formatted;
  }

  /**
   * Check if user should see suggestion based on preferences
   */
  shouldShowSuggestion(suggestion, preferences) {
    // Check frequency preference
    if (preferences.frequency === 'low' && suggestion.priority !== 'high') {
      return false;
    }

    // Check category preferences
    if (!preferences.categories.includes('all') && 
        !preferences.categories.includes(suggestion.category)) {
      return false;
    }

    return true;
  }
}

export default new SmartAssistantService();
