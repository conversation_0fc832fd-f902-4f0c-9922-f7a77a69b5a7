{"name": "hr-management-system-backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@langchain/community": "^0.3.44", "@langchain/core": "^0.3.57", "@langchain/openai": "^0.5.11", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "chromadb": "^2.4.6", "cors": "^2.8.5", "dotenv": "^16.4.7", "emailjs-com": "^3.2.0", "express": "^4.21.2", "express-validator": "^7.2.1", "fs": "^0.0.1-security", "https": "^1.0.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.14.1", "mongoose": "^8.12.2", "multer": "^1.4.5-lts.2", "natural": "^8.1.0", "node-cron": "^4.0.5", "nodemailer": "^6.10.0", "openai": "^4.103.0", "pdfkit": "^0.17.1", "socket.io": "^4.8.1", "uuid": "^11.1.0"}}