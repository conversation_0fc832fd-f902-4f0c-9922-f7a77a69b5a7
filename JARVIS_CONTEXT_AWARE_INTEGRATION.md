# Jarvis Context-Aware Popup Integration Guide

## Overview

This guide explains how to integrate the enhanced Jarvis context-aware popup system into your HR management application. The system provides intelligent, proactive assistance based on user actions and context.

## 🚀 Features

- **Real-time Context Detection**: Automatically detects user actions and form interactions
- **Intelligent Suggestions**: Provides relevant tips and guidance based on current context
- **Proactive Assistance**: Popup appears automatically when users need help
- **Smart Caching**: Efficient suggestion caching to reduce API calls
- **Customizable Rules**: Context rules can be configured for different scenarios
- **Multi-level Priority**: Different contexts have different priority levels
- **Cooldown System**: Prevents spam by implementing cooldown periods

## 📦 Installation

### 1. Install Required Dependencies

```bash
# Frontend dependencies
npm install framer-motion @mui/x-date-pickers

# Backend dependencies (if not already installed)
cd hr-management-system-backend
npm install ws
```

### 2. Backend Setup

The backend services are already configured. Ensure your server is running with the new context-aware endpoints:

```javascript
// The following endpoints are now available:
POST /ai/context/process
GET /ai/context/suggestions/:contextType
GET /ai/context/history
DELETE /ai/context/history
GET /ai/context/stats
```

### 3. Frontend Integration

#### Basic Integration

The context-aware popup is already integrated into the `ChatButton` component. It will automatically appear when users interact with forms or navigate through the application.

#### Using the Context-Aware Hook

```javascript
import { useContextAware } from '../hooks/useContextAware';

const MyComponent = () => {
  const {
    trackFormInteraction,
    trackDialogInteraction,
    getSuggestions
  } = useContextAware();

  // Track when user opens a form
  const handleFormOpen = () => {
    trackFormInteraction('leave-request', { userId: user.id });
  };

  // Get suggestions manually
  const loadSuggestions = async () => {
    const suggestions = await getSuggestions('leave_request_form');
    console.log('Suggestions:', suggestions);
  };

  return (
    // Your component JSX
  );
};
```

#### Form-Specific Integration

```javascript
import { useFormContextAware } from '../hooks/useContextAware';

const LeaveRequestForm = () => {
  const { trackFormInteraction, getFormSuggestions } = useFormContextAware(
    'leave-request',
    { leaveType: 'annual' }
  );

  useEffect(() => {
    // Suggestions are automatically loaded when form is detected
    getFormSuggestions().then(suggestions => {
      console.log('Form suggestions:', suggestions);
    });
  }, []);

  return (
    // Your form JSX
  );
};
```

## 🎯 Context Types and Triggers

### Supported Context Types

1. **Leave Request Forms** (`leave_request_form`)
   - Triggers: Form focus, dialog open
   - Suggestions: Leave balance checks, date conflicts, approval tips

2. **User Management Forms** (`user_management_form`)
   - Triggers: Form focus, dialog open
   - Suggestions: Email validation, role assignment, security tips

3. **Task Management Forms** (`task_management_form`)
   - Triggers: Form focus, dialog open
   - Suggestions: Deadline setting, priority assignment, workload tips

4. **Job Application Forms** (`job_application_form`)
   - Triggers: Form focus, page visit
   - Suggestions: CV upload tips, form completion, application guidelines

5. **Dashboard Navigation** (`dashboard_navigation`)
   - Triggers: Page visit
   - Suggestions: Feature highlights, quick actions, role-specific tips

### Custom Context Rules

You can add custom context rules in the backend:

```javascript
// In contextAwareService.js
contextAwareService.updateContextRule('custom_form', {
  triggers: ['form_focus', 'dialog_open'],
  suggestions: async (userId, data) => {
    return ['Custom suggestion 1', 'Custom suggestion 2'];
  },
  priority: 4,
  cooldown: 300000 // 5 minutes
});
```

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```env
# AI Assistant Configuration - Jarvis Advanced HR Intelligence System
AI_ASSISTANT_NAME=Jarvis
AI_ASSISTANT_VERSION=2.0.0
ENABLE_AI_LOGGING=true
AI_RESPONSE_TIMEOUT=15000
AI_REAL_TIME_PROCESSING=true
AI_ADVANCED_ANALYTICS=true
AI_EMOTIONAL_INTELLIGENCE=true
```

### Popup Customization

You can customize the popup behavior by modifying the `ContextAwarePopup` component:

```javascript
<ContextAwarePopup
  onOpenChat={handleOpenChat}
  onDismiss={handleDismiss}
  autoHideDelay={15000}  // Auto-hide after 15 seconds
  enableExpansion={true}  // Allow expanding for more suggestions
  maxSuggestions={4}      // Maximum suggestions to show initially
/>
```

## 📝 Integration Examples

### 1. Adding Context Awareness to Existing Forms

```javascript
import { useFormContextAware } from '../hooks/useContextAware';

const ExistingForm = () => {
  // Add this line to enable context awareness
  const { trackFormInteraction } = useFormContextAware('user-management');

  // Your existing form logic remains unchanged
  return (
    <form>
      {/* Your existing form fields */}
    </form>
  );
};
```

### 2. Adding Context Awareness to Dialogs

```javascript
import { useDialogContextAware } from '../hooks/useContextAware';

const UserDialog = ({ open, onClose }) => {
  // Add this line to enable context awareness
  const { trackDialogInteraction } = useDialogContextAware('add-user');

  return (
    <Dialog open={open} onClose={onClose}>
      {/* Your dialog content */}
    </Dialog>
  );
};
```

### 3. Manual Context Tracking

```javascript
import { useContextAware } from '../hooks/useContextAware';

const CustomComponent = () => {
  const { processContext } = useContextAware();

  const handleCustomAction = async () => {
    // Manually trigger context processing
    await processContext('custom_action', 'button_click', {
      actionType: 'export_data',
      timestamp: Date.now()
    });
  };

  return (
    <Button onClick={handleCustomAction}>
      Export Data
    </Button>
  );
};
```

## 🎨 Styling and Theming

The popup uses Material-UI theming and can be customized through your theme:

```javascript
const theme = createTheme({
  components: {
    // Customize popup appearance
    MuiPaper: {
      styleOverrides: {
        root: {
          // Custom popup styles
        }
      }
    }
  }
});
```

## 📊 Analytics and Monitoring

### Context Statistics

Get context usage statistics:

```javascript
import contextApiService from '../services/ContextApiService';

const getStats = async () => {
  const stats = await contextApiService.getContextStats();
  console.log('Context Stats:', stats);
};
```

### User Context History

View user's context interaction history:

```javascript
const getHistory = async () => {
  const history = await contextApiService.getContextHistory();
  console.log('User Context History:', history);
};
```

## 🔍 Troubleshooting

### Common Issues

1. **Popup not appearing**
   - Check if context detection is enabled
   - Verify form types are correctly mapped
   - Check browser console for errors

2. **Suggestions not loading**
   - Verify backend API endpoints are accessible
   - Check authentication tokens
   - Review network requests in browser dev tools

3. **Performance issues**
   - Adjust debounce timing in useContextAware hook
   - Review cache settings in ContextApiService
   - Monitor context processing frequency

### Debug Mode

Enable debug logging:

```javascript
// In ContextDetectionService.js
console.log('🔍 Context detected:', context);

// In ContextApiService.js
console.log('📡 API request:', endpoint, data);
```

## 🚀 Advanced Features

### Custom Suggestion Providers

Create custom suggestion providers for specific business logic:

```javascript
// In contextAwareService.js
const getCustomSuggestions = async (userId, data) => {
  // Your custom logic here
  const userRole = await getUserRole(userId);
  const suggestions = [];
  
  if (userRole === 'manager') {
    suggestions.push('Manager-specific suggestion');
  }
  
  return suggestions;
};
```

### Real-time Updates

The system supports real-time updates through WebSocket connections. Suggestions can be updated dynamically as users interact with forms.

### Machine Learning Integration

The context system is designed to support machine learning models for better suggestion accuracy. You can integrate ML models to analyze user patterns and provide more personalized suggestions.

## 📚 API Reference

### Context Detection Service

- `activate()`: Enable context detection
- `deactivate()`: Disable context detection
- `getCurrentContext()`: Get current context
- `getContextHistory()`: Get context history
- `clearHistory()`: Clear context history

### Context API Service

- `processContext(type, action, data)`: Process context
- `getContextSuggestions(type, data)`: Get suggestions
- `getContextHistory()`: Get user history
- `clearContextHistory()`: Clear user history

### Hooks

- `useContextAware(options)`: Main context hook
- `useFormContextAware(formType, formData)`: Form-specific hook
- `useDialogContextAware(dialogType, dialogData)`: Dialog-specific hook
- `useInputContextAware()`: Input-specific hook

## 🎯 Best Practices

1. **Use appropriate context types** for different scenarios
2. **Implement proper error handling** for API calls
3. **Cache suggestions** to improve performance
4. **Respect user privacy** by not logging sensitive data
5. **Test thoroughly** across different user roles and scenarios
6. **Monitor performance** and adjust settings as needed

## 🔄 Updates and Maintenance

The context-aware system is designed to be easily maintainable:

- Context rules can be updated without code changes
- Suggestions can be modified through the backend service
- New context types can be added by extending the service
- Performance can be monitored through built-in analytics

## 📞 Support

For issues or questions about the context-aware popup system:

1. Check the troubleshooting section above
2. Review browser console for error messages
3. Verify backend API responses
4. Test with different user roles and scenarios

The Jarvis context-aware popup system enhances user experience by providing intelligent, proactive assistance throughout the application workflow.
