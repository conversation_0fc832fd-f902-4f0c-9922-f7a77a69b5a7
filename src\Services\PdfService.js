import jsPD<PERSON> from 'jspdf';
import 'jspdf-autotable';
import html2canvas from 'html2canvas';
import { REPORT_TYPES } from './ReportService';

/**
 * Generate a PDF report
 * @param {Object} reportData - The report data
 * @param {string} reportType - The type of report
 * @returns {jsPDF} - The PDF document
 */
export const generatePdfReport = (reportData, reportType) => {
  // Create a new PDF document
  const doc = new jsPDF();
  
  // Add company logo and header
  addHeader(doc, reportType, reportData);
  
  // Add report content based on report type
  switch (reportType) {
    case REPORT_TYPES.NLP:
      addNlpReportContent(doc, reportData);
      break;
    case REPORT_TYPES.ATTENDANCE:
      addAttendanceReportContent(doc, reportData);
      break;
    case REPORT_TYPES.USER:
      addUserReportContent(doc, reportData);
      break;
    case REPORT_TYPES.JOB:
      addJobReportContent(doc, reportData);
      break;
    case REPORT_TYPES.LEAVE:
      addLeaveReportContent(doc, reportData);
      break;
    case REPORT_TYPES.TASK:
      addTaskReportContent(doc, reportData);
      break;
    default:
      doc.text('Report content not available', 14, 50);
  }
  
  // Add footer with page number
  addFooter(doc);
  
  return doc;
};

/**
 * Add header to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {string} reportType - The type of report
 * @param {Object} reportData - The report data
 */
const addHeader = (doc, reportType, reportData) => {
  // Add company name
  doc.setFontSize(20);
  doc.setTextColor(220, 53, 69); // Red color
  doc.text('HR Management System', 105, 15, { align: 'center' });
  
  // Add report title
  doc.setFontSize(16);
  doc.setTextColor(33, 37, 41); // Dark gray
  
  let title = 'Report';
  switch (reportType) {
    case REPORT_TYPES.NLP:
      title = `NLP Analysis Report: ${reportData.candidateName || 'Unknown Candidate'}`;
      break;
    case REPORT_TYPES.ATTENDANCE:
      title = 'Attendance Report';
      break;
    case REPORT_TYPES.USER:
      title = 'User Report';
      break;
    case REPORT_TYPES.JOB:
      title = 'Job Report';
      break;
    case REPORT_TYPES.LEAVE:
      title = 'Leave Request Report';
      break;
    case REPORT_TYPES.TASK:
      title = 'Task Report';
      break;
  }
  
  doc.text(title, 105, 25, { align: 'center' });
  
  // Add date
  doc.setFontSize(10);
  doc.setTextColor(108, 117, 125); // Gray
  doc.text(`Generated on: ${new Date().toLocaleString()}`, 105, 30, { align: 'center' });
  
  // Add horizontal line
  doc.setDrawColor(220, 53, 69); // Red color
  doc.setLineWidth(0.5);
  doc.line(14, 35, 196, 35);
};

/**
 * Add footer to the PDF document
 * @param {jsPDF} doc - The PDF document
 */
const addFooter = (doc) => {
  const pageCount = doc.internal.getNumberOfPages();
  
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(10);
    doc.setTextColor(108, 117, 125); // Gray
    doc.text(`Page ${i} of ${pageCount}`, 105, 287, { align: 'center' });
    doc.text('HR Management System - Confidential', 105, 292, { align: 'center' });
  }
};

/**
 * Add NLP report content to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} reportData - The report data
 */
const addNlpReportContent = (doc, reportData) => {
  const nlpResults = reportData.nlpResults || {};
  const startY = 45;
  
  // Candidate Information
  doc.setFontSize(14);
  doc.setTextColor(33, 37, 41); // Dark gray
  doc.text('Candidate Information', 14, startY);
  
  doc.setFontSize(12);
  doc.text(`Name: ${reportData.candidateName || 'N/A'}`, 14, startY + 10);
  doc.text(`Position: ${reportData.position || 'N/A'}`, 14, startY + 16);
  doc.text(`Email: ${nlpResults.email || 'N/A'}`, 14, startY + 22);
  doc.text(`Phone: ${nlpResults.phone || 'N/A'}`, 14, startY + 28);
  
  // Match Score
  doc.setFontSize(14);
  doc.text('Match Analysis', 14, startY + 38);
  
  doc.setFontSize(12);
  doc.text(`Match Score: ${reportData.matchScore || 0}%`, 14, startY + 48);
  
  if (nlpResults.semanticScore !== undefined) {
    doc.text(`Semantic Similarity: ${nlpResults.semanticScore}`, 14, startY + 54);
  }
  
  // Match Analysis
  if (nlpResults.matchAnalysis) {
    doc.setFontSize(12);
    const splitMatchAnalysis = doc.splitTextToSize(nlpResults.matchAnalysis, 180);
    doc.text(splitMatchAnalysis, 14, startY + 64);
  }
  
  // Skills
  const skillsY = startY + (nlpResults.matchAnalysis ? 80 : 64);
  doc.setFontSize(14);
  doc.text('Skills & Qualifications', 14, skillsY);
  
  // Matched Skills
  doc.setFontSize(12);
  doc.setTextColor(40, 167, 69); // Green
  doc.text('Matching Qualifications:', 14, skillsY + 10);
  
  if (nlpResults.matchedSkills && nlpResults.matchedSkills.length > 0) {
    let y = skillsY + 16;
    nlpResults.matchedSkills.forEach(skill => {
      doc.text(`• ${skill}`, 20, y);
      y += 6;
    });
  } else {
    doc.text('No matching qualifications found', 20, skillsY + 16);
  }
  
  // Missing Skills
  const missingSkillsY = skillsY + (nlpResults.matchedSkills && nlpResults.matchedSkills.length > 0 ? 
    16 + nlpResults.matchedSkills.length * 6 : 22);
  
  doc.setTextColor(220, 53, 69); // Red
  doc.text('Missing Qualifications:', 14, missingSkillsY);
  
  if (nlpResults.missingSkills && nlpResults.missingSkills.length > 0) {
    let y = missingSkillsY + 6;
    nlpResults.missingSkills.forEach(skill => {
      doc.text(`• ${skill}`, 20, y);
      y += 6;
    });
  } else {
    doc.text('No missing qualifications found', 20, missingSkillsY + 6);
  }
  
  // Add a new page if needed
  if (missingSkillsY + 20 > 270) {
    doc.addPage();
  }
  
  // Education and Experience
  const eduExpY = missingSkillsY + (nlpResults.missingSkills && nlpResults.missingSkills.length > 0 ? 
    12 + nlpResults.missingSkills.length * 6 : 12);
  
  // Check if we need to add a new page
  if (eduExpY > 250) {
    doc.addPage();
    doc.setFontSize(14);
    doc.setTextColor(33, 37, 41); // Dark gray
    doc.text('Education & Experience', 14, 45);
    
    // Education
    doc.setFontSize(12);
    doc.text('Education:', 14, 55);
    
    if (nlpResults.education && nlpResults.education.length > 0) {
      let y = 61;
      nlpResults.education.forEach(edu => {
        const splitEdu = doc.splitTextToSize(edu, 170);
        doc.text(splitEdu, 20, y);
        y += splitEdu.length * 6 + 4;
      });
    } else {
      doc.text('No education details found', 20, 61);
    }
    
    // Experience
    const expY = nlpResults.education && nlpResults.education.length > 0 ? 
      61 + nlpResults.education.length * 10 : 67;
    
    doc.text('Experience:', 14, expY);
    
    if (nlpResults.experience && nlpResults.experience.length > 0) {
      let y = expY + 6;
      nlpResults.experience.forEach(exp => {
        const splitExp = doc.splitTextToSize(exp, 170);
        doc.text(splitExp, 20, y);
        y += splitExp.length * 6 + 4;
      });
    } else {
      doc.text('No experience details found', 20, expY + 6);
    }
  } else {
    doc.setFontSize(14);
    doc.setTextColor(33, 37, 41); // Dark gray
    doc.text('Education & Experience', 14, eduExpY);
    
    // Education
    doc.setFontSize(12);
    doc.text('Education:', 14, eduExpY + 10);
    
    if (nlpResults.education && nlpResults.education.length > 0) {
      let y = eduExpY + 16;
      nlpResults.education.forEach(edu => {
        const splitEdu = doc.splitTextToSize(edu, 170);
        doc.text(splitEdu, 20, y);
        y += splitEdu.length * 6 + 4;
      });
    } else {
      doc.text('No education details found', 20, eduExpY + 16);
    }
    
    // Experience
    const expY = eduExpY + (nlpResults.education && nlpResults.education.length > 0 ? 
      16 + nlpResults.education.length * 10 : 22);
    
    // Check if we need to add a new page
    if (expY > 250) {
      doc.addPage();
      doc.setFontSize(12);
      doc.setTextColor(33, 37, 41); // Dark gray
      doc.text('Experience:', 14, 45);
      
      if (nlpResults.experience && nlpResults.experience.length > 0) {
        let y = 51;
        nlpResults.experience.forEach(exp => {
          const splitExp = doc.splitTextToSize(exp, 170);
          doc.text(splitExp, 20, y);
          y += splitExp.length * 6 + 4;
        });
      } else {
        doc.text('No experience details found', 20, 51);
      }
    } else {
      doc.text('Experience:', 14, expY);
      
      if (nlpResults.experience && nlpResults.experience.length > 0) {
        let y = expY + 6;
        nlpResults.experience.forEach(exp => {
          const splitExp = doc.splitTextToSize(exp, 170);
          doc.text(splitExp, 20, y);
          y += splitExp.length * 6 + 4;
        });
      } else {
        doc.text('No experience details found', 20, expY + 6);
      }
    }
  }
};

/**
 * Add attendance report content to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} reportData - The report data
 */
const addAttendanceReportContent = (doc, reportData) => {
  const startY = 45;
  
  // Report information
  doc.setFontSize(14);
  doc.setTextColor(33, 37, 41); // Dark gray
  doc.text('Attendance Report Information', 14, startY);
  
  doc.setFontSize(12);
  doc.text(`Period: ${reportData.period || 'All Time'}`, 14, startY + 10);
  doc.text(`Total Employees: ${reportData.totalEmployees || 'N/A'}`, 14, startY + 16);
  
  // Attendance data
  if (reportData.attendanceData && reportData.attendanceData.length > 0) {
    // Create table
    const tableColumn = ["Employee", "Date", "Check In", "Check Out", "Total Hours"];
    const tableRows = [];
    
    reportData.attendanceData.forEach(record => {
      const checkInTime = record.checkIn ? new Date(record.checkIn).toLocaleTimeString() : 'N/A';
      const checkOutTime = record.checkOut ? new Date(record.checkOut).toLocaleTimeString() : 'N/A';
      const totalHours = record.totalHours || 'N/A';
      
      tableRows.push([
        record.employeeName || 'N/A',
        record.date ? new Date(record.date).toLocaleDateString() : 'N/A',
        checkInTime,
        checkOutTime,
        totalHours
      ]);
    });
    
    doc.autoTable({
      startY: startY + 25,
      head: [tableColumn],
      body: tableRows,
      theme: 'striped',
      headStyles: { fillColor: [220, 53, 69], textColor: 255 },
      margin: { top: 30 }
    });
  } else {
    doc.text('No attendance data available', 14, startY + 25);
  }
};

/**
 * Add user report content to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} reportData - The report data
 */
const addUserReportContent = (doc, reportData) => {
  const startY = 45;
  
  // Report information
  doc.setFontSize(14);
  doc.setTextColor(33, 37, 41); // Dark gray
  doc.text('User Report Information', 14, startY);
  
  doc.setFontSize(12);
  doc.text(`Total Users: ${reportData.totalUsers || 'N/A'}`, 14, startY + 10);
  
  // User data
  if (reportData.users && reportData.users.length > 0) {
    // Create table
    const tableColumn = ["Name", "Email", "Role", "Department", "Status"];
    const tableRows = [];
    
    reportData.users.forEach(user => {
      tableRows.push([
        user.name || 'N/A',
        user.email || 'N/A',
        user.role || 'N/A',
        user.department || 'N/A',
        user.status || 'N/A'
      ]);
    });
    
    doc.autoTable({
      startY: startY + 20,
      head: [tableColumn],
      body: tableRows,
      theme: 'striped',
      headStyles: { fillColor: [220, 53, 69], textColor: 255 },
      margin: { top: 30 }
    });
  } else {
    doc.text('No user data available', 14, startY + 20);
  }
};

/**
 * Add job report content to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} reportData - The report data
 */
const addJobReportContent = (doc, reportData) => {
  const startY = 45;
  
  // Report information
  doc.setFontSize(14);
  doc.setTextColor(33, 37, 41); // Dark gray
  doc.text('Job Report Information', 14, startY);
  
  doc.setFontSize(12);
  doc.text(`Total Jobs: ${reportData.totalJobs || 'N/A'}`, 14, startY + 10);
  
  // Job data
  if (reportData.jobs && reportData.jobs.length > 0) {
    // Create table
    const tableColumn = ["Title", "Department", "Location", "Status", "Applications"];
    const tableRows = [];
    
    reportData.jobs.forEach(job => {
      tableRows.push([
        job.title || 'N/A',
        job.department || 'N/A',
        job.location || 'N/A',
        job.status || 'N/A',
        job.applications || '0'
      ]);
    });
    
    doc.autoTable({
      startY: startY + 20,
      head: [tableColumn],
      body: tableRows,
      theme: 'striped',
      headStyles: { fillColor: [220, 53, 69], textColor: 255 },
      margin: { top: 30 }
    });
  } else {
    doc.text('No job data available', 14, startY + 20);
  }
};

/**
 * Add leave report content to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} reportData - The report data
 */
const addLeaveReportContent = (doc, reportData) => {
  const startY = 45;
  
  // Report information
  doc.setFontSize(14);
  doc.setTextColor(33, 37, 41); // Dark gray
  doc.text('Leave Request Report Information', 14, startY);
  
  doc.setFontSize(12);
  doc.text(`Total Leave Requests: ${reportData.totalLeaves || 'N/A'}`, 14, startY + 10);
  
  // Leave data
  if (reportData.leaves && reportData.leaves.length > 0) {
    // Create table
    const tableColumn = ["Employee", "Type", "Start Date", "End Date", "Status"];
    const tableRows = [];
    
    reportData.leaves.forEach(leave => {
      tableRows.push([
        leave.employeeName || 'N/A',
        leave.leaveType || 'N/A',
        leave.startDate ? new Date(leave.startDate).toLocaleDateString() : 'N/A',
        leave.endDate ? new Date(leave.endDate).toLocaleDateString() : 'N/A',
        leave.status || 'N/A'
      ]);
    });
    
    doc.autoTable({
      startY: startY + 20,
      head: [tableColumn],
      body: tableRows,
      theme: 'striped',
      headStyles: { fillColor: [220, 53, 69], textColor: 255 },
      margin: { top: 30 }
    });
  } else {
    doc.text('No leave request data available', 14, startY + 20);
  }
};

/**
 * Add task report content to the PDF document
 * @param {jsPDF} doc - The PDF document
 * @param {Object} reportData - The report data
 */
const addTaskReportContent = (doc, reportData) => {
  const startY = 45;
  
  // Report information
  doc.setFontSize(14);
  doc.setTextColor(33, 37, 41); // Dark gray
  doc.text('Task Report Information', 14, startY);
  
  doc.setFontSize(12);
  doc.text(`Total Tasks: ${reportData.totalTasks || 'N/A'}`, 14, startY + 10);
  
  // Task data
  if (reportData.tasks && reportData.tasks.length > 0) {
    // Create table
    const tableColumn = ["Title", "Assigned To", "Due Date", "Priority", "Status"];
    const tableRows = [];
    
    reportData.tasks.forEach(task => {
      tableRows.push([
        task.title || 'N/A',
        task.assignedTo || 'N/A',
        task.dueDate ? new Date(task.dueDate).toLocaleDateString() : 'N/A',
        task.priority || 'N/A',
        task.status || 'N/A'
      ]);
    });
    
    doc.autoTable({
      startY: startY + 20,
      head: [tableColumn],
      body: tableRows,
      theme: 'striped',
      headStyles: { fillColor: [220, 53, 69], textColor: 255 },
      margin: { top: 30 }
    });
  } else {
    doc.text('No task data available', 14, startY + 20);
  }
};

/**
 * Generate a PDF from an HTML element
 * @param {HTMLElement} element - The HTML element to convert to PDF
 * @param {string} filename - The filename for the PDF
 */
export const generatePdfFromHtml = async (element, filename) => {
  try {
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      logging: false
    });
    
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });
    
    const imgProps = pdf.getImageProperties(imgData);
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    
    pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
    pdf.save(filename);
    
    return pdf;
  } catch (error) {
    console.error('Error generating PDF from HTML:', error);
    throw error;
  }
};
