import React, { useState, useEffect, useRef } from 'react';
import {
  Paper,
  Box,
  Typography,
  IconButton,
  Button,
  Chip,
  Avatar,
  Fade,
  Slide,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Tooltip
} from '@mui/material';
import {
  SmartToy as JarvisIcon,
  Close as CloseIcon,
  LightbulbOutlined as SuggestionIcon,
  CheckCircleOutlined as AcceptIcon,
  InfoOutlined as InfoIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Chat as ChatIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import contextDetectionService from '../../services/ContextDetectionService';

// Styled components
const PopupContainer = styled(motion.div)(({ theme }) => ({
  position: 'fixed',
  bottom: theme.spacing(12),
  right: theme.spacing(3),
  zIndex: theme.zIndex.speedDial - 1,
  maxWidth: 380,
  minWidth: 320,
  [theme.breakpoints.down('md')]: {
    bottom: theme.spacing(10),
    right: theme.spacing(2),
    left: theme.spacing(2),
    maxWidth: 'none',
    minWidth: 'auto'
  }
}));

const PopupPaper = styled(Paper)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}10 100%)`,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.primary.main}30`,
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
}));

const PopupHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  padding: theme.spacing(1.5, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
}));

const PopupContent = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  maxHeight: 300,
  overflowY: 'auto'
}));

const SuggestionChip = styled(Chip)(({ theme }) => ({
  margin: theme.spacing(0.5),
  '&:hover': {
    backgroundColor: theme.palette.primary.main,
    color: 'white'
  }
}));

const ContextAwarePopup = ({ onOpenChat, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentContext, setCurrentContext] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [dismissedContexts, setDismissedContexts] = useState(new Set());
  const [autoHideTimer, setAutoHideTimer] = useState(null);
  const popupRef = useRef(null);

  useEffect(() => {
    // Subscribe to context changes
    const unsubscribe = contextDetectionService.onContextChange((context) => {
      handleContextChange(context);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    // Auto-hide popup after 15 seconds if not interacted with
    if (isVisible && !isExpanded) {
      const timer = setTimeout(() => {
        handleAutoHide();
      }, 15000);
      
      setAutoHideTimer(timer);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, isExpanded]);

  const handleContextChange = (context) => {
    // Don't show popup for low priority contexts or already dismissed ones
    if (context.priority < 3 || dismissedContexts.has(context.type)) {
      return;
    }

    // Don't show for navigation contexts unless it's a significant change
    if (context.type === 'navigation' && currentContext?.type === 'navigation') {
      return;
    }

    setCurrentContext(context);
    setIsVisible(true);
    setIsExpanded(false);
    
    // Clear any existing auto-hide timer
    if (autoHideTimer) {
      clearTimeout(autoHideTimer);
    }
  };

  const handleAutoHide = () => {
    setIsVisible(false);
    setIsExpanded(false);
  };

  const handleDismiss = () => {
    if (currentContext) {
      setDismissedContexts(prev => new Set([...prev, currentContext.type]));
    }
    setIsVisible(false);
    setIsExpanded(false);
    onDismiss?.();
  };

  const handleExpand = () => {
    setIsExpanded(!isExpanded);
    
    // Clear auto-hide timer when expanded
    if (autoHideTimer) {
      clearTimeout(autoHideTimer);
      setAutoHideTimer(null);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    // You can implement specific actions for suggestions here
    console.log('Suggestion clicked:', suggestion);
    
    // For now, just show a brief feedback
    // In a real implementation, you might trigger specific actions
  };

  const handleOpenChat = () => {
    onOpenChat?.();
    setIsVisible(false);
  };

  const getContextIcon = (contextType) => {
    const icons = {
      'leave_request': '🏖️',
      'user_management': '👥',
      'task_management': '📋',
      'job_application': '💼',
      'form_submission': '📝',
      'creation_action': '➕',
      'dialog_interaction': '💬',
      'navigation': '🧭'
    };
    
    return icons[contextType] || '💡';
  };

  const getContextTitle = (context) => {
    const titles = {
      'leave_request': 'Leave Request Assistance',
      'user_management': 'User Management Help',
      'task_management': 'Task Management Tips',
      'job_application': 'Application Guidance',
      'form_submission': 'Form Submission Help',
      'creation_action': 'Creation Assistance',
      'dialog_interaction': 'Dialog Help',
      'navigation': 'Navigation Assistance'
    };
    
    return titles[context?.type] || 'Jarvis Assistance';
  };

  const getContextMessage = (context) => {
    const messages = {
      'leave_request': 'I can help you with your leave request. Let me provide some guidance.',
      'user_management': 'I notice you\'re managing users. Here are some helpful tips.',
      'task_management': 'Working with tasks? I can provide some optimization suggestions.',
      'job_application': 'Applying for a position? Let me guide you through the process.',
      'form_submission': 'Ready to submit? Let me help ensure everything is correct.',
      'creation_action': 'Creating something new? I have some recommendations.',
      'dialog_interaction': 'I see you\'ve opened a dialog. Need any assistance?',
      'navigation': 'Welcome to this section! Here\'s what you can do here.'
    };
    
    return messages[context?.type] || 'I\'m here to help with whatever you\'re working on.';
  };

  if (!isVisible || !currentContext) {
    return null;
  }

  return (
    <AnimatePresence>
      <PopupContainer
        ref={popupRef}
        initial={{ opacity: 0, y: 50, scale: 0.9 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 50, scale: 0.9 }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
      >
        <PopupPaper elevation={8}>
          <PopupHeader>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'white', color: 'primary.main' }}>
                <JarvisIcon fontSize="small" />
              </Avatar>
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', lineHeight: 1.2 }}>
                  {getContextIcon(currentContext.type)} Jarvis
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.9, lineHeight: 1 }}>
                  Context-Aware Assistant
                </Typography>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title={isExpanded ? "Collapse" : "Expand"}>
                <IconButton
                  size="small"
                  onClick={handleExpand}
                  sx={{ color: 'white', mr: 0.5 }}
                >
                  {isExpanded ? <CollapseIcon /> : <ExpandIcon />}
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Dismiss">
                <IconButton
                  size="small"
                  onClick={handleDismiss}
                  sx={{ color: 'white' }}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </PopupHeader>

          <PopupContent>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
              {getContextTitle(currentContext)}
            </Typography>
            
            <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
              {getContextMessage(currentContext)}
            </Typography>

            {currentContext.suggestions && currentContext.suggestions.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" sx={{ fontWeight: 'bold', mb: 1, display: 'block' }}>
                  💡 Quick Tips:
                </Typography>
                
                {isExpanded ? (
                  <List dense>
                    {currentContext.suggestions.map((suggestion, index) => (
                      <ListItem
                        key={index}
                        button
                        onClick={() => handleSuggestionClick(suggestion)}
                        sx={{ py: 0.5, px: 0 }}
                      >
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          <SuggestionIcon fontSize="small" color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={suggestion}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {currentContext.suggestions.slice(0, 2).map((suggestion, index) => (
                      <SuggestionChip
                        key={index}
                        label={suggestion}
                        size="small"
                        onClick={() => handleSuggestionClick(suggestion)}
                        icon={<SuggestionIcon />}
                      />
                    ))}
                    {currentContext.suggestions.length > 2 && (
                      <Chip
                        label={`+${currentContext.suggestions.length - 2} more`}
                        size="small"
                        variant="outlined"
                        onClick={handleExpand}
                      />
                    )}
                  </Box>
                )}
              </Box>
            )}

            <Divider sx={{ my: 1.5 }} />

            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'space-between' }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<ChatIcon />}
                onClick={handleOpenChat}
                sx={{ flex: 1 }}
              >
                Chat with Jarvis
              </Button>
              
              <Button
                variant="contained"
                size="small"
                startIcon={<AcceptIcon />}
                onClick={handleDismiss}
                sx={{ flex: 1 }}
              >
                Got it!
              </Button>
            </Box>
          </PopupContent>
        </PopupPaper>
      </PopupContainer>
    </AnimatePresence>
  );
};

export default ContextAwarePopup;
