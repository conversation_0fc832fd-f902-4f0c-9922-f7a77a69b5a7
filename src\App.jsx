import { useEffect, useState, Suspense, lazy } from "react";
import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation } from "react-router-dom";
import { setNavigator } from "./Utils/navigation";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./Styles/ToastStyles.css"; // Custom toast styles
import "./Styles/Transitions.css";

// Import transition components
import TransitionLayout from "./components/transitions/TransitionLayout";
import PageTransition from "./components/transitions/PageTransition";
import { Box, CircularProgress } from "@mui/material";


// Import HR Dashboard directly to avoid issues with GEK components
import NewHRDashboard from "./pages/NewHRDashboard";

// Lazy load other page components for better performance
const Login = lazy(() => import("./pages/Login"));
const NewAdminDashboard = lazy(() => import("./pages/NewAdminDashboard"));
const ForgotPassword = lazy(() => import("./pages/ForgotPassword"));
const ResetPassword = lazy(() => import("./pages/ResetPassword"));
const NewUserDashboard = lazy(() => import("./pages/NewUserDashboard"));
const ProfilePage = lazy(() => import("./pages/ProfilePage"));
const Apply = lazy(() => import('./pages/Apply'));
const NewLandingPage = lazy(() => import("./pages/NewLandingPage"));
const ReportsPage = lazy(() => import("./pages/ReportsPage"));






// Optimized loading fallback component for instant display
const LoadingFallback = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      width: '100vw',
      backgroundColor: 'background.default',
      transition: 'opacity 0.1s ease-in-out', // Smooth fade-in
    }}
  >
    <CircularProgress
      color="primary"
      size={40}
      thickness={4}
      sx={{
        animation: 'spin 1s linear infinite',
      }}
    />
  </Box>
);

// Wrap component with page transition
const withPageTransition = (Component) => (props) => (
  <PageTransition>
    <Component {...props} />
  </PageTransition>
);

// Navigation component with routes
const RoutesWithNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isAuthenticated, setIsAuthenticated] = useState(null);

  useEffect(() => {
    setNavigator(navigate);

    // Instant authentication check - no API calls needed here
    const token = localStorage.getItem('token');

    if (token) {
      try {
        // Quick token validation without API call
        const payload = JSON.parse(atob(token.split('.')[1]));
        const isExpired = payload.exp * 1000 < Date.now();

        if (isExpired) {
          localStorage.removeItem('token');
          setIsAuthenticated(false);
        } else {
          setIsAuthenticated(true);
        }
      } catch (error) {
        localStorage.removeItem('token');
        setIsAuthenticated(false);
      }
    } else {
      setIsAuthenticated(false);
    }
  }, [navigate]);

  // No loading state - instant authentication check
  if (isAuthenticated === null) {
    return <LoadingFallback />;
  }

  return (
    <>
      <TransitionLayout>
        <Suspense fallback={<LoadingFallback />}>
          <Routes location={location} key={location.pathname}>
            <Route path="/" element={withPageTransition(NewLandingPage)()} />
            <Route path="/Login" element={withPageTransition(Login)()} />
            <Route path="/dashboard" element={withPageTransition(NewAdminDashboard)()} />
            <Route path="/hr-dashboard" element={withPageTransition(NewHRDashboard)()} />
            <Route path="/forgot-password" element={withPageTransition(ForgotPassword)()} />
            <Route path="/reset-password" element={withPageTransition(ResetPassword)()} />
            <Route path="/user-dashboard" element={withPageTransition(NewUserDashboard)()} />
            <Route path="/profile" element={withPageTransition(ProfilePage)()} />
            <Route path="/apply" element={withPageTransition(Apply)()} />
            <Route path="/reports" element={withPageTransition(ReportsPage)()} />
          </Routes>
        </Suspense>
      </TransitionLayout>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
        limit={3}
        closeButton={true}
      />
    </>
  );
};

// Main App component
function App() {
  return (
    <Router>
      <RoutesWithNavigation />
    </Router>
  );
}

export default App;
