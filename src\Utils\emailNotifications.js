/**
 * Email notification utilities for the HR Management System
 * This file contains functions for sending email notifications using EmailJS
 */

import emailjs from '@emailjs/browser';

// EmailJS configuration
const EMAILJS_SERVICE_ID = 'service_fi4a5re';
const EMAILJS_USER_ID = 'qGI5Si9Z1pyqvNTYS';

// EmailJS templates
const TEMPLATES = {
  PASSWORD_RESET: 'template_ncg6255',
  PASSWORD_CHANGE: 'template_o9tsbug'
};

/**
 * Send a password reset email notification
 * @param {Object} params - Parameters for the email template
 * @param {string} params.email - User's email address
 * @param {string} params.password - New password
 * @param {string} params.to_name - User's name
 * @returns {Promise} - Promise that resolves when the email is sent
 */
export const sendPasswordResetEmail = async (params) => {
  try {
    console.log('Sending password reset email notification with EmailJS');

    const templateParams = {
      email: params.email,
      password: params.password,
      to_name: params.to_name || params.email.split('@')[0] || 'User'
    };

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      TEMPLATES.PASSWORD_RESET,
      templateParams,
      EMAILJS_USER_ID
    );

    console.log('EmailJS response for password reset notification:', response);
    return response;
  } catch (error) {
    console.error('Error sending password reset email notification:', error);
    throw error;
  }
};

/**
 * Send a password change email notification
 * @param {Object} params - Parameters for the email template
 * @param {string} params.email - User's email address
 * @param {string} params.to_name - User's name
 * @param {string} params.date - Date and time of the password change
 * @param {boolean} params.byAdmin - Whether the password was changed by an admin
 * @returns {Promise} - Promise that resolves when the email is sent
 */
export const sendPasswordChangeEmail = async (params) => {
  try {
    console.log('Sending password change email notification with EmailJS');

    const templateParams = {
      email: params.email,
      to_name: params.to_name || params.email.split('@')[0] || 'User',
      date: params.date || new Date().toLocaleString(),
      byAdmin: params.byAdmin || false
    };

    const response = await emailjs.send(
      EMAILJS_SERVICE_ID,
      TEMPLATES.PASSWORD_CHANGE,
      templateParams,
      EMAILJS_USER_ID
    );

    console.log('EmailJS response for password change notification:', response);
    return response;
  } catch (error) {
    console.error('Error sending password change email notification:', error);
    throw error;
  }
};

export default {
  sendPasswordResetEmail,
  sendPasswordChangeEmail
};
