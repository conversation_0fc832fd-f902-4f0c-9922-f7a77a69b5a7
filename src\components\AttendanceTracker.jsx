import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Divider,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  TextField,
  CircularProgress
} from '@mui/material';
import {
  AccessTime as AccessTimeIcon,
  ExitToApp as ExitToAppIcon,
  History as HistoryIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import {
  showSuccessToast,
  showErrorToast,
  TOAST_CATEGORIES
} from '../Utils/toastUtils';
import api from '../Services/ApiService';
import '../styles/AttendanceTracker.css';
import ContextualSuggestions from './ai-assistant/ContextualSuggestions';
import { useContextualSuggestions } from '../hooks/useContextualSuggestions';

const AttendanceTracker = () => {
  const [loading, setLoading] = useState(true);
  const [todayStatus, setTodayStatus] = useState(null);
  const [attendanceHistory, setAttendanceHistory] = useState([]);
  const [notes, setNotes] = useState('');
  const [checkingIn, setCheckingIn] = useState(false);
  const [checkingOut, setCheckingOut] = useState(false);

  // Contextual AI suggestions
  const { generateSuggestions, activeSuggestions, clearSuggestions } = useContextualSuggestions();

  // Fetch today's attendance status on component mount
  useEffect(() => {
    fetchTodayStatus();
    fetchAttendanceHistory();
  }, []);

  const fetchTodayStatus = async () => {
    try {
      console.log('Fetching today\'s attendance status...');

      const response = await api.get('/user/attendance/today');
      console.log('Today\'s status response:', response.data);

      // Check if the response has the expected structure
      if (response.data && (response.data.checkIn || response.data.status)) {
        setTodayStatus(response.data);
      } else {
        console.warn('Unexpected data structure:', response.data);
        // Set a default status
        setTodayStatus({
          date: new Date().toISOString().split('T')[0],
          checkIn: null,
          checkOut: null,
          status: 'Not Checked In'
        });
      }
    } catch (error) {
      console.error('Error fetching today\'s attendance status:', error);
      // Set a default status
      setTodayStatus({
        date: new Date().toISOString().split('T')[0],
        checkIn: null,
        checkOut: null,
        status: 'Not Checked In'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchAttendanceHistory = async () => {
    try {
      console.log('Fetching attendance history...');

      const response = await api.get('/user/attendance/history');
      console.log('Attendance history response:', response.data);

      // Check if the response has the expected structure
      if (Array.isArray(response.data)) {
        setAttendanceHistory(response.data);
      } else if (response.data && Array.isArray(response.data.records)) {
        setAttendanceHistory(response.data.records);
      } else {
        console.warn('Unexpected data structure:', response.data);
        setAttendanceHistory([]);
      }
    } catch (error) {
      console.error('Error fetching attendance history:', error);
      setAttendanceHistory([]);
    }
  };

  const handleCheckIn = async () => {
    try {
      setCheckingIn(true);

      // Get user's location (if supported)
      let location = 'Office';
      try {
        if (navigator.geolocation) {
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 5000,
              maximumAge: 0
            });
          });

          location = `Lat: ${position.coords.latitude}, Long: ${position.coords.longitude}`;
        }
      } catch (geoError) {
        console.warn('Could not get location:', geoError);
        location = 'Location not available';
      }

      console.log('Sending check-in request with location:', location);

      // Use the correct endpoint for normal users
      const response = await api.post('/user/attendance/check-in', { location });
      console.log('Check-in response:', response.data);
      showSuccessToast('Check-in successful!', TOAST_CATEGORIES.ACTION, 'attendanceRecorded');

      // Generate contextual suggestions for check-in
      generateSuggestions('attendance', 'check_in', {
        time: new Date().toLocaleTimeString(),
        location: location
      });

      fetchTodayStatus();
      fetchAttendanceHistory();
    } catch (error) {
      console.error('Check-in error:', error);
      showErrorToast(error.response?.data?.message || 'Failed to check in', TOAST_CATEGORIES.ACTION, 'attendanceRecorded');
    } finally {
      setCheckingIn(false);
    }
  };

  const handleCheckOut = async () => {
    try {
      setCheckingOut(true);

      // Use the correct endpoint for normal users
      const response = await api.post('/user/attendance/check-out', { notes });
      console.log('Check-out response:', response.data);
      showSuccessToast('Check-out successful!', TOAST_CATEGORIES.ACTION, 'attendanceRecorded');

      // Generate contextual suggestions for check-out
      const hoursWorked = response.data?.attendance?.hoursWorked || 0;
      generateSuggestions('attendance', 'check_out', {
        time: new Date().toLocaleTimeString(),
        hoursWorked: hoursWorked,
        notes: notes
      });

      fetchTodayStatus();
      fetchAttendanceHistory();
      setNotes('');
    } catch (error) {
      console.error('Check-out error:', error);
      showErrorToast(error.response?.data?.message || 'Failed to check out', TOAST_CATEGORIES.ACTION, 'attendanceRecorded');
    } finally {
      setCheckingOut(false);
    }
  };

  // Format time for display
  const formatTime = (isoString) => {
    if (!isoString) return 'N/A';

    try {
      const date = new Date(isoString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'Invalid time';
    }
  };

  // Format date for display
  const formatDate = (isoString) => {
    if (!isoString) return 'N/A';

    try {
      const date = new Date(isoString);
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Calculate hours worked
  const calculateHoursWorked = (checkIn, checkOut) => {
    if (!checkIn || !checkOut) return 'N/A';

    try {
      const startTime = new Date(checkIn).getTime();
      const endTime = new Date(checkOut).getTime();
      const diffMs = endTime - startTime;
      const diffHrs = diffMs / (1000 * 60 * 60);
      return diffHrs.toFixed(2) + ' hrs';
    } catch (error) {
      console.error('Error calculating hours worked:', error);
      return 'Error';
    }
  };

  return (
    <Box className="attendance-tracker">
      {loading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Today's Attendance Card */}
          <Card className="today-card" elevation={3}>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Today's Attendance
              </Typography>
              <Typography variant="subtitle1" color="textSecondary" gutterBottom>
                {new Date().toLocaleDateString()} ({new Date().toLocaleDateString([], { weekday: 'long' })})
              </Typography>

              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} sm={6}>
                  <Paper elevation={1} className="time-paper">
                    <Box display="flex" alignItems="center">
                      <AccessTimeIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="body1">
                        Check-in Time: {todayStatus?.checkIn ? formatTime(todayStatus.checkIn) : 'Not checked in'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Paper elevation={1} className="time-paper">
                    <Box display="flex" alignItems="center">
                      <ExitToAppIcon color="secondary" sx={{ mr: 1 }} />
                      <Typography variant="body1">
                        Check-out Time: {todayStatus?.checkOut ? formatTime(todayStatus.checkOut) : 'Not checked out'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>

              <Box mt={3}>
                <Typography variant="body1" gutterBottom>
                  Status:
                  <Chip
                    label={
                      todayStatus?.checkOut ? 'Checked Out' :
                      todayStatus?.checkIn ? 'Checked In' :
                      'Not Checked In'
                    }
                    color={
                      todayStatus?.checkOut ? 'success' :
                      todayStatus?.checkIn ? 'primary' :
                      'default'
                    }
                    size="small"
                    sx={{ ml: 1 }}
                  />
                </Typography>

                {todayStatus?.checkIn && !todayStatus?.checkOut && (
                  <TextField
                    label="Notes for today"
                    multiline
                    rows={2}
                    fullWidth
                    variant="outlined"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    margin="normal"
                    placeholder="Add any notes about your work today"
                  />
                )}
              </Box>

              <Box mt={3} display="flex" justifyContent="center" gap={2}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<AccessTimeIcon />}
                  onClick={handleCheckIn}
                  disabled={checkingIn || !!todayStatus?.checkIn}
                >
                  {checkingIn ? 'Checking In...' : 'Check In'}
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<ExitToAppIcon />}
                  onClick={handleCheckOut}
                  disabled={checkingOut || !todayStatus?.checkIn || !!todayStatus?.checkOut}
                >
                  {checkingOut ? 'Checking Out...' : 'Check Out'}
                </Button>
              </Box>
            </CardContent>
          </Card>

          {/* Recent Attendance History */}
          <Card className="history-card" elevation={3} sx={{ mt: 4 }}>
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Recent Attendance History
              </Typography>

              {attendanceHistory.length === 0 ? (
                <Typography align="center" sx={{ py: 3 }}>No attendance records found.</Typography>
              ) : (
                <TableContainer component={Paper} sx={{ mt: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Check In</TableCell>
                        <TableCell>Check Out</TableCell>
                        <TableCell>Hours Worked</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {attendanceHistory.slice(0, 5).map((record) => (
                        <TableRow key={record._id || record.date}>
                          <TableCell>{formatDate(record.date)}</TableCell>
                          <TableCell>{formatTime(record.checkIn)}</TableCell>
                          <TableCell>{formatTime(record.checkOut)}</TableCell>
                          <TableCell>{calculateHoursWorked(record.checkIn, record.checkOut)}</TableCell>
                          <TableCell>
                            <Chip
                              label={
                                record.checkOut ? 'Completed' :
                                record.checkIn ? 'Checked In' :
                                'Absent'
                              }
                              color={
                                record.checkOut ? 'success' :
                                record.checkIn ? 'primary' :
                                'error'
                              }
                              size="small"
                              icon={
                                record.checkOut ? <CheckCircleIcon /> :
                                record.checkIn ? <AccessTimeIcon /> :
                                <CancelIcon />
                              }
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {/* AI Suggestions for Attendance */}
      {activeSuggestions.length > 0 && (
        <ContextualSuggestions
          context="attendance"
          actionType={checkingIn ? 'check_in' : checkingOut ? 'check_out' : 'view'}
          data={{
            time: new Date().toLocaleTimeString(),
            status: todayStatus?.status,
            checkedIn: todayStatus?.checkedIn,
            checkedOut: todayStatus?.checkedOut,
            hoursWorked: todayStatus?.hoursWorked
          }}
          position="bottom-right"
          onSuggestionApplied={(suggestion) => {
            console.log('Applied attendance suggestion:', suggestion);
            // Handle suggestion application if needed
          }}
        />
      )}
    </Box>
  );
};

export default AttendanceTracker;
