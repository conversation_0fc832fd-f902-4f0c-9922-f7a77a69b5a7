# Sprint 1: Sequence Diagrams (UML)

## Authentication Flow Sequence Diagram

```
@startuml Authentication Flow
actor User
participant "React Frontend\n(View)" as View
participant "Express Routes\n(Controller)" as Controller
participant "User Model" as Model
database MongoDB as DB
participant "JWT Service" as JWT
participant "Login History Service" as History

User -> View: Enter credentials
activate View

View -> Controller: POST /api/auth/login\n(email, password)
activate Controller

Controller -> Model: findOne({email})
activate Model

Model -> DB: Query user by email
activate DB
DB --> Model: Return user data
deactivate DB

alt User not found
    Model --> Controller: null
    Controller --> View: 401 Unauthorized\n(Invalid email)
    View --> User: Display error message
else User found
    Model --> Controller: User object
    
    Controller -> Model: comparePassword(password)
    Model --> Controller: isMatch (boolean)
    
    alt Invalid password
        Controller --> View: 401 Unauthorized\n(Invalid password)
        View --> User: Display error message
        
        Controller -> History: logFailedLogin(user, reason)
        activate History
        History -> DB: Save failed login record
        History --> Controller: Confirmation
        deactivate History
    else Valid password
        Controller -> JWT: generateToken(user)
        activate JWT
        JWT --> Controller: JWT token
        deactivate JWT
        
        Controller -> History: logSuccessfulLogin(user)
        activate History
        History -> DB: Save login record
        History --> Controller: Confirmation
        deactivate History
        
        Controller --> View: 200 OK with token and user data
        
        View -> View: Store token in localStorage
        View -> View: Redirect to appropriate dashboard
        View --> User: Display dashboard
    end
end

deactivate Model
deactivate Controller
deactivate View

@enduml
```

## User Registration Sequence Diagram

```
@startuml User Registration
actor Admin
participant "React Frontend\n(View)" as View
participant "Express Routes\n(Controller)" as Controller
participant "User Model" as Model
database MongoDB as DB
participant "Audit Service" as Audit

Admin -> View: Enter new user details
activate View

View -> View: Validate form inputs

View -> Controller: POST /api/admin/users\n(user data)
activate Controller

Controller -> Controller: Validate request data

Controller -> Model: findOne({email})
activate Model

Model -> DB: Query user by email
activate DB
DB --> Model: Return result
deactivate DB

alt Email already exists
    Model --> Controller: User object
    Controller --> View: 409 Conflict\n(Email already exists)
    View --> Admin: Display error message
else Email available
    Model --> Controller: null
    
    Controller -> Model: new User(userData)
    Model --> Controller: User instance
    
    Controller -> Model: save()
    Model -> DB: Insert new user
    activate DB
    DB --> Model: Confirmation
    deactivate DB
    Model --> Controller: Saved user object
    
    Controller -> Audit: logUserCreation(admin, newUser)
    activate Audit
    Audit -> DB: Save audit record
    Audit --> Controller: Confirmation
    deactivate Audit
    
    Controller --> View: 201 Created with user data
    View --> Admin: Display success message
end

deactivate Model
deactivate Controller
deactivate View

@enduml
```

## Password Reset Sequence Diagram

```
@startuml Password Reset
actor User
participant "React Frontend\n(View)" as View
participant "Express Routes\n(Controller)" as Controller
participant "User Model" as Model
database MongoDB as DB
participant "Email.js Service" as EmailJS
participant "Token Service" as Token
participant "Audit Service" as Audit

== Request Password Reset ==

User -> View: Request password reset
activate View

View -> View: Display reset form
User -> View: Enter email address
View -> Controller: POST /api/forgot-password\n(email)
activate Controller

Controller -> Model: findOne({email})
activate Model
Model -> DB: Query user by email
activate DB
DB --> Model: Return user data
deactivate DB

alt User not found
    Model --> Controller: null
    Controller --> View: 404 Not Found
    View --> User: Display error message
else User found
    Model --> Controller: User object
    
    Controller -> Token: generateResetToken(user)
    activate Token
    Token --> Controller: Reset token
    deactivate Token
    
    Controller -> EmailJS: sendResetEmail(email, token)
    activate EmailJS
    EmailJS --> Controller: Confirmation
    deactivate EmailJS
    
    Controller -> Audit: logPasswordResetRequest(user)
    activate Audit
    Audit -> DB: Save audit record
    Audit --> Controller: Confirmation
    deactivate Audit
    
    Controller --> View: 200 OK
    View --> User: Display confirmation message
end

deactivate Model
deactivate Controller
deactivate View

== Reset Password ==

User -> View: Click reset link in email
activate View

View -> View: Display new password form
User -> View: Enter new password
View -> Controller: POST /api/reset-password\n(token, newPassword)
activate Controller

Controller -> Token: verifyResetToken(token)
activate Token
Token --> Controller: userId or error
deactivate Token

alt Invalid or expired token
    Controller --> View: 400 Bad Request
    View --> User: Display error message
else Valid token
    Controller -> Model: findById(userId)
    activate Model
    Model -> DB: Query user by ID
    activate DB
    DB --> Model: Return user data
    deactivate DB
    Model --> Controller: User object
    
    Controller -> Model: setPassword(newPassword)
    Model -> Model: Hash password
    Model -> DB: Update user password
    activate DB
    DB --> Model: Confirmation
    deactivate DB
    Model --> Controller: Updated user
    
    Controller -> Audit: logPasswordReset(user)
    activate Audit
    Audit -> DB: Save audit record
    Audit --> Controller: Confirmation
    deactivate Audit
    
    Controller --> View: 200 OK
    View -> View: Redirect to login page
    View --> User: Display success message
end

deactivate Model
deactivate Controller
deactivate View

@enduml
```
