import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Toolbar,
  AppBar,
  Grid,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  CircularProgress,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Avatar,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Print as PrintIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  Code as CodeIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  EventNote as EventNoteIcon,
  Assignment as AssignmentIcon,
  AccessTime as AccessTimeIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  Description as DescriptionIcon,
  Translate as TranslateIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { getReports, deleteReport, getReportById, REPORT_TYPES } from '../Services/ReportService';
import DashboardLayout from '../components/layout/DashboardLayout';

// Report type icons mapping
const reportTypeIcons = {
  [REPORT_TYPES.NLP]: <AssessmentIcon />,
  [REPORT_TYPES.ATTENDANCE]: <AccessTimeIcon />,
  [REPORT_TYPES.USER]: <PeopleIcon />,
  [REPORT_TYPES.JOB]: <WorkIcon />,
  [REPORT_TYPES.LEAVE]: <EventNoteIcon />,
  [REPORT_TYPES.TASK]: <AssignmentIcon />
};

// Report type labels mapping
const reportTypeLabels = {
  [REPORT_TYPES.NLP]: 'NLP Analysis',
  [REPORT_TYPES.ATTENDANCE]: 'Attendance',
  [REPORT_TYPES.USER]: 'User',
  [REPORT_TYPES.JOB]: 'Job',
  [REPORT_TYPES.LEAVE]: 'Leave',
  [REPORT_TYPES.TASK]: 'Task'
};

const ReportsPage = () => {
  const navigate = useNavigate();
  const [reports, setReports] = useState([]);
  const [selectedReport, setSelectedReport] = useState(null);
  const [openReportDialog, setOpenReportDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredReports, setFilteredReports] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [loading, setLoading] = useState(true);

  // Load reports from backend API with localStorage fallback
  useEffect(() => {
    const loadReports = async () => {
      try {
        setLoading(true);

        // Get reports from backend API with localStorage fallback
        const allReports = await getReports();
        console.log('Loaded reports:', allReports);

        setReports(allReports);

        // Check if there's a reportId in the URL query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const reportId = urlParams.get('reportId');

        if (reportId) {
          console.log('Report ID found in URL:', reportId);

          // First, try to find the report in the loaded reports
          // Check both id and _id fields to be safe
          const reportToOpen = allReports.find(report =>
            report.id === reportId ||
            report._id === reportId ||
            String(report.id) === String(reportId) ||
            String(report._id) === String(reportId)
          );

          if (reportToOpen) {
            console.log('Found report in loaded reports:', reportToOpen);
            // Open the report dialog with this report
            handleViewReport(reportToOpen);
          } else {
            console.log('Report not found in loaded reports, fetching from backend...');
            // Try to fetch the specific report by ID
            try {
              const specificReport = await getReportById(reportId);
              console.log('Fetched specific report:', specificReport);

              if (specificReport) {
                setSelectedReport(specificReport);
                setOpenReportDialog(true);
              } else {
                console.error('Failed to fetch report with ID:', reportId);
                toast.error('Could not find the requested report');
              }
            } catch (fetchError) {
              console.error('Error fetching specific report:', fetchError);
              toast.error('Error loading the requested report');
            }
          }
        }
      } catch (error) {
        console.error('Error loading reports:', error);
        toast.error('Failed to load reports');
        setReports([]);
      } finally {
        setLoading(false);
      }
    };

    loadReports();
  }, []);

  // Function to refresh reports
  const handleRefreshReports = async () => {
    try {
      setLoading(true);
      toast.info('Refreshing reports...');

      // Get fresh reports from backend API
      const allReports = await getReports();
      console.log('Refreshed reports:', allReports);

      setReports(allReports);
      toast.success('Reports refreshed successfully');
    } catch (error) {
      console.error('Error refreshing reports:', error);
      toast.error('Failed to refresh reports');
    } finally {
      setLoading(false);
    }
  };

  // Filter reports based on search term and active tab
  useEffect(() => {
    // Only show NLP reports
    let filtered = reports.filter(report => report.reportType === REPORT_TYPES.NLP);

    // Filter by tab (report type)
    if (activeTab !== 'all') {
      filtered = filtered.filter(report => report.reportType === activeTab);
    }

    // Filter by search term
    if (searchTerm.trim() !== '') {
      filtered = filtered.filter(report =>
        report.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.reportData?.candidateName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.reportData?.position?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredReports(filtered);
  }, [searchTerm, reports, activeTab]);

  // Handle viewing a report
  const handleViewReport = async (report) => {
    try {
      console.log('Viewing report:', report);

      // Make sure we have a valid report ID
      const reportId = report.id || report._id;

      if (!reportId) {
        console.error('Report has no ID:', report);
        toast.error('Cannot view report: missing ID');
        return;
      }

      console.log('Fetching latest version of report with ID:', reportId);

      // Get the latest version of the report from the backend
      const latestReport = await getReportById(reportId);

      if (latestReport) {
        console.log('Got latest report:', latestReport);
        setSelectedReport(latestReport);
      } else {
        console.log('Could not get latest report, using provided report');
        // Fallback to the report from the list if backend fetch fails
        setSelectedReport(report);
      }

      setOpenReportDialog(true);
    } catch (error) {
      console.error('Error fetching report details:', error);
      // Fallback to the report from the list
      setSelectedReport(report);
      setOpenReportDialog(true);
    }
  };

  // Handle deleting a report
  const handleDeleteReport = async (reportId) => {
    if (window.confirm('Are you sure you want to delete this report?')) {
      try {
        // Use our service to delete the report (handles both backend and localStorage)
        const result = await deleteReport(reportId);

        // Update local state
        const updatedReports = reports.filter(report => report.id !== reportId);
        setReports(updatedReports);

        toast.success('Report deleted successfully');
      } catch (error) {
        console.error('Error deleting report:', error);
        toast.error('Failed to delete report');
      }
    }
  };

  // Handle printing a report
  const handlePrintReport = () => {
    window.print();
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Render report content based on report type
  const renderReportContent = (report) => {
    if (!report) return null;

    switch (report.reportType) {
      case REPORT_TYPES.NLP:
        return renderNlpReport(report.reportData);
      default:
        return (
          <Typography variant="body1" color="text.secondary">
            Only NLP reports are available. Other report types are not supported.
          </Typography>
        );
    }
  };

  // Render NLP report content
  const renderNlpReport = (data) => {
    if (!data) return null;

    const nlpResults = data.nlpResults || {};
    const matchScore = data.matchScore || 0;

    // Function to categorize skills
    const categorizeSkills = (skills) => {
      if (!skills || !Array.isArray(skills) || skills.length === 0) return {};

      const categories = {
        technical: [],
        soft: [],
        domain: [],
        other: []
      };

      skills.forEach(skill => {
        if (/\b(programming|software|code|develop|engineer|framework|language|database|cloud|api|web|mobile|app|frontend|backend|fullstack|devops|security)\b/i.test(skill)) {
          categories.technical.push(skill);
        } else if (/\b(communication|teamwork|leadership|management|organization|problem.solving|creativity|adaptability|collaboration|presentation|negotiation)\b/i.test(skill)) {
          categories.soft.push(skill);
        } else if (/\b(finance|marketing|sales|hr|accounting|legal|healthcare|education|retail|manufacturing|logistics|consulting)\b/i.test(skill)) {
          categories.domain.push(skill);
        } else {
          categories.other.push(skill);
        }
      });

      return categories;
    };

    const skillCategories = categorizeSkills(nlpResults.skills);

    return (
      <Box sx={{ maxWidth: '1000px', mx: 'auto' }}>
        {/* Top Summary Section - More compact */}
        <Paper elevation={1} sx={{ p: 1.5, mb: 1.5, borderRadius: 1 }}>
          <Grid container spacing={1} alignItems="center">
            {/* Candidate Info */}
            <Grid item xs={12} sm={8}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'primary.main',
                    mr: 1
                  }}
                >
                  {data.candidateName ? data.candidateName.charAt(0).toUpperCase() : 'C'}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold', lineHeight: 1.1 }}>
                    {data.candidateName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {data.position || 'Unknown Position'} | {new Date(data.timestamp).toLocaleDateString()}
                  </Typography>
                </Box>
              </Box>

              <Grid container spacing={1} sx={{ mt: 0.5 }}>
                <Grid item xs={6} sm={4}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    Email
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem', wordBreak: 'break-word' }}>
                    {nlpResults.email || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    Phone
                  </Typography>
                  <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                    {nlpResults.phone || 'N/A'}
                  </Typography>
                </Grid>
                {nlpResults.location && (
                  <Grid item xs={6} sm={4}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                      Location
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                      {nlpResults.location}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Grid>

            {/* Match Score */}
            <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: 'center' }}>
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                p: 1,
                borderRadius: 1,
                bgcolor: matchScore >= 70 ? 'success.light' :
                         matchScore >= 40 ? 'warning.light' : 'error.light',
              }}>
                <Typography variant="h4" sx={{
                  fontWeight: 'bold',
                  color: matchScore >= 70 ? 'success.dark' :
                         matchScore >= 40 ? 'warning.dark' : 'error.dark',
                }}>
                  {matchScore}%
                </Typography>
                <Typography variant="caption" sx={{ fontWeight: 'medium' }}>
                  {matchScore >= 80 ? 'Excellent Match' :
                   matchScore >= 60 ? 'Good Match' :
                   matchScore >= 40 ? 'Average Match' : 'Poor Match'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Tabbed Content for better organization */}
        <Tabs
          value={0}
          sx={{ mb: 1.5, borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Qualifications Match" />
          <Tab label="Skills" />
          <Tab label="Experience" />
          <Tab label="Education" />
        </Tabs>

        {/* Qualifications Analysis - More compact */}
        <Paper elevation={1} sx={{ p: 1.5, mb: 1.5, borderRadius: 1 }}>
          <Grid container spacing={1}>
            {/* Matched Skills */}
            <Grid item xs={12} md={6}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'success.main', mb: 0.5 }}>
                <CheckCircleIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                Matching Qualifications ({nlpResults.matchedSkills?.length || 0})
              </Typography>
              {nlpResults.matchedSkills && nlpResults.matchedSkills.length > 0 ? (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {nlpResults.matchedSkills.map((skill, index) => (
                    <Chip
                      key={index}
                      label={skill}
                      color="success"
                      variant="outlined"
                      size="small"
                      sx={{ mb: 0.5, fontSize: '0.7rem', height: 22 }}
                    />
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                  No matching qualifications found
                </Typography>
              )}
            </Grid>

            {/* Missing Skills */}
            <Grid item xs={12} md={6}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'error.main', mb: 0.5 }}>
                <CancelIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                Missing Qualifications ({nlpResults.missingSkills?.length || 0})
              </Typography>
              {nlpResults.missingSkills && nlpResults.missingSkills.length > 0 ? (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {nlpResults.missingSkills.map((skill, index) => (
                    <Chip
                      key={index}
                      label={skill}
                      color="error"
                      variant="outlined"
                      size="small"
                      sx={{ mb: 0.5, fontSize: '0.7rem', height: 22 }}
                    />
                  ))}
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                  No missing qualifications found
                </Typography>
              )}
            </Grid>
          </Grid>

          {/* Match Analysis - Condensed */}
          {nlpResults.matchAnalysis && (
            <Box sx={{ mt: 1, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
              <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                <strong>Analysis:</strong> {nlpResults.matchAnalysis}
              </Typography>
            </Box>
          )}
        </Paper>

        {/* Skills Section - Categorized and more compact */}
        <Paper elevation={1} sx={{ p: 1.5, mb: 1.5, borderRadius: 1 }}>
          <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
            <CodeIcon fontSize="small" sx={{ mr: 0.5 }} /> Skills & Expertise
          </Typography>

          <Grid container spacing={1}>
            {Object.entries(skillCategories).map(([category, skills]) =>
              skills.length > 0 && (
                <Grid item xs={12} sm={6} md={3} key={category}>
                  <Typography variant="caption" sx={{
                    fontWeight: 'bold',
                    color: category === 'technical' ? 'primary.main' :
                           category === 'soft' ? 'success.main' :
                           category === 'domain' ? 'warning.main' : 'text.secondary',
                    display: 'block',
                    mb: 0.5
                  }}>
                    {category.charAt(0).toUpperCase() + category.slice(1)} ({skills.length})
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {skills.map((skill, index) => (
                      <Chip
                        key={index}
                        label={skill}
                        size="small"
                        variant="outlined"
                        color={
                          category === 'technical' ? 'primary' :
                          category === 'soft' ? 'success' :
                          category === 'domain' ? 'warning' : 'default'
                        }
                        sx={{ mb: 0.5, fontSize: '0.7rem', height: 22 }}
                      />
                    ))}
                  </Box>
                </Grid>
              )
            )}
            {!nlpResults.skills || nlpResults.skills.length === 0 && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                  No skills detected
                </Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Education & Experience - Side by side and compact */}
        <Grid container spacing={1.5}>
          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1, height: '100%' }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <SchoolIcon fontSize="small" sx={{ mr: 0.5 }} /> Education
              </Typography>

              {nlpResults.education && nlpResults.education.length > 0 ? (
                <List dense disablePadding>
                  {nlpResults.education.map((edu, index) => (
                    <ListItem key={index} sx={{ py: 0.25, px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 24 }}>
                        <SchoolIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={<Typography variant="body2" sx={{ fontSize: '0.8rem' }}>{edu}</Typography>}
                        sx={{ m: 0 }}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                  No education details detected
                </Typography>
              )}
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={1} sx={{ p: 1.5, borderRadius: 1, height: '100%' }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
                <WorkIcon fontSize="small" sx={{ mr: 0.5 }} /> Experience
              </Typography>

              {nlpResults.experience && nlpResults.experience.length > 0 ? (
                <List dense disablePadding>
                  {nlpResults.experience.map((exp, index) => (
                    <ListItem key={index} sx={{ py: 0.25, px: 0 }}>
                      <ListItemIcon sx={{ minWidth: 24 }}>
                        <WorkIcon fontSize="small" color="primary" />
                      </ListItemIcon>
                      <ListItemText
                        primary={<Typography variant="body2" sx={{ fontSize: '0.8rem' }}>{exp}</Typography>}
                        sx={{ m: 0 }}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                  No experience details detected
                </Typography>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Languages Section - If available */}
        {nlpResults.languages && nlpResults.languages.length > 0 && (
          <Paper elevation={1} sx={{ p: 1.5, mt: 1.5, mb: 1.5, borderRadius: 1 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center' }}>
              <TranslateIcon fontSize="small" sx={{ mr: 0.5 }} /> Languages
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {nlpResults.languages.map((language, index) => (
                <Chip
                  key={index}
                  label={language}
                  color="info"
                  variant="outlined"
                  size="small"
                  sx={{ mb: 0.5, fontSize: '0.7rem', height: 22 }}
                />
              ))}
            </Box>
          </Paper>
        )}

        {/* Extracted Text Preview - Collapsible */}
        {nlpResults.extracted_text && (
          <Accordion sx={{ mt: 1.5, mb: 0, borderRadius: 1, '&:before': { display: 'none' } }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle2">
                <DescriptionIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                Extracted Text Preview
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{ p: 0 }}>
              <Box
                sx={{
                  p: 1,
                  bgcolor: 'grey.100',
                  maxHeight: '150px',
                  overflow: 'auto',
                  fontFamily: 'monospace',
                  fontSize: '0.7rem',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}
              >
                {nlpResults.extracted_text}
              </Box>
            </AccordionDetails>
          </Accordion>
        )}
      </Box>
    );
  };

  // Only NLP reports are supported

  // Menu items for the sidebar
  const menuItems = [
    {
      text: 'Dashboard',
      icon: <AssessmentIcon />,
      onClick: () => navigate('/hr-dashboard'),
      active: false,
    },
    {
      text: 'Reports',
      icon: <AssessmentIcon />,
      onClick: () => {},
      active: true,
    },
  ];

  return (
    <DashboardLayout
      title="Reports"
      menuItems={menuItems}
      userName="HR Manager"
      userRole="Human Resources"
      onLogout={() => {
        localStorage.removeItem('token');
        navigate('/');
      }}
    >
      <ToastContainer position="top-right" autoClose={3000} />

      <Box sx={{ p: 3, maxWidth: '1600px', margin: '0 auto' }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: '#2c3e50' }}>
          Reports
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          View and manage all generated reports.
        </Typography>

        {/* Tabs for filtering by report type */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab label="All Reports" value="all" />
            <Tab label="NLP Analysis" value={REPORT_TYPES.NLP} />
          </Tabs>
        </Paper>

        {/* Search and Filter Bar */}
        <Paper sx={{ p: 2, mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <TextField
              placeholder="Search reports..."
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ width: '300px' }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => toast.info('Advanced filtering coming soon!')}
            >
              Filter
            </Button>
          </Box>
          <Button
            variant="contained"
            color="primary"
            startIcon={<RefreshIcon />}
            onClick={handleRefreshReports}
            disabled={loading}
          >
            Refresh Reports
          </Button>
        </Paper>

        {/* Reports Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} sx={{ mb: 4 }}>
            <Table>
              <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Report Type</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Title</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Date Generated</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredReports.length > 0 ? (
                  filteredReports.map((report) => (
                    <TableRow key={report.id} hover>
                      <TableCell>
                        <Chip
                          icon={reportTypeIcons[report.reportType] || <AssessmentIcon />}
                          label={reportTypeLabels[report.reportType] || 'Unknown'}
                          color={report.reportType === REPORT_TYPES.NLP ? 'primary' :
                                 report.reportType === REPORT_TYPES.ATTENDANCE ? 'secondary' :
                                 report.reportType === REPORT_TYPES.USER ? 'success' :
                                 report.reportType === REPORT_TYPES.JOB ? 'info' :
                                 report.reportType === REPORT_TYPES.LEAVE ? 'warning' : 'default'}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{report.title || 'Untitled Report'}</TableCell>
                      <TableCell>{new Date(report.timestamp).toLocaleString()}</TableCell>
                      <TableCell>
                        <IconButton
                          color="primary"
                          onClick={() => handleViewReport(report)}
                          title="View Report"
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => handleDeleteReport(report.id)}
                          title="Delete Report"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        No reports found. Generate reports from different sections to see them here.
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>

      {/* Report Viewer Dialog */}
      <Dialog
        open={openReportDialog}
        onClose={() => setOpenReportDialog(false)}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            bgcolor: '#f8f9fa',
            height: '85vh',
            maxHeight: '85vh'
          }
        }}
        // Fix accessibility issues with focus management
        keepMounted
        container={() => document.getElementById('dialog-container') || document.body}
        disableScrollLock={false}
        aria-labelledby="report-dialog-title"
      >
        {selectedReport && (
          <>
            <AppBar position="static" sx={{ bgcolor: '#e53935' }}>
              <Toolbar variant="dense">
                <Typography id="report-dialog-title" variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
                  {selectedReport.title || `${reportTypeLabels[selectedReport.reportType] || 'Unknown'} Report`}
                </Typography>
                <Button
                  color="inherit"
                  startIcon={<PrintIcon fontSize="small" />}
                  onClick={handlePrintReport}
                  size="small"
                  sx={{ mr: 1 }}
                >
                  Print
                </Button>
                <IconButton
                  edge="end"
                  color="inherit"
                  onClick={() => setOpenReportDialog(false)}
                  aria-label="close"
                  size="small"
                >
                  <CloseIcon />
                </IconButton>
              </Toolbar>
            </AppBar>
            <DialogContent sx={{ p: 2 }}>
              {renderReportContent(selectedReport)}
            </DialogContent>
          </>
        )}
      </Dialog>
    </DashboardLayout>
  );
};

export default ReportsPage;
