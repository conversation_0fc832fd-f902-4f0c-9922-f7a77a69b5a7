.attendance-tracker {
  padding: 20px;
}

.status-card, .history-card {
  height: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border-radius: 12px !important;
  transition: transform 0.3s ease;
}

.status-card:hover, .history-card:hover {
  transform: translateY(-5px);
}

.status-card {
  background: linear-gradient(to bottom right, #ffffff, #f5f5f5) !important;
}

.history-card {
  background: linear-gradient(to bottom right, #ffffff, #f0f7ff) !important;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .attendance-tracker {
    padding: 10px;
  }
}

/* Animation for check-in/out buttons */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

button[color="primary"] {
  animation: pulse 2s infinite;
}

button[color="secondary"] {
  animation: pulse 2s infinite;
  animation-delay: 1s;
}
