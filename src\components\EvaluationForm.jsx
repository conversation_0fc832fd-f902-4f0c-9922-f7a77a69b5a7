import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Grid,
  Divider,
  Paper,
  Chip,
  Alert,
  AlertTitle,
  Tooltip,
  Avatar,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import Rating from '@mui/material/Rating';
import StarIcon from '@mui/icons-material/Star';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import AssignmentIcon from '@mui/icons-material/Assignment';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import Collapse from '@mui/material/Collapse';
import {
  showSuccessToast,
  showErrorToast,
  TOAST_CATEGORIES
} from '../Utils/toastUtils';
import api from '../Services/ApiService';

const EvaluationForm = ({ open, onClose, userId, userName, userJob, evaluationId = null }) => {
  const [loading, setLoading] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);
  const [formData, setFormData] = useState({
    evaluationPeriod: 'Monthly',
    performanceRating: 3,
    attitudeRating: 3,
    communicationRating: 3,
    teamworkRating: 3,
    initiativeRating: 3,
    strengths: '',
    areasForImprovement: '',
    goals: '',
    comments: '',
    status: 'Draft',
    visibleToUser: false
  });
  const [aiGenerated, setAiGenerated] = useState(false);
  const [insights, setInsights] = useState(null);
  const [insightsOpen, setInsightsOpen] = useState(true);

  // Fetch existing evaluation if evaluationId is provided
  useEffect(() => {
    if (evaluationId) {
      setLoading(true);
      api.get(`/hr/evaluations/${evaluationId}`)
        .then(response => {
          const evaluation = response.data;
          setFormData({
            evaluationPeriod: evaluation.evaluationPeriod,
            performanceRating: evaluation.performanceRating,
            attitudeRating: evaluation.attitudeRating,
            communicationRating: evaluation.communicationRating,
            teamworkRating: evaluation.teamworkRating,
            initiativeRating: evaluation.initiativeRating,
            strengths: evaluation.strengths || '',
            areasForImprovement: evaluation.areasForImprovement || '',
            goals: evaluation.goals || '',
            comments: evaluation.comments || '',
            status: evaluation.status,
            visibleToUser: evaluation.visibleToUser || false
          });

          // Check if this is an AI-generated evaluation
          if (evaluation.aiGenerated) {
            setAiGenerated(true);
            setInsights(evaluation.insights || null);
          }
        })
        .catch(error => {
          console.error('Error fetching evaluation:', error);
          showErrorToast('Failed to load evaluation data', TOAST_CATEGORIES.EVALUATION, 'failed');
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [evaluationId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRatingChange = (name, value) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const calculateOverallRating = () => {
    const { performanceRating, attitudeRating, communicationRating, teamworkRating, initiativeRating } = formData;
    const ratings = [performanceRating, attitudeRating, communicationRating, teamworkRating, initiativeRating];
    return (ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(1);
  };

  const handleGenerateAI = async () => {
    try {
      setAiGenerating(true);

      // Call the AI evaluation endpoint
      const response = await api.post(`/hr/evaluations/ai/${userId}`);

      // Get the generated evaluation
      const aiEvaluation = response.data.evaluation;

      // Update form data with AI-generated values
      setFormData({
        evaluationPeriod: aiEvaluation.evaluationPeriod,
        performanceRating: aiEvaluation.performanceRating,
        attitudeRating: aiEvaluation.attitudeRating,
        communicationRating: aiEvaluation.communicationRating,
        teamworkRating: aiEvaluation.teamworkRating,
        initiativeRating: aiEvaluation.initiativeRating,
        strengths: aiEvaluation.strengths || '',
        areasForImprovement: aiEvaluation.areasForImprovement || '',
        goals: aiEvaluation.goals || '',
        comments: aiEvaluation.comments || '',
        status: 'Draft'
      });

      // Set AI-generated flag and insights
      setAiGenerated(true);
      setInsights(aiEvaluation.insights || null);

      // Close this form and reopen with the new evaluation ID
      onClose(true); // Close with refresh flag

      // Show success message
      showSuccessToast('AI evaluation generated successfully', TOAST_CATEGORIES.EVALUATION, 'generated');

      // Open the evaluation in edit mode
      setTimeout(() => {
        // This will be handled by the parent component's refresh logic
      }, 500);
    } catch (error) {
      console.error('Error generating AI evaluation:', error);
      showErrorToast('Failed to generate AI evaluation: ' + (error.response?.data?.message || error.message), TOAST_CATEGORIES.EVALUATION, 'failed');
    } finally {
      setAiGenerating(false);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      // For AI-generated evaluations, ensure all manual ratings are provided
      if (aiGenerated) {
        if (!formData.attitudeRating || !formData.communicationRating ||
            !formData.teamworkRating || !formData.initiativeRating) {
          showErrorToast('Please provide ratings for all criteria (Attitude, Communication, Teamwork, Initiative)', TOAST_CATEGORIES.EVALUATION, 'failed');
          setLoading(false);
          return;
        }
      }

      // Calculate overall rating
      const validRatings = [
        formData.performanceRating,
        formData.attitudeRating || 0,
        formData.communicationRating || 0,
        formData.teamworkRating || 0,
        formData.initiativeRating || 0
      ].filter(rating => rating !== null && rating !== undefined);

      const overallRating = parseFloat((validRatings.reduce((a, b) => a + b, 0) / validRatings.length).toFixed(1));

      const payload = {
        ...formData,
        userId,
        overallRating,
        // Ensure all ratings have values
        attitudeRating: formData.attitudeRating || 0,
        communicationRating: formData.communicationRating || 0,
        teamworkRating: formData.teamworkRating || 0,
        initiativeRating: formData.initiativeRating || 0
      };

      let response;

      if (evaluationId) {
        // Update existing evaluation
        response = await api.put(`/hr/evaluations/${evaluationId}`, payload);
        showSuccessToast('Evaluation updated successfully', TOAST_CATEGORIES.EVALUATION, 'updated');
      } else {
        // Create new evaluation
        response = await api.post('/hr/evaluations', payload);
        showSuccessToast('Evaluation created successfully', TOAST_CATEGORIES.EVALUATION, 'created');
      }

      onClose(true); // Close with refresh flag
    } catch (error) {
      console.error('Error saving evaluation:', error);
      showErrorToast('Failed to save evaluation: ' + (error.response?.data?.message || error.message), TOAST_CATEGORIES.EVALUATION, 'failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => onClose(false)}
      maxWidth="md" // Medium size dialog
      fullWidth={true}
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 10,
          maxHeight: '90vh',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', py: 3, px: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
            {evaluationId ? 'Edit Employee Evaluation' : 'New Employee Evaluation'}
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              {aiGenerated && (
                <Chip
                  icon={<SmartToyIcon sx={{ fontSize: '1.2rem' }} />}
                  label="AI Generated"
                  color="secondary"
                  size="large"
                  sx={{ mr: 1, color: 'white', fontWeight: 'bold', fontSize: '1.1rem', height: '36px' }}
                />
              )}
              <Chip
                icon={formData.visibleToUser ? <VisibilityIcon /> : <VisibilityOffIcon />}
                label={formData.visibleToUser ? "Visible to User" : "Hidden from User"}
                color={formData.visibleToUser ? "success" : "default"}
                variant="outlined"
                size="medium"
                sx={{
                  color: 'white',
                  borderColor: 'white',
                  '& .MuiChip-icon': { color: 'white' },
                  transition: 'all 0.3s ease'
                }}
              />
            </Box>
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <Typography variant="body1">Loading evaluation data...</Typography>
          </Box>
        ) : (
          <Box
            sx={{
              overflow: 'auto', // Make content scrollable
              display: 'flex',
              flexDirection: 'column',
              width: '100%'
            }}
          >
            <Paper elevation={1} sx={{ p: 2, mb: 2, borderLeft: '3px solid #1976d2', bgcolor: 'rgba(25, 118, 210, 0.03)' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    width: 40,
                    height: 40,
                    bgcolor: 'primary.main',
                    fontSize: '1rem',
                    mr: 2
                  }}
                >
                  {userName?.charAt(0) || 'U'}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                    {userName}
                  </Typography>
                  {userJob && (
                    <Typography variant="body2" color="text.secondary">
                      {userJob}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Paper>

            {!evaluationId && (
              <Paper elevation={1} sx={{ p: 2, mb: 2, borderLeft: '3px solid #9c27b0', bgcolor: 'rgba(156, 39, 176, 0.03)' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <SmartToyIcon color="secondary" sx={{ fontSize: '1.2rem', mr: 1 }} />
                  <Typography variant="subtitle2" color="secondary.main" sx={{ fontWeight: 'bold' }}>
                    AI-Powered Evaluation
                  </Typography>
                </Box>

                <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>
                  Generate AI evaluation based on task performance and attendance data.
                </Typography>

                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<SmartToyIcon sx={{ fontSize: '1rem' }} />}
                  onClick={handleGenerateAI}
                  disabled={aiGenerating}
                  size="small"
                  sx={{ fontWeight: 'bold' }}
                >
                  {aiGenerating ? 'Generating...' : 'Generate AI Evaluation'}
                </Button>
              </Paper>
            )}

            {aiGenerated && insights && (
              <Paper elevation={3} sx={{ mb: 4, overflow: 'hidden', borderRadius: 2 }}>
                <Box
                  sx={{
                    bgcolor: 'secondary.main',
                    color: 'white',
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer'
                  }}
                  onClick={() => setInsightsOpen(!insightsOpen)}
                >
                  <SmartToyIcon sx={{ mr: 2, fontSize: '1.8rem' }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
                    AI Insights & Analysis
                  </Typography>
                  {insightsOpen ?
                    <ExpandLessIcon fontSize="large" /> :
                    <ExpandMoreIcon fontSize="large" />
                  }
                </Box>

                <Collapse in={insightsOpen}>
                  <Box sx={{ p: 3 }}>
                    {/* Task Performance Insights */}
                    <Paper
                      elevation={1}
                      sx={{
                        p: 3,
                        mb: 3,
                        borderLeft: '5px solid #1976d2',
                        bgcolor: 'rgba(25, 118, 210, 0.03)'
                      }}
                    >
                      <Typography variant="h6" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                        <AssignmentIcon sx={{ mr: 1 }} /> Task Performance Analysis
                      </Typography>

                      <Divider sx={{ mb: 2 }} />

                      {insights.tasks && Object.entries(insights.tasks).map(([criterion, insight]) => (
                        <Box key={criterion} sx={{ mb: 2 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: 'primary.dark', mb: 0.5 }}>
                            {criterion.charAt(0).toUpperCase() + criterion.slice(1)}:
                          </Typography>
                          <Typography variant="body1" sx={{ pl: 2, borderLeft: '3px solid', borderColor: 'primary.light', py: 0.5 }}>
                            {insight}
                          </Typography>
                        </Box>
                      ))}
                    </Paper>

                    {/* Attendance Insights */}
                    {insights.attendance && (
                      <Paper
                        elevation={1}
                        sx={{
                          p: 3,
                          borderLeft: '5px solid #4caf50',
                          bgcolor: 'rgba(76, 175, 80, 0.03)'
                        }}
                      >
                        <Typography variant="h6" gutterBottom sx={{ color: 'success.main', fontWeight: 'bold', display: 'flex', alignItems: 'center' }}>
                          <EventAvailableIcon sx={{ mr: 1 }} /> Attendance Analysis
                        </Typography>

                        <Divider sx={{ mb: 2 }} />

                        <Typography variant="body1" sx={{ pl: 2, borderLeft: '3px solid', borderColor: 'success.light', py: 0.5 }}>
                          {insights.attendance}
                        </Typography>
                      </Paper>
                    )}
                  </Box>
                </Collapse>
              </Paper>
            )}

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Evaluation Period</InputLabel>
                  <Select
                    name="evaluationPeriod"
                    value={formData.evaluationPeriod}
                    onChange={handleChange}
                    label="Evaluation Period"
                  >
                    <MenuItem value="Monthly">Monthly</MenuItem>
                    <MenuItem value="Quarterly">Quarterly</MenuItem>
                    <MenuItem value="Semi-Annual">Semi-Annual</MenuItem>
                    <MenuItem value="Annual">Annual</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        label="Status"
                      >
                        <MenuItem value="Draft">Draft</MenuItem>
                        <MenuItem value="Completed">Completed</MenuItem>
                        <MenuItem value="Acknowledged">Acknowledged</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth margin="normal">
                      <InputLabel>Visibility</InputLabel>
                      <Select
                        name="visibleToUser"
                        value={formData.visibleToUser}
                        onChange={handleChange}
                        label="Visibility"
                        sx={{
                          '& .MuiSelect-select': {
                            display: 'flex',
                            alignItems: 'center',
                          }
                        }}
                      >
                        <MenuItem value={true} sx={{ display: 'flex', alignItems: 'center' }}>
                          <VisibilityIcon fontSize="small" sx={{ mr: 1, color: 'success.main' }} />
                          Visible to User
                        </MenuItem>
                        <MenuItem value={false} sx={{ display: 'flex', alignItems: 'center' }}>
                          <VisibilityOffIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          Hidden from User
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                Performance Ratings
                {aiGenerated && (
                  <Chip
                    icon={<SmartToyIcon sx={{ fontSize: '0.8rem' }} />}
                    label="AI"
                    color="secondary"
                    size="small"
                    sx={{ ml: 1, height: 20, '& .MuiChip-label': { fontSize: '0.7rem', px: 1 } }}
                  />
                )}
              </Typography>
              <Divider sx={{ mb: 1 }} />

              {aiGenerated && (
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                  AI evaluation based on task performance and attendance metrics.
                </Typography>
              )}

              <Box sx={{ mb: 2 }}>
                <table style={{ width: '100%', borderCollapse: 'separate', borderSpacing: '0 10px' }}>
                  <thead>
                    <tr>
                      <th width="20%" style={{ textAlign: 'left', padding: '8px', fontSize: '0.8rem', fontWeight: 'bold' }}>Criteria</th>
                      <th width="10%" style={{ textAlign: 'center', padding: '8px', fontSize: '0.8rem', fontWeight: 'bold' }}>Score</th>
                      <th width="70%" style={{ textAlign: 'left', padding: '8px', fontSize: '0.8rem', fontWeight: 'bold' }}>Rating</th>
                    </tr>
                  </thead>
                  <tbody>
                    {/* Performance Row */}
                    <tr>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px 0 0 4px',
                        borderLeft: aiGenerated ? '4px solid #9c27b0' : '4px solid #1976d2',
                        fontWeight: 'bold'
                      }}>
                        Performance
                        {aiGenerated && (
                          <Tooltip title="AI-evaluated">
                            <SmartToyIcon sx={{ ml: 0.5, fontSize: '0.8rem', verticalAlign: 'middle' }} color="secondary" />
                          </Tooltip>
                        )}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#1976d2'
                      }}>
                        {formData.performanceRating?.toFixed(1) || '0.0'}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '0 4px 4px 0'
                      }}>
                        <Rating
                          name="performanceRating"
                          value={formData.performanceRating}
                          onChange={(e, newValue) => handleRatingChange('performanceRating', newValue)}
                          precision={0.5}
                          size="medium"
                          readOnly={aiGenerated}
                          sx={{ '& .MuiRating-icon': { fontSize: '1.1rem' } }}
                        />
                      </td>
                    </tr>

                    {/* Attitude Row */}
                    <tr>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px 0 0 4px',
                        borderLeft: '4px solid #f44336',
                        fontWeight: 'bold'
                      }}>
                        Attitude
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#f44336'
                      }}>
                        {formData.attitudeRating?.toFixed(1) || '0.0'}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '0 4px 4px 0'
                      }}>
                        <Rating
                          name="attitudeRating"
                          value={formData.attitudeRating || 0}
                          onChange={(e, newValue) => handleRatingChange('attitudeRating', newValue)}
                          precision={0.5}
                          size="medium"
                          sx={{ '& .MuiRating-icon': { fontSize: '1.1rem' } }}
                        />
                      </td>
                    </tr>

                    {/* Communication Row */}
                    <tr>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px 0 0 4px',
                        borderLeft: '4px solid #2196f3',
                        fontWeight: 'bold'
                      }}>
                        Communication
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#2196f3'
                      }}>
                        {formData.communicationRating?.toFixed(1) || '0.0'}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '0 4px 4px 0'
                      }}>
                        <Rating
                          name="communicationRating"
                          value={formData.communicationRating || 0}
                          onChange={(e, newValue) => handleRatingChange('communicationRating', newValue)}
                          precision={0.5}
                          size="medium"
                          sx={{ '& .MuiRating-icon': { fontSize: '1.1rem' } }}
                        />
                      </td>
                    </tr>

                    {/* Teamwork Row */}
                    <tr>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px 0 0 4px',
                        borderLeft: '4px solid #4caf50',
                        fontWeight: 'bold'
                      }}>
                        Teamwork
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#4caf50'
                      }}>
                        {formData.teamworkRating?.toFixed(1) || '0.0'}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '0 4px 4px 0'
                      }}>
                        <Rating
                          name="teamworkRating"
                          value={formData.teamworkRating || 0}
                          onChange={(e, newValue) => handleRatingChange('teamworkRating', newValue)}
                          precision={0.5}
                          size="medium"
                          sx={{ '& .MuiRating-icon': { fontSize: '1.1rem' } }}
                        />
                      </td>
                    </tr>

                    {/* Initiative Row */}
                    <tr>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px 0 0 4px',
                        borderLeft: '4px solid #ff9800',
                        fontWeight: 'bold'
                      }}>
                        Initiative
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#ff9800'
                      }}>
                        {formData.initiativeRating?.toFixed(1) || '0.0'}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '0 4px 4px 0'
                      }}>
                        <Rating
                          name="initiativeRating"
                          value={formData.initiativeRating || 0}
                          onChange={(e, newValue) => handleRatingChange('initiativeRating', newValue)}
                          precision={0.5}
                          size="medium"
                          sx={{ '& .MuiRating-icon': { fontSize: '1.1rem' } }}
                        />
                      </td>
                    </tr>

                    {/* Overall Row */}
                    <tr>
                      <td style={{
                        padding: '12px',
                        backgroundColor: 'rgba(156, 39, 176, 0.1)',
                        borderRadius: '4px 0 0 4px',
                        borderLeft: '4px solid #9c27b0',
                        fontWeight: 'bold'
                      }}>
                        Overall Rating
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: 'rgba(156, 39, 176, 0.1)',
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#9c27b0',
                        fontSize: '1rem'
                      }}>
                        {calculateOverallRating()}
                      </td>
                      <td style={{
                        padding: '12px',
                        backgroundColor: 'rgba(156, 39, 176, 0.1)',
                        borderRadius: '0 4px 4px 0'
                      }}>
                        <Rating
                          value={parseFloat(calculateOverallRating())}
                          readOnly
                          precision={0.1}
                          size="medium"
                          sx={{ '& .MuiRating-icon': { fontSize: '1.1rem' } }}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </Box>
            </Box>



            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 1 }}>
                Feedback & Comments
              </Typography>
              <Divider sx={{ mb: 1 }} />

              <Grid container spacing={1}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 1 }}>
                    <Box sx={{
                      bgcolor: 'success.light',
                      color: 'white',
                      py: 0.5,
                      px: 1,
                      borderTopLeftRadius: 4,
                      borderTopRightRadius: 4,
                      borderLeft: '3px solid #4caf50'
                    }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                        Strengths
                      </Typography>
                    </Box>
                    <TextField
                      fullWidth
                      placeholder="Enter employee's key strengths..."
                      name="strengths"
                      value={formData.strengths}
                      onChange={handleChange}
                      multiline
                      rows={2}
                      variant="outlined"
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderTopLeftRadius: 0,
                          fontSize: '0.75rem'
                        }
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 1 }}>
                    <Box sx={{
                      bgcolor: 'error.light',
                      color: 'white',
                      py: 0.5,
                      px: 1,
                      borderTopLeftRadius: 4,
                      borderTopRightRadius: 4,
                      borderLeft: '3px solid #f44336'
                    }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                        Areas for Improvement
                      </Typography>
                    </Box>
                    <TextField
                      fullWidth
                      placeholder="Enter areas where employee can improve..."
                      name="areasForImprovement"
                      value={formData.areasForImprovement}
                      onChange={handleChange}
                      multiline
                      rows={2}
                      variant="outlined"
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderTopLeftRadius: 0,
                          fontSize: '0.75rem'
                        }
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 1 }}>
                    <Box sx={{
                      bgcolor: 'primary.light',
                      color: 'white',
                      py: 0.5,
                      px: 1,
                      borderTopLeftRadius: 4,
                      borderTopRightRadius: 4,
                      borderLeft: '3px solid #2196f3'
                    }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                        Goals for Next Period
                      </Typography>
                    </Box>
                    <TextField
                      fullWidth
                      placeholder="Enter specific goals for the next evaluation period..."
                      name="goals"
                      value={formData.goals}
                      onChange={handleChange}
                      multiline
                      rows={2}
                      variant="outlined"
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderTopLeftRadius: 0,
                          fontSize: '0.75rem'
                        }
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 1 }}>
                    <Box sx={{
                      bgcolor: 'secondary.light',
                      color: 'white',
                      py: 0.5,
                      px: 1,
                      borderTopLeftRadius: 4,
                      borderTopRightRadius: 4,
                      borderLeft: '3px solid #9c27b0'
                    }}>
                      <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                        Additional Comments
                      </Typography>
                    </Box>
                    <TextField
                      fullWidth
                      placeholder="Enter any additional feedback or observations..."
                      name="comments"
                      value={formData.comments}
                      onChange={handleChange}
                      multiline
                      rows={2}
                      variant="outlined"
                      size="small"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderTopLeftRadius: 0,
                          fontSize: '0.75rem'
                        }
                      }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions
        sx={{
          p: 2,
          bgcolor: 'grey.100',
          borderTop: '1px solid',
          borderColor: 'divider',
          justifyContent: 'flex-end',
          width: '100%',
          boxSizing: 'border-box',
          maxWidth: '1400px', // Match content max width
          mx: 'auto' // Center horizontally
        }}
      >
        <Button
          onClick={() => onClose(false)}
          color="error"
          variant="outlined"
          size="small"
          sx={{ fontWeight: 'bold' }}
          startIcon={<CloseIcon fontSize="small" />}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          disabled={loading}
          size="small"
          sx={{ fontWeight: 'bold', ml: 1 }}
          startIcon={<SaveIcon fontSize="small" />}
        >
          {loading ? 'Saving...' : 'Save Evaluation'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EvaluationForm;
