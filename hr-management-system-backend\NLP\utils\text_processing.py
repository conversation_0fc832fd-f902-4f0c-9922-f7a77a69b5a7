import re
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# Ensure NLTK resources are downloaded
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('wordnet')

def extract_basic_info(text):
    """
    Extract basic information from text (name, email, phone)
    
    Args:
        text (str): The text to extract information from
        
    Returns:
        dict: Dictionary containing name, email, and phone
    """
    # Extract email
    email_pattern = r'\b[\w.-]+?@\w+?\.\w{2,4}\b'
    email = re.search(email_pattern, text)

    # Extract phone
    phone_pattern = r'(\+?\d[\d\s\-]{8,}\d)'
    phone = re.search(phone_pattern, text)

    # Extract name (first line or first sentence)
    lines = text.split('\n')
    name = lines[0].strip() if lines else "Unknown"

    # If name contains email or phone, try to get a better name
    if re.search(email_pattern, name) or re.search(phone_pattern, name):
        for line in lines[:10]:  # Check first 10 lines
            line = line.strip()
            if line and len(line) < 50 and not re.search(email_pattern, line) and not re.search(phone_pattern, line):
                name = line
                break

    return {
        "name": name,
        "email": email.group(0) if email else None,
        "phone": phone.group(0) if phone else None
    }

def extract_skills(text):
    """
    Extract skills from text
    
    Args:
        text (str): The text to extract skills from
        
    Returns:
        list: List of extracted skills
    """
    # Common technical skills
    common_skills = [
        'Python', 'Java', 'JavaScript', 'C++', 'C#', 'Ruby', 'PHP', 'Swift', 'Kotlin', 'Go',
        'HTML', 'CSS', 'SQL', 'NoSQL', 'MongoDB', 'MySQL', 'PostgreSQL', 'Oracle', 'SQLite',
        'React', 'Angular', 'Vue', 'Node.js', 'Express', 'Django', 'Flask', 'Spring', 'ASP.NET',
        'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'GitHub',
        'Linux', 'Unix', 'Windows', 'MacOS', 'Android', 'iOS',
        'Machine Learning', 'Deep Learning', 'AI', 'Data Science', 'Big Data', 'Data Analysis',
        'TensorFlow', 'PyTorch', 'Keras', 'Scikit-learn', 'Pandas', 'NumPy',
        'REST API', 'GraphQL', 'Microservices', 'Serverless',
        'Agile', 'Scrum', 'Kanban', 'Waterfall', 'DevOps', 'CI/CD',
        'Photoshop', 'Illustrator', 'Figma', 'Sketch',
        'Microsoft Office', 'Excel', 'Word', 'PowerPoint',
        'Project Management', 'Team Leadership', 'Communication', 'Problem Solving',
    ]
    
    # Extract skills
    skills = []
    
    # Find skills sections
    skill_sections = re.findall(r'(?i)(?:skills|technical skills|technologies|programming languages|tools|frameworks|platforms)(?:\s*:|\s*-|\s*•|\n)([\s\S]*?)(?:\n\n|\n\w+:|\Z)', text)
    
    for section in skill_sections:
        # Split by common delimiters
        for skill in re.split(r'[,;•\n]|\s{2,}', section):
            skill = skill.strip()
            if skill and len(skill) > 2 and len(skill) < 50:
                skills.append(skill)
    
    # Direct matching with common skills
    for skill in common_skills:
        if re.search(r'\b' + re.escape(skill) + r'\b', text, re.IGNORECASE):
            skills.append(skill)
    
    # Remove duplicates and sort
    skills = list(set(skills))
    skills.sort()
    
    return skills

def extract_education(text):
    """
    Extract education information from text
    
    Args:
        text (str): The text to extract education from
        
    Returns:
        list: List of education entries
    """
    education = []
    
    # Find education sections
    edu_sections = re.findall(r'(?i)(?:education|qualifications|academic background|degrees)(?:\s*:|\s*-|\s*•|\n)([\s\S]*?)(?:\n\n|\n\w+:|\Z)', text)
    
    for section in edu_sections:
        # Split by newlines to get individual entries
        for entry in section.split('\n'):
            entry = entry.strip()
            if entry and len(entry) > 10:
                education.append(entry)
    
    # Look for degree patterns
    degree_patterns = [
        r'\b(?:Bachelor|Master|PhD|Doctorate|BSc|BA|MSc|MA|MBA|BBA|B\.S\.|M\.S\.|B\.A\.|M\.A\.|Ph\.D\.)[^.]*?(?:degree|in|of)[^.]*?',
        r'\b(?:University|College|Institute|School)[^.]*?(?:\d{4}|\d{2})'
    ]
    
    for pattern in degree_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if match.strip() and len(match) < 200:
                education.append(match.strip())
    
    # Remove duplicates
    education = list(set(education))
    
    return education

def extract_experience(text):
    """
    Extract work experience information from text
    
    Args:
        text (str): The text to extract experience from
        
    Returns:
        list: List of experience entries
    """
    experience = []
    
    # Find experience sections
    exp_sections = re.findall(r'(?i)(?:experience|work experience|employment|work history|professional experience)(?:\s*:|\s*-|\s*•|\n)([\s\S]*?)(?:\n\n|\n\w+:|\Z)', text)
    
    for section in exp_sections:
        # Split by double newlines to get individual entries
        for entry in re.split(r'\n\s*\n', section):
            entry = entry.strip()
            if entry and len(entry) > 15 and len(entry) < 500:
                experience.append(entry)
    
    # Look for job title patterns
    job_patterns = [
        r'\b(?:Senior|Junior|Lead|Chief|Principal|Director|Manager|Engineer|Developer|Analyst|Consultant|Specialist|Coordinator|Administrator|Supervisor)[^.]*?(?:\d{4}|\d{2})[^.]*?',
        r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[^.]*?\d{4}[^.]*?(?:Present|Current|\d{4})[^.]*?'
    ]
    
    for pattern in job_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            if match.strip() and len(match) < 300:
                experience.append(match.strip())
    
    # Remove duplicates
    experience = list(set(experience))
    
    return experience
