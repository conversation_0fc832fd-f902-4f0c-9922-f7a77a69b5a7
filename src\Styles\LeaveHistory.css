.leave-history {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.leave-history .MuiTableContainer-root {
  margin-top: 16px;
  margin-bottom: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.leave-history .MuiTableHead-root {
  background-color: #f5f5f5;
  position: sticky;
  top: 0;
  z-index: 10;
}

.leave-history .MuiTableHead-root .MuiTableCell-root {
  font-weight: bold;
}

.leave-history .MuiTableRow-root:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.leave-history .MuiChip-root {
  font-weight: bold;
}

.leave-history .MuiChip-colorSuccess {
  background-color: #e6f7ed;
  color: #2e7d32;
}

.leave-history .MuiChip-colorError {
  background-color: #fdeded;
  color: #d32f2f;
}

.leave-history .MuiChip-colorWarning {
  background-color: #fff4e5;
  color: #ed6c02;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .leave-history .MuiTableCell-root {
    padding: 8px;
    font-size: 12px;
  }
  
  .leave-history .MuiChip-root {
    height: 24px;
    font-size: 11px;
  }
}
