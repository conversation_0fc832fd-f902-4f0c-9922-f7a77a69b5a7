# Sprint 1: Detailed Use Cases

## Use Case 1: User Authentication
**Primary Actor**: All Users (Admin, HR, Normal User)

**Preconditions**:
- User has a registered account in the system
- User has valid credentials

**Main Flow**:
1. User navigates to the login page
2. User enters email and password
3. System validates the credentials
4. System generates a JWT token
5. System stores the token in local storage
6. System redirects user to appropriate dashboard based on role

**Alternative Flows**:
- **Invalid Credentials**:
  1. System displays specific error message (wrong email or password)
  2. User can retry or use "Forgot Password" option
- **Account Locked**:
  1. System displays message that account is locked
  2. User must contact administrator or wait for lock to expire

**Postconditions**:
- User is authenticated and has access to role-specific features
- User's login is recorded in login history

## Use Case 2: User Registration (Admin Only)
**Primary Actor**: Admin

**Preconditions**:
- Admin is logged in with valid credentials

**Main Flow**:
1. Admin navigates to user management section
2. Admin selects "Add New User" option
3. <PERSON><PERSON> enters user details (name, email, role, department, job title)
4. <PERSON><PERSON> sets initial password for user
5. System validates input data
6. System creates new user account
7. System displays success message

**Alternative Flows**:
- **Validation Error**:
  1. System displays validation errors for incorrect inputs
  2. Admin corrects errors and resubmits
- **Email Already Exists**:
  1. System notifies admin that email is already registered
  2. Admin must use a different email

**Postconditions**:
- New user account is created in the system
- Admin can view the new user in the user list

## Use Case 3: Password Reset
**Primary Actor**: Any User

**Preconditions**:
- User has a registered email in the system

**Main Flow**:
1. User selects "Forgot Password" on login page
2. User enters registered email address
3. System validates email exists in database
4. System generates password reset token
5. System sends reset link via email.js
6. User receives email and clicks reset link
7. User enters new password and confirms
8. System validates password strength
9. System updates user's password
10. System redirects user to login page

**Alternative Flows**:
- **Email Not Found**:
  1. System displays message that email was not found
  2. User can try different email or contact administrator
- **Token Expired**:
  1. System notifies user that reset link has expired
  2. User must restart the password reset process

**Postconditions**:
- User's password is updated in the system
- Password reset event is logged in audit trail

## Use Case 4: User Profile Management
**Primary Actor**: Any User

**Preconditions**:
- User is logged in with valid credentials

**Main Flow**:
1. User navigates to profile section
2. System displays user's current profile information
3. User selects "Edit Profile" option
4. User updates profile information
5. System validates input data
6. System saves updated profile information
7. System displays success message

**Alternative Flows**:
- **Validation Error**:
  1. System displays validation errors for incorrect inputs
  2. User corrects errors and resubmits

**Postconditions**:
- User's profile information is updated in the system

## Use Case 5: User Management (Admin Only)
**Primary Actor**: Admin

**Preconditions**:
- Admin is logged in with valid credentials

**Main Flow**:
1. Admin navigates to user management section
2. System displays list of all users
3. Admin can filter users by role, department, or status
4. Admin selects a user to view details
5. Admin can edit user information, reset password, or deactivate account
6. System validates any changes
7. System saves updated user information
8. System displays success message

**Alternative Flows**:
- **Validation Error**:
  1. System displays validation errors for incorrect inputs
  2. Admin corrects errors and resubmits

**Postconditions**:
- User information is updated in the system
- Changes are logged in audit trail

## Use Case 6: Role-Based Dashboard Access
**Primary Actor**: Any User

**Preconditions**:
- User is logged in with valid credentials

**Main Flow**:
1. User successfully authenticates
2. System identifies user's role (admin, HR, or normal user)
3. System redirects user to appropriate dashboard:
   - Admin → Admin Dashboard
   - HR → HR Dashboard
   - Normal User → User Dashboard
4. System displays role-specific navigation options
5. User can access only authorized features

**Alternative Flows**:
- **Unauthorized Access Attempt**:
  1. User attempts to access unauthorized route
  2. System blocks access and displays error message
  3. System logs unauthorized access attempt

**Postconditions**:
- User can only access features appropriate for their role
- Unauthorized access attempts are prevented and logged
