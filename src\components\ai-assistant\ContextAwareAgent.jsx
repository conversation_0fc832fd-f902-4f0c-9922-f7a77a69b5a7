import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  IconButton,
  Chip,
  Fade,
  Slide,
  Collapse,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Divider
} from '@mui/material';
import {
  SmartToy as JarvisIcon,
  Close as CloseIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Lightbulb as SuggestionIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Schedule as TimeIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { motion, AnimatePresence } from 'framer-motion';
import contextApiService from '../../Services/ContextApiService';

// Styled components
const AgentContainer = styled(motion.div)(({ theme }) => ({
  position: 'fixed',
  bottom: theme.spacing(3),
  right: theme.spacing(3),
  zIndex: 1300,
  maxWidth: '400px',
  minWidth: '320px'
}));

const AgentPaper = styled(Paper)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  overflow: 'hidden',
  boxShadow: theme.shadows[12],
  border: `2px solid ${theme.palette.primary.main}`,
  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`
}));

const AgentHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: 'white',
  padding: theme.spacing(1.5, 2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  cursor: 'pointer'
}));

const SuggestionsList = styled(List)(({ theme }) => ({
  padding: theme.spacing(1),
  maxHeight: '300px',
  overflowY: 'auto'
}));

const SuggestionItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  marginBottom: theme.spacing(0.5),
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
    color: 'white',
    transform: 'translateX(4px)'
  }
}));

const ContextAwareAgent = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentContext, setCurrentContext] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [dismissedContexts, setDismissedContexts] = useState(new Set());
  
  const autoHideTimer = useRef(null);
  const contextCheckTimer = useRef(null);

  // Context detection and monitoring
  useEffect(() => {
    const detectContext = () => {
      const context = detectCurrentContext();
      if (context && !dismissedContexts.has(context.type)) {
        handleContextChange(context);
      }
    };

    // Check for context changes every 2 seconds
    contextCheckTimer.current = setInterval(detectContext, 2000);

    // Initial context detection
    detectContext();

    return () => {
      if (contextCheckTimer.current) {
        clearInterval(contextCheckTimer.current);
      }
      if (autoHideTimer.current) {
        clearTimeout(autoHideTimer.current);
      }
    };
  }, [dismissedContexts]);

  // Detect current context based on DOM elements and URL
  const detectCurrentContext = () => {
    const url = window.location.pathname;
    const activeElement = document.activeElement;
    
    // Check for leave request forms
    if (url.includes('dashboard') || url.includes('leave')) {
      const leaveForm = document.querySelector('form[data-form-type="leave"]') || 
                       document.querySelector('select[name*="leave"]') ||
                       document.querySelector('input[name*="leave"]') ||
                       document.querySelector('[data-testid*="leave"]');
      
      if (leaveForm) {
        return {
          type: 'leave_request_form',
          priority: 5,
          action: 'form_detected',
          data: { formType: 'leave_request' }
        };
      }
    }

    // Check for user management forms
    if (url.includes('user') || url.includes('admin')) {
      const userForm = document.querySelector('form[data-form-type="user"]') ||
                      document.querySelector('input[name*="user"]') ||
                      document.querySelector('input[name="name"]');
      
      if (userForm) {
        return {
          type: 'user_management_form',
          priority: 4,
          action: 'form_detected',
          data: { formType: 'user_management' }
        };
      }
    }

    // Check for task management
    if (url.includes('task')) {
      return {
        type: 'task_management',
        priority: 4,
        action: 'page_visit',
        data: { page: 'tasks' }
      };
    }

    // Check for dialog boxes
    const dialog = document.querySelector('[role="dialog"]');
    if (dialog && dialog.style.display !== 'none') {
      const dialogTitle = dialog.querySelector('[data-testid="dialog-title"]')?.textContent ||
                         dialog.querySelector('h2')?.textContent ||
                         dialog.querySelector('.MuiDialogTitle-root')?.textContent;
      
      if (dialogTitle?.toLowerCase().includes('leave')) {
        return {
          type: 'leave_request_dialog',
          priority: 5,
          action: 'dialog_open',
          data: { dialogType: 'leave_request' }
        };
      }
    }

    return null;
  };

  // Handle context changes
  const handleContextChange = async (context) => {
    if (!context || currentContext?.type === context.type) return;

    setCurrentContext(context);
    setIsLoading(true);
    setIsVisible(true);
    setIsExpanded(false);

    try {
      // Get context-specific suggestions
      const result = await contextApiService.processContext(
        context.type,
        context.action,
        context.data
      );

      if (result?.data?.suggestions) {
        setSuggestions(result.data.suggestions);
      } else {
        setSuggestions(getDefaultSuggestions(context.type));
      }
    } catch (error) {
      console.error('Error getting context suggestions:', error);
      setSuggestions(getDefaultSuggestions(context.type));
    } finally {
      setIsLoading(false);
    }

    // Auto-hide after 10 seconds if not expanded
    if (autoHideTimer.current) {
      clearTimeout(autoHideTimer.current);
    }
    autoHideTimer.current = setTimeout(() => {
      if (!isExpanded) {
        setIsVisible(false);
      }
    }, 10000);
  };

  // Get default suggestions based on context type
  const getDefaultSuggestions = (contextType) => {
    const defaultSuggestions = {
      leave_request_form: [
        '📊 Check your current leave balance before submitting',
        '📅 Ensure dates don\'t conflict with important deadlines',
        '✍️ Provide a clear and specific reason for your leave',
        '⏰ Submit at least 2 weeks in advance for better approval'
      ],
      leave_request_dialog: [
        '🏖️ Annual leave requires manager approval',
        '🤒 Sick leave may require medical documentation',
        '📋 Check company policy for leave requirements',
        '⚡ Emergency leave can be submitted immediately'
      ],
      user_management_form: [
        '🔐 Ensure strong password requirements',
        '📧 Verify email address format',
        '🏢 Select appropriate department and role',
        '📱 Add contact information for emergencies'
      ],
      task_management: [
        '📋 Set clear and measurable task objectives',
        '⏰ Assign realistic deadlines',
        '👥 Consider team workload when assigning',
        '🔄 Set up regular progress check-ins'
      ]
    };

    return defaultSuggestions[contextType] || [
      '💡 I\'m here to help with your current task',
      '🤖 Ask me anything about the HR system'
    ];
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    // You can implement specific actions based on the suggestion
    console.log('Suggestion clicked:', suggestion);
    // For now, just show a brief feedback
  };

  // Dismiss the agent for current context
  const handleDismiss = () => {
    if (currentContext) {
      setDismissedContexts(prev => new Set([...prev, currentContext.type]));
    }
    setIsVisible(false);
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (autoHideTimer.current) {
      clearTimeout(autoHideTimer.current);
    }
  };

  // Get context icon
  const getContextIcon = (contextType) => {
    const icons = {
      leave_request_form: '🏖️',
      leave_request_dialog: '📝',
      user_management_form: '👥',
      task_management: '📋'
    };
    return icons[contextType] || '🤖';
  };

  if (!isVisible || !currentContext) {
    return null;
  }

  return (
    <AnimatePresence>
      <AgentContainer
        initial={{ opacity: 0, y: 100, scale: 0.8 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: 100, scale: 0.8 }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 25
        }}
      >
        <AgentPaper elevation={12}>
          <AgentHeader onClick={toggleExpanded}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Avatar sx={{ width: 32, height: 32, bgcolor: 'white', color: 'primary.main' }}>
                <JarvisIcon fontSize="small" />
              </Avatar>
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', lineHeight: 1.2 }}>
                  {getContextIcon(currentContext.type)} Jarvis Assistant
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.9, lineHeight: 1 }}>
                  Context-Aware Suggestions
                </Typography>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton 
                size="small" 
                sx={{ color: 'white', mr: 1 }}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleExpanded();
                }}
              >
                {isExpanded ? <CollapseIcon /> : <ExpandIcon />}
              </IconButton>
              <IconButton 
                size="small" 
                sx={{ color: 'white' }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDismiss();
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </AgentHeader>

          <Collapse in={isExpanded}>
            <Box sx={{ p: 2 }}>
              <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                💡 Smart suggestions for your current task:
              </Typography>
              
              <SuggestionsList>
                {suggestions.map((suggestion, index) => (
                  <SuggestionItem
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                  >
                    <ListItemIcon>
                      <SuggestionIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={suggestion}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </SuggestionItem>
                ))}
              </SuggestionsList>

              <Divider sx={{ my: 1 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="caption" color="textSecondary">
                  Powered by Jarvis AI
                </Typography>
                <Button 
                  size="small" 
                  onClick={handleDismiss}
                  sx={{ textTransform: 'none' }}
                >
                  Don't show again
                </Button>
              </Box>
            </Box>
          </Collapse>
        </AgentPaper>
      </AgentContainer>
    </AnimatePresence>
  );
};

export default ContextAwareAgent;
