/**
 * Advanced Context Understanding and Memory Service
 * Maintains conversation context, user preferences, and intelligent memory
 */

class ContextAnalyzer {
  constructor() {
    this.initializeContextPatterns();
    this.initializeTopicModeling();
    this.initializeUserProfiles();
    this.conversationMemory = new Map();
  }

  /**
   * Initialize context patterns for better understanding
   */
  initializeContextPatterns() {
    this.contextPatterns = {
      // Temporal context
      temporal: {
        immediate: ['now', 'right now', 'currently', 'at the moment', 'today'],
        recent: ['recently', 'lately', 'just now', 'a moment ago', 'earlier'],
        future: ['later', 'tomorrow', 'next week', 'soon', 'in the future', 'upcoming'],
        past: ['yesterday', 'last week', 'before', 'previously', 'ago', 'earlier'],
        urgent: ['urgent', 'asap', 'immediately', 'emergency', 'critical', 'rush']
      },

      // Reference context
      reference: {
        self: ['my', 'mine', 'i', 'me', 'myself', 'personal'],
        others: ['his', 'her', 'their', 'team', 'colleague', 'manager', 'employee'],
        previous: ['that', 'this', 'it', 'the one', 'mentioned', 'discussed'],
        comparative: ['better', 'worse', 'different', 'same', 'similar', 'unlike']
      },

      // Emotional context
      emotional: {
        positive: ['happy', 'excited', 'pleased', 'satisfied', 'grateful', 'optimistic'],
        negative: ['upset', 'frustrated', 'disappointed', 'worried', 'concerned', 'angry'],
        neutral: ['okay', 'fine', 'normal', 'standard', 'regular', 'typical']
      },

      // Action context
      action: {
        request: ['please', 'can you', 'could you', 'would you', 'help me', 'i need'],
        information: ['what', 'how', 'when', 'where', 'why', 'which', 'tell me'],
        confirmation: ['yes', 'no', 'correct', 'right', 'wrong', 'exactly', 'confirm'],
        completion: ['done', 'finished', 'completed', 'ready', 'submitted', 'sent']
      }
    };
  }

  /**
   * Initialize topic modeling for conversation themes
   */
  initializeTopicModeling() {
    this.topicModels = {
      // HR domain topics
      leave_management: {
        keywords: ['leave', 'vacation', 'time off', 'holiday', 'absence', 'pto', 'sick leave', 'personal leave'],
        weight: 1.0,
        subtopics: {
          request: ['request', 'apply', 'submit', 'book'],
          balance: ['balance', 'remaining', 'available', 'left'],
          approval: ['approve', 'deny', 'pending', 'status'],
          policy: ['policy', 'rules', 'guidelines', 'procedure']
        }
      },

      task_management: {
        keywords: ['task', 'assignment', 'project', 'work', 'deadline', 'priority', 'todo'],
        weight: 1.0,
        subtopics: {
          creation: ['create', 'new', 'add', 'assign'],
          status: ['status', 'progress', 'update', 'complete'],
          deadline: ['deadline', 'due', 'urgent', 'schedule'],
          collaboration: ['team', 'collaborate', 'share', 'delegate']
        }
      },

      performance: {
        keywords: ['performance', 'evaluation', 'review', 'feedback', 'rating', 'assessment'],
        weight: 0.9,
        subtopics: {
          self_assessment: ['self', 'my performance', 'self evaluation'],
          goals: ['goals', 'objectives', 'targets', 'kpi'],
          improvement: ['improve', 'develop', 'growth', 'training'],
          recognition: ['recognition', 'achievement', 'success', 'accomplishment']
        }
      },

      attendance: {
        keywords: ['attendance', 'check in', 'check out', 'hours', 'schedule', 'timesheet'],
        weight: 0.8,
        subtopics: {
          tracking: ['track', 'record', 'log', 'monitor'],
          schedule: ['schedule', 'shift', 'hours', 'timing'],
          overtime: ['overtime', 'extra hours', 'additional time']
        }
      },

      benefits: {
        keywords: ['benefits', 'insurance', 'health', 'retirement', 'pension', 'compensation'],
        weight: 0.7,
        subtopics: {
          health: ['health', 'medical', 'dental', 'vision'],
          financial: ['salary', 'bonus', 'raise', 'compensation'],
          retirement: ['retirement', 'pension', '401k', 'savings']
        }
      },

      support: {
        keywords: ['help', 'support', 'assistance', 'problem', 'issue', 'question'],
        weight: 0.9,
        subtopics: {
          technical: ['technical', 'system', 'login', 'access'],
          policy: ['policy', 'procedure', 'guideline', 'rule'],
          personal: ['personal', 'confidential', 'private', 'sensitive']
        }
      }
    };
  }

  /**
   * Initialize user profile tracking
   */
  initializeUserProfiles() {
    this.userProfiles = new Map();
  }

  /**
   * Analyze conversation context
   * @param {string} message - Current message
   * @param {Array} conversationHistory - Previous messages
   * @param {Object} userContext - User information
   * @returns {Object} - Context analysis
   */
  analyzeContext(message, conversationHistory = [], userContext = {}) {
    const lowerMessage = message.toLowerCase();
    
    // Analyze temporal context
    const temporalContext = this.analyzeTemporalContext(lowerMessage);
    
    // Analyze reference context
    const referenceContext = this.analyzeReferenceContext(lowerMessage, conversationHistory);
    
    // Analyze topic context
    const topicContext = this.analyzeTopicContext(lowerMessage);
    
    // Analyze conversation flow
    const flowContext = this.analyzeConversationFlow(conversationHistory);
    
    // Analyze user intent progression
    const intentProgression = this.analyzeIntentProgression(conversationHistory);
    
    // Update user profile
    this.updateUserProfile(userContext.userId, {
      message,
      topics: topicContext.primaryTopics,
      timestamp: new Date()
    });

    return {
      temporal: temporalContext,
      reference: referenceContext,
      topic: topicContext,
      flow: flowContext,
      intentProgression: intentProgression,
      conversationStage: this.determineConversationStage(conversationHistory),
      userFamiliarity: this.assessUserFamiliarity(userContext.userId),
      contextualRelevance: this.calculateContextualRelevance(message, conversationHistory)
    };
  }

  /**
   * Analyze temporal context in message
   * @param {string} message - Message to analyze
   * @returns {Object} - Temporal context
   */
  analyzeTemporalContext(message) {
    const context = {
      timeframe: 'present',
      urgency: 'normal',
      indicators: []
    };

    Object.entries(this.contextPatterns.temporal).forEach(([timeframe, keywords]) => {
      keywords.forEach(keyword => {
        if (message.includes(keyword)) {
          context.timeframe = timeframe;
          context.indicators.push(keyword);
          
          if (timeframe === 'urgent') {
            context.urgency = 'high';
          }
        }
      });
    });

    return context;
  }

  /**
   * Analyze reference context (what the user is referring to)
   * @param {string} message - Current message
   * @param {Array} conversationHistory - Previous messages
   * @returns {Object} - Reference context
   */
  analyzeReferenceContext(message, conversationHistory) {
    const context = {
      references: [],
      pronounReferences: [],
      topicContinuation: false
    };

    // Check for pronouns and references
    const pronouns = ['it', 'this', 'that', 'they', 'them', 'these', 'those'];
    pronouns.forEach(pronoun => {
      if (message.toLowerCase().includes(pronoun)) {
        context.pronounReferences.push(pronoun);
        
        // Try to resolve reference from recent conversation
        if (conversationHistory.length > 0) {
          const lastMessage = conversationHistory[conversationHistory.length - 1];
          context.references.push({
            pronoun: pronoun,
            likelyReference: this.extractPossibleReference(lastMessage.content),
            confidence: 0.7
          });
        }
      }
    });

    // Check for topic continuation
    if (conversationHistory.length > 0) {
      const lastTopics = this.analyzeTopicContext(conversationHistory[conversationHistory.length - 1].content);
      const currentTopics = this.analyzeTopicContext(message);
      
      const commonTopics = lastTopics.primaryTopics.filter(topic => 
        currentTopics.primaryTopics.includes(topic)
      );
      
      if (commonTopics.length > 0) {
        context.topicContinuation = true;
        context.continuedTopics = commonTopics;
      }
    }

    return context;
  }

  /**
   * Analyze topic context using topic modeling
   * @param {string} message - Message to analyze
   * @returns {Object} - Topic analysis
   */
  analyzeTopicContext(message) {
    const lowerMessage = message.toLowerCase();
    const topicScores = {};
    const detectedSubtopics = {};

    // Calculate topic scores
    Object.entries(this.topicModels).forEach(([topic, model]) => {
      let score = 0;
      const matchedKeywords = [];

      // Check main keywords
      model.keywords.forEach(keyword => {
        if (lowerMessage.includes(keyword)) {
          score += model.weight;
          matchedKeywords.push(keyword);
        }
      });

      // Check subtopics
      Object.entries(model.subtopics).forEach(([subtopic, keywords]) => {
        keywords.forEach(keyword => {
          if (lowerMessage.includes(keyword)) {
            score += 0.5;
            if (!detectedSubtopics[topic]) {
              detectedSubtopics[topic] = [];
            }
            detectedSubtopics[topic].push(subtopic);
          }
        });
      });

      if (score > 0) {
        topicScores[topic] = {
          score: score,
          matchedKeywords: matchedKeywords,
          confidence: Math.min(score / 2, 1)
        };
      }
    });

    // Sort topics by score
    const sortedTopics = Object.entries(topicScores)
      .sort(([,a], [,b]) => b.score - a.score);

    return {
      primaryTopics: sortedTopics.slice(0, 3).map(([topic]) => topic),
      allTopics: topicScores,
      subtopics: detectedSubtopics,
      topicConfidence: sortedTopics.length > 0 ? sortedTopics[0][1].confidence : 0,
      isMultiTopic: sortedTopics.length > 1 && 
                   sortedTopics[1][1].score > sortedTopics[0][1].score * 0.7
    };
  }

  /**
   * Analyze conversation flow and patterns
   * @param {Array} conversationHistory - Previous messages
   * @returns {Object} - Flow analysis
   */
  analyzeConversationFlow(conversationHistory) {
    if (conversationHistory.length === 0) {
      return {
        stage: 'initiation',
        pattern: 'new_conversation',
        momentum: 'starting'
      };
    }

    const recentMessages = conversationHistory.slice(-5);
    const userMessages = recentMessages.filter(m => m.role === 'user');
    const assistantMessages = recentMessages.filter(m => m.role === 'assistant');

    // Analyze conversation patterns
    let pattern = 'balanced';
    if (userMessages.length > assistantMessages.length * 1.5) {
      pattern = 'user_driven';
    } else if (assistantMessages.length > userMessages.length * 1.5) {
      pattern = 'assistant_driven';
    }

    // Analyze momentum
    const messageIntervals = this.calculateMessageIntervals(recentMessages);
    const avgInterval = messageIntervals.reduce((sum, interval) => sum + interval, 0) / messageIntervals.length;
    
    let momentum = 'steady';
    if (avgInterval < 30000) { // Less than 30 seconds
      momentum = 'fast';
    } else if (avgInterval > 300000) { // More than 5 minutes
      momentum = 'slow';
    }

    return {
      stage: this.determineConversationStage(conversationHistory),
      pattern: pattern,
      momentum: momentum,
      messageCount: conversationHistory.length,
      averageInterval: avgInterval,
      isEngaged: momentum === 'fast' && pattern !== 'assistant_driven'
    };
  }

  /**
   * Analyze intent progression through conversation
   * @param {Array} conversationHistory - Previous messages
   * @returns {Object} - Intent progression analysis
   */
  analyzeIntentProgression(conversationHistory) {
    const userMessages = conversationHistory.filter(m => m.role === 'user');
    const intents = userMessages.map(m => m.metadata?.intent).filter(Boolean);

    if (intents.length === 0) {
      return {
        progression: 'none',
        currentPhase: 'exploration',
        intentStability: 0
      };
    }

    // Analyze intent changes
    const intentChanges = [];
    for (let i = 1; i < intents.length; i++) {
      if (intents[i] !== intents[i-1]) {
        intentChanges.push({
          from: intents[i-1],
          to: intents[i],
          position: i
        });
      }
    }

    // Calculate intent stability
    const uniqueIntents = [...new Set(intents)];
    const intentStability = 1 - (intentChanges.length / Math.max(intents.length - 1, 1));

    // Determine progression pattern
    let progression = 'stable';
    if (intentChanges.length > intents.length * 0.5) {
      progression = 'exploratory';
    } else if (intentChanges.length === 0) {
      progression = 'focused';
    }

    // Determine current phase
    let currentPhase = 'exploration';
    if (intentStability > 0.7) {
      currentPhase = 'execution';
    } else if (intentStability > 0.4) {
      currentPhase = 'clarification';
    }

    return {
      progression: progression,
      currentPhase: currentPhase,
      intentStability: intentStability,
      intentChanges: intentChanges,
      dominantIntent: this.findDominantIntent(intents),
      intentDiversity: uniqueIntents.length / intents.length
    };
  }

  /**
   * Update user profile with conversation data
   * @param {string} userId - User ID
   * @param {Object} data - Conversation data
   */
  updateUserProfile(userId, data) {
    if (!userId) return;

    if (!this.userProfiles.has(userId)) {
      this.userProfiles.set(userId, {
        conversationCount: 0,
        topicPreferences: {},
        communicationStyle: 'formal',
        averageSessionLength: 0,
        lastInteraction: null,
        preferredResponseLength: 'medium',
        emotionalTone: 'neutral'
      });
    }

    const profile = this.userProfiles.get(userId);
    
    // Update conversation count
    profile.conversationCount++;
    
    // Update topic preferences
    data.topics.forEach(topic => {
      profile.topicPreferences[topic] = (profile.topicPreferences[topic] || 0) + 1;
    });
    
    // Update last interaction
    profile.lastInteraction = data.timestamp;
    
    // Analyze communication style from message
    profile.communicationStyle = this.analyzeCommunicationStyle(data.message);
    
    this.userProfiles.set(userId, profile);
  }

  /**
   * Determine conversation stage
   * @param {Array} conversationHistory - Conversation history
   * @returns {string} - Conversation stage
   */
  determineConversationStage(conversationHistory) {
    const messageCount = conversationHistory.length;
    
    if (messageCount <= 2) return 'initiation';
    if (messageCount <= 6) return 'exploration';
    if (messageCount <= 12) return 'engagement';
    return 'deep_conversation';
  }

  /**
   * Assess user familiarity with the system
   * @param {string} userId - User ID
   * @returns {Object} - Familiarity assessment
   */
  assessUserFamiliarity(userId) {
    if (!userId || !this.userProfiles.has(userId)) {
      return {
        level: 'new',
        confidence: 1.0,
        conversationCount: 0
      };
    }

    const profile = this.userProfiles.get(userId);
    let level = 'new';
    
    if (profile.conversationCount > 20) {
      level = 'expert';
    } else if (profile.conversationCount > 5) {
      level = 'experienced';
    } else if (profile.conversationCount > 1) {
      level = 'familiar';
    }

    return {
      level: level,
      confidence: Math.min(profile.conversationCount / 10, 1),
      conversationCount: profile.conversationCount,
      topicExpertise: Object.keys(profile.topicPreferences)
    };
  }

  /**
   * Calculate contextual relevance score
   * @param {string} message - Current message
   * @param {Array} conversationHistory - Conversation history
   * @returns {number} - Relevance score (0-1)
   */
  calculateContextualRelevance(message, conversationHistory) {
    if (conversationHistory.length === 0) return 1.0;

    const currentTopics = this.analyzeTopicContext(message);
    const recentMessages = conversationHistory.slice(-3);
    
    let relevanceScore = 0;
    let totalWeight = 0;

    recentMessages.forEach((msg, index) => {
      const weight = (index + 1) / recentMessages.length; // More recent = higher weight
      const msgTopics = this.analyzeTopicContext(msg.content);
      
      const commonTopics = currentTopics.primaryTopics.filter(topic =>
        msgTopics.primaryTopics.includes(topic)
      );
      
      const topicRelevance = commonTopics.length / Math.max(currentTopics.primaryTopics.length, 1);
      relevanceScore += topicRelevance * weight;
      totalWeight += weight;
    });

    return totalWeight > 0 ? relevanceScore / totalWeight : 0.5;
  }

  /**
   * Helper methods
   */
  extractPossibleReference(text) {
    // Simple extraction of nouns that could be referenced
    const words = text.toLowerCase().split(/\s+/);
    const nouns = words.filter(word => 
      word.length > 3 && 
      !['the', 'and', 'but', 'for', 'are', 'with', 'this', 'that'].includes(word)
    );
    return nouns[nouns.length - 1] || 'unknown';
  }

  calculateMessageIntervals(messages) {
    const intervals = [];
    for (let i = 1; i < messages.length; i++) {
      const interval = new Date(messages[i].timestamp) - new Date(messages[i-1].timestamp);
      intervals.push(interval);
    }
    return intervals;
  }

  findDominantIntent(intents) {
    const intentCounts = {};
    intents.forEach(intent => {
      intentCounts[intent] = (intentCounts[intent] || 0) + 1;
    });
    
    return Object.entries(intentCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'unknown';
  }

  analyzeCommunicationStyle(message) {
    const formalIndicators = ['please', 'thank you', 'could you', 'would you'];
    const casualIndicators = ['hey', 'hi', 'thanks', 'ok', 'yeah'];
    
    const lowerMessage = message.toLowerCase();
    const formalCount = formalIndicators.filter(indicator => lowerMessage.includes(indicator)).length;
    const casualCount = casualIndicators.filter(indicator => lowerMessage.includes(indicator)).length;
    
    if (formalCount > casualCount) return 'formal';
    if (casualCount > formalCount) return 'casual';
    return 'neutral';
  }

  /**
   * Get conversation context summary
   * @param {string} userId - User ID
   * @param {Array} conversationHistory - Conversation history
   * @returns {Object} - Context summary
   */
  getContextSummary(userId, conversationHistory) {
    const userProfile = this.userProfiles.get(userId);
    const flowAnalysis = this.analyzeConversationFlow(conversationHistory);
    const familiarity = this.assessUserFamiliarity(userId);

    return {
      userProfile: userProfile,
      conversationFlow: flowAnalysis,
      userFamiliarity: familiarity,
      recommendations: this.generateContextualRecommendations(userProfile, flowAnalysis, familiarity)
    };
  }

  /**
   * Generate contextual recommendations for response adaptation
   * @param {Object} userProfile - User profile
   * @param {Object} flowAnalysis - Conversation flow analysis
   * @param {Object} familiarity - User familiarity assessment
   * @returns {Object} - Recommendations
   */
  generateContextualRecommendations(userProfile, flowAnalysis, familiarity) {
    const recommendations = {
      responseLength: 'medium',
      tone: 'professional',
      detailLevel: 'standard',
      suggestions: true,
      examples: false
    };

    // Adjust based on familiarity
    if (familiarity.level === 'new') {
      recommendations.responseLength = 'long';
      recommendations.detailLevel = 'detailed';
      recommendations.examples = true;
    } else if (familiarity.level === 'expert') {
      recommendations.responseLength = 'short';
      recommendations.detailLevel = 'concise';
    }

    // Adjust based on conversation flow
    if (flowAnalysis.momentum === 'fast') {
      recommendations.responseLength = 'short';
      recommendations.suggestions = false;
    } else if (flowAnalysis.momentum === 'slow') {
      recommendations.responseLength = 'long';
      recommendations.examples = true;
    }

    // Adjust based on user profile
    if (userProfile?.communicationStyle === 'casual') {
      recommendations.tone = 'friendly';
    } else if (userProfile?.communicationStyle === 'formal') {
      recommendations.tone = 'professional';
    }

    return recommendations;
  }
}

// Singleton instance
const contextAnalyzer = new ContextAnalyzer();

module.exports = contextAnalyzer;
