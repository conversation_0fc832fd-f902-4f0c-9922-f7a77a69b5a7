const mongoose = require('mongoose');

const loginHistorySchema = new mongoose.Schema({
  // User who logged in
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // User information at the time of login
  userInfo: {
    name: String,
    email: String,
    role: String
  },
  
  // Login status
  status: {
    type: String,
    required: true,
    enum: ['SUCCESS', 'FAILED'],
    default: 'SUCCESS'
  },
  
  // Failure reason (if applicable)
  failureReason: {
    type: String,
    enum: [
      'INVALID_CREDENTIALS',
      'ACCOUNT_LOCKED',
      'ACCOUNT_DISABLED',
      'ACCOUNT_EXPIRED',
      'INVALID_TOKEN',
      'SESSION_EXPIRED',
      null
    ],
    default: null
  },
  
  // IP address of the user
  ipAddress: {
    type: String,
    required: true
  },
  
  // Location information (if available)
  location: {
    country: String,
    region: String,
    city: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Device information
  device: {
    type: String,
    default: 'Unknown'
  },
  
  // Browser information
  browser: {
    name: String,
    version: String
  },
  
  // Operating system information
  operatingSystem: {
    name: String,
    version: String
  },
  
  // User agent string
  userAgent: {
    type: String,
    default: null
  },
  
  // Login timestamp
  loginTime: {
    type: Date,
    default: Date.now
  },
  
  // Logout timestamp (if available)
  logoutTime: {
    type: Date,
    default: null
  },
  
  // Session duration in seconds (calculated on logout)
  sessionDuration: {
    type: Number,
    default: null
  }
}, { timestamps: true });

// Create indexes for better query performance
loginHistorySchema.index({ userId: 1 });
loginHistorySchema.index({ status: 1 });
loginHistorySchema.index({ loginTime: -1 });
loginHistorySchema.index({ ipAddress: 1 });

// Method to record logout and calculate session duration
loginHistorySchema.methods.recordLogout = function() {
  this.logoutTime = new Date();
  
  if (this.loginTime) {
    const loginTimeMs = new Date(this.loginTime).getTime();
    const logoutTimeMs = new Date(this.logoutTime).getTime();
    this.sessionDuration = Math.floor((logoutTimeMs - loginTimeMs) / 1000); // Duration in seconds
  }
  
  return this.save();
};

const LoginHistory = mongoose.model('LoginHistory', loginHistorySchema);

module.exports = LoginHistory;
