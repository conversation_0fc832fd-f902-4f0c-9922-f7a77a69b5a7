# parser.py
import sys
import json
import re
import pdfplumber
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer
import spacy
from collections import Counter
import os
from paddleocr import PaddleOCR
from pdf2image import convert_from_path
from PIL import Image
import io
import tempfile

# Download all required NLTK resources
def download_nltk_resources():
    resources = [
        'punkt', 'stopwords', 'wordnet', 'averaged_perceptron_tagger',
        'maxent_ne_chunker', 'words'
    ]

    for resource in resources:
        try:
            if resource in ['punkt', 'averaged_perceptron_tagger']:
                nltk.data.find(f'tokenizers/{resource}')
            elif resource in ['stopwords', 'wordnet', 'words']:
                nltk.data.find(f'corpora/{resource}')
            elif resource == 'maxent_ne_chunker':
                nltk.data.find(f'chunkers/{resource}')
            print(f"Resource {resource} already downloaded", file=sys.stderr)
        except LookupError:
            print(f"Downloading resource {resource}", file=sys.stderr)
            nltk.download(resource, quiet=True)
            print(f"Downloaded resource {resource}", file=sys.stderr)

# Download all required NLTK resources
download_nltk_resources()

# Initialize spaCy
try:
    nlp = spacy.load("en_core_web_sm")
    print("Loaded spaCy model", file=sys.stderr)
except:
    print("Downloading spaCy model", file=sys.stderr)
    os.system("python -m spacy download en_core_web_sm")
    nlp = spacy.load("en_core_web_sm")
    print("Downloaded and loaded spaCy model", file=sys.stderr)

def extract_text_from_pdf(file_path):
    try:
        print(f"Attempting to open PDF file: {file_path}", file=sys.stderr)

        # Check if file exists
        import os
        if not os.path.exists(file_path):
            print(f"File does not exist: {file_path}", file=sys.stderr)
            return f"File not found: {file_path}"

        # Check if file is accessible
        if not os.access(file_path, os.R_OK):
            print(f"File is not readable: {file_path}", file=sys.stderr)
            return f"File not readable: {file_path}"

        # First try: Extract text using pdfplumber
        try:
            with pdfplumber.open(file_path) as pdf:
                print(f"PDF opened successfully with pdfplumber, pages: {len(pdf.pages)}", file=sys.stderr)

                # Extract text from each page
                text = ""
                for i, page in enumerate(pdf.pages):
                    if page is None:
                        print(f"Page {i} is None", file=sys.stderr)
                        continue

                    page_text = page.extract_text()
                    if page_text:
                        text += page_text
                    else:
                        print(f"No text extracted from page {i} with pdfplumber", file=sys.stderr)

                print(f"Extracted {len(text)} characters of text with pdfplumber", file=sys.stderr)

                # If we got text, return it
                if text.strip():
                    return text
                else:
                    print("No text extracted with pdfplumber, trying PaddleOCR...", file=sys.stderr)
        except Exception as e:
            print(f"Error with pdfplumber: {str(e)}, trying PaddleOCR...", file=sys.stderr)

        # Second try: Use PaddleOCR
        try:
            print("Initializing PaddleOCR...", file=sys.stderr)
            # Initialize PaddleOCR with English language
            ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)

            print("Converting PDF to images for OCR...", file=sys.stderr)

            # Create a temporary directory for the images
            with tempfile.TemporaryDirectory() as temp_dir:
                # Convert PDF to images
                images = convert_from_path(file_path, output_folder=temp_dir)
                print(f"Converted PDF to {len(images)} images", file=sys.stderr)

                # Extract text from each image using PaddleOCR
                ocr_text = ""
                for i, image in enumerate(images):
                    print(f"Processing image {i+1}/{len(images)} with PaddleOCR...", file=sys.stderr)

                    # Save the image to a temporary file
                    img_path = os.path.join(temp_dir, f"page_{i}.jpg")
                    image.save(img_path)

                    # Run OCR on the image
                    result = ocr.ocr(img_path, cls=True)

                    # Extract text from the result
                    page_text = ""
                    if result:
                        for line in result[0]:
                            if line and len(line) >= 1:
                                # PaddleOCR returns a list of detected text regions
                                # Each region contains the bounding box and the text
                                if isinstance(line, list) and len(line) > 1:
                                    # Extract the text from the region
                                    text_info = line[1]
                                    if isinstance(text_info, tuple) and len(text_info) > 0:
                                        page_text += text_info[0] + " "

                    ocr_text += page_text + "\n\n"

                print(f"Extracted {len(ocr_text)} characters of text with PaddleOCR", file=sys.stderr)

                if ocr_text.strip():
                    return ocr_text
                else:
                    print("No text extracted with PaddleOCR either", file=sys.stderr)
                    return "No text could be extracted from the PDF using OCR"
        except Exception as e:
            print(f"Error with PaddleOCR: {str(e)}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            return f"Error processing PDF with PaddleOCR: {str(e)}"

    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)
        return f"Error processing PDF: {str(e)}"

def extract_basic_info(text):
    # Extract email
    email_pattern = r'\b[\w.-]+?@\w+?\.\w{2,4}\b'
    email = re.search(email_pattern, text)

    # Extract phone
    phone_pattern = r'(\+?\d[\d\s\-]{8,}\d)'
    phone = re.search(phone_pattern, text)

    # Extract name (first line or first sentence)
    lines = text.split('\n')
    name = lines[0].strip() if lines else ""

    # If name contains email or phone, try to get a better name
    if re.search(email_pattern, name) or re.search(phone_pattern, name):
        # Try to find a name using NER
        doc = nlp(text[:500])  # Process just the beginning of the document
        person_entities = [ent.text for ent in doc.ents if ent.label_ == "PERSON"]
        if person_entities:
            name = person_entities[0]

    return {
        "name": name,
        "email": email.group(0) if email else None,
        "phone": phone.group(0) if phone else None,
    }

def extract_skills(text):
    # Common skill keywords
    skill_keywords = [
        "python", "java", "javascript", "html", "css", "react", "angular", "vue",
        "node.js", "express", "django", "flask", "spring", "hibernate", "sql",
        "mysql", "postgresql", "mongodb", "nosql", "aws", "azure", "gcp",
        "docker", "kubernetes", "jenkins", "git", "github", "gitlab", "ci/cd",
        "agile", "scrum", "kanban", "jira", "confluence", "leadership", "teamwork",
        "communication", "problem-solving", "critical thinking", "time management",
        "project management", "product management", "ux", "ui", "design", "figma",
        "sketch", "adobe", "photoshop", "illustrator", "indesign", "premiere",
        "after effects", "final cut", "excel", "word", "powerpoint", "outlook",
        "analytics", "data analysis", "data science", "machine learning", "ai",
        "deep learning", "nlp", "computer vision", "statistics", "mathematics",
        "r", "matlab", "tableau", "power bi", "looker", "data studio", "seo",
        "sem", "google analytics", "google ads", "facebook ads", "social media",
        "content marketing", "email marketing", "crm", "salesforce", "hubspot",
        "marketing automation", "customer service", "technical support", "helpdesk",
        "networking", "security", "cybersecurity", "penetration testing", "ethical hacking",
        "linux", "unix", "windows", "macos", "ios", "android", "mobile development",
        "web development", "full stack", "frontend", "backend", "devops", "sre",
        "system administration", "network administration", "database administration",
        "cloud computing", "serverless", "microservices", "api", "rest", "graphql",
        "soap", "json", "xml", "yaml", "markdown", "latex", "documentation",
        "testing", "qa", "quality assurance", "unit testing", "integration testing",
        "e2e testing", "selenium", "cypress", "jest", "mocha", "chai", "junit",
        "pytest", "phpunit", "tdd", "bdd", "continuous integration", "continuous deployment",
        "continuous delivery", "version control", "code review", "pair programming",
        "mentoring", "coaching", "training", "public speaking", "writing", "blogging",
        "technical writing", "research", "analysis", "strategy", "planning", "execution",
        "monitoring", "evaluation", "reporting", "budgeting", "forecasting", "negotiation",
        "sales", "account management", "business development", "partnership", "alliance",
        "vendor management", "procurement", "supply chain", "logistics", "inventory",
        "warehouse", "shipping", "receiving", "customer experience", "user experience",
        "user interface", "accessibility", "localization", "internationalization",
        "multilingual", "translation", "interpretation", "cultural awareness",
        "diversity", "inclusion", "equity", "compliance", "regulation", "legal",
        "policy", "governance", "risk management", "audit", "control", "security",
        "privacy", "data protection", "gdpr", "ccpa", "hipaa", "pci", "iso",
        "certification", "accreditation", "licensing", "credential", "degree",
        "diploma", "certificate", "training", "workshop", "seminar", "conference",
        "webinar", "podcast", "video", "audio", "multimedia", "streaming", "broadcast",
        "live", "recorded", "editing", "production", "post-production", "animation",
        "3d", "ar", "vr", "xr", "game development", "unity", "unreal", "godot",
        "c++", "c#", "objective-c", "swift", "kotlin", "dart", "flutter", "react native",
        "xamarin", "ionic", "cordova", "phonegap", "capacitor", "electron", "pwa",
        "spa", "ssr", "ssg", "jamstack", "wordpress", "drupal", "joomla", "magento",
        "shopify", "woocommerce", "prestashop", "opencart", "ecommerce", "payment",
        "stripe", "paypal", "square", "braintree", "authorize.net", "checkout",
        "cart", "inventory", "product", "service", "subscription", "recurring",
        "billing", "invoice", "receipt", "accounting", "bookkeeping", "tax",
        "finance", "investment", "banking", "loan", "mortgage", "insurance",
        "retirement", "pension", "401k", "ira", "estate", "will", "trust",
        "healthcare", "medical", "dental", "vision", "pharmacy", "prescription",
        "diagnosis", "treatment", "therapy", "rehabilitation", "wellness", "fitness",
        "nutrition", "diet", "exercise", "yoga", "meditation", "mindfulness",
        "stress management", "work-life balance", "remote work", "telecommuting",
        "virtual", "hybrid", "office", "workspace", "ergonomic", "safety", "health",
        "environment", "sustainability", "green", "eco-friendly", "carbon neutral",
        "renewable", "recycling", "waste management", "conservation", "preservation",
        "restoration", "revitalization", "urban planning", "architecture", "interior design",
        "landscape", "construction", "engineering", "mechanical", "electrical", "civil",
        "structural", "chemical", "biomedical", "aerospace", "automotive", "manufacturing",
        "production", "assembly", "fabrication", "machining", "welding", "cnc",
        "3d printing", "additive manufacturing", "robotics", "automation", "iot",
        "embedded systems", "firmware", "hardware", "pcb", "circuit", "electronics",
        "telecommunications", "networking", "routing", "switching", "firewall", "vpn",
        "dns", "dhcp", "tcp/ip", "http", "https", "ftp", "ssh", "ssl", "tls",
        "encryption", "decryption", "hashing", "authentication", "authorization",
        "identity management", "single sign-on", "oauth", "openid", "saml",
        "ldap", "active directory", "kerberos", "biometric", "mfa", "2fa",
        "password management", "credential management", "secret management",
        "key management", "certificate management", "pki", "ca", "blockchain",
        "cryptocurrency", "bitcoin", "ethereum", "smart contract", "nft", "defi",
        "web3", "dao", "consensus", "mining", "staking", "wallet", "exchange",
        "trading", "investing", "portfolio", "asset", "liability", "equity",
        "revenue", "expense", "profit", "loss", "balance sheet", "income statement",
        "cash flow", "depreciation", "amortization", "valuation", "appraisal",
        "assessment", "evaluation", "measurement", "metrics", "kpi", "okr",
        "goal", "objective", "target", "milestone", "roadmap", "timeline",
        "schedule", "calendar", "appointment", "meeting", "event", "conference",
        "convention", "exhibition", "trade show", "fair", "festival", "ceremony",
        "celebration", "party", "gathering", "social", "networking", "community",
        "group", "team", "department", "division", "unit", "organization",
        "company", "corporation", "enterprise", "startup", "small business",
        "medium business", "large business", "multinational", "global", "local",
        "regional", "national", "international", "worldwide", "universal"
    ]

    # Preprocess text
    text_lower = text.lower()
    words = word_tokenize(text_lower)
    stop_words = set(stopwords.words('english'))
    lemmatizer = WordNetLemmatizer()

    # Filter out stopwords and lemmatize
    filtered_words = [lemmatizer.lemmatize(word) for word in words if word.isalpha() and word not in stop_words]

    # Find skills in text
    skills = []
    for skill in skill_keywords:
        if skill in text_lower or skill in filtered_words:
            skills.append(skill)

    # Use NLP to extract additional skills
    doc = nlp(text)
    noun_phrases = [chunk.text.lower() for chunk in doc.noun_chunks]

    # Add multi-word skills
    for phrase in noun_phrases:
        for skill in skill_keywords:
            if skill in phrase and skill not in skills:
                skills.append(skill)

    return list(set(skills))  # Remove duplicates

def extract_education(text):
    # Common education keywords
    education_keywords = [
        "bachelor", "master", "phd", "doctorate", "degree", "diploma", "certificate",
        "university", "college", "school", "institute", "academy", "education",
        "major", "minor", "concentration", "specialization", "thesis", "dissertation",
        "graduated", "graduation", "alumni", "student", "gpa", "honors", "dean's list",
        "scholarship", "fellowship", "grant", "award", "recognition", "achievement"
    ]

    # Extract sentences containing education keywords
    sentences = sent_tokenize(text)
    education_sentences = []

    for sentence in sentences:
        sentence_lower = sentence.lower()
        if any(keyword in sentence_lower for keyword in education_keywords):
            education_sentences.append(sentence.strip())

    # If no sentences found, try to extract lines containing education keywords
    if not education_sentences:
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in education_keywords):
                education_sentences.append(line.strip())

    return education_sentences

def extract_experience(text):
    # Common experience keywords
    experience_keywords = [
        "experience", "work", "job", "career", "position", "role", "title",
        "company", "organization", "employer", "employment", "worked", "working",
        "responsible", "responsibility", "duty", "task", "project", "achievement",
        "accomplishment", "success", "led", "managed", "supervised", "coordinated",
        "developed", "implemented", "created", "designed", "built", "launched",
        "improved", "enhanced", "optimized", "increased", "decreased", "reduced",
        "saved", "generated", "produced", "delivered", "completed", "achieved"
    ]

    # Extract sentences containing experience keywords
    sentences = sent_tokenize(text)
    experience_sentences = []

    for sentence in sentences:
        sentence_lower = sentence.lower()
        if any(keyword in sentence_lower for keyword in experience_keywords):
            experience_sentences.append(sentence.strip())

    # If no sentences found, try to extract lines containing experience keywords
    if not experience_sentences:
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in experience_keywords):
                experience_sentences.append(line.strip())

    return experience_sentences

def calculate_job_match(cv_data, job_data):
    if not job_data:
        return {
            "score": 0,
            "matched_skills": [],
            "missing_skills": [],
            "job_requirements": [],
            "analysis": "No job data provided for comparison."
        }

    # Extract job requirements
    requirements = []
    if 'requirements' in job_data and isinstance(job_data['requirements'], list):
        requirements.extend(job_data['requirements'])
    elif 'requirements' in job_data and isinstance(job_data['requirements'], str):
        requirements.append(job_data['requirements'])

    # Extract job responsibilities
    responsibilities = []
    if 'responsibilities' in job_data and isinstance(job_data['responsibilities'], list):
        responsibilities.extend(job_data['responsibilities'])
    elif 'responsibilities' in job_data and isinstance(job_data['responsibilities'], str):
        responsibilities.append(job_data['responsibilities'])

    # Process requirements and responsibilities to extract key skills and qualifications
    job_keywords = []
    processed_requirements = []

    # Process requirements
    for req in requirements:
        if not isinstance(req, str):
            continue

        # Clean and normalize the requirement
        req = req.strip()
        processed_requirements.append(req)

        # Split by commas if it's a comma-separated string
        if ',' in req:
            parts = [k.strip().lower() for k in req.split(',')]
            job_keywords.extend(parts)
        else:
            # Extract key terms using NLP
            doc = nlp(req.lower())
            for chunk in doc.noun_chunks:
                job_keywords.append(chunk.text.strip())

            # Add the whole requirement as a keyword if it's short
            if len(req.split()) <= 5:
                job_keywords.append(req.lower())

    # Process responsibilities similarly
    for resp in responsibilities:
        if not isinstance(resp, str):
            continue

        # Split by commas if it's a comma-separated string
        if ',' in resp:
            parts = [k.strip().lower() for k in resp.split(',')]
            job_keywords.extend(parts)
        else:
            # Extract key terms using NLP
            doc = nlp(resp.lower())
            for chunk in doc.noun_chunks:
                job_keywords.append(chunk.text.strip())

    # Clean up keywords
    cleaned_keywords = []
    for keyword in job_keywords:
        # Remove very short keywords
        if len(keyword.split()) == 1 and len(keyword) < 3:
            continue

        # Remove common stop words if they're standalone
        if keyword in stopwords.words('english'):
            continue

        cleaned_keywords.append(keyword)

    # Remove duplicates and very similar terms
    unique_keywords = []
    for keyword in cleaned_keywords:
        # Check if this keyword is a subset of any existing keyword
        is_subset = False
        for existing in unique_keywords:
            if keyword in existing and len(keyword) < len(existing):
                is_subset = True
                break

        if not is_subset:
            unique_keywords.append(keyword)

    # Get candidate skills and other relevant information
    candidate_skills = cv_data.get('skills', [])
    candidate_education = cv_data.get('education', [])
    candidate_experience = cv_data.get('experience', [])

    # Combine all candidate text for comprehensive matching
    all_candidate_text = ' '.join(candidate_skills + candidate_education + candidate_experience).lower()

    # Calculate match score with detailed analysis
    matched_skills = []
    missing_skills = []

    for keyword in unique_keywords:
        if keyword in all_candidate_text or any(keyword in skill.lower() for skill in candidate_skills):
            matched_skills.append(keyword)
        else:
            missing_skills.append(keyword)

    # Calculate percentage match
    if not unique_keywords:
        match_percentage = 0
    else:
        match_percentage = min(100, int((len(matched_skills) / len(unique_keywords)) * 100))

    # Generate analysis text
    if match_percentage >= 80:
        analysis = "Excellent match! The candidate has most of the required skills and qualifications."
    elif match_percentage >= 60:
        analysis = "Good match. The candidate has many of the required skills but is missing some key qualifications."
    elif match_percentage >= 40:
        analysis = "Moderate match. The candidate has some relevant skills but lacks several important requirements."
    else:
        analysis = "Poor match. The candidate's profile does not align well with the job requirements."

    # Add specific details about strengths and weaknesses
    if matched_skills:
        analysis += f" Strengths include: {', '.join(matched_skills[:5])}"
        if len(matched_skills) > 5:
            analysis += f" and {len(matched_skills) - 5} more."
        else:
            analysis += "."

    if missing_skills:
        analysis += f" Missing qualifications include: {', '.join(missing_skills[:5])}"
        if len(missing_skills) > 5:
            analysis += f" and {len(missing_skills) - 5} more."
        else:
            analysis += "."

    return {
        "score": match_percentage,
        "matched_skills": matched_skills,
        "missing_skills": missing_skills,
        "job_requirements": processed_requirements,
        "analysis": analysis
    }

def parse_cv(text, job_data=None):
    # Check if text is an error message
    if text.startswith("Error") or text.startswith("File not") or text.startswith("No text"):
        return {
            "error": text,
            "name": "Unknown",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": [],
            "extracted_text": text
        }

    try:
        print(f"Parsing CV text of length: {len(text)}", file=sys.stderr)

        # Extract basic information
        basic_info = extract_basic_info(text)
        print(f"Extracted basic info: {basic_info}", file=sys.stderr)

        # Extract skills
        skills = extract_skills(text)
        print(f"Extracted {len(skills)} skills", file=sys.stderr)

        # Extract education
        education = extract_education(text)
        print(f"Extracted {len(education)} education entries", file=sys.stderr)

        # Extract experience
        experience = extract_experience(text)
        print(f"Extracted {len(experience)} experience entries", file=sys.stderr)

        # Create result dictionary
        result = {
            "name": basic_info["name"],
            "email": basic_info["email"],
            "phone": basic_info["phone"],
            "skills": skills,
            "education": education,
            "experience": experience,
            "extracted_text": text[:1000] + "..." if len(text) > 1000 else text  # Include a preview of the extracted text
        }

        # Calculate job match if job data is provided
        if job_data:
            try:
                print(f"Calculating job match with job: {job_data.get('title', 'Unknown')}", file=sys.stderr)
                match_result = calculate_job_match(result, job_data)

                result["matchScore"] = match_result["score"]
                result["matchedSkills"] = match_result["matched_skills"]
                result["missingSkills"] = match_result["missing_skills"]
                result["jobRequirements"] = match_result["job_requirements"]
                result["matchAnalysis"] = match_result["analysis"]
                result["jobTitle"] = job_data.get('title', 'Unknown Job')

                print(f"Match score: {match_result['score']}%", file=sys.stderr)
                print(f"Matched {len(match_result['matched_skills'])} skills, missing {len(match_result['missing_skills'])} skills", file=sys.stderr)
            except Exception as e:
                print(f"Error calculating job match: {str(e)}", file=sys.stderr)
                import traceback
                traceback.print_exc(file=sys.stderr)

                result["matchScore"] = 0
                result["matchError"] = str(e)
                result["matchAnalysis"] = "Error analyzing job match: " + str(e)

        # Generate a summary report
        summary = []
        summary.append(f"Candidate Name: {basic_info['name']}")

        if basic_info['email']:
            summary.append(f"Contact: {basic_info['email']}")
            if basic_info['phone']:
                summary.append(f", {basic_info['phone']}")

        summary.append("\n")

        if skills:
            summary.append("Skills Summary:")
            for skill in skills[:10]:  # Show top 10 skills
                summary.append(f"- {skill}")
            if len(skills) > 10:
                summary.append(f"- And {len(skills) - 10} more skills")
            summary.append("\n")

        if education:
            summary.append("Education Highlights:")
            for edu in education[:3]:  # Show top 3 education entries
                summary.append(f"- {edu}")
            if len(education) > 3:
                summary.append(f"- And {len(education) - 3} more education entries")
            summary.append("\n")

        if experience:
            summary.append("Experience Highlights:")
            for exp in experience[:3]:  # Show top 3 experience entries
                summary.append(f"- {exp}")
            if len(experience) > 3:
                summary.append(f"- And {len(experience) - 3} more experience entries")
            summary.append("\n")

        if job_data and "matchScore" in result:
            summary.append(f"Job Match: {result['matchScore']}% for {result['jobTitle']}")
            summary.append(result.get("matchAnalysis", ""))
            summary.append("\n")

            if "matchedSkills" in result and result["matchedSkills"]:
                summary.append("Matching Qualifications:")
                for skill in result["matchedSkills"][:5]:
                    summary.append(f"- {skill}")
                if len(result["matchedSkills"]) > 5:
                    summary.append(f"- And {len(result['matchedSkills']) - 5} more")
                summary.append("\n")

            if "missingSkills" in result and result["missingSkills"]:
                summary.append("Missing Qualifications:")
                for skill in result["missingSkills"][:5]:
                    summary.append(f"- {skill}")
                if len(result["missingSkills"]) > 5:
                    summary.append(f"- And {len(result['missingSkills']) - 5} more")

        result["summary"] = "\n".join(summary)

        return result
    except Exception as e:
        print(f"Error parsing CV: {str(e)}", file=sys.stderr)
        import traceback
        traceback.print_exc(file=sys.stderr)

        return {
            "error": f"Error parsing CV: {str(e)}",
            "name": "Error",
            "email": None,
            "phone": None,
            "skills": [],
            "education": [],
            "experience": [],
            "extracted_text": text[:1000] + "..." if len(text) > 1000 else text
        }

if __name__ == "__main__":
    try:
        # Get file path from command line arguments
        file_path = sys.argv[1]

        # Check if job data is provided
        job_data = None
        if len(sys.argv) > 2:
            # Check if we're using a job data file
            is_job_file = False
            if len(sys.argv) > 3 and sys.argv[3] == '--job-file':
                is_job_file = True
                print(f"Using job data from file", file=sys.stderr)

            if is_job_file:
                # Read job data from file
                try:
                    job_file_path = sys.argv[2]
                    print(f"Reading job data from file: {job_file_path}", file=sys.stderr)

                    with open(job_file_path, 'r') as f:
                        job_data_str = f.read()

                    # Try to parse the JSON
                    job_data = json.loads(job_data_str)
                    print(f"Successfully parsed job data from file", file=sys.stderr)
                except Exception as e:
                    print(f"Error reading job data from file: {str(e)}", file=sys.stderr)
            else:
                # Parse job data from command line argument
                try:
                    # Print the raw job data for debugging
                    print(f"Raw job data length: {len(sys.argv[2])}", file=sys.stderr)

                    # Clean the job data string to ensure it's valid JSON
                    job_data_str = sys.argv[2].strip('"\'')

                    # Fix common JSON issues
                    # Replace single quotes with double quotes
                    job_data_str = job_data_str.replace("'", '"')

                    # Fix newlines in JSON strings
                    job_data_str = job_data_str.replace('\n', '\\n')

                    # Fix unescaped backslashes
                    job_data_str = job_data_str.replace('\\', '\\\\')
                    job_data_str = job_data_str.replace('\\\\n', '\\n')

                    # Try to parse the JSON
                    job_data = json.loads(job_data_str)

                    # Print the parsed job data for debugging
                    print(f"Successfully parsed job data", file=sys.stderr)
                except json.JSONDecodeError as e:
                    print(f"Error parsing job data: {str(e)}", file=sys.stderr)
                    print(f"Job data string preview: {job_data_str[:100]}...", file=sys.stderr)

                    # Try a different approach - use a more lenient parser
                    try:
                        import ast
                        # Convert string representation of dict to actual dict
                        job_data = ast.literal_eval(sys.argv[2])
                        print(f"Successfully parsed job data using ast.literal_eval", file=sys.stderr)
                    except Exception as e2:
                        print(f"Second parsing attempt failed: {str(e2)}", file=sys.stderr)

        # Extract text from PDF
        text = extract_text_from_pdf(file_path)

        # Parse CV
        parsed = parse_cv(text, job_data)

        # Output as JSON
        print(json.dumps(parsed))

    except Exception as e:
        error_data = {
            "error": str(e),
            "traceback": str(sys.exc_info())
        }
        print(json.dumps(error_data))
        sys.exit(1)
