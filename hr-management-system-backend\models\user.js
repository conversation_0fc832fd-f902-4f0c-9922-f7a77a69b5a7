const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');

// User Schema
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: {
    type: String,
    required: true,
    enum: ['admin', 'hr', 'user'],  // Added 'user'
    default: 'user'  // Default is now 'user'
  },
  job: { type: String, required: true },
  department: {
    type: String,
    required: true,
    enum: ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance', 'Operations', 'IT', 'Customer Support', 'Executive', 'Other'],
    default: 'Other'
  },
  name: { type: String, required: true },
  creationDate: { type: Date, required: true },
  birthdate: { type: Date, required: false },
  active: { type: Boolean, default: true }, // Whether the account is active
  lastPasswordChange: { type: Date, default: Date.now }, // When the password was last changed
  lastLogin: { type: Date }, // When the user last logged in
  failedLoginAttempts: { type: Number, default: 0 }, // Count of failed login attempts
  accountLocked: { type: Boolean, default: false }, // Whether the account is locked due to too many failed attempts
  accountLockedUntil: { type: Date } // When the account lock expires
}, { timestamps: true });

// Hash password before saving
userSchema.pre('save', async function (next) {
  // Check if password is already hashed (starts with $2a$ or $2b$)
  if (this.password.startsWith('$2a$') || this.password.startsWith('$2b$')) {
    console.log('Password already appears to be hashed, skipping hashing');
    return next();
  }

  // Only hash if password is modified and not already hashed
  if (this.isModified('password')) {
    console.log('Hashing password in pre-save hook');
    try {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
      console.log('Password hashed successfully');
    } catch (error) {
      console.error('Error hashing password:', error);
      return next(error);
    }
  }

  next();
});

// Method to compare passwords
userSchema.methods.comparePassword = async function (password) {
  try {
    return await bcrypt.compare(password, this.password);
  } catch (error) {
    console.error('Error comparing passwords:', error);
    throw error;
  }
};

module.exports = mongoose.model('User', userSchema);
