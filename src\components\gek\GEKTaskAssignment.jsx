/**
 * GEK Task Assignment Component
 * Dedicated section for AI-powered task assignment with GEK analytics
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  LinearProgress,
  CircularProgress
} from '@mui/material';
import {
  Psychology as AIIcon,
  Assignment as AssignmentIcon,
  Person as PersonIcon,
  TrendingUp as TrendingUpIcon,
  Star as StarIcon,
  CheckCircle as CheckCircleIcon,
  Add as AddIcon
} from '@mui/icons-material';
import AITaskAssignment from './AITaskAssignment';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

const GEKTaskAssignment = () => {
  const [taskData, setTaskData] = useState({
    title: '',
    category: '',
    priority: '',
    deadline: '',
    description: ''
  });
  const [showAIRecommendations, setShowAIRecommendations] = useState(false);
  const [users, setUsers] = useState([]);
  const [recentAssignments, setRecentAssignments] = useState([]);
  const [availableTasks, setAvailableTasks] = useState([]);
  const [selectedExistingTask, setSelectedExistingTask] = useState(null);
  const [showExistingTasks, setShowExistingTasks] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [usersResponse, tasksResponse] = await Promise.all([
        api.get('/hr/users', { params: { role: 'user' } }),
        api.get('/tasks/hr')
      ]);

      setUsers(usersResponse.data || []);

      // Get all tasks
      const tasks = tasksResponse.data || [];

      // Get recent task assignments
      const recent = tasks
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5)
        .map(task => {
          const assignedUser = usersResponse.data.find(u => u._id === task.assignedTo || u.email === task.assignedTo);
          return {
            ...task,
            assignedUserName: assignedUser?.name || 'Unknown User'
          };
        });

      // Get available tasks (unassigned or not started)
      const available = tasks
        .filter(task =>
          !task.assignedTo ||
          task.status === 'Not Started' ||
          task.assignedTo === '' ||
          task.assignedTo === null
        )
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      setRecentAssignments(recent);
      setAvailableTasks(available);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTask = () => {
    if (!taskData.title || !taskData.category || !taskData.priority) {
      toast.warning('Please fill in task title, category, and priority');
      return;
    }
    setShowAIRecommendations(true);
  };

  const handleTaskAssignment = async (selectedUser) => {
    try {
      const taskPayload = {
        ...taskData,
        assignedTo: selectedUser._id,
        status: 'Not Started',
        createdAt: new Date().toISOString()
      };

      const response = await api.post('/tasks/hr', taskPayload);

      if (response.status === 201) {
        toast.success(`Task "${taskData.title}" assigned to ${selectedUser.name} successfully!`);

        // Reset form
        setTaskData({
          title: '',
          category: '',
          priority: '',
          deadline: '',
          description: ''
        });
        setShowAIRecommendations(false);

        // Refresh recent assignments
        fetchData();
      }
    } catch (error) {
      console.error('Error creating task:', error);
      toast.error('Failed to create task');
    }
  };

  const handleExistingTaskAssignment = async (selectedUser) => {
    try {
      if (!selectedExistingTask) {
        toast.error('Please select a task first');
        return;
      }

      const response = await api.put(`/tasks/hr/${selectedExistingTask._id}`, {
        assignedTo: selectedUser._id,
        status: 'Not Started'
      });

      if (response.status === 200) {
        toast.success(`Task "${selectedExistingTask.title}" assigned to ${selectedUser.name} successfully!`);

        // Reset selection
        setSelectedExistingTask(null);
        setShowExistingTasks(false);

        // Refresh data
        fetchData();
      }
    } catch (error) {
      console.error('Error assigning existing task:', error);
      toast.error('Failed to assign task');
    }
  };

  const handleSelectExistingTask = (task) => {
    setSelectedExistingTask(task);
    setShowExistingTasks(false);
    setShowAIRecommendations(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'In Progress': return 'primary';
      case 'Not Started': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Urgent': return 'error';
      case 'High': return 'warning';
      case 'Medium': return 'info';
      case 'Low': return 'success';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>Loading GEK Task Assignment...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
          GEK Task Assignment
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Use AI-powered analytics to assign tasks to the most suitable team members
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Task Creation Form */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <AssignmentIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Create New Task
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    label="Task Title"
                    fullWidth
                    required
                    value={taskData.title}
                    onChange={(e) => setTaskData({ ...taskData, title: e.target.value })}
                    placeholder="Enter task title..."
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Category</InputLabel>
                    <Select
                      value={taskData.category}
                      label="Category"
                      onChange={(e) => setTaskData({ ...taskData, category: e.target.value })}
                    >
                      <MenuItem value="General">General</MenuItem>
                      <MenuItem value="Development">Development</MenuItem>
                      <MenuItem value="Design">Design</MenuItem>
                      <MenuItem value="Marketing">Marketing</MenuItem>
                      <MenuItem value="HR">HR</MenuItem>
                      <MenuItem value="Finance">Finance</MenuItem>
                      <MenuItem value="Operations">Operations</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Priority</InputLabel>
                    <Select
                      value={taskData.priority}
                      label="Priority"
                      onChange={(e) => setTaskData({ ...taskData, priority: e.target.value })}
                    >
                      <MenuItem value="Low">Low</MenuItem>
                      <MenuItem value="Medium">Medium</MenuItem>
                      <MenuItem value="High">High</MenuItem>
                      <MenuItem value="Urgent">Urgent</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Deadline"
                    type="date"
                    fullWidth
                    InputLabelProps={{ shrink: true }}
                    value={taskData.deadline}
                    onChange={(e) => setTaskData({ ...taskData, deadline: e.target.value })}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Description"
                    fullWidth
                    multiline
                    rows={3}
                    value={taskData.description}
                    onChange={(e) => setTaskData({ ...taskData, description: e.target.value })}
                    placeholder="Describe the task requirements..."
                  />
                </Grid>

                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    startIcon={<AIIcon />}
                    onClick={handleCreateTask}
                    disabled={!taskData.title || !taskData.category || !taskData.priority}
                    sx={{ py: 1.5 }}
                  >
                    Get AI Assignment Recommendations
                  </Button>
                </Grid>
              </Grid>

              {/* Task Preview */}
              {(taskData.title || taskData.category || taskData.priority) && (
                <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    Task Preview:
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {taskData.title && (
                      <Chip label={`Title: ${taskData.title}`} size="small" />
                    )}
                    {taskData.category && (
                      <Chip label={`Category: ${taskData.category}`} size="small" color="primary" />
                    )}
                    {taskData.priority && (
                      <Chip 
                        label={`Priority: ${taskData.priority}`} 
                        size="small" 
                        color={getPriorityColor(taskData.priority)}
                      />
                    )}
                    {taskData.deadline && (
                      <Chip 
                        label={`Deadline: ${new Date(taskData.deadline).toLocaleDateString()}`} 
                        size="small" 
                        variant="outlined"
                      />
                    )}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Available Tasks for Assignment */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Box display="flex" alignItems="center" gap={1}>
                  <AssignmentIcon color="secondary" />
                  <Typography variant="h6" fontWeight="bold">
                    Available Tasks
                  </Typography>
                </Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => setShowExistingTasks(!showExistingTasks)}
                >
                  {showExistingTasks ? 'Hide' : 'Show All'}
                </Button>
              </Box>

              {availableTasks.length === 0 ? (
                <Alert severity="info">
                  No unassigned tasks available. Create a new task above.
                </Alert>
              ) : (
                <List sx={{ maxHeight: showExistingTasks ? 400 : 200, overflow: 'auto' }}>
                  {(showExistingTasks ? availableTasks : availableTasks.slice(0, 3)).map((task, index) => (
                    <React.Fragment key={task._id || index}>
                      <ListItem
                        alignItems="flex-start"
                        sx={{
                          cursor: 'pointer',
                          '&:hover': { bgcolor: 'action.hover' },
                          border: selectedExistingTask?._id === task._id ? '2px solid' : '1px solid transparent',
                          borderColor: selectedExistingTask?._id === task._id ? 'primary.main' : 'transparent',
                          borderRadius: 1,
                          mb: 1
                        }}
                        onClick={() => handleSelectExistingTask(task)}
                      >
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: getPriorityColor(task.priority) + '.main' }}>
                            <AssignmentIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                              <Typography variant="subtitle2" fontWeight="bold">
                                {task.title}
                              </Typography>
                              <Chip
                                label={task.priority}
                                size="small"
                                color={getPriorityColor(task.priority)}
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Category: {task.category}
                              </Typography>
                              {task.deadline && (
                                <Typography variant="body2" color="text.secondary">
                                  Deadline: {new Date(task.deadline).toLocaleDateString()}
                                </Typography>
                              )}
                              <Typography variant="caption" color="text.secondary">
                                Created: {new Date(task.createdAt).toLocaleDateString()}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < (showExistingTasks ? availableTasks : availableTasks.slice(0, 3)).length - 1 &&
                        <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              )}

              {selectedExistingTask && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'primary.light', borderRadius: 1 }}>
                  <Typography variant="subtitle2" color="primary.dark" gutterBottom>
                    Selected Task: {selectedExistingTask.title}
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    fullWidth
                    startIcon={<AIIcon />}
                    onClick={() => setShowAIRecommendations(true)}
                    sx={{ py: 1 }}
                  >
                    Get AI Assignment Recommendations
                  </Button>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Assignments */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={1} mb={2}>
                <TrendingUpIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  Recent Task Assignments
                </Typography>
              </Box>

              {recentAssignments.length === 0 ? (
                <Alert severity="info">
                  No recent task assignments found. Create your first AI-powered assignment!
                </Alert>
              ) : (
                <List>
                  {recentAssignments.map((task, index) => (
                    <React.Fragment key={task._id || index}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <AssignmentIcon />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                              <Typography variant="subtitle2" fontWeight="bold">
                                {task.title}
                              </Typography>
                              <Chip 
                                label={task.status} 
                                size="small" 
                                color={getStatusColor(task.status)}
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                Assigned to: <strong>{task.assignedUserName}</strong>
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Category: {task.category} • Priority: {task.priority}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Created: {new Date(task.createdAt).toLocaleDateString()}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < recentAssignments.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* AI Recommendations Modal */}
      {showAIRecommendations && (
        <Box sx={{ mt: 3 }}>
          <AITaskAssignment
            taskData={selectedExistingTask || taskData}
            onUserSelected={selectedExistingTask ? handleExistingTaskAssignment : handleTaskAssignment}
            onClose={() => {
              setShowAIRecommendations(false);
              if (selectedExistingTask) {
                setSelectedExistingTask(null);
              }
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default GEKTaskAssignment;
