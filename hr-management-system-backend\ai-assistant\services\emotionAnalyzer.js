/**
 * Advanced Emotion Detection and Sentiment Analysis Service
 * Detects emotions, sentiment, stress levels, and provides empathetic responses
 */

class EmotionAnalyzer {
  constructor() {
    this.initializeEmotionPatterns();
    this.initializeSentimentLexicon();
    this.initializeStressIndicators();
  }

  /**
   * Initialize emotion detection patterns
   */
  initializeEmotionPatterns() {
    this.emotionPatterns = {
      // Joy and Happiness
      joy: {
        keywords: ['happy', 'excited', 'great', 'awesome', 'fantastic', 'wonderful', 'amazing', 'love', 'perfect', 'excellent', 'thrilled', 'delighted', 'cheerful', 'glad', 'pleased', 'satisfied', 'content', 'joyful', 'ecstatic', 'elated'],
        phrases: ['feeling good', 'so happy', 'really excited', 'love this', 'this is great', 'having a good day', 'feeling positive'],
        emojis: ['😊', '😄', '😃', '😁', '🙂', '😍', '🥰', '😘', '🤗', '🎉', '🎊', '👏', '💖', '❤️', '💕'],
        intensity: 0.8
      },

      // Sadness and Disappointment
      sadness: {
        keywords: ['sad', 'disappointed', 'upset', 'down', 'depressed', 'unhappy', 'miserable', 'heartbroken', 'devastated', 'gloomy', 'melancholy', 'sorrowful', 'dejected', 'despondent', 'blue', 'low', 'hurt', 'crying', 'tears'],
        phrases: ['feeling down', 'really sad', 'so disappointed', 'having a bad day', 'feeling low', 'not good', 'feeling blue'],
        emojis: ['😢', '😭', '😞', '😔', '☹️', '🙁', '😿', '💔', '😰', '😨'],
        intensity: 0.7
      },

      // Anger and Frustration
      anger: {
        keywords: ['angry', 'mad', 'furious', 'frustrated', 'annoyed', 'irritated', 'pissed', 'rage', 'outraged', 'livid', 'enraged', 'irate', 'incensed', 'infuriated', 'aggravated', 'exasperated', 'fed up', 'sick of'],
        phrases: ['so angry', 'really mad', 'fed up with', 'sick of this', 'driving me crazy', 'had enough', 'losing patience'],
        emojis: ['😠', '😡', '🤬', '😤', '💢', '🔥', '👿', '😾'],
        intensity: 0.9
      },

      // Anxiety and Worry
      anxiety: {
        keywords: ['anxious', 'worried', 'nervous', 'stressed', 'overwhelmed', 'panic', 'scared', 'afraid', 'concerned', 'uneasy', 'tense', 'restless', 'agitated', 'troubled', 'distressed', 'fearful', 'apprehensive', 'jittery'],
        phrases: ['feeling anxious', 'really worried', 'so stressed', 'overwhelmed with', 'nervous about', 'scared that', 'afraid of'],
        emojis: ['😰', '😨', '😟', '😧', '🤯', '😵', '🥺', '😬'],
        intensity: 0.8
      },

      // Confusion and Uncertainty
      confusion: {
        keywords: ['confused', 'lost', 'unclear', 'uncertain', 'puzzled', 'bewildered', 'perplexed', 'baffled', 'stumped', 'clueless', 'mixed up', 'unsure', 'doubtful', 'questioning'],
        phrases: ['not sure', 'dont understand', 'confused about', 'not clear', 'mixed up', 'lost here', 'what does this mean'],
        emojis: ['😕', '🤔', '😵‍💫', '🤷‍♀️', '🤷‍♂️', '❓', '❔'],
        intensity: 0.5
      },

      // Excitement and Enthusiasm
      excitement: {
        keywords: ['excited', 'thrilled', 'pumped', 'hyped', 'enthusiastic', 'eager', 'energetic', 'motivated', 'inspired', 'passionate', 'fired up', 'psyched', 'stoked', 'amped'],
        phrases: ['so excited', 'really thrilled', 'cant wait', 'looking forward', 'pumped up', 'fired up about'],
        emojis: ['🤩', '🥳', '🎉', '🎊', '🚀', '⚡', '🔥', '💪', '🙌'],
        intensity: 0.9
      },

      // Gratitude and Appreciation
      gratitude: {
        keywords: ['thank', 'thanks', 'grateful', 'appreciate', 'thankful', 'blessed', 'fortunate', 'lucky', 'indebted', 'obliged'],
        phrases: ['thank you', 'thanks so much', 'really appreciate', 'so grateful', 'thanks for helping', 'appreciate your help'],
        emojis: ['🙏', '💖', '❤️', '💕', '🤗', '😊'],
        intensity: 0.7
      },

      // Fatigue and Exhaustion
      fatigue: {
        keywords: ['tired', 'exhausted', 'drained', 'worn out', 'fatigued', 'weary', 'burnt out', 'depleted', 'spent', 'beat', 'wiped out', 'sleepy', 'drowsy'],
        phrases: ['so tired', 'really exhausted', 'worn out', 'burnt out', 'need rest', 'feeling drained', 'running on empty'],
        emojis: ['😴', '🥱', '😪', '💤', '😵'],
        intensity: 0.6
      }
    };
  }

  /**
   * Initialize sentiment lexicon for advanced sentiment analysis
   */
  initializeSentimentLexicon() {
    this.sentimentLexicon = {
      // Positive words with scores
      positive: {
        'excellent': 3, 'amazing': 3, 'outstanding': 3, 'fantastic': 3, 'wonderful': 3,
        'great': 2, 'good': 2, 'nice': 2, 'pleasant': 2, 'helpful': 2, 'useful': 2,
        'fine': 1, 'okay': 1, 'decent': 1, 'adequate': 1, 'satisfactory': 1,
        'love': 3, 'like': 2, 'enjoy': 2, 'appreciate': 2, 'prefer': 1,
        'perfect': 3, 'brilliant': 3, 'superb': 3, 'magnificent': 3, 'marvelous': 3
      },

      // Negative words with scores
      negative: {
        'terrible': -3, 'awful': -3, 'horrible': -3, 'disgusting': -3, 'hate': -3,
        'bad': -2, 'poor': -2, 'disappointing': -2, 'frustrating': -2, 'annoying': -2,
        'wrong': -1, 'difficult': -1, 'hard': -1, 'confusing': -1, 'unclear': -1,
        'dislike': -2, 'despise': -3, 'loathe': -3, 'detest': -3,
        'useless': -2, 'worthless': -3, 'pathetic': -3, 'ridiculous': -2
      },

      // Intensifiers
      intensifiers: {
        'very': 1.5, 'really': 1.5, 'extremely': 2.0, 'incredibly': 2.0, 'absolutely': 2.0,
        'quite': 1.3, 'pretty': 1.2, 'somewhat': 0.8, 'slightly': 0.7, 'barely': 0.5,
        'totally': 2.0, 'completely': 2.0, 'utterly': 2.0, 'entirely': 2.0
      },

      // Negators
      negators: ['not', 'no', 'never', 'none', 'nothing', 'nobody', 'nowhere', 'neither', 'nor', 'dont', 'doesnt', 'didnt', 'wont', 'wouldnt', 'cant', 'couldnt', 'shouldnt']
    };
  }

  /**
   * Initialize stress and burnout indicators
   */
  initializeStressIndicators() {
    this.stressIndicators = {
      workload: ['overwhelmed', 'too much', 'cant handle', 'drowning', 'swamped', 'buried', 'overloaded', 'pressure', 'deadline', 'rush'],
      interpersonal: ['conflict', 'argument', 'disagreement', 'tension', 'difficult person', 'toxic', 'harassment', 'bullying'],
      personal: ['family issues', 'health problems', 'financial stress', 'relationship problems', 'personal matters'],
      burnout: ['burnt out', 'exhausted', 'drained', 'no motivation', 'giving up', 'quit', 'resign', 'leave job']
    };
  }

  /**
   * Analyze emotions in text
   * @param {string} text - Input text to analyze
   * @returns {Object} - Emotion analysis results
   */
  analyzeEmotions(text) {
    const lowerText = text.toLowerCase();
    const words = lowerText.split(/\s+/);
    const detectedEmotions = {};
    let primaryEmotion = null;
    let maxIntensity = 0;

    // Check for each emotion
    Object.entries(this.emotionPatterns).forEach(([emotion, pattern]) => {
      let score = 0;
      let matches = 0;

      // Check keywords
      pattern.keywords.forEach(keyword => {
        if (lowerText.includes(keyword)) {
          score += pattern.intensity;
          matches++;
        }
      });

      // Check phrases
      pattern.phrases.forEach(phrase => {
        if (lowerText.includes(phrase)) {
          score += pattern.intensity * 1.5; // Phrases are more significant
          matches++;
        }
      });

      // Check emojis
      pattern.emojis.forEach(emoji => {
        if (text.includes(emoji)) {
          score += pattern.intensity * 1.2;
          matches++;
        }
      });

      if (score > 0) {
        const normalizedScore = Math.min(score / Math.max(words.length * 0.1, 1), 1);
        detectedEmotions[emotion] = {
          score: normalizedScore,
          intensity: pattern.intensity,
          matches: matches,
          confidence: Math.min(normalizedScore * matches * 0.3, 1)
        };

        if (normalizedScore > maxIntensity) {
          maxIntensity = normalizedScore;
          primaryEmotion = emotion;
        }
      }
    });

    return {
      primaryEmotion,
      emotions: detectedEmotions,
      emotionalIntensity: maxIntensity,
      hasEmotionalContent: Object.keys(detectedEmotions).length > 0
    };
  }

  /**
   * Analyze sentiment with advanced scoring
   * @param {string} text - Input text to analyze
   * @returns {Object} - Sentiment analysis results
   */
  analyzeSentiment(text) {
    const words = text.toLowerCase().split(/\s+/);
    let score = 0;
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    let isNegated = false;

    for (let i = 0; i < words.length; i++) {
      const word = words[i];

      // Check for negators
      if (this.sentimentLexicon.negators.includes(word)) {
        isNegated = true;
        continue;
      }

      // Check for intensifiers
      let intensifier = 1;
      if (i > 0 && this.sentimentLexicon.intensifiers[words[i-1]]) {
        intensifier = this.sentimentLexicon.intensifiers[words[i-1]];
      }

      // Check sentiment
      let wordScore = 0;
      if (this.sentimentLexicon.positive[word]) {
        wordScore = this.sentimentLexicon.positive[word] * intensifier;
        positiveCount++;
      } else if (this.sentimentLexicon.negative[word]) {
        wordScore = this.sentimentLexicon.negative[word] * intensifier;
        negativeCount++;
      } else {
        neutralCount++;
      }

      // Apply negation
      if (isNegated) {
        wordScore = -wordScore;
        isNegated = false;
      }

      score += wordScore;
    }

    // Normalize score
    const totalWords = words.length;
    const normalizedScore = totalWords > 0 ? score / totalWords : 0;

    // Determine sentiment
    let sentiment = 'neutral';
    let confidence = 0;

    if (normalizedScore > 0.1) {
      sentiment = 'positive';
      confidence = Math.min(normalizedScore, 1);
    } else if (normalizedScore < -0.1) {
      sentiment = 'negative';
      confidence = Math.min(Math.abs(normalizedScore), 1);
    } else {
      sentiment = 'neutral';
      confidence = 1 - Math.abs(normalizedScore);
    }

    return {
      sentiment,
      score: normalizedScore,
      confidence,
      breakdown: {
        positive: positiveCount,
        negative: negativeCount,
        neutral: neutralCount
      }
    };
  }

  /**
   * Detect stress levels and burnout indicators
   * @param {string} text - Input text to analyze
   * @returns {Object} - Stress analysis results
   */
  analyzeStress(text) {
    const lowerText = text.toLowerCase();
    const stressFactors = {};
    let totalStressScore = 0;

    Object.entries(this.stressIndicators).forEach(([category, indicators]) => {
      let categoryScore = 0;
      const matches = [];

      indicators.forEach(indicator => {
        if (lowerText.includes(indicator)) {
          categoryScore += 1;
          matches.push(indicator);
        }
      });

      if (categoryScore > 0) {
        stressFactors[category] = {
          score: categoryScore,
          matches: matches,
          severity: categoryScore > 2 ? 'high' : categoryScore > 1 ? 'medium' : 'low'
        };
        totalStressScore += categoryScore;
      }
    });

    const stressLevel = totalStressScore > 4 ? 'high' : 
                       totalStressScore > 2 ? 'medium' : 
                       totalStressScore > 0 ? 'low' : 'none';

    return {
      stressLevel,
      totalScore: totalStressScore,
      factors: stressFactors,
      needsSupport: stressLevel === 'high' || stressLevel === 'medium'
    };
  }

  /**
   * Generate empathetic response based on emotional analysis
   * @param {Object} emotionAnalysis - Results from emotion analysis
   * @param {Object} sentimentAnalysis - Results from sentiment analysis
   * @param {Object} stressAnalysis - Results from stress analysis
   * @returns {Object} - Empathetic response suggestions
   */
  generateEmpatheticResponse(emotionAnalysis, sentimentAnalysis, stressAnalysis) {
    const responses = {
      supportive: [],
      actionable: [],
      tone: 'neutral'
    };

    // Handle primary emotion
    if (emotionAnalysis.primaryEmotion) {
      switch (emotionAnalysis.primaryEmotion) {
        case 'sadness':
          responses.supportive.push("I can sense you're going through a difficult time. I'm here to help however I can.");
          responses.actionable.push("Would you like me to help you find resources for employee support or schedule time with HR?");
          responses.tone = 'compassionate';
          break;

        case 'anger':
          responses.supportive.push("I understand you're feeling frustrated. Let's work together to address what's bothering you.");
          responses.actionable.push("Would you like to discuss this issue with your manager or file a formal concern?");
          responses.tone = 'calm';
          break;

        case 'anxiety':
          responses.supportive.push("I can tell you're feeling overwhelmed. Take a deep breath - we'll figure this out together.");
          responses.actionable.push("Let me help you prioritize your tasks or connect you with stress management resources.");
          responses.tone = 'reassuring';
          break;

        case 'joy':
          responses.supportive.push("It's wonderful to hear you're feeling positive! I'm glad things are going well.");
          responses.tone = 'enthusiastic';
          break;

        case 'confusion':
          responses.supportive.push("I can see you're looking for clarity. Let me help explain things step by step.");
          responses.actionable.push("Would you like me to break this down into simpler parts or provide additional resources?");
          responses.tone = 'patient';
          break;

        case 'gratitude':
          responses.supportive.push("Thank you for your kind words! It means a lot to know I'm helping.");
          responses.tone = 'warm';
          break;

        case 'fatigue':
          responses.supportive.push("It sounds like you're feeling drained. Your wellbeing is important.");
          responses.actionable.push("Would you like information about work-life balance resources or time off policies?");
          responses.tone = 'caring';
          break;
      }
    }

    // Handle stress levels
    if (stressAnalysis.needsSupport) {
      responses.supportive.push("I notice you might be experiencing some stress. Remember that support is available.");
      responses.actionable.push("I can help you access employee assistance programs or wellness resources.");
    }

    // Handle negative sentiment
    if (sentimentAnalysis.sentiment === 'negative' && sentimentAnalysis.confidence > 0.6) {
      responses.supportive.push("I want to make sure you have a positive experience. Let me see how I can help improve things.");
    }

    return responses;
  }

  /**
   * Complete emotional intelligence analysis
   * @param {string} text - Input text to analyze
   * @returns {Object} - Complete emotional analysis
   */
  analyzeComplete(text) {
    const emotionAnalysis = this.analyzeEmotions(text);
    const sentimentAnalysis = this.analyzeSentiment(text);
    const stressAnalysis = this.analyzeStress(text);
    const empatheticResponse = this.generateEmpatheticResponse(emotionAnalysis, sentimentAnalysis, stressAnalysis);

    return {
      emotions: emotionAnalysis,
      sentiment: sentimentAnalysis,
      stress: stressAnalysis,
      empathy: empatheticResponse,
      summary: {
        needsEmotionalSupport: emotionAnalysis.hasEmotionalContent && 
                              (sentimentAnalysis.sentiment === 'negative' || stressAnalysis.needsSupport),
        recommendedTone: empatheticResponse.tone,
        urgency: stressAnalysis.stressLevel === 'high' ? 'high' : 
                emotionAnalysis.primaryEmotion === 'anger' ? 'medium' : 'low'
      }
    };
  }
}

// Singleton instance
const emotionAnalyzer = new EmotionAnalyzer();

module.exports = emotionAnalyzer;
