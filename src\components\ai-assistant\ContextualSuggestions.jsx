/**
 * Contextual Suggestions Component
 * Provides real-time AI suggestions based on user actions and context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  <PERSON>lapse,
  Chip,
  Alert,
  <PERSON><PERSON>,
  Fade,
  Tooltip
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  Close as CloseIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';
import api from '../../Services/ApiService';

const ContextualSuggestions = ({ 
  context, 
  actionType, 
  data = {}, 
  onSuggestionApplied,
  position = 'bottom-right' 
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState(true);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (context && actionType) {
      generateSuggestions();
    }
  }, [context, actionType, data]);

  const generateSuggestions = async () => {
    try {
      setLoading(true);

      // Generate suggestions based on context and data directly
      const directSuggestions = generateDirectSuggestions(context, actionType, data);

      if (directSuggestions.length > 0) {
        setSuggestions(directSuggestions);
        setVisible(true);
        console.log('Generated suggestions:', directSuggestions);
      } else {
        console.log('No suggestions generated for:', { context, actionType, data });
      }
    } catch (error) {
      console.error('Error generating suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateDirectSuggestions = (context, actionType, data) => {
    const suggestions = [];
    console.log('Generating suggestions for:', { context, actionType, data });

    // Leave request suggestions
    if (context === 'leave_request') {
      // Always show basic leave suggestion
      suggestions.push({
        id: `leave_basic_${Date.now()}`,
        title: 'Leave Request Tips',
        description: 'Submit your leave request at least 3 days in advance for better approval chances.',
        type: 'tip',
        priority: 'medium',
        action: 'plan_ahead'
      });

      // Sick leave specific
      if (data.leaveType && data.leaveType.toLowerCase().includes('sick')) {
        suggestions.push({
          id: `sick_leave_${Date.now()}`,
          title: 'Medical Documentation',
          description: 'For sick leave, consider attaching medical documentation to speed up approval.',
          type: 'reminder',
          priority: 'high',
          action: 'attach_document'
        });
      }

      // Extended leave
      if (data.duration && data.duration > 5) {
        suggestions.push({
          id: `extended_leave_${Date.now()}`,
          title: 'Extended Leave Notice',
          description: 'For leaves longer than 5 days, consider giving at least 2 weeks notice.',
          type: 'best_practice',
          priority: 'medium',
          action: 'plan_ahead'
        });
      }

      // Vacation planning
      if (data.leaveType && (data.leaveType.toLowerCase().includes('vacation') || data.leaveType.toLowerCase().includes('annual'))) {
        suggestions.push({
          id: `vacation_planning_${Date.now()}`,
          title: 'Vacation Planning',
          description: 'Consider planning your vacation during less busy periods for better approval chances.',
          type: 'tip',
          priority: 'medium',
          action: 'check_calendar'
        });
      }
    }

    // Task management suggestions
    if (context === 'task_management') {
      // High priority task alert
      if (actionType === 'create' && data.priority === 'High') {
        suggestions.push({
          id: `high_priority_${Date.now()}`,
          title: 'High Priority Task Alert',
          description: 'Consider setting a clear deadline and notifying the assignee immediately.',
          type: 'alert',
          priority: 'high',
          action: 'notify_assignee'
        });
      }

      // Urgent deadline warning
      if (data.deadline && new Date(data.deadline) < new Date(Date.now() + 24 * 60 * 60 * 1000)) {
        suggestions.push({
          id: `urgent_deadline_${Date.now()}`,
          title: 'Urgent Deadline',
          description: 'This task has a deadline within 24 hours. Consider immediate assignment.',
          type: 'warning',
          priority: 'high',
          action: 'immediate_action'
        });
      }

      // General task creation tip
      if (actionType === 'create') {
        suggestions.push({
          id: `task_creation_${Date.now()}`,
          title: 'Task Management Tip',
          description: 'Provide clear instructions and set realistic deadlines for better task completion.',
          type: 'tip',
          priority: 'medium',
          action: 'optimize_task'
        });
      }
    }

    // Attendance suggestions
    if (context === 'attendance') {
      // Late check-in
      if (actionType === 'check_in' && new Date().getHours() > 9) {
        suggestions.push({
          id: `late_checkin_${Date.now()}`,
          title: 'Late Check-in Detected',
          description: 'You\'re checking in after 9 AM. Consider setting an earlier alarm for tomorrow.',
          type: 'tip',
          priority: 'low',
          action: 'set_reminder'
        });
      }

      // Early check-in recognition
      if (actionType === 'check_in' && new Date().getHours() < 8) {
        suggestions.push({
          id: `early_checkin_${Date.now()}`,
          title: 'Early Bird!',
          description: 'Great job arriving early! This shows excellent dedication.',
          type: 'recognition',
          priority: 'low',
          action: 'keep_it_up'
        });
      }

      // Overtime detection
      if (actionType === 'check_out' && data.hoursWorked && data.hoursWorked > 8) {
        suggestions.push({
          id: `overtime_${Date.now()}`,
          title: 'Overtime Detected',
          description: `You've worked ${data.hoursWorked} hours today. Great dedication! Make sure to track overtime properly.`,
          type: 'recognition',
          priority: 'medium',
          action: 'track_overtime'
        });
      }

      // Regular check-out
      if (actionType === 'check_out' && data.hoursWorked && data.hoursWorked >= 7 && data.hoursWorked <= 8) {
        suggestions.push({
          id: `regular_checkout_${Date.now()}`,
          title: 'Good Work Day',
          description: 'You\'ve completed a full work day. Have a great evening!',
          type: 'recognition',
          priority: 'low',
          action: 'well_done'
        });
      }
    }

    // User management suggestions
    if (context === 'user_management') {
      // Always show basic user management tip
      suggestions.push({
        id: `user_mgmt_basic_${Date.now()}`,
        title: 'User Management Best Practice',
        description: 'Ensure all user information is complete and accurate for better system functionality.',
        type: 'tip',
        priority: 'medium',
        action: 'verify_info'
      });

      if (actionType === 'create') {
        suggestions.push({
          id: `user_onboarding_${Date.now()}`,
          title: 'New User Onboarding',
          description: 'Create an onboarding checklist and send welcome email with system access details.',
          type: 'best_practice',
          priority: 'high',
          action: 'create_checklist'
        });

        // Role-specific suggestions
        if (data.role === 'admin') {
          suggestions.push({
            id: `admin_role_${Date.now()}`,
            title: 'Admin Role Assignment',
            description: 'Admin users have full system access. Ensure this person requires administrative privileges.',
            type: 'warning',
            priority: 'high',
            action: 'verify_permissions'
          });
        }

        if (data.role === 'hr') {
          suggestions.push({
            id: `hr_role_${Date.now()}`,
            title: 'HR Role Assignment',
            description: 'HR users can manage employees and sensitive data. Verify appropriate access level.',
            type: 'reminder',
            priority: 'medium',
            action: 'verify_access'
          });
        }
      }

      if (actionType === 'update') {
        suggestions.push({
          id: `user_update_${Date.now()}`,
          title: 'User Information Update',
          description: 'Notify the user about any changes to their account and verify the updates are correct.',
          type: 'reminder',
          priority: 'medium',
          action: 'notify_user'
        });
      }
    }

    // Recruitment suggestions
    if (context === 'recruitment') {
      // Always show basic recruitment tip
      suggestions.push({
        id: `recruitment_basic_${Date.now()}`,
        title: 'Recruitment Best Practice',
        description: 'Clear job descriptions and competitive offerings attract better candidates.',
        type: 'tip',
        priority: 'medium',
        action: 'optimize_posting'
      });

      if (actionType === 'create') {
        suggestions.push({
          id: `job_posting_${Date.now()}`,
          title: 'Job Posting Optimization',
          description: 'Include salary range, benefits, and growth opportunities to attract top talent.',
          type: 'tip',
          priority: 'medium',
          action: 'optimize_posting'
        });

        // Job type specific suggestions
        if (data.jobType === 'Remote') {
          suggestions.push({
            id: `remote_job_${Date.now()}`,
            title: 'Remote Work Considerations',
            description: 'Specify time zone requirements and remote work tools/equipment provided.',
            type: 'reminder',
            priority: 'medium',
            action: 'specify_remote_details'
          });
        }

        if (data.jobType === 'Internship') {
          suggestions.push({
            id: `internship_program_${Date.now()}`,
            title: 'Internship Program',
            description: 'Mention mentorship opportunities and potential for full-time conversion.',
            type: 'best_practice',
            priority: 'medium',
            action: 'highlight_growth'
          });
        }

        if (data.academicLevel === 'PhD') {
          suggestions.push({
            id: `phd_position_${Date.now()}`,
            title: 'PhD-Level Position',
            description: 'Highlight research opportunities and academic collaboration potential.',
            type: 'tip',
            priority: 'medium',
            action: 'emphasize_research'
          });
        }
      }

      if (actionType === 'review') {
        suggestions.push({
          id: `application_review_${Date.now()}`,
          title: 'Application Review Timeline',
          description: 'Review applications within 48 hours for better candidate experience and employer branding.',
          type: 'best_practice',
          priority: 'high',
          action: 'schedule_review'
        });
      }
    }

    // Evaluation suggestions
    if (context === 'evaluation') {
      // Always show basic evaluation tip
      suggestions.push({
        id: `evaluation_basic_${Date.now()}`,
        title: 'Performance Evaluation Insight',
        description: 'Use evaluation feedback to identify growth opportunities and career development paths.',
        type: 'tip',
        priority: 'medium',
        action: 'review_feedback'
      });

      if (actionType === 'acknowledge') {
        suggestions.push({
          id: `evaluation_acknowledge_${Date.now()}`,
          title: 'Evaluation Acknowledgment',
          description: 'Acknowledging evaluations shows professionalism and commitment to continuous improvement.',
          type: 'best_practice',
          priority: 'high',
          action: 'acknowledge_feedback'
        });

        // Performance-based suggestions
        if (data.overallRating >= 4) {
          suggestions.push({
            id: `high_performance_${Date.now()}`,
            title: 'Excellent Performance',
            description: 'Consider discussing leadership opportunities or advanced projects with your manager.',
            type: 'recognition',
            priority: 'medium',
            action: 'explore_growth'
          });
        } else if (data.overallRating <= 2) {
          suggestions.push({
            id: `improvement_needed_${Date.now()}`,
            title: 'Performance Improvement',
            description: 'Schedule a meeting with your manager to discuss development plans and support resources.',
            type: 'alert',
            priority: 'high',
            action: 'seek_support'
          });
        }

        // Areas for improvement suggestions
        if (data.areasForImprovement && data.areasForImprovement.length > 10) {
          suggestions.push({
            id: `improvement_focus_${Date.now()}`,
            title: 'Focus on Development Areas',
            description: 'Create a personal development plan to address the areas for improvement mentioned.',
            type: 'reminder',
            priority: 'medium',
            action: 'create_plan'
          });
        }
      }

      if (actionType === 'view') {
        suggestions.push({
          id: `evaluation_review_${Date.now()}`,
          title: 'Regular Review',
          description: 'Review past evaluations periodically to track your professional growth and achievements.',
          type: 'reminder',
          priority: 'low',
          action: 'track_progress'
        });
      }
    }

    console.log('Generated suggestions:', suggestions);
    return suggestions.slice(0, 3); // Limit to 3 suggestions
  };

  const handleSuggestionFeedback = async (suggestionId, feedback) => {
    try {
      // Track feedback for learning
      await api.post('/api/ai/feedback', {
        suggestionId,
        feedback,
        context,
        actionType
      });

      // Remove suggestion after feedback
      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
      
      if (suggestions.length <= 1) {
        setVisible(false);
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  const handleApplySuggestion = (suggestion) => {
    if (onSuggestionApplied) {
      onSuggestionApplied(suggestion);
    }
    handleSuggestionFeedback(suggestion.id, 'applied');
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'primary';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'warning': return '⚠️';
      case 'tip': return '💡';
      case 'reminder': return '📝';
      case 'best_practice': return '✨';
      case 'alert': return '🚨';
      case 'recognition': return '🎉';
      default: return '💡';
    }
  };

  if (!visible || suggestions.length === 0) {
    return null;
  }

  const positionStyles = {
    'bottom-right': {
      position: 'fixed',
      bottom: 20,
      right: 20,
      zIndex: 1300,
      maxWidth: 400
    },
    'top-right': {
      position: 'fixed',
      top: 80,
      right: 20,
      zIndex: 1300,
      maxWidth: 400
    },
    'inline': {
      width: '100%',
      mb: 1
    }
  };

  return (
    <Fade in={visible}>
      <Box sx={positionStyles[position]}>
        <Card elevation={position === 'inline' ? 2 : 8} sx={{
          border: position === 'inline' ? '1px solid' : '2px solid',
          borderColor: 'primary.main',
          background: position === 'inline'
            ? 'linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%)'
            : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
        }}>
          <CardContent sx={{ pb: position === 'inline' ? 1 : 1, pt: position === 'inline' ? 1.5 : 2 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={position === 'inline' ? 0.5 : 1}>
              <Box display="flex" alignItems="center" gap={1}>
                <AIIcon color="primary" fontSize={position === 'inline' ? 'small' : 'medium'} />
                <Typography variant={position === 'inline' ? 'subtitle2' : 'h6'} color="primary" fontWeight="bold">
                  AI Suggestions
                </Typography>
              </Box>
              <Box>
                {position !== 'inline' && (
                  <IconButton
                    size="small"
                    onClick={() => setExpanded(!expanded)}
                    sx={{ mr: 1 }}
                  >
                    {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                )}
                <IconButton
                  size="small"
                  onClick={() => setVisible(false)}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </Box>

            <Collapse in={position === 'inline' ? true : expanded}>
              <Box>
                {suggestions.map((suggestion, index) => (
                  <Alert
                    key={suggestion.id}
                    severity={getPriorityColor(suggestion.priority)}
                    sx={{
                      mb: position === 'inline' ? 0.5 : 1,
                      '&:last-child': { mb: 0 },
                      fontSize: position === 'inline' ? '0.875rem' : '1rem'
                    }}
                    icon={<span style={{ fontSize: position === 'inline' ? '14px' : '16px' }}>{getTypeIcon(suggestion.type)}</span>}
                    action={
                      <Box display="flex" gap={0.5}>
                        <Tooltip title="Helpful">
                          <IconButton
                            size="small"
                            onClick={() => handleSuggestionFeedback(suggestion.id, 'helpful')}
                          >
                            <ThumbUpIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Not helpful">
                          <IconButton
                            size="small"
                            onClick={() => handleSuggestionFeedback(suggestion.id, 'not_helpful')}
                          >
                            <ThumbDownIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <Typography variant={position === 'inline' ? 'body2' : 'subtitle2'} fontWeight="bold">
                      {suggestion.title}
                    </Typography>
                    <Typography variant={position === 'inline' ? 'caption' : 'body2'}>
                      {suggestion.description}
                    </Typography>
                    {suggestion.action && (
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ mt: 0.5, fontSize: position === 'inline' ? '0.75rem' : '0.875rem' }}
                        onClick={() => handleApplySuggestion(suggestion)}
                      >
                        Apply Suggestion
                      </Button>
                    )}
                  </Alert>
                ))}
              </Box>
            </Collapse>
          </CardContent>
        </Card>
      </Box>
    </Fade>
  );
};

export default ContextualSuggestions;
