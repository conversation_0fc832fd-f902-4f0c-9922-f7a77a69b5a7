/**
 * Contextual Suggestions Component
 * Provides real-time AI suggestions based on user actions and context
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  <PERSON>lapse,
  Chip,
  Alert,
  <PERSON>ton,
  <PERSON>ade,
  Tooltip
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  Close as CloseIcon,
  ThumbUp as ThumbUpIcon,
  ThumbDown as ThumbDownIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Lightbulb as LightbulbIcon
} from '@mui/icons-material';
import api from '../../Services/ApiService';

const ContextualSuggestions = ({ 
  context, 
  actionType, 
  data = {}, 
  onSuggestionApplied,
  position = 'bottom-right' 
}) => {
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState(true);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (context && actionType) {
      generateSuggestions();
    }
  }, [context, actionType, data]);

  const generateSuggestions = async () => {
    try {
      setLoading(true);
      
      // Use Jarvis API for generating contextual suggestions
      const response = await api.post('/api/ai/message', {
        message: `Generate smart suggestions for ${actionType} in ${context}. Data: ${JSON.stringify(data)}`,
        context: {
          action: actionType,
          module: context,
          data: data
        }
      });

      // Parse suggestions from Jarvis response
      const aiResponse = response.data.response;
      const parsedSuggestions = parseAISuggestions(aiResponse, actionType, context);
      
      if (parsedSuggestions.length > 0) {
        setSuggestions(parsedSuggestions);
        setVisible(true);
      }
    } catch (error) {
      console.error('Error generating suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const parseAISuggestions = (aiResponse, actionType, context) => {
    const suggestions = [];

    // Parse AI response and extract actionable suggestions
    if (aiResponse && aiResponse.content) {
      const content = aiResponse.content.toLowerCase();

      // Leave request suggestions
      if (context === 'leave_request') {
        if (content.includes('vacation') || content.includes('holiday')) {
          suggestions.push({
            id: 'vacation_planning',
            title: 'Plan Your Vacation Wisely',
            description: 'Consider planning your vacation during less busy periods for better approval chances.',
            type: 'tip',
            priority: 'medium',
            action: 'check_calendar'
          });
        }
        
        if (content.includes('sick') || content.includes('medical')) {
          suggestions.push({
            id: 'medical_documentation',
            title: 'Medical Documentation',
            description: 'For medical leave, consider attaching relevant documentation to speed up approval.',
            type: 'reminder',
            priority: 'high',
            action: 'attach_document'
          });
        }

        if (actionType === 'create' && data.duration && data.duration > 5) {
          suggestions.push({
            id: 'long_leave_notice',
            title: 'Extended Leave Notice',
            description: 'For leaves longer than 5 days, consider giving at least 2 weeks notice.',
            type: 'best_practice',
            priority: 'medium',
            action: 'plan_ahead'
          });
        }
      }

      // Task management suggestions
      if (context === 'task_management') {
        if (actionType === 'create' && data.priority === 'High') {
          suggestions.push({
            id: 'high_priority_task',
            title: 'High Priority Task Alert',
            description: 'Consider setting a clear deadline and notifying the assignee immediately.',
            type: 'alert',
            priority: 'high',
            action: 'notify_assignee'
          });
        }

        if (data.deadline && new Date(data.deadline) < new Date(Date.now() + 24 * 60 * 60 * 1000)) {
          suggestions.push({
            id: 'urgent_deadline',
            title: 'Urgent Deadline',
            description: 'This task has a deadline within 24 hours. Consider immediate assignment.',
            type: 'warning',
            priority: 'high',
            action: 'immediate_action'
          });
        }
      }

      // Attendance suggestions
      if (context === 'attendance') {
        if (actionType === 'check_in' && new Date().getHours() > 9) {
          suggestions.push({
            id: 'late_checkin',
            title: 'Late Check-in Detected',
            description: 'You\'re checking in after 9 AM. Consider setting an earlier alarm.',
            type: 'tip',
            priority: 'low',
            action: 'set_reminder'
          });
        }

        if (actionType === 'check_out' && data.hoursWorked && data.hoursWorked > 8) {
          suggestions.push({
            id: 'overtime_alert',
            title: 'Overtime Detected',
            description: 'You\'ve worked more than 8 hours today. Great dedication!',
            type: 'recognition',
            priority: 'medium',
            action: 'track_overtime'
          });
        }
      }

      // User management suggestions
      if (context === 'user_management') {
        if (actionType === 'create') {
          suggestions.push({
            id: 'new_user_onboarding',
            title: 'New User Onboarding',
            description: 'Consider creating an onboarding checklist for the new user.',
            type: 'best_practice',
            priority: 'medium',
            action: 'create_checklist'
          });
        }
      }

      // Application review suggestions
      if (context === 'recruitment') {
        if (actionType === 'review' && data.status === 'pending') {
          suggestions.push({
            id: 'application_review_time',
            title: 'Timely Review',
            description: 'Consider reviewing applications within 48 hours for better candidate experience.',
            type: 'best_practice',
            priority: 'medium',
            action: 'schedule_review'
          });
        }
      }
    }

    return suggestions;
  };

  const handleSuggestionFeedback = async (suggestionId, feedback) => {
    try {
      // Track feedback for learning
      await api.post('/api/ai/feedback', {
        suggestionId,
        feedback,
        context,
        actionType
      });

      // Remove suggestion after feedback
      setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
      
      if (suggestions.length <= 1) {
        setVisible(false);
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  const handleApplySuggestion = (suggestion) => {
    if (onSuggestionApplied) {
      onSuggestionApplied(suggestion);
    }
    handleSuggestionFeedback(suggestion.id, 'applied');
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'primary';
    }
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'warning': return '⚠️';
      case 'tip': return '💡';
      case 'reminder': return '📝';
      case 'best_practice': return '✨';
      case 'alert': return '🚨';
      case 'recognition': return '🎉';
      default: return '💡';
    }
  };

  if (!visible || suggestions.length === 0) {
    return null;
  }

  const positionStyles = {
    'bottom-right': {
      position: 'fixed',
      bottom: 20,
      right: 20,
      zIndex: 1300,
      maxWidth: 400
    },
    'top-right': {
      position: 'fixed',
      top: 80,
      right: 20,
      zIndex: 1300,
      maxWidth: 400
    },
    'inline': {
      width: '100%',
      mb: 2
    }
  };

  return (
    <Fade in={visible}>
      <Box sx={positionStyles[position]}>
        <Card elevation={8} sx={{ 
          border: '2px solid',
          borderColor: 'primary.main',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
        }}>
          <CardContent sx={{ pb: 1 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
              <Box display="flex" alignItems="center" gap={1}>
                <AIIcon color="primary" />
                <Typography variant="h6" color="primary">
                  AI Suggestions
                </Typography>
              </Box>
              <Box>
                <IconButton 
                  size="small" 
                  onClick={() => setExpanded(!expanded)}
                  sx={{ mr: 1 }}
                >
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
                <IconButton 
                  size="small" 
                  onClick={() => setVisible(false)}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </Box>

            <Collapse in={expanded}>
              <Box>
                {suggestions.map((suggestion, index) => (
                  <Alert
                    key={suggestion.id}
                    severity={getPriorityColor(suggestion.priority)}
                    sx={{ mb: 1, '&:last-child': { mb: 0 } }}
                    icon={<span style={{ fontSize: '16px' }}>{getTypeIcon(suggestion.type)}</span>}
                    action={
                      <Box display="flex" gap={0.5}>
                        <Tooltip title="Helpful">
                          <IconButton
                            size="small"
                            onClick={() => handleSuggestionFeedback(suggestion.id, 'helpful')}
                          >
                            <ThumbUpIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Not helpful">
                          <IconButton
                            size="small"
                            onClick={() => handleSuggestionFeedback(suggestion.id, 'not_helpful')}
                          >
                            <ThumbDownIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <Typography variant="subtitle2" fontWeight="bold">
                      {suggestion.title}
                    </Typography>
                    <Typography variant="body2">
                      {suggestion.description}
                    </Typography>
                    {suggestion.action && (
                      <Button
                        size="small"
                        variant="outlined"
                        sx={{ mt: 1 }}
                        onClick={() => handleApplySuggestion(suggestion)}
                      >
                        Apply Suggestion
                      </Button>
                    )}
                  </Alert>
                ))}
              </Box>
            </Collapse>
          </CardContent>
        </Card>
      </Box>
    </Fade>
  );
};

export default ContextualSuggestions;
