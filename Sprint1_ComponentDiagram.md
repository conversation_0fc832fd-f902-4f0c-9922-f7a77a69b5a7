# Sprint 1: Component Diagram (UML)

```
@startuml HR Management System - Sprint 1 Component Diagram

skinparam componentStyle rectangle
skinparam componentBackgroundColor #f8f9fa
skinparam componentBorderColor #495057
skinparam databaseBackgroundColor #e9ecef
skinparam interfaceBackgroundColor #dee2e6

package "Frontend (React)" {
  [Login Page] as LoginPage
  [Admin Dashboard] as AdminDashboard
  [HR Dashboard] as HRDashboard
  [User Dashboard] as UserDashboard
  [User Management UI] as UserManagementUI
  [Profile Page] as ProfilePage
  [Password Reset Pages] as PasswordResetPages
  
  component "Shared Components" {
    [Navigation] as Navigation
    [Form Components] as FormComponents
    [Auth Provider] as AuthProvider
  }
  
  component "Services" {
    [API Service] as APIService
    [Auth Service] as FrontendAuthService
    [Storage Service] as StorageService
  }
}

package "Backend (Express)" {
  component "API Routes" {
    [Auth Routes] as AuthRoutes
    [Admin Routes] as AdminRoutes
    [HR Routes] as HRRoutes
    [User Routes] as UserRoutes
    [Forgot Password Routes] as ForgotPasswordRoutes
  }
  
  component "Controllers" {
    [Auth Controller] as AuthController
    [User Controller] as UserController
    [Password Controller] as PasswordController
    [Audit Controller] as AuditController
  }
  
  component "Middleware" {
    [Auth Middleware] as AuthMiddleware
    [Validation Middleware] as ValidationMiddleware
    [Error Handling Middleware] as ErrorHandlingMiddleware
  }
  
  component "Services" {
    [JWT Service] as JWTService
    [Password Service] as PasswordService
    [Email Service] as EmailService
    [Audit Service] as AuditService
  }
  
  component "Models" {
    [User Model] as UserModel
    [Login History Model] as LoginHistoryModel
    [Audit Log Model] as AuditLogModel
    [Password Reset Model] as PasswordResetModel
  }
}

database "MongoDB" {
  [Users Collection] as UsersCollection
  [Login History Collection] as LoginHistoryCollection
  [Audit Logs Collection] as AuditLogsCollection
  [Password Resets Collection] as PasswordResetsCollection
}

cloud "External Services" {
  [Email.js] as EmailJS
}

' Frontend internal relationships
LoginPage --> AuthProvider
AdminDashboard --> AuthProvider
HRDashboard --> AuthProvider
UserDashboard --> AuthProvider
UserManagementUI --> AuthProvider
ProfilePage --> AuthProvider
PasswordResetPages --> AuthProvider

LoginPage --> FormComponents
UserManagementUI --> FormComponents
ProfilePage --> FormComponents
PasswordResetPages --> FormComponents

AdminDashboard --> Navigation
HRDashboard --> Navigation
UserDashboard --> Navigation
ProfilePage --> Navigation

AuthProvider --> FrontendAuthService
FrontendAuthService --> APIService
APIService --> StorageService

' Backend internal relationships
AuthRoutes --> AuthController
AdminRoutes --> UserController
AdminRoutes --> AuditController
HRRoutes --> UserController
UserRoutes --> UserController
ForgotPasswordRoutes --> PasswordController

AuthController --> JWTService
AuthController --> PasswordService
AuthController --> AuditService
UserController --> AuditService
PasswordController --> PasswordService
PasswordController --> EmailService
PasswordController --> AuditService

AuthMiddleware --> JWTService
ValidationMiddleware --> ErrorHandlingMiddleware

JWTService --> UserModel
PasswordService --> UserModel
PasswordService --> PasswordResetModel
AuditService --> AuditLogModel
AuditService --> LoginHistoryModel

' Database relationships
UserModel --> UsersCollection
LoginHistoryModel --> LoginHistoryCollection
AuditLogModel --> AuditLogsCollection
PasswordResetModel --> PasswordResetsCollection

' External service relationships
EmailService --> EmailJS

' Frontend to Backend relationships
APIService --> AuthRoutes
APIService --> AdminRoutes
APIService --> HRRoutes
APIService --> UserRoutes
APIService --> ForgotPasswordRoutes

@enduml
```

## Component Diagram Description

The Component Diagram for Sprint 1 illustrates the high-level architecture of the HR Management System, showing how different components interact across the frontend, backend, database, and external services.

### Frontend (React)
- **Pages**: Login Page, Admin Dashboard, HR Dashboard, User Dashboard, User Management UI, Profile Page, and Password Reset Pages
- **Shared Components**: Navigation, Form Components, and Auth Provider for managing authentication state
- **Services**: API Service for backend communication, Auth Service for authentication logic, and Storage Service for local storage management

### Backend (Express)
- **API Routes**: Auth Routes, Admin Routes, HR Routes, User Routes, and Forgot Password Routes that define the API endpoints
- **Controllers**: Auth Controller, User Controller, Password Controller, and Audit Controller that handle request processing
- **Middleware**: Auth Middleware for authentication, Validation Middleware for input validation, and Error Handling Middleware
- **Services**: JWT Service, Password Service, Email Service, and Audit Service that implement business logic
- **Models**: User Model, Login History Model, Audit Log Model, and Password Reset Model that define data structures

### Database (MongoDB)
- Collections for Users, Login History, Audit Logs, and Password Resets

### External Services
- Email.js for sending emails

### Key Relationships
- Frontend components use the Auth Provider for authentication state
- Frontend services communicate with backend routes through the API Service
- Backend routes delegate to controllers for request handling
- Controllers use services to implement business logic
- Services interact with models to access and manipulate data
- Models connect to MongoDB collections for data persistence
- Email Service integrates with Email.js for sending notifications
