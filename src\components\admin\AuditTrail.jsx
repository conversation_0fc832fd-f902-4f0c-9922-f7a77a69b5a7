import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Info as InfoIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

// Action type colors
const actionColors = {
  USER_CREATE: 'primary',
  USER_UPDATE: 'info',
  USER_DELETE: 'error',
  USER_PASSWORD_CHANGE: 'warning',
  USER_ACTIVATE: 'success',
  USER_DEACTIVATE: 'error',
  USER_LOGIN: 'success',
  USER_LOGOUT: 'default',
  USER_ACCESS_DENIED: 'error',
  APPLICATION_STATUS_CHANGE: 'info',
  LEAVE_REQUEST_STATUS_CHANGE: 'info',
  TASK_STATUS_CHANGE: 'info',
  SYSTEM_SETTING_CHANGE: 'warning'
};

const AuditTrail = () => {
  const [auditLogs, setAuditLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalLogs, setTotalLogs] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    userId: '',
    action: '',
    resourceType: '',
    startDate: null,
    endDate: null
  });

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  // Action types for filter dropdown
  const actionTypes = [
    'USER_CREATE', 'USER_UPDATE', 'USER_DELETE', 'USER_PASSWORD_CHANGE',
    'USER_ACTIVATE', 'USER_DEACTIVATE', 'USER_LOGIN', 'USER_LOGOUT',
    'USER_ACCESS_DENIED', 'APPLICATION_STATUS_CHANGE', 'LEAVE_REQUEST_STATUS_CHANGE',
    'TASK_STATUS_CHANGE', 'SYSTEM_SETTING_CHANGE'
  ];

  // Resource types for filter dropdown
  const resourceTypes = [
    'USER', 'APPLICATION', 'LEAVE_REQUEST', 'JOB', 'TASK', 'REPORT', 'SYSTEM'
  ];

  // Fetch audit logs
  const fetchAuditLogs = async () => {
    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = {
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage,
        sortBy: 'timestamp',
        sortOrder: 'desc'
      };

      // Add search query if it exists
      if (searchQuery) params.search = searchQuery;

      // Add filters if they exist
      if (filters.userId) params.userId = filters.userId;
      if (filters.action) params.action = filters.action;
      if (filters.resourceType) params.resourceType = filters.resourceType;
      if (filters.startDate) params.startDate = filters.startDate.toISOString().split('T')[0];
      if (filters.endDate) params.endDate = filters.endDate.toISOString().split('T')[0];

      const response = await api.get('/admin/audit-logs', { params });

      setAuditLogs(response.data.logs);
      setTotalLogs(response.data.pagination.total);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      setError('Failed to fetch audit logs. Please try again later.');
      toast.error('Failed to fetch audit logs');
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchAuditLogs();
  }, [page, rowsPerPage]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Apply filters
  const applyFilters = () => {
    setPage(0); // Reset to first page
    fetchAuditLogs();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      userId: '',
      action: '',
      resourceType: '',
      startDate: null,
      endDate: null
    });
    setSearchQuery('');
    setPage(0);
    fetchAuditLogs();
  };

  // Handle search
  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Debounce search to avoid too many API calls
    if (window.searchTimeout) {
      clearTimeout(window.searchTimeout);
    }

    window.searchTimeout = setTimeout(() => {
      setPage(0); // Reset to first page
      fetchAuditLogs();
    }, 500);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // View log details
  const handleViewLogDetails = (log) => {
    setSelectedLog(log);
  };

  // Close log details
  const handleCloseLogDetails = () => {
    setSelectedLog(null);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Typography variant="h5" component="h2" fontWeight={600}>
          Audit Trail
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            placeholder="Search audit logs..."
            size="small"
            value={searchQuery}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <SearchIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />,
              endAdornment: searchQuery ? (
                <IconButton size="small" onClick={() => { setSearchQuery(''); fetchAuditLogs(); }}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              ) : null
            }}
            sx={{ width: { xs: '100%', sm: '250px' } }}
          />
          <Button
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ ml: { xs: 0, sm: 1 } }}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
          <Button
            startIcon={<RefreshIcon />}
            onClick={fetchAuditLogs}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="User ID"
                fullWidth
                value={filters.userId}
                onChange={(e) => handleFilterChange('userId', e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Action</InputLabel>
                <Select
                  value={filters.action}
                  label="Action"
                  onChange={(e) => handleFilterChange('action', e.target.value)}
                >
                  <MenuItem value="">All Actions</MenuItem>
                  {actionTypes.map(action => (
                    <MenuItem key={action} value={action}>{action}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Resource Type</InputLabel>
                <Select
                  value={filters.resourceType}
                  label="Resource Type"
                  onChange={(e) => handleFilterChange('resourceType', e.target.value)}
                >
                  <MenuItem value="">All Resources</MenuItem>
                  {resourceTypes.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  value={filters.startDate}
                  onChange={(date) => handleFilterChange('startDate', date)}
                  renderInput={(params) => <TextField {...params} fullWidth size="small" />}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="End Date"
                  value={filters.endDate}
                  onChange={(date) => handleFilterChange('endDate', date)}
                  renderInput={(params) => <TextField {...params} fullWidth size="small" />}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={applyFilters}
                >
                  Apply Filters
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<ClearIcon />}
                  onClick={resetFilters}
                >
                  Reset
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : auditLogs.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography>No audit logs found</Typography>
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Timestamp</TableCell>
                    <TableCell>User</TableCell>
                    <TableCell>Action</TableCell>
                    <TableCell>Resource</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>IP Address</TableCell>
                    <TableCell>Details</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {auditLogs.map((log) => (
                    <TableRow key={log._id} hover>
                      <TableCell>{formatDate(log.timestamp)}</TableCell>
                      <TableCell>
                        {log.userInfo?.name || 'Unknown'}
                        <Typography variant="caption" display="block" color="text.secondary">
                          {log.userInfo?.email || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={log.action}
                          size="small"
                          color={actionColors[log.action] || 'default'}
                        />
                      </TableCell>
                      <TableCell>{log.resourceType}</TableCell>
                      <TableCell>{log.description}</TableCell>
                      <TableCell>{log.ipAddress || 'N/A'}</TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small" onClick={() => handleViewLogDetails(log)}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={totalLogs}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Log Details Dialog */}
      {selectedLog && (
        <Dialog open={!!selectedLog} onClose={handleCloseLogDetails} maxWidth="md" fullWidth>
          <DialogTitle>
            Audit Log Details
            <IconButton
              aria-label="close"
              onClick={handleCloseLogDetails}
              sx={{ position: 'absolute', right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">Timestamp</Typography>
                <Typography variant="body1">{formatDate(selectedLog.timestamp)}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">User</Typography>
                <Typography variant="body1">
                  {selectedLog.userInfo?.name || 'Unknown'} ({selectedLog.userInfo?.email || 'N/A'})
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Role: {selectedLog.userInfo?.role || 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">Action</Typography>
                <Chip
                  label={selectedLog.action}
                  color={actionColors[selectedLog.action] || 'default'}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">Resource Type</Typography>
                <Typography variant="body1">{selectedLog.resourceType}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                <Typography variant="body1">{selectedLog.description}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">IP Address</Typography>
                <Typography variant="body1">{selectedLog.ipAddress || 'N/A'}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">User Agent</Typography>
                <Typography variant="body1" sx={{ wordBreak: 'break-word' }}>
                  {selectedLog.userAgent || 'N/A'}
                </Typography>
              </Grid>

              {(selectedLog.previousState || selectedLog.newState) && (
                <>
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="subtitle1" fontWeight={600} sx={{ mt: 2 }}>
                      Changes
                    </Typography>
                  </Grid>
                  {selectedLog.previousState && (
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Previous State</Typography>
                      <pre style={{
                        backgroundColor: '#f5f5f5',
                        padding: '8px',
                        borderRadius: '4px',
                        overflow: 'auto',
                        maxHeight: '200px'
                      }}>
                        {JSON.stringify(selectedLog.previousState, null, 2)}
                      </pre>
                    </Grid>
                  )}
                  {selectedLog.newState && (
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">New State</Typography>
                      <pre style={{
                        backgroundColor: '#f5f5f5',
                        padding: '8px',
                        borderRadius: '4px',
                        overflow: 'auto',
                        maxHeight: '200px'
                      }}>
                        {JSON.stringify(selectedLog.newState, null, 2)}
                      </pre>
                    </Grid>
                  )}
                </>
              )}
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseLogDetails}>Close</Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default AuditTrail;
