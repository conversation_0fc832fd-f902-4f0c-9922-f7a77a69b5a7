/**
 * Simple Mock Server for HR Management System
 * Run with: node mock-server.js
 */

const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 5001;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
const mockUsers = [
  { _id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'user', job: 'Developer', department: 'IT' },
  { _id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'user', job: 'Designer', department: 'Design' },
  { _id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'hr', job: 'HR Manager', department: 'HR' }
];

const mockTasks = [
  { _id: '1', title: 'Complete Project A', category: 'Development', priority: 'High', status: 'In Progress', assignedTo: '1', createdAt: new Date().toISOString() },
  { _id: '2', title: 'Design Review', category: 'Design', priority: 'Medium', status: 'Not Started', assignedTo: null, createdAt: new Date().toISOString() }
];

const mockEvaluations = [
  { _id: '1', userId: '1', overallRating: 4, performanceRating: 4, attitudeRating: 5, communicationRating: 4, teamworkRating: 4, initiativeRating: 4, createdAt: new Date().toISOString() }
];

const mockAttendance = [
  { _id: '1', userId: '1', date: new Date().toISOString(), checkIn: new Date().toISOString(), checkOut: null }
];

// Routes
app.get('/api/hr/users', (req, res) => {
  res.json(mockUsers);
});

app.get('/api/tasks/hr', (req, res) => {
  res.json(mockTasks);
});

app.get('/api/hr/evaluations', (req, res) => {
  res.json(mockEvaluations);
});

app.get('/api/normaluser/attendance', (req, res) => {
  res.json(mockAttendance);
});

// AI Feedback endpoint
app.post('/api/ai/feedback', (req, res) => {
  console.log('AI Feedback received:', req.body);
  res.json({ success: true, message: 'Feedback recorded' });
});

// Task creation
app.post('/api/tasks/hr', (req, res) => {
  const newTask = {
    _id: Date.now().toString(),
    ...req.body,
    createdAt: new Date().toISOString()
  };
  mockTasks.push(newTask);
  res.status(201).json(newTask);
});

// Task update
app.put('/api/tasks/hr/:id', (req, res) => {
  const taskId = req.params.id;
  const taskIndex = mockTasks.findIndex(t => t._id === taskId);
  if (taskIndex !== -1) {
    mockTasks[taskIndex] = { ...mockTasks[taskIndex], ...req.body };
    res.json(mockTasks[taskIndex]);
  } else {
    res.status(404).json({ error: 'Task not found' });
  }
});

// Catch all for missing routes
app.use('*', (req, res) => {
  console.log(`Missing route: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ error: 'Route not found', path: req.originalUrl });
});

app.listen(PORT, () => {
  console.log(`🚀 Mock server running on http://localhost:${PORT}`);
  console.log(`📊 Available endpoints:`);
  console.log(`   GET  /api/hr/users`);
  console.log(`   GET  /api/tasks/hr`);
  console.log(`   GET  /api/hr/evaluations`);
  console.log(`   GET  /api/normaluser/attendance`);
  console.log(`   POST /api/ai/feedback`);
  console.log(`   POST /api/tasks/hr`);
  console.log(`   PUT  /api/tasks/hr/:id`);
});
