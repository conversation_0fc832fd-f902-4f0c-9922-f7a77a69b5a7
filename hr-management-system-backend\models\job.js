const mongoose = require('mongoose');

const jobSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  responsibilities: {
    type: [String],
    required: true
  },
  requirements: {
    type: [String],
    required: true
  },
  academicLevel: {
    type: String,
    enum: ['Bachelor', 'Engineer', 'Master', 'PhD', 'Other'],
    default: 'Bachelor'
  },
  location: {
    type: String,
    default: 'Remote'
  },
  jobType: {
    type: String,
    enum: ['Internship', 'Full-Time', 'Part-Time', 'Contract'],
    default: 'Internship'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date,
    default: null
  }
});

// Check if the model already exists to prevent recompilation
const Job = mongoose.models.Job || mongoose.model('Job', jobSchema);

module.exports = Job;
