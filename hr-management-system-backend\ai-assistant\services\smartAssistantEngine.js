/**
 * Smart Assistant Engine - Advanced Behavioral AI for HR Management
 * Provides proactive, humanized, and context-aware suggestions
 */

const User = require('../../models/user');
const Task = require('../../models/Task');
const LeaveRequest = require('../../models/LeaveRequest');
const Job = require('../../models/job');
const Application = require('../../models/application');
const AuditLog = require('../../models/AuditLog');
const AIInsight = require('../../models/AIInsight');

class SmartAssistantEngine {
  constructor() {
    this.behaviorPatterns = new Map();
    this.userPreferences = new Map();
    this.suggestionHistory = new Map();
    this.messageVariations = this.initializeMessageVariations();
    this.patternThresholds = {
      frequentEdits: 3,
      delayedTasks: 2,
      repeatedActions: 4,
      timeWindow: 7 * 24 * 60 * 60 * 1000 // 7 days
    };
    
    console.log('🧠 Smart Assistant Engine initialized with behavioral awareness');
  }

  /**
   * Initialize message variations for humanized responses
   */
  initializeMessageVariations() {
    return {
      greetings: [
        "Hey there! I noticed something interesting...",
        "I've been keeping an eye on things and thought you might want to know...",
        "Quick heads up from your AI assistant...",
        "I spotted a pattern that might be worth your attention...",
        "Hope you're having a great day! I have a suggestion..."
      ],
      
      userProfileUpdates: [
        "You've been quite busy updating user profiles lately! Would you like me to help review pending evaluations?",
        "I see you've updated several employee records recently. How about we check if any evaluations need attention?",
        "Looks like you're on a profile-updating spree! Don't forget about those pending performance reviews.",
        "You've made {count} profile updates this week. Perfect time to sync up evaluations, don't you think?"
      ],
      
      jobPostings: [
        "This job posting hasn't gotten much love lately. Maybe it's time to jazz up the description?",
        "Your '{jobTitle}' position has been quiet for {days} days. Want to try a different approach?",
        "I noticed the '{jobTitle}' post isn't getting applications. How about we brainstorm some improvements?",
        "That job posting might need a refresh - it's been {days} days without applications."
      ],
      
      taskReminders: [
        "Just a friendly reminder - you often forget attendance updates after assigning tasks. Want me to set a reminder?",
        "I've noticed a pattern: task assignments sometimes skip attendance logging. Shall we fix that?",
        "Your task assignment workflow is great, but attendance updates tend to slip through. Need a hand?",
        "Based on your habits, you might want to check attendance after those recent task assignments."
      ],
      
      encouragement: [
        "You're doing amazing work managing the team!",
        "Your attention to detail is really paying off.",
        "The team is lucky to have such a dedicated HR professional.",
        "Keep up the excellent work - the patterns show real improvement!"
      ]
    };
  }

  /**
   * Analyze user behavior patterns from audit logs
   */
  async analyzeBehaviorPatterns(userId, timeWindow = null) {
    try {
      const window = timeWindow || this.patternThresholds.timeWindow;
      const startDate = new Date(Date.now() - window);
      
      // Get recent audit logs for the user
      const auditLogs = await AuditLog.find({
        userId,
        timestamp: { $gte: startDate }
      }).sort({ timestamp: -1 });

      // Analyze patterns
      const patterns = {
        frequentUserEdits: this.detectFrequentUserEdits(auditLogs),
        delayedTaskAssignments: this.detectDelayedTaskPatterns(auditLogs),
        repeatedLeaveRequests: this.detectRepeatedLeavePatterns(auditLogs),
        jobPostingStagnation: await this.detectJobPostingIssues(userId),
        attendanceOversights: this.detectAttendanceOversights(auditLogs),
        workflowEfficiency: this.analyzeWorkflowEfficiency(auditLogs)
      };

      // Store patterns for future reference
      this.behaviorPatterns.set(userId, {
        patterns,
        lastAnalyzed: new Date(),
        confidence: this.calculatePatternConfidence(patterns)
      });

      return patterns;
    } catch (error) {
      console.error('Error analyzing behavior patterns:', error);
      return {};
    }
  }

  /**
   * Detect frequent user profile editing patterns
   */
  detectFrequentUserEdits(auditLogs) {
    const userUpdates = auditLogs.filter(log => 
      log.action === 'USER_UPDATE' && 
      log.resourceType === 'USER'
    );

    if (userUpdates.length >= this.patternThresholds.frequentEdits) {
      return {
        detected: true,
        count: userUpdates.length,
        lastUpdate: userUpdates[0]?.timestamp,
        confidence: Math.min(userUpdates.length / 10, 1),
        suggestion: 'review_evaluations'
      };
    }

    return { detected: false };
  }

  /**
   * Detect delayed task assignment patterns
   */
  detectDelayedTaskPatterns(auditLogs) {
    const taskCreations = auditLogs.filter(log => 
      log.action === 'TASK_CREATE'
    );

    // Check for tasks created but not assigned quickly
    const delayedTasks = taskCreations.filter(task => {
      const timeSinceCreation = Date.now() - new Date(task.timestamp).getTime();
      return timeSinceCreation > 24 * 60 * 60 * 1000; // 24 hours
    });

    if (delayedTasks.length >= this.patternThresholds.delayedTasks) {
      return {
        detected: true,
        count: delayedTasks.length,
        confidence: 0.8,
        suggestion: 'expedite_assignments'
      };
    }

    return { detected: false };
  }

  /**
   * Detect repeated leave request patterns
   */
  detectRepeatedLeavePatterns(auditLogs) {
    const leaveRequests = auditLogs.filter(log => 
      log.action === 'LEAVE_REQUEST_CREATE'
    );

    // Group by user to find repeated requests
    const userLeaveCount = {};
    leaveRequests.forEach(request => {
      const targetUser = request.resourceId;
      userLeaveCount[targetUser] = (userLeaveCount[targetUser] || 0) + 1;
    });

    const repeatedUsers = Object.entries(userLeaveCount)
      .filter(([userId, count]) => count >= 3)
      .map(([userId, count]) => ({ userId, count }));

    if (repeatedUsers.length > 0) {
      return {
        detected: true,
        users: repeatedUsers,
        confidence: 0.9,
        suggestion: 'investigate_leave_patterns'
      };
    }

    return { detected: false };
  }

  /**
   * Detect job posting stagnation
   */
  async detectJobPostingIssues(userId) {
    try {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      // Find jobs posted more than a week ago with no applications
      const stagnantJobs = await Job.aggregate([
        {
          $match: {
            createdAt: { $lte: oneWeekAgo },
            isActive: true
          }
        },
        {
          $lookup: {
            from: 'applications',
            localField: '_id',
            foreignField: 'jobId',
            as: 'applications'
          }
        },
        {
          $match: {
            'applications.0': { $exists: false }
          }
        }
      ]);

      if (stagnantJobs.length > 0) {
        return {
          detected: true,
          jobs: stagnantJobs,
          confidence: 0.95,
          suggestion: 'improve_job_descriptions'
        };
      }

      return { detected: false };
    } catch (error) {
      console.error('Error detecting job posting issues:', error);
      return { detected: false };
    }
  }

  /**
   * Detect attendance oversight patterns
   */
  detectAttendanceOversights(auditLogs) {
    const taskAssignments = auditLogs.filter(log => 
      log.action === 'TASK_CREATE' || log.action === 'TASK_UPDATE'
    );

    const attendanceUpdates = auditLogs.filter(log => 
      log.resourceType === 'ATTENDANCE'
    );

    // Check if task assignments are followed by attendance updates
    let missedAttendanceCount = 0;
    
    taskAssignments.forEach(task => {
      const taskTime = new Date(task.timestamp).getTime();
      const hasFollowUpAttendance = attendanceUpdates.some(attendance => {
        const attendanceTime = new Date(attendance.timestamp).getTime();
        return attendanceTime > taskTime && (attendanceTime - taskTime) < 2 * 60 * 60 * 1000; // 2 hours
      });

      if (!hasFollowUpAttendance) {
        missedAttendanceCount++;
      }
    });

    if (missedAttendanceCount >= 2) {
      return {
        detected: true,
        missedCount: missedAttendanceCount,
        confidence: 0.7,
        suggestion: 'attendance_reminder'
      };
    }

    return { detected: false };
  }

  /**
   * Analyze overall workflow efficiency
   */
  analyzeWorkflowEfficiency(auditLogs) {
    const actionCounts = {};
    const timeSpans = [];

    auditLogs.forEach((log, index) => {
      actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
      
      if (index > 0) {
        const timeDiff = new Date(auditLogs[index - 1].timestamp) - new Date(log.timestamp);
        timeSpans.push(timeDiff);
      }
    });

    const avgTimeSpan = timeSpans.length > 0 ? 
      timeSpans.reduce((a, b) => a + b, 0) / timeSpans.length : 0;

    return {
      actionCounts,
      averageTimeBetweenActions: avgTimeSpan,
      totalActions: auditLogs.length,
      efficiency: this.calculateEfficiencyScore(actionCounts, avgTimeSpan)
    };
  }

  /**
   * Calculate pattern confidence score
   */
  calculatePatternConfidence(patterns) {
    const detectedPatterns = Object.values(patterns).filter(p => p.detected);
    if (detectedPatterns.length === 0) return 0;

    const avgConfidence = detectedPatterns.reduce((sum, pattern) => 
      sum + (pattern.confidence || 0), 0) / detectedPatterns.length;

    return Math.round(avgConfidence * 100) / 100;
  }

  /**
   * Calculate workflow efficiency score
   */
  calculateEfficiencyScore(actionCounts, avgTimeSpan) {
    const totalActions = Object.values(actionCounts).reduce((a, b) => a + b, 0);
    const uniqueActions = Object.keys(actionCounts).length;

    // Higher variety and faster execution = higher efficiency
    const varietyScore = Math.min(uniqueActions / 10, 1);
    const speedScore = avgTimeSpan > 0 ? Math.min(3600000 / avgTimeSpan, 1) : 0; // 1 hour baseline

    return Math.round((varietyScore * 0.4 + speedScore * 0.6) * 100) / 100;
  }

  /**
   * Generate proactive suggestions based on patterns
   */
  async generateProactiveSuggestions(userId, patterns) {
    const suggestions = [];
    const userPrefs = this.getUserPreferences(userId);

    // User profile update suggestions
    if (patterns.frequentUserEdits?.detected) {
      const message = this.getRandomMessage('userProfileUpdates')
        .replace('{count}', patterns.frequentUserEdits.count);

      suggestions.push({
        type: 'proactive',
        category: 'user_management',
        priority: 'medium',
        title: 'Review Pending Evaluations',
        message,
        action: 'review_evaluations',
        confidence: patterns.frequentUserEdits.confidence,
        metadata: {
          userUpdateCount: patterns.frequentUserEdits.count,
          lastUpdate: patterns.frequentUserEdits.lastUpdate
        }
      });
    }

    // Job posting suggestions
    if (patterns.jobPostingStagnation?.detected) {
      for (const job of patterns.jobPostingStagnation.jobs) {
        const daysSincePosted = Math.floor(
          (Date.now() - new Date(job.createdAt).getTime()) / (24 * 60 * 60 * 1000)
        );

        const message = this.getRandomMessage('jobPostings')
          .replace('{jobTitle}', job.title)
          .replace('{days}', daysSincePosted);

        suggestions.push({
          type: 'proactive',
          category: 'recruitment',
          priority: 'high',
          title: 'Job Posting Needs Attention',
          message,
          action: 'improve_job_posting',
          confidence: 0.9,
          metadata: {
            jobId: job._id,
            jobTitle: job.title,
            daysSincePosted
          }
        });
      }
    }

    // Attendance reminder suggestions
    if (patterns.attendanceOversights?.detected) {
      const message = this.getRandomMessage('taskReminders');

      suggestions.push({
        type: 'proactive',
        category: 'workflow',
        priority: 'low',
        title: 'Attendance Update Reminder',
        message,
        action: 'set_attendance_reminder',
        confidence: patterns.attendanceOversights.confidence,
        metadata: {
          missedCount: patterns.attendanceOversights.missedCount
        }
      });
    }

    // Leave pattern suggestions
    if (patterns.repeatedLeaveRequests?.detected) {
      suggestions.push({
        type: 'proactive',
        category: 'leave_management',
        priority: 'medium',
        title: 'Unusual Leave Patterns Detected',
        message: `I've noticed some employees have submitted multiple leave requests recently. Might be worth checking if there's an underlying issue or if they need additional support.`,
        action: 'investigate_leave_patterns',
        confidence: patterns.repeatedLeaveRequests.confidence,
        metadata: {
          affectedUsers: patterns.repeatedLeaveRequests.users
        }
      });
    }

    // Workflow efficiency suggestions
    if (patterns.workflowEfficiency?.efficiency < 0.6) {
      suggestions.push({
        type: 'proactive',
        category: 'productivity',
        priority: 'medium',
        title: 'Workflow Optimization Opportunity',
        message: `Your workflow efficiency score is ${Math.round(patterns.workflowEfficiency.efficiency * 100)}%. I can help you streamline some processes to save time and reduce repetitive tasks.`,
        action: 'optimize_workflow',
        confidence: 0.8,
        metadata: {
          efficiencyScore: patterns.workflowEfficiency.efficiency,
          totalActions: patterns.workflowEfficiency.totalActions
        }
      });
    }

    // Filter suggestions based on user preferences
    return this.filterSuggestionsByPreferences(suggestions, userPrefs);
  }

  /**
   * Get random message variation for humanized responses
   */
  getRandomMessage(category) {
    const messages = this.messageVariations[category] || ['I have a suggestion for you.'];
    return messages[Math.floor(Math.random() * messages.length)];
  }

  /**
   * Get user preferences for suggestion filtering
   */
  getUserPreferences(userId) {
    if (!this.userPreferences.has(userId)) {
      this.userPreferences.set(userId, {
        frequency: 'normal', // low, normal, high
        categories: ['all'], // or specific categories
        tone: 'professional', // casual, professional, friendly
        dismissedSuggestions: [],
        lastInteraction: null
      });
    }
    return this.userPreferences.get(userId);
  }

  /**
   * Filter suggestions based on user preferences
   */
  filterSuggestionsByPreferences(suggestions, preferences) {
    let filtered = suggestions;

    // Filter by frequency preference
    if (preferences.frequency === 'low') {
      filtered = filtered.filter(s => s.priority === 'high');
    } else if (preferences.frequency === 'high') {
      // Include all suggestions
    } else {
      // Normal: exclude low priority unless high confidence
      filtered = filtered.filter(s =>
        s.priority !== 'low' || s.confidence > 0.8
      );
    }

    // Filter by categories
    if (!preferences.categories.includes('all')) {
      filtered = filtered.filter(s =>
        preferences.categories.includes(s.category)
      );
    }

    // Filter out recently dismissed suggestions
    const recentDismissals = preferences.dismissedSuggestions
      .filter(d => Date.now() - d.timestamp < 24 * 60 * 60 * 1000); // 24 hours

    filtered = filtered.filter(s =>
      !recentDismissals.some(d => d.action === s.action)
    );

    return filtered;
  }

  /**
   * Store suggestion feedback for learning
   */
  async storeSuggestionFeedback(userId, suggestionId, feedback) {
    try {
      const insight = await AIInsight.findById(suggestionId);
      if (insight) {
        await insight.addFeedback({
          rating: feedback.rating, // thumbs up/down
          comment: feedback.comment,
          userId
        });

        // Update user preferences based on feedback
        this.updateUserPreferencesFromFeedback(userId, insight, feedback);
      }
    } catch (error) {
      console.error('Error storing suggestion feedback:', error);
    }
  }

  /**
   * Update user preferences based on feedback
   */
  updateUserPreferencesFromFeedback(userId, insight, feedback) {
    const preferences = this.getUserPreferences(userId);

    if (feedback.rating === 'down') {
      // Add to dismissed suggestions
      preferences.dismissedSuggestions.push({
        action: insight.actionType,
        category: insight.category,
        timestamp: Date.now()
      });

      // Adjust frequency if user consistently dismisses
      const recentDismissals = preferences.dismissedSuggestions
        .filter(d => Date.now() - d.timestamp < 7 * 24 * 60 * 60 * 1000);

      if (recentDismissals.length > 5) {
        preferences.frequency = 'low';
      }
    } else if (feedback.rating === 'up') {
      // User likes suggestions, maybe increase frequency
      if (preferences.frequency === 'low') {
        preferences.frequency = 'normal';
      }
    }

    this.userPreferences.set(userId, preferences);
  }

  /**
   * Create AI insights from suggestions
   */
  async createAIInsights(userId, suggestions) {
    const insights = [];

    for (const suggestion of suggestions) {
      try {
        const insight = new AIInsight({
          userId,
          type: 'proactive_suggestion',
          category: suggestion.category,
          title: suggestion.title,
          description: suggestion.message,
          actionType: suggestion.action,
          priority: suggestion.priority,
          confidence: suggestion.confidence,
          metadata: suggestion.metadata,
          status: 'active'
        });

        await insight.save();
        insights.push(insight);
      } catch (error) {
        console.error('Error creating AI insight:', error);
      }
    }

    return insights;
  }

  /**
   * Get personalized tone based on user preferences and history
   */
  getPersonalizedTone(userId, baseMessage) {
    const preferences = this.getUserPreferences(userId);
    const tone = preferences.tone || 'professional';

    const toneModifiers = {
      casual: {
        greetings: ['Hey!', 'Hi there!', 'What\'s up?'],
        endings: ['Cheers!', 'Talk soon!', 'Take care!'],
        style: 'relaxed'
      },
      professional: {
        greetings: ['Good day!', 'I hope this finds you well.', 'I wanted to reach out...'],
        endings: ['Best regards,', 'Kind regards,', 'Thank you for your attention.'],
        style: 'formal'
      },
      friendly: {
        greetings: ['Hope you\'re having a great day!', 'I\'ve got something interesting...', 'Quick friendly update...'],
        endings: ['Have a wonderful day!', 'Looking forward to helping!', 'Always here to assist!'],
        style: 'warm'
      }
    };

    const modifier = toneModifiers[tone] || toneModifiers.professional;

    // Add greeting if message doesn't start with one
    if (!baseMessage.match(/^(hey|hi|hello|good|hope)/i)) {
      const greeting = modifier.greetings[Math.floor(Math.random() * modifier.greetings.length)];
      return `${greeting} ${baseMessage}`;
    }

    return baseMessage;
  }

  /**
   * Process CRUD operation for behavioral analysis
   */
  async processCRUDOperation(userId, operation, resourceType, resourceId, metadata = {}) {
    try {
      // Log the operation for pattern analysis
      const auditLog = new AuditLog({
        userId,
        action: operation,
        resourceType,
        resourceId,
        description: `${operation} performed on ${resourceType}`,
        metadata,
        timestamp: new Date()
      });

      await auditLog.save();

      // Trigger real-time pattern analysis for high-frequency operations
      if (this.shouldTriggerRealTimeAnalysis(operation)) {
        setTimeout(() => {
          this.analyzeAndSuggest(userId);
        }, 5000); // 5-second delay to allow for completion
      }

      return auditLog;
    } catch (error) {
      console.error('Error processing CRUD operation:', error);
    }
  }

  /**
   * Determine if operation should trigger real-time analysis
   */
  shouldTriggerRealTimeAnalysis(operation) {
    const triggerOperations = [
      'USER_UPDATE',
      'TASK_CREATE',
      'LEAVE_REQUEST_CREATE',
      'JOB_CREATE'
    ];

    return triggerOperations.includes(operation);
  }

  /**
   * Main method to analyze patterns and generate suggestions
   */
  async analyzeAndSuggest(userId) {
    try {
      console.log(`🔍 Analyzing patterns for user ${userId}...`);

      // Analyze behavior patterns
      const patterns = await this.analyzeBehaviorPatterns(userId);

      // Generate proactive suggestions
      const suggestions = await this.generateProactiveSuggestions(userId, patterns);

      if (suggestions.length > 0) {
        console.log(`💡 Generated ${suggestions.length} suggestions for user ${userId}`);

        // Create AI insights in database
        const insights = await this.createAIInsights(userId, suggestions);

        // Return suggestions for immediate use
        return {
          patterns,
          suggestions: insights,
          confidence: this.calculatePatternConfidence(patterns)
        };
      }

      return { patterns, suggestions: [], confidence: 0 };
    } catch (error) {
      console.error('Error in analyzeAndSuggest:', error);
      return { patterns: {}, suggestions: [], confidence: 0 };
    }
  }

  /**
   * Get analytics data for user dashboard
   */
  async getAnalyticsData(userId) {
    try {
      const patterns = this.behaviorPatterns.get(userId) || { patterns: {} };
      const preferences = this.getUserPreferences(userId);

      // Get recent insights
      const recentInsights = await AIInsight.find({
        userId,
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // 30 days
      }).sort({ createdAt: -1 }).limit(10);

      // Calculate statistics
      const totalSuggestions = recentInsights.length;
      const acceptedSuggestions = recentInsights.filter(i => i.status === 'acted_upon').length;
      const dismissedSuggestions = recentInsights.filter(i => i.status === 'dismissed').length;
      const acceptanceRate = totalSuggestions > 0 ? (acceptedSuggestions / totalSuggestions) * 100 : 0;

      return {
        patterns: patterns.patterns,
        preferences,
        statistics: {
          totalSuggestions,
          acceptedSuggestions,
          dismissedSuggestions,
          acceptanceRate: Math.round(acceptanceRate),
          lastAnalyzed: patterns.lastAnalyzed
        },
        recentInsights: recentInsights.map(insight => ({
          id: insight._id,
          title: insight.title,
          category: insight.category,
          status: insight.status,
          createdAt: insight.createdAt,
          confidence: insight.confidence
        }))
      };
    } catch (error) {
      console.error('Error getting analytics data:', error);
      return null;
    }
  }

  /**
   * Update user preferences
   */
  updateUserPreferences(userId, newPreferences) {
    const currentPrefs = this.getUserPreferences(userId);
    const updatedPrefs = { ...currentPrefs, ...newPreferences };
    this.userPreferences.set(userId, updatedPrefs);
    return updatedPrefs;
  }
}

module.exports = new SmartAssistantEngine();
