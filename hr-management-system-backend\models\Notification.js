const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  // User who will receive the notification
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Type of notification
  type: {
    type: String,
    required: true,
    enum: [
      'TASK_ASSIGNED',
      'TASK_UPDATED',
      'TASK_COMPLETED',
      'TASK_DEADLINE_APPROACHING',
      'LEAVE_REQUEST_SUBMITTED',
      'LEAVE_REQUEST_APPROVED',
      'LEAVE_REQUEST_REJECTED',
      'NEW_APPLICATION',
      'APPLICATION_STATUS_CHANGED',
      'EVALUATION_CREATED',
      'EVALUATION_UPDATED',
      'NEW_USER',
      'ATTENDANCE_RECORDED'
    ]
  },

  // Title of the notification
  title: {
    type: String,
    required: true
  },

  // Content/message of the notification
  content: {
    type: String,
    required: true
  },

  // Related resource type (e.g., 'TASK', 'LEAVE_REQUEST', 'APPLICATION')
  resourceType: {
    type: String,
    required: true,
    enum: [
      'TASK',
      'LEAVE_REQUEST',
      'APPLICATION',
      'EVALUATION',
      'USER',
      'ATTENDANCE',
      'SYSTEM'
    ]
  },

  // Related resource ID
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    required: false // Not required for system-level notifications
  },



  // Additional data related to the notification (stored as JSON)
  data: {
    type: Object,
    default: {}
  },

  // Creation timestamp
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create indexes for better query performance
notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ resourceType: 1, resourceId: 1 });

// Virtual for checking if notification is recent (less than 24 hours old)
notificationSchema.virtual('isRecent').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diff = now - created;
  return diff < 24 * 60 * 60 * 1000; // 24 hours in milliseconds
});

// Check if the model already exists to prevent recompilation
const Notification = mongoose.models.Notification || mongoose.model('Notification', notificationSchema);

module.exports = Notification;
