const axios = require('axios');

async function testSimpleAttendance() {
  try {
    console.log('🔍 Testing Simple Attendance...\n');

    // Login
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });
    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Test check-in
    console.log('\n2. Testing check-in...');
    const checkinResponse = await axios.post('http://localhost:5000/api/ai/chat/message', {
      message: 'I want to check in for work'
    }, {
      headers: { 'Authorization': `Bearer ${token}` },
      timeout: 10000
    });

    console.log('🤖 Response:', checkinResponse.data.data.assistantMessage.content.substring(0, 100) + '...');
    console.log('🎯 Intent:', checkinResponse.data.data.classification.intent);
    console.log('⚡ Confidence:', Math.round(checkinResponse.data.data.classification.confidence * 100) + '%');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testSimpleAttendance();
