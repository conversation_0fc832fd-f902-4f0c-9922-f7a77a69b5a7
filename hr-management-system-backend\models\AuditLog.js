const mongoose = require('mongoose');

const auditLogSchema = new mongoose.Schema({
  // User who performed the action
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // User information at the time of the action
  userInfo: {
    name: String,
    email: String,
    role: String
  },
  
  // Action performed
  action: {
    type: String,
    required: true,
    enum: [
      // User management actions
      'USER_CREATE', 
      'USER_UPDATE', 
      'USER_DELETE',
      'USER_PASSWORD_CHANGE',
      'USER_ACTIVATE',
      'USER_DEACTIVATE',
      
      // Authentication actions
      'USER_LOGIN', 
      'USER_LOGOUT',
      'PASSWORD_RESET_REQUEST',
      'PASSWORD_RESET_COMPLETE',
      
      // HR actions
      'APPLICATION_CREATE',
      'APPLICATION_UPDATE',
      'APPLICATION_DELETE',
      'APPLICATION_STATUS_CHANGE',
      
      // Leave request actions
      'LEAVE_REQUEST_CREATE',
      'LEAVE_REQUEST_UPDATE',
      'LEAVE_REQUEST_DELETE',
      'LEAVE_REQUEST_STATUS_CHANGE',
      
      // Job actions
      'JOB_CREATE',
      'JOB_UPDATE',
      'JOB_DELETE',
      
      // Task actions
      'TASK_CREATE',
      'TASK_UPDATE',
      'TASK_DELETE',
      'TASK_STATUS_CHANGE',
      
      // Report actions
      'REPORT_CREATE',
      'REPORT_VIEW',
      'REPORT_DELETE',
      
      // System actions
      'SYSTEM_SETTING_CHANGE'
    ]
  },
  
  // Target resource type
  resourceType: {
    type: String,
    required: true,
    enum: [
      'USER', 
      'APPLICATION', 
      'LEAVE_REQUEST', 
      'JOB', 
      'TASK', 
      'REPORT',
      'SYSTEM'
    ]
  },
  
  // Target resource ID
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    required: false // Not required for system-level actions
  },
  
  // Description of the action
  description: {
    type: String,
    required: true
  },
  
  // Previous state of the resource (for updates)
  previousState: {
    type: Object,
    default: null
  },
  
  // New state of the resource (for updates)
  newState: {
    type: Object,
    default: null
  },
  
  // IP address of the user
  ipAddress: {
    type: String,
    default: null
  },
  
  // User agent information
  userAgent: {
    type: String,
    default: null
  },
  
  // Timestamp of the action
  timestamp: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create indexes for better query performance
auditLogSchema.index({ userId: 1 });
auditLogSchema.index({ action: 1 });
auditLogSchema.index({ resourceType: 1, resourceId: 1 });
auditLogSchema.index({ timestamp: -1 });

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

module.exports = AuditLog;
