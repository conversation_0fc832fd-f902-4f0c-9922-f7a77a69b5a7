# Sprint 1: Class Diagram (UML)

```
@startuml HR Management System - Sprint 1 Class Diagram

skinparam classAttributeIconSize 0
skinparam classFontStyle bold
skinparam classBackgroundColor #f8f9fa
skinparam classBorderColor #495057

package "Models" {
  class User {
    - _id: ObjectId
    - email: String
    - password: String
    - role: String
    - job: String
    - department: String
    - name: String
    - creationDate: Date
    - birthdate: Date
    - active: Boolean
    - lastPasswordChange: Date
    - lastLogin: Date
    - failedLoginAttempts: Number
    - accountLocked: Boolean
    - accountLockedUntil: Date
    + comparePassword(password): <PERSON><PERSON><PERSON>
  }

  class LoginHistory {
    - _id: ObjectId
    - userId: ObjectId
    - userInfo: Object
    - status: String
    - failureReason: String
    - ipAddress: String
    - device: String
    - browser: String
    - operatingSystem: String
    - userAgent: String
    - timestamp: Date
  }

  class AuditLog {
    - _id: ObjectId
    - userId: ObjectId
    - userInfo: Object
    - action: String
    - resourceType: String
    - resourceId: String
    - description: String
    - ipAddress: String
    - userAgent: String
    - timestamp: Date
  }

  class PasswordReset {
    - _id: ObjectId
    - userId: ObjectId
    - token: String
    - createdAt: Date
    - expiresAt: Date
    - used: Boolean
  }
}

package "Services" {
  class AuthService {
    + login(email, password): Promise<{token, user}>
    + logout(userId): Promise<void>
    + verifyToken(token): Promise<User>
  }

  class UserService {
    + createUser(userData): Promise<User>
    + updateUser(userId, userData): Promise<User>
    + deleteUser(userId): Promise<void>
    + getUserById(userId): Promise<User>
    + getUserByEmail(email): Promise<User>
    + getAllUsers(): Promise<User[]>
    + getUsersByRole(role): Promise<User[]>
  }

  class PasswordService {
    + hashPassword(password): Promise<String>
    + comparePasswords(password, hash): Promise<Boolean>
    + generateResetToken(userId): Promise<String>
    + verifyResetToken(token): Promise<ObjectId>
    + resetPassword(userId, newPassword): Promise<void>
  }

  class AuditService {
    + logAction(userId, action, resourceType, resourceId, description): Promise<void>
    + getAuditLogs(filters): Promise<AuditLog[]>
  }

  class EmailService {
    + sendPasswordResetEmail(email, token): Promise<void>
    + sendAccountCreationEmail(email, tempPassword): Promise<void>
    + sendPasswordChangedEmail(email): Promise<void>
  }
}

package "Controllers" {
  class AuthController {
    + login(req, res): Promise<Response>
    + logout(req, res): Promise<Response>
    + verifyToken(req, res): Promise<Response>
  }

  class UserController {
    + createUser(req, res): Promise<Response>
    + updateUser(req, res): Promise<Response>
    + deleteUser(req, res): Promise<Response>
    + getUser(req, res): Promise<Response>
    + getAllUsers(req, res): Promise<Response>
    + getUsersByRole(req, res): Promise<Response>
  }

  class PasswordController {
    + forgotPassword(req, res): Promise<Response>
    + resetPassword(req, res): Promise<Response>
    + changePassword(req, res): Promise<Response>
  }

  class AuditController {
    + getAuditLogs(req, res): Promise<Response>
    + getLoginHistory(req, res): Promise<Response>
  }
}

package "Middleware" {
  class AuthMiddleware {
    + authenticate(req, res, next): Promise<void>
    + authorizeRoles(...roles): Function
  }
}

' Relationships
User "1" -- "0..*" LoginHistory : has >
User "1" -- "0..*" AuditLog : has >
User "1" -- "0..1" PasswordReset : has >

AuthService ..> User : uses >
AuthService ..> LoginHistory : creates >
UserService ..> User : manages >
PasswordService ..> User : modifies >
PasswordService ..> PasswordReset : manages >
AuditService ..> AuditLog : creates >
EmailService ..> User : sends to >

AuthController ..> AuthService : uses >
UserController ..> UserService : uses >
PasswordController ..> PasswordService : uses >
PasswordController ..> EmailService : uses >
AuditController ..> AuditService : uses >

AuthMiddleware ..> AuthService : uses >

@enduml
```

## Class Diagram Description

The Class Diagram for Sprint 1 illustrates the structure of the HR Management System's authentication and user management components, showing the relationships between different classes.

### Models
- **User**: Represents user accounts in the system with attributes like email, password, role, etc.
- **LoginHistory**: Tracks user login attempts, both successful and failed
- **AuditLog**: Records system events and user actions for security auditing
- **PasswordReset**: Stores password reset tokens and their status

### Services
- **AuthService**: Handles authentication operations like login, logout, and token verification
- **UserService**: Manages user operations like creation, updating, and retrieval
- **PasswordService**: Handles password-related operations including hashing and reset functionality
- **AuditService**: Manages the creation and retrieval of audit logs
- **EmailService**: Handles sending emails for password resets and account notifications

### Controllers
- **AuthController**: Handles authentication-related HTTP requests
- **UserController**: Processes user management HTTP requests
- **PasswordController**: Manages password-related HTTP requests
- **AuditController**: Handles audit log retrieval requests

### Middleware
- **AuthMiddleware**: Provides authentication and authorization for protected routes

### Relationships
- One User can have many LoginHistory records
- One User can have many AuditLog records
- One User can have at most one active PasswordReset
- Services use Models to perform operations
- Controllers use Services to handle requests
- Middleware uses Services for authentication and authorization
