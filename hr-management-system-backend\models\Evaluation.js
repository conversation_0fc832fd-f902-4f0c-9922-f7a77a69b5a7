const mongoose = require('mongoose');

const evaluationSchema = new mongoose.Schema({
  // The user being evaluated
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // The HR who performed the evaluation
  evaluatorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Evaluation period
  evaluationPeriod: {
    type: String,
    required: true,
    enum: ['Monthly', 'Quarterly', 'Semi-Annual', 'Annual']
  },

  // Date of evaluation
  evaluationDate: {
    type: Date,
    default: Date.now,
    required: true
  },

  // Performance metrics (1-5 stars, can be decimal)
  performanceRating: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },

  // Attitude/behavior metrics (1-5 stars, can be decimal)
  attitudeRating: {
    type: Number,
    required: false, // Not required for initial AI-generated evaluations
    min: 0,
    max: 5
  },

  // Communication skills (1-5 stars, can be decimal)
  communicationRating: {
    type: Number,
    required: false, // Not required for initial AI-generated evaluations
    min: 0,
    max: 5
  },

  // Teamwork (1-5 stars, can be decimal)
  teamworkRating: {
    type: Number,
    required: false, // Not required for initial AI-generated evaluations
    min: 0,
    max: 5
  },

  // Initiative/proactivity (1-5 stars, can be decimal)
  initiativeRating: {
    type: Number,
    required: false, // Not required for initial AI-generated evaluations
    min: 0,
    max: 5
  },

  // Overall rating (calculated average)
  overallRating: {
    type: Number,
    required: false, // Make it optional so we can calculate it in the pre-save hook
    min: 1,
    max: 5
  },

  // Strengths (text)
  strengths: {
    type: String,
    required: false,
    maxlength: 1000
  },

  // Areas for improvement (text)
  areasForImprovement: {
    type: String,
    required: false,
    maxlength: 1000
  },

  // Goals for next period (text)
  goals: {
    type: String,
    required: false,
    maxlength: 1000
  },

  // Additional comments (text)
  comments: {
    type: String,
    required: false,
    maxlength: 1000
  },

  // Status of the evaluation
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Completed', 'Acknowledged'],
    default: 'Draft'
  },

  // Flag to indicate if this evaluation was generated by AI
  aiGenerated: {
    type: Boolean,
    default: false
  },

  // AI-generated insights (stored as JSON)
  insights: {
    type: Object,
    default: null
  },

  // Flag to control visibility to the user
  visibleToUser: {
    type: Boolean,
    default: false
  }
}, { timestamps: true });

// Calculate overall rating before saving
evaluationSchema.pre('save', function(next) {
  // Only include ratings that are not null or undefined
  const ratings = [
    this.performanceRating,
    this.attitudeRating,
    this.communicationRating,
    this.teamworkRating,
    this.initiativeRating
  ].filter(rating => rating !== null && rating !== undefined);

  if (ratings.length > 0) {
    // Calculate the average rating
    this.overallRating = parseFloat((ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(1));
  } else {
    // Default to performance rating if no other ratings are available
    this.overallRating = this.performanceRating || 3.0;
  }

  next();
});

const Evaluation = mongoose.model('Evaluation', evaluationSchema);

module.exports = Evaluation;
