/* Custom Toast Styles */

/* Make sure the close button is always visible and properly sized */
.Toastify__close-button {
  opacity: 1 !important;
  color: #fff !important;
  font-size: 18px !important;
  padding: 6px !important;
  align-self: center !important;
  cursor: pointer !important;
  z-index: 9999 !important;
  margin-left: 10px !important;
}

/* Ensure the close button has a proper hover state */
.Toastify__close-button:hover {
  opacity: 0.8 !important;
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-radius: 50% !important;
}

/* Make the toast container more compact */
.Toastify__toast-container {
  width: auto !important;
  min-width: 320px !important;
  max-width: 400px !important;
}

/* Style the toast itself */
.Toastify__toast {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin-bottom: 12px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

/* Style the toast body */
.Toastify__toast-body {
  padding: 0 !important;
  margin: 0 !important;
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* Success toast */
.Toastify__toast--success {
  background-color: #4CAF50 !important;
  color: white !important;
}

/* Error toast */
.Toastify__toast--error {
  background-color: #D32F2F !important;
  color: white !important;
}

/* Info toast */
.Toastify__toast--info {
  background-color: #2196F3 !important;
  color: white !important;
}

/* Warning toast */
.Toastify__toast--warning {
  background-color: #FF9800 !important;
  color: white !important;
}

/* Progress bar */
.Toastify__progress-bar {
  height: 3px !important;
}

/* Success progress bar */
.Toastify__progress-bar--success {
  background-color: rgba(255, 255, 255, 0.7) !important;
}

/* Error progress bar */
.Toastify__progress-bar--error {
  background-color: rgba(255, 255, 255, 0.7) !important;
}

/* Info progress bar */
.Toastify__progress-bar--info {
  background-color: rgba(255, 255, 255, 0.7) !important;
}

/* Warning progress bar */
.Toastify__progress-bar--warning {
  background-color: rgba(255, 255, 255, 0.7) !important;
}

/* Toast icon */
.Toastify__toast-icon {
  margin-right: 12px !important;
}

/* Make sure the toast is properly positioned */
.Toastify__toast-container--top-right {
  top: 1em !important;
  right: 1em !important;
}

/* Ensure the toast is properly sized on mobile */
@media only screen and (max-width: 480px) {
  .Toastify__toast-container {
    width: 100% !important;
    max-width: none !important;
    padding: 0 16px !important;
    left: 0 !important;
    margin: 0 !important;
  }

  .Toastify__toast-container--top-right {
    top: 1em !important;
    right: 0 !important;
  }
}
