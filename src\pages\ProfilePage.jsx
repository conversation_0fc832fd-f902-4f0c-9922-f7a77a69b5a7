import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';
import { sendPasswordChangeEmail } from '../utils/emailNotifications';
import {
  Container,
  Grid,
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  TextField,
  Divider,
  Avatar,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Alert,
  Snackbar,
  CircularProgress,
  useTheme,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Work as WorkIcon,
  Cake as CakeIcon,
  CalendarToday as CalendarIcon,
  Badge as BadgeIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers';
import { toast } from 'react-toastify';
import api from '../Services/ApiService';
import DashboardLayout from '../components/layout/DashboardLayout';

// Styled components
const ProfileCard = styled(Card)(({ theme }) => ({
  height: '100%',
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  overflow: 'hidden',
}));

const ProfileHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
  padding: theme.spacing(4),
  color: theme.palette.common.white,
  textAlign: 'center',
  position: 'relative',
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 120,
  height: 120,
  margin: '0 auto',
  border: `4px solid ${theme.palette.common.white}`,
  boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
  fontSize: '3rem',
}));

const InfoSection = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius,
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
}));

const InfoItem = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  '&:last-child': {
    marginBottom: 0,
  },
}));

const InfoIcon = styled(Box)(({ theme }) => ({
  marginRight: theme.spacing(2),
  color: theme.palette.primary.main,
}));

const ProfilePage = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [profile, setProfile] = useState({});
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [editedProfile, setEditedProfile] = useState({});
  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [passwordError, setPasswordError] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // State to store the dashboard URL based on user role
  const [dashboardUrl, setDashboardUrl] = useState('/user-dashboard');

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          toast.error('Authentication required. Please log in.');
          navigate('/');
          return;
        }

        // Decode token to get basic user info
        const decoded = jwtDecode(token);
        console.log('Decoded token:', decoded);

        // Set dashboard URL based on user role
        if (decoded.role === 'admin') {
          setDashboardUrl('/dashboard');
        } else if (decoded.role === 'hr') {
          setDashboardUrl('/hr-dashboard');
        } else {
          setDashboardUrl('/user-dashboard');
        }

        // Fetch full profile from API
        const response = await api.get('/user/profile');

        if (response.data) {
          setProfile(response.data);
          setEditedProfile(response.data);
        } else {
          toast.warning('Could not load complete profile data.');
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
        toast.error('Failed to load profile data.');
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfile();
  }, [navigate]);

  const handleEditToggle = () => {
    if (editMode) {
      // Cancel edit mode
      setEditedProfile(profile);
    }
    setEditMode(!editMode);
  };

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setEditedProfile(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleBirthdateChange = (date) => {
    setEditedProfile(prev => ({
      ...prev,
      birthdate: date
    }));
  };

  const handleSaveProfile = async () => {
    try {
      setLoading(true);

      const response = await api.put('/user/update-profile', {
        name: editedProfile.name,
        email: editedProfile.email,
        job: editedProfile.job,
        birthdate: editedProfile.birthdate
      });

      if (response.data && response.data.user) {
        setProfile(response.data.user);
        setEditMode(false);
        setSnackbar({
          open: true,
          message: 'Profile updated successfully!',
          severity: 'success'
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Failed to update profile',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordDialogOpen = () => {
    setOpenPasswordDialog(true);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setPasswordError('');
  };

  const handlePasswordDialogClose = () => {
    setOpenPasswordDialog(false);
  };

  const handleChangePassword = async () => {
    // Validate passwords
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }

    try {
      setLoading(true);

      // Get the user role from the token
      const token = localStorage.getItem('token');
      const decoded = jwtDecode(token);
      const userRole = decoded.role;
      const userId = decoded.id;

      console.log('Changing password for user:', {
        userId,
        role: userRole,
        currentPasswordLength: passwordData.currentPassword.length,
        newPasswordLength: passwordData.newPassword.length
      });

      // Use the appropriate endpoint based on user role
      let endpoint = '/user/change-password';

      console.log('Using endpoint:', endpoint);

      try {
        const response = await api.put(endpoint, {
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        });

        console.log('Password change response:', response.data);

        if (response.data) {
          // Send email notification about password change
          try {
            // Get the user's name from the profile
            const userName = profile.name || 'User';
            const userEmail = profile.email;

            // Send password change email notification
            await sendPasswordChangeEmail({
              to_name: userName,
              email: userEmail,
              date: new Date().toLocaleString()
            });
          } catch (emailError) {
            console.error('Error sending password change email notification:', emailError);
            // Continue with the flow even if email fails
          }

          handlePasswordDialogClose();
          setSnackbar({
            open: true,
            message: 'Password changed successfully!',
            severity: 'success'
          });
        }
      } catch (apiError) {
        console.error('API Error details:', {
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          data: apiError.response?.data,
          message: apiError.message
        });

        // More specific error message based on the error
        if (apiError.response?.status === 400) {
          setPasswordError(apiError.response.data.message || 'Invalid password information');
        } else if (apiError.response?.status === 500) {
          setPasswordError('Server error. Please try again later or contact support.');
          console.error('Server error details:', apiError.response?.data);
        } else {
          setPasswordError(apiError.response?.data?.message || 'Failed to change password');
        }

        throw apiError; // Re-throw to be caught by outer catch
      }
    } catch (error) {
      console.error('Error changing password:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    navigate('/');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <DashboardLayout
      title="Profile"
      userName={profile.name || 'User'}
      userRole={profile.role || 'User'}
      menuItems={[]}
      onLogout={handleLogout}
    >
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(dashboardUrl)}
          >
            Return to Dashboard
          </Button>
        </Box>

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={4}>
            {/* Profile Header Card */}
            <Grid item xs={12}>
              <ProfileCard>
                <ProfileHeader>
                  <ProfileAvatar>
                    {profile.name ? profile.name.charAt(0).toUpperCase() : 'U'}
                  </ProfileAvatar>
                  <Typography variant="h4" sx={{ mt: 2, fontWeight: 600 }}>
                    {profile.name || 'User'}
                  </Typography>
                  <Typography variant="h6" sx={{ opacity: 0.9 }}>
                    {profile.job || 'Employee'}
                  </Typography>
                  <Typography variant="body1" sx={{ mt: 1, opacity: 0.8 }}>
                    {profile.department || 'Department'}
                  </Typography>
                </ProfileHeader>
              </ProfileCard>
            </Grid>

            {/* Personal Information */}
            <Grid item xs={12} md={6}>
              <InfoSection>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Typography variant="h5" fontWeight="600">
                    Personal Information
                  </Typography>
                  {!editMode ? (
                    <Button
                      startIcon={<EditIcon />}
                      variant="outlined"
                      onClick={handleEditToggle}
                    >
                      Edit
                    </Button>
                  ) : (
                    <Box>
                      <Button
                        startIcon={<SaveIcon />}
                        variant="contained"
                        color="primary"
                        onClick={handleSaveProfile}
                        sx={{ mr: 1 }}
                      >
                        Save
                      </Button>
                      <Button
                        startIcon={<CancelIcon />}
                        variant="outlined"
                        color="secondary"
                        onClick={handleEditToggle}
                      >
                        Cancel
                      </Button>
                    </Box>
                  )}
                </Box>

                <Divider sx={{ mb: 3 }} />

                {!editMode ? (
                  // View mode
                  <>
                    <InfoItem>
                      <InfoIcon>
                        <PersonIcon />
                      </InfoIcon>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Full Name
                        </Typography>
                        <Typography variant="body1">
                          {profile.name || 'Not set'}
                        </Typography>
                      </Box>
                    </InfoItem>

                    <InfoItem>
                      <InfoIcon>
                        <EmailIcon />
                      </InfoIcon>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Email Address
                        </Typography>
                        <Typography variant="body1">
                          {profile.email || 'Not set'}
                        </Typography>
                      </Box>
                    </InfoItem>

                    <InfoItem>
                      <InfoIcon>
                        <WorkIcon />
                      </InfoIcon>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Job Title
                        </Typography>
                        <Typography variant="body1">
                          {profile.job || 'Not set'}
                        </Typography>
                      </Box>
                    </InfoItem>

                    <InfoItem>
                      <InfoIcon>
                        <CakeIcon />
                      </InfoIcon>
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Birthdate
                        </Typography>
                        <Typography variant="body1">
                          {profile.birthdate ? formatDate(profile.birthdate) : 'Not set'}
                        </Typography>
                      </Box>
                    </InfoItem>
                  </>
                ) : (
                  // Edit mode
                  <Box component="form" noValidate>
                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Full Name"
                          name="name"
                          value={editedProfile.name || ''}
                          onChange={handleProfileChange}
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Email Address"
                          name="email"
                          type="email"
                          value={editedProfile.email || ''}
                          onChange={handleProfileChange}
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Job Title"
                          name="job"
                          value={editedProfile.job || ''}
                          onChange={handleProfileChange}
                          variant="outlined"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <DatePicker
                          label="Birthdate"
                          value={editedProfile.birthdate ? new Date(editedProfile.birthdate) : null}
                          onChange={handleBirthdateChange}
                          renderInput={(params) => <TextField {...params} fullWidth />}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              variant: 'outlined'
                            }
                          }}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </InfoSection>
            </Grid>

            {/* Account Information */}
            <Grid item xs={12} md={6}>
              <InfoSection>
                <Typography variant="h5" fontWeight="600" mb={3}>
                  Account Information
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <InfoItem>
                  <InfoIcon>
                    <BadgeIcon />
                  </InfoIcon>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Role
                    </Typography>
                    <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                      {profile.role || 'User'}
                    </Typography>
                  </Box>
                </InfoItem>

                <InfoItem>
                  <InfoIcon>
                    <CalendarIcon />
                  </InfoIcon>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Account Created
                    </Typography>
                    <Typography variant="body1">
                      {profile.creationDate ? formatDate(profile.creationDate) : 'Not available'}
                    </Typography>
                  </Box>
                </InfoItem>

                <InfoItem>
                  <InfoIcon>
                    <LockIcon />
                  </InfoIcon>
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Password
                    </Typography>
                    <Typography variant="body1">
                      ••••••••
                    </Typography>
                  </Box>
                </InfoItem>

                <Box mt={4}>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<LockIcon />}
                    onClick={handlePasswordDialogOpen}
                    fullWidth
                  >
                    Change Password
                  </Button>
                </Box>
              </InfoSection>
            </Grid>
          </Grid>
        )}

        {/* Password Change Dialog */}
        <Dialog open={openPasswordDialog} onClose={handlePasswordDialogClose} maxWidth="sm" fullWidth>
          <DialogTitle>Change Password</DialogTitle>
          <DialogContent>
            {passwordError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {passwordError}
              </Alert>
            )}
            <TextField
              margin="dense"
              label="Current Password"
              type="password"
              name="currentPassword"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange}
              fullWidth
              variant="outlined"
              required
              sx={{ mb: 2, mt: 1 }}
            />
            <TextField
              margin="dense"
              label="New Password"
              type="password"
              name="newPassword"
              value={passwordData.newPassword}
              onChange={handlePasswordChange}
              fullWidth
              variant="outlined"
              required
              sx={{ mb: 2 }}
              helperText="Password must be at least 8 characters long"
            />
            <TextField
              margin="dense"
              label="Confirm New Password"
              type="password"
              name="confirmPassword"
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange}
              fullWidth
              variant="outlined"
              required
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handlePasswordDialogClose} color="secondary">
              Cancel
            </Button>
            <Button
              onClick={handleChangePassword}
              color="primary"
              variant="contained"
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Change Password'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={handleSnackbarClose}
            severity={snackbar.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </DashboardLayout>
  );
};

export default ProfilePage;
