const express = require('express');
const mongoose = require('mongoose');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const cron = require('node-cron');

// Load environment variables
require('dotenv').config();

// Debug environment variables
console.log('Environment check:');
console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY ? 'Set' : 'Not set');
console.log('NODE_ENV:', process.env.NODE_ENV);

// Import routes
const authRoutes = require('./routes/authRoutes');
const adminRoutes = require('./routes/adminRoutes');
const hrRoutes = require('./routes/hrRoutes');
const normaluserRoutes = require('./routes/normaluserRoutes'); // Normal user routes
const applications = require('./routes/applications'); // Applications routes
const leaveRoutes = require('./routes/leaveRoutes'); // Leave routes
const jobRoutes = require('./routes/jobRoutes'); // Job routes
const taskRoutes = require('./routes/taskRoutes'); // Task management routes
const reportRoutes = require('./routes/reportRoutes'); // Report management routes
const statisticsRoutes = require('./routes/statisticsRoutes'); // Statistics routes

const notificationRoutes = require('./routes/notificationRoutes'); // Notification routes
const gekRoutes = require('./routes/gekRoutes'); // General Estimating Knowledge routes
const aiRoutes = require('./ai-assistant/routes/aiRoutes'); // AI Assistant routes

// Initialize Express app
const app = express();

// Middleware
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));
app.use(bodyParser.json());

// Import file utilities
const fileUtils = require('./utils/fileUtils');

// Direct file download endpoint
app.get('/download/:filename', (req, res) => {
  try {
    const filename = req.params.filename;

    // Clean the filename to prevent directory traversal
    const cleanFilename = filename.replace(/^\/+/, '').replace(/\.\.\//g, '');

    // Construct the file path
    const filePath = path.join(__dirname, 'uploads', cleanFilename);
    console.log('Download request for file:', filePath);

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      console.error('File not found for download:', filePath);
      return res.status(404).send('File not found');
    }

    // Set headers for download
    res.setHeader('Content-Disposition', `attachment; filename="${path.basename(filePath)}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error downloading file:', error);
    res.status(500).send('Server error');
  }
});

// Serve uploaded files directly
app.use('/uploads', (req, res) => {
  try {
    console.log('Accessing file:', req.path);

    // Clean the path to prevent directory traversal
    const cleanPath = req.path.replace(/^\/+/, '').replace(/\.\.\//g, '');

    // Get the full path to the requested file
    const filePath = path.join(__dirname, 'uploads', cleanPath);
    console.log('Full file path:', filePath);

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      console.error('File not found:', filePath);
      return res.status(404).send('File not found');
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    if (!stats.isFile()) {
      console.error('Not a file:', filePath);
      return res.status(400).send('Not a file');
    }

    // Set appropriate content type based on file extension
    const ext = path.extname(filePath).toLowerCase();
    const contentTypes = {
      '.pdf': 'application/pdf',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    };

    // Set headers
    res.setHeader('Content-Type', contentTypes[ext] || 'application/octet-stream');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Disposition', 'inline; filename=' + path.basename(filePath));
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Accept-Ranges', 'bytes');

    // Stream the file directly to the response
    const fileStream = fs.createReadStream(filePath);
    fileStream.on('error', (error) => {
      console.error('Error streaming file:', error);
      if (!res.headersSent) {
        res.status(500).send('Error streaming file');
      }
    });

    fileStream.pipe(res);
  } catch (error) {
    console.error('Error serving file:', error);
    if (!res.headersSent) {
      res.status(500).send('Server error');
    }
  }
});

// Route to check if a file exists
app.get('/api/file-exists', (req, res) => {
  try {
    const { filePath } = req.query;

    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    console.log('Checking if file exists:', filePath);

    // Try different path formats
    let fileInfo = null;

    // First try: as provided
    fileInfo = fileUtils.getFileInfo(filePath);

    // Second try: with uploads/ prefix
    if (!fileInfo && !filePath.startsWith('uploads/')) {
      const pathWithPrefix = `uploads/${filePath}`;
      console.log('Trying with uploads/ prefix:', pathWithPrefix);
      fileInfo = fileUtils.getFileInfo(pathWithPrefix);
    }

    // Third try: without uploads/ prefix
    if (!fileInfo && filePath.startsWith('uploads/')) {
      const pathWithoutPrefix = filePath.replace('uploads/', '');
      console.log('Trying without uploads/ prefix:', pathWithoutPrefix);
      fileInfo = fileUtils.getFileInfo(pathWithoutPrefix);
    }

    if (fileInfo) {
      console.log('File found:', fileInfo.absolutePath);
      res.json({
        exists: true,
        fileInfo
      });
    } else {
      console.log('File not found after trying all path formats');
      res.json({
        exists: false,
        error: 'File not found',
        triedPath: filePath
      });
    }
  } catch (error) {
    console.error('Error checking if file exists:', error);
    res.status(500).json({
      error: 'Server error checking file existence',
      message: error.message
    });
  }
});

// Use routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/hr', hrRoutes);
app.use('/api/user', normaluserRoutes); // Normal user routes
app.use('/api/applications', applications); // Applications routes
app.use('/api/leaves', leaveRoutes); // Leave routes
app.use('/api/jobs', jobRoutes); // Job routes
app.use('/api/tasks', taskRoutes); // Task management routes
app.use('/api/reports', reportRoutes); // Report management routes
app.use('/api/statistics', statisticsRoutes); // Statistics routes

app.use('/api/notifications', notificationRoutes); // Notification routes
app.use('/api/gek', gekRoutes); // General Estimating Knowledge routes
app.use('/api/ai', aiRoutes); // AI Assistant routes



// Test route to check if an email exists in the database
app.get('/check-email/:email', async (req, res) => {
  try {
    const User = require('./models/user');
    const email = req.params.email;

    console.log('Checking if email exists:', email);

    const user = await User.findOne({ email });

    if (user) {
      console.log('User found:', user.email);
      return res.json({
        exists: true,
        email: user.email,
        name: user.name
      });
    } else {
      console.log('User not found for email:', email);
      return res.json({
        exists: false,
        message: 'User not found'
      });
    }
  } catch (error) {
    console.error('Error checking email:', error);
    res.status(500).json({ message: 'Error checking email', error: error.message });
  }
});

// Test route to create a test user (REMOVE IN PRODUCTION)
app.get('/create-test-user', async (req, res) => {
  try {
    const User = require('./models/user');
    const bcrypt = require('bcryptjs');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });

    if (existingUser) {
      // Update the existing user's password
      const hashedPassword = await bcrypt.hash('test123', 10);
      existingUser.password = hashedPassword;
      existingUser.role = 'user';
      existingUser.department = existingUser.department || 'IT';
      existingUser.creationDate = existingUser.creationDate || new Date();
      await existingUser.save();

      return res.json({
        message: 'Test user updated successfully',
        user: {
          email: existingUser.email,
          role: existingUser.role,
          id: existingUser._id,
          passwordHash: existingUser.password.substring(0, 10) + '...'
        }
      });
    }

    // Create a new test user
    const hashedPassword = await bcrypt.hash('test123', 10);
    const testUser = new User({
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'user',
      job: 'Tester',
      department: 'IT',
      creationDate: new Date(),
      birthdate: new Date('1990-01-01')
    });

    await testUser.save();

    res.json({
      message: 'Test user created successfully',
      user: {
        email: testUser.email,
        role: testUser.role,
        id: testUser._id,
        passwordHash: testUser.password.substring(0, 10) + '...'
      }
    });
  } catch (error) {
    console.error('Error creating test user:', error);
    res.status(500).json({ message: 'Error creating test user', error: error.message });
  }
});

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/hr')
  .then(() => console.log('Connected to MongoDB'))
  .catch((error) => console.error('Error connecting to MongoDB:', error));

// Schedule job to check for expired jobs (runs daily at midnight)
cron.schedule('0 0 * * *', async () => {
  try {
    console.log('Running scheduled task: Checking for expired jobs...');
    const Job = require('./models/Job');

    // Find all jobs with an endDate that has passed
    const currentDate = new Date();
    const expiredJobs = await Job.find({
      endDate: { $lt: currentDate, $ne: null }
    });

    console.log(`Found ${expiredJobs.length} expired jobs to delete`);

    // Delete each expired job
    for (const job of expiredJobs) {
      console.log(`Deleting expired job: ${job.title} (ID: ${job._id})`);
      await Job.findByIdAndDelete(job._id);
    }

    console.log('Expired jobs cleanup completed');
  } catch (error) {
    console.error('Error in expired jobs cleanup:', error);
  }
});

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
