import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Button,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  Divider,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  FilterList as FilterListIcon,
  Search as SearchIcon,
  Clear as ClearIcon,
  Info as InfoIcon,
  Close as CloseIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Computer as ComputerIcon,
  PhoneAndroid as PhoneIcon,
  Tablet as TabletIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

// Status colors
const statusColors = {
  SUCCESS: 'success',
  FAILED: 'error'
};

// Failure reason display
const failureReasonDisplay = {
  INVALID_CREDENTIALS: 'Invalid Credentials',
  ACCOUNT_LOCKED: 'Account Locked',
  ACCOUNT_DISABLED: 'Account Disabled',
  ACCOUNT_EXPIRED: 'Account Expired',
  INVALID_TOKEN: 'Invalid Token',
  SESSION_EXPIRED: 'Session Expired'
};

// Device icon mapping
const deviceIcons = {
  Desktop: <ComputerIcon fontSize="small" />,
  Mobile: <PhoneIcon fontSize="small" />,
  Tablet: <TabletIcon fontSize="small" />
};

const LoginHistory = () => {
  const [loginHistory, setLoginHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalEntries, setTotalEntries] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedEntry, setSelectedEntry] = useState(null);

  // Filter states
  const [filters, setFilters] = useState({
    userId: '',
    status: '',
    ipAddress: '',
    startDate: null,
    endDate: null
  });

  // Search state
  const [searchQuery, setSearchQuery] = useState('');

  // Fetch login history
  const fetchLoginHistory = async () => {
    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = {
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage,
        sortBy: 'loginTime',
        sortOrder: 'desc'
      };

      // Add search query if it exists
      if (searchQuery) params.search = searchQuery;

      // Add filters if they exist
      if (filters.userId) params.userId = filters.userId;
      if (filters.status) params.status = filters.status;
      if (filters.ipAddress) params.ipAddress = filters.ipAddress;
      if (filters.startDate) params.startDate = filters.startDate.toISOString().split('T')[0];
      if (filters.endDate) params.endDate = filters.endDate.toISOString().split('T')[0];

      const response = await api.get('/admin/login-history', { params });

      setLoginHistory(response.data.history);
      setTotalEntries(response.data.pagination.total);
    } catch (error) {
      console.error('Error fetching login history:', error);
      setError('Failed to fetch login history. Please try again later.');
      toast.error('Failed to fetch login history');
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchLoginHistory();
  }, [page, rowsPerPage]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Apply filters
  const applyFilters = () => {
    setPage(0); // Reset to first page
    fetchLoginHistory();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      userId: '',
      status: '',
      ipAddress: '',
      startDate: null,
      endDate: null
    });
    setSearchQuery('');
    setPage(0);
    fetchLoginHistory();
  };

  // Handle search
  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Debounce search to avoid too many API calls
    if (window.loginHistorySearchTimeout) {
      clearTimeout(window.loginHistorySearchTimeout);
    }

    window.loginHistorySearchTimeout = setTimeout(() => {
      setPage(0); // Reset to first page
      fetchLoginHistory();
    }, 500);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format duration for display
  const formatDuration = (seconds) => {
    if (!seconds) return 'N/A';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let result = '';
    if (hours > 0) result += `${hours}h `;
    if (minutes > 0 || hours > 0) result += `${minutes}m `;
    result += `${remainingSeconds}s`;

    return result;
  };

  // View entry details
  const handleViewEntryDetails = (entry) => {
    setSelectedEntry(entry);
  };

  // Close entry details
  const handleCloseEntryDetails = () => {
    setSelectedEntry(null);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Typography variant="h5" component="h2" fontWeight={600}>
          Login History
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            placeholder="Search login history..."
            size="small"
            value={searchQuery}
            onChange={handleSearch}
            InputProps={{
              startAdornment: <SearchIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />,
              endAdornment: searchQuery ? (
                <IconButton size="small" onClick={() => { setSearchQuery(''); fetchLoginHistory(); }}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              ) : null
            }}
            sx={{ width: { xs: '100%', sm: '250px' } }}
          />
          <Button
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ ml: { xs: 0, sm: 1 } }}
          >
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
          <Button
            startIcon={<RefreshIcon />}
            onClick={fetchLoginHistory}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 2 }}>
            Filters
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="User ID"
                fullWidth
                value={filters.userId}
                onChange={(e) => handleFilterChange('userId', e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="SUCCESS">Success</MenuItem>
                  <MenuItem value="FAILED">Failed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                label="IP Address"
                fullWidth
                value={filters.ipAddress}
                onChange={(e) => handleFilterChange('ipAddress', e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="Start Date"
                  value={filters.startDate}
                  onChange={(date) => handleFilterChange('startDate', date)}
                  renderInput={(params) => <TextField {...params} fullWidth size="small" />}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label="End Date"
                  value={filters.endDate}
                  onChange={(date) => handleFilterChange('endDate', date)}
                  renderInput={(params) => <TextField {...params} fullWidth size="small" />}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6} md={6}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={applyFilters}
                >
                  Apply Filters
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<ClearIcon />}
                  onClick={resetFilters}
                >
                  Reset
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : loginHistory.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography>No login history found</Typography>
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Login Time</TableCell>
                    <TableCell>User</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>IP Address</TableCell>
                    <TableCell>Device</TableCell>
                    <TableCell>Browser</TableCell>
                    <TableCell>Session Duration</TableCell>
                    <TableCell>Details</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {loginHistory.map((entry) => (
                    <TableRow key={entry._id} hover>
                      <TableCell>{formatDate(entry.loginTime)}</TableCell>
                      <TableCell>
                        {entry.userInfo?.name || 'Unknown'}
                        <Typography variant="caption" display="block" color="text.secondary">
                          {entry.userInfo?.email || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={entry.status === 'SUCCESS' ? <CheckCircleIcon /> : <CancelIcon />}
                          label={entry.status}
                          size="small"
                          color={statusColors[entry.status] || 'default'}
                        />
                        {entry.failureReason && (
                          <Typography variant="caption" display="block" color="text.secondary">
                            {failureReasonDisplay[entry.failureReason] || entry.failureReason}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>{entry.ipAddress || 'N/A'}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          {deviceIcons[entry.device] || <ComputerIcon fontSize="small" />}
                          <Typography variant="body2">{entry.device || 'Unknown'}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{entry.browser?.name || 'Unknown'}</TableCell>
                      <TableCell>{formatDuration(entry.sessionDuration)}</TableCell>
                      <TableCell>
                        <Tooltip title="View Details">
                          <IconButton size="small" onClick={() => handleViewEntryDetails(entry)}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25, 50]}
              component="div"
              count={totalEntries}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>

      {/* Entry Details Dialog */}
      {selectedEntry && (
        <Dialog open={!!selectedEntry} onClose={handleCloseEntryDetails} maxWidth="md" fullWidth>
          <DialogTitle>
            Login Details
            <IconButton
              aria-label="close"
              onClick={handleCloseEntryDetails}
              sx={{ position: 'absolute', right: 8, top: 8 }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">Login Time</Typography>
                <Typography variant="body1">{formatDate(selectedEntry.loginTime)}</Typography>
              </Grid>
              {selectedEntry.logoutTime && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">Logout Time</Typography>
                  <Typography variant="body1">{formatDate(selectedEntry.logoutTime)}</Typography>
                </Grid>
              )}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">Session Duration</Typography>
                <Typography variant="body1">{formatDuration(selectedEntry.sessionDuration)}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">User</Typography>
                <Typography variant="body1">
                  {selectedEntry.userInfo?.name || 'Unknown'} ({selectedEntry.userInfo?.email || 'N/A'})
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Role: {selectedEntry.userInfo?.role || 'N/A'}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                <Chip
                  icon={selectedEntry.status === 'SUCCESS' ? <CheckCircleIcon /> : <CancelIcon />}
                  label={selectedEntry.status}
                  color={statusColors[selectedEntry.status] || 'default'}
                />
                {selectedEntry.failureReason && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Failure Reason: {failureReasonDisplay[selectedEntry.failureReason] || selectedEntry.failureReason}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">IP Address</Typography>
                <Typography variant="body1">{selectedEntry.ipAddress || 'N/A'}</Typography>
              </Grid>
              {selectedEntry.location && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">Location</Typography>
                  <Typography variant="body1">
                    {[
                      selectedEntry.location.city,
                      selectedEntry.location.region,
                      selectedEntry.location.country
                    ].filter(Boolean).join(', ') || 'Unknown'}
                  </Typography>
                </Grid>
              )}
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" color="text.secondary">Device</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {deviceIcons[selectedEntry.device] || <ComputerIcon />}
                  <Typography variant="body1">{selectedEntry.device || 'Unknown'}</Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" color="text.secondary">Browser</Typography>
                <Typography variant="body1">
                  {selectedEntry.browser?.name || 'Unknown'} {selectedEntry.browser?.version || ''}
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" color="text.secondary">Operating System</Typography>
                <Typography variant="body1">
                  {selectedEntry.operatingSystem?.name || 'Unknown'} {selectedEntry.operatingSystem?.version || ''}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">User Agent</Typography>
                <Typography variant="body1" sx={{ wordBreak: 'break-word' }}>
                  {selectedEntry.userAgent || 'N/A'}
                </Typography>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseEntryDetails}>Close</Button>
          </DialogActions>
        </Dialog>
      )}
    </Box>
  );
};

export default LoginHistory;
