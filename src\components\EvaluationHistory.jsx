import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Stack,
  Switch
} from '@mui/material';
import Rating from '@mui/material/Rating';
import StarIcon from '@mui/icons-material/Star';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';
import {
  showSuccessToast,
  showErrorToast,
  showInfoToast,
  showLoadingToast,
  dismissToast,
  TOAST_CATEGORIES
} from '../Utils/toastUtils';
import api from '../Services/ApiService';
import EvaluationForm from './EvaluationForm';

const EvaluationHistory = ({ open, onClose, userId, userName, userJob }) => {
  const [loading, setLoading] = useState(true);
  const [evaluations, setEvaluations] = useState([]);
  const [editEvaluationId, setEditEvaluationId] = useState(null);
  const [showEvaluationForm, setShowEvaluationForm] = useState(false);

  // Fetch evaluations for the user
  useEffect(() => {
    if (open && userId) {
      fetchEvaluations();
    }
  }, [open, userId]);

  const fetchEvaluations = () => {
    setLoading(true);
    api.get(`/hr/evaluations/user/${userId}`)
      .then(response => {
        setEvaluations(response.data);
      })
      .catch(error => {
        console.error('Error fetching evaluations:', error);
        showErrorToast('Failed to load evaluation history', TOAST_CATEGORIES.EVALUATION, 'failed');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleCreateEvaluation = () => {
    setEditEvaluationId(null);
    setShowEvaluationForm(true);
  };

  const handleEditEvaluation = (evaluationId) => {
    setEditEvaluationId(evaluationId);
    setShowEvaluationForm(true);
  };

  const handleDeleteEvaluation = (evaluationId) => {
    if (window.confirm('Are you sure you want to delete this evaluation?')) {
      api.delete(`/hr/evaluations/${evaluationId}`)
        .then(() => {
          showSuccessToast('Evaluation deleted successfully', TOAST_CATEGORIES.EVALUATION, 'deleted');
          fetchEvaluations();
        })
        .catch(error => {
          console.error('Error deleting evaluation:', error);
          showErrorToast('Failed to delete evaluation', TOAST_CATEGORIES.EVALUATION, 'failed');
        });
    }
  };

  // Function to toggle evaluation visibility
  const handleToggleVisibility = async (evaluationId, currentVisibility) => {
    try {
      setLoading(true);

      // Update the evaluation visibility
      await api.put(`/hr/evaluations/${evaluationId}`, {
        visibleToUser: !currentVisibility
      });

      // Update the local state
      setEvaluations(evaluations.map(evaluation =>
        evaluation._id === evaluationId
          ? { ...evaluation, visibleToUser: !evaluation.visibleToUser }
          : evaluation
      ));

      showSuccessToast(`Evaluation is now ${!currentVisibility ? 'visible' : 'hidden'} to the user`, TOAST_CATEGORIES.EVALUATION, 'updated');
    } catch (error) {
      console.error('Error toggling evaluation visibility:', error);
      showErrorToast('Failed to update evaluation visibility', TOAST_CATEGORIES.EVALUATION, 'failed');
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluationFormClose = (refresh) => {
    setShowEvaluationForm(false);
    if (refresh) {
      fetchEvaluations();
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Draft':
        return 'warning';
      case 'Completed':
        return 'success';
      case 'Acknowledged':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              Evaluation History: {userName}
            </Typography>
            <Box>
              <Button
                variant="contained"
                color="primary"
                onClick={handleCreateEvaluation}
                startIcon={<EditIcon />}
                sx={{ mr: 1 }}
              >
                New Evaluation
              </Button>
              <Button
                variant="contained"
                color="secondary"
                onClick={() => {
                  // Call the AI evaluation endpoint
                  const loadingToastId = showLoadingToast('Generating AI evaluation...', { autoClose: false });

                  api.post(`/hr/evaluations/ai/${userId}`)
                    .then(response => {
                      dismissToast(loadingToastId);
                      showSuccessToast('AI evaluation generated successfully!', TOAST_CATEGORIES.EVALUATION, 'generated');
                      fetchEvaluations();
                    })
                    .catch(error => {
                      dismissToast(loadingToastId);
                      showErrorToast(`Failed to generate AI evaluation: ${error.response?.data?.message || error.message}`, TOAST_CATEGORIES.EVALUATION, 'failed');
                    });
                }}
                startIcon={<SmartToyIcon />}
              >
                Generate AI Evaluation
              </Button>
            </Box>
          </Box>
        </DialogTitle>

        <DialogContent>
          {loading ? (
            <Typography align="center" sx={{ py: 3 }}>Loading evaluations...</Typography>
          ) : evaluations.length === 0 ? (
            <Typography align="center" sx={{ py: 3 }}>No evaluations found for this employee.</Typography>
          ) : (
            <TableContainer component={Paper} sx={{ mt: 2 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Period</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Performance</TableCell>
                    <TableCell>Attitude</TableCell>
                    <TableCell>Communication</TableCell>
                    <TableCell>Teamwork</TableCell>
                    <TableCell>Initiative</TableCell>
                    <TableCell>Overall</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Visible to User</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {evaluations.map((evaluation) => (
                    <TableRow key={evaluation._id}>
                      <TableCell>{formatDate(evaluation.evaluationDate)}</TableCell>
                      <TableCell>{evaluation.evaluationPeriod}</TableCell>
                      <TableCell>
                        {evaluation.aiGenerated ? (
                          <Tooltip title="AI Generated Evaluation">
                            <Chip
                              icon={<SmartToyIcon fontSize="small" />}
                              label="AI"
                              size="small"
                              color="secondary"
                            />
                          </Tooltip>
                        ) : (
                          <Chip
                            label="Manual"
                            size="small"
                            variant="outlined"
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        <Rating
                          value={evaluation.performanceRating}
                          readOnly
                          size="small"
                          icon={<StarIcon fontSize="inherit" color="primary" />}
                          emptyIcon={<StarIcon fontSize="inherit" />}
                        />
                      </TableCell>
                      <TableCell>
                        <Rating
                          value={evaluation.attitudeRating}
                          readOnly
                          size="small"
                          icon={<StarIcon fontSize="inherit" color="primary" />}
                          emptyIcon={<StarIcon fontSize="inherit" />}
                        />
                      </TableCell>
                      <TableCell>
                        <Rating
                          value={evaluation.communicationRating}
                          readOnly
                          size="small"
                          icon={<StarIcon fontSize="inherit" color="primary" />}
                          emptyIcon={<StarIcon fontSize="inherit" />}
                        />
                      </TableCell>
                      <TableCell>
                        <Rating
                          value={evaluation.teamworkRating}
                          readOnly
                          size="small"
                          icon={<StarIcon fontSize="inherit" color="primary" />}
                          emptyIcon={<StarIcon fontSize="inherit" />}
                        />
                      </TableCell>
                      <TableCell>
                        <Rating
                          value={evaluation.initiativeRating}
                          readOnly
                          size="small"
                          icon={<StarIcon fontSize="inherit" color="primary" />}
                          emptyIcon={<StarIcon fontSize="inherit" />}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ fontWeight: 'bold' }}>{evaluation.overallRating}</Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={evaluation.status}
                          color={getStatusColor(evaluation.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Tooltip title={evaluation.visibleToUser ? "Click to hide from user" : "Click to make visible to user"}>
                          <Chip
                            icon={evaluation.visibleToUser ? <VisibilityIcon fontSize="small" /> : <VisibilityOffIcon fontSize="small" />}
                            label={evaluation.visibleToUser ? "Visible" : "Hidden"}
                            color={evaluation.visibleToUser ? "success" : "default"}
                            size="small"
                            onClick={() => handleToggleVisibility(evaluation._id, evaluation.visibleToUser)}
                            sx={{
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                bgcolor: evaluation.visibleToUser ? 'success.light' : 'action.hover',
                                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                              }
                            }}
                          />
                        </Tooltip>
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => handleEditEvaluation(evaluation._id)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteEvaluation(evaluation._id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {showEvaluationForm && (
        <EvaluationForm
          open={showEvaluationForm}
          onClose={handleEvaluationFormClose}
          userId={userId}
          userName={userName}
          userJob={userJob}
          evaluationId={editEvaluationId}
        />
      )}
    </>
  );
};

export default EvaluationHistory;
