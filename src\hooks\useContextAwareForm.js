import { useEffect, useCallback, useRef } from 'react';
import contextApiService from '../Services/ContextApiService';

/**
 * Hook for context-aware form functionality
 * Automatically detects form interactions and provides intelligent suggestions
 */
export const useContextAwareForm = (formType, options = {}) => {
  const {
    enableAutoDetection = true,
    debounceMs = 1000,
    onSuggestionsReceived = null
  } = options;

  const formRef = useRef(null);
  const debounceRef = useRef(null);
  const lastContextRef = useRef(null);

  /**
   * Process form context with debouncing
   */
  const processFormContext = useCallback(async (action, data = {}) => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(async () => {
      try {
        const contextKey = `${formType}_${action}_${JSON.stringify(data)}`;
        
        // Avoid duplicate processing
        if (lastContextRef.current === contextKey) {
          return;
        }
        
        lastContextRef.current = contextKey;
        
        const result = await contextApiService.processContext(formType, action, {
          ...data,
          formType,
          timestamp: Date.now()
        });
        
        if (result?.data?.suggestions?.length > 0 && onSuggestionsReceived) {
          onSuggestionsReceived(result.data.suggestions, action, data);
        }
        
        return result;
      } catch (error) {
        console.error('Error processing form context:', error);
      }
    }, debounceMs);
  }, [formType, debounceMs, onSuggestionsReceived]);

  /**
   * Track form field interactions
   */
  const trackFieldInteraction = useCallback((fieldName, fieldValue, fieldType = 'input') => {
    if (!enableAutoDetection) return;

    const data = {
      fieldName,
      fieldValue,
      fieldType,
      hasValue: !!fieldValue
    };

    // Special handling for different form types
    if (formType === 'leave_request_form') {
      if (fieldName.includes('type') || fieldName.includes('leave')) {
        processFormContext('leave_type_selected', { 
          ...data, 
          leaveType: fieldValue 
        });
      } else if (fieldType === 'date') {
        processFormContext('dates_entered', { 
          ...data, 
          [fieldName]: fieldValue 
        });
      } else if (fieldName.includes('reason')) {
        processFormContext('reason_entered', { 
          ...data, 
          reasonLength: fieldValue?.length || 0 
        });
      }
    } else if (formType === 'user_management_form') {
      if (fieldName.includes('email')) {
        processFormContext('email_entered', { 
          ...data, 
          emailValid: /\S+@\S+\.\S+/.test(fieldValue) 
        });
      } else if (fieldName.includes('role') || fieldName.includes('department')) {
        processFormContext('role_selected', { 
          ...data, 
          role: fieldValue 
        });
      }
    } else {
      // Generic form interaction
      processFormContext('field_interaction', data);
    }
  }, [formType, enableAutoDetection, processFormContext]);

  /**
   * Track form submission
   */
  const trackFormSubmission = useCallback((formData = {}) => {
    processFormContext('form_submission', {
      formData,
      fieldCount: Object.keys(formData).length,
      completedFields: Object.values(formData).filter(value => !!value).length
    });
  }, [processFormContext]);

  /**
   * Track form validation errors
   */
  const trackValidationErrors = useCallback((errors = {}) => {
    processFormContext('validation_errors', {
      errors,
      errorCount: Object.keys(errors).length,
      errorFields: Object.keys(errors)
    });
  }, [processFormContext]);

  /**
   * Track form focus/blur events
   */
  const trackFormFocus = useCallback(() => {
    processFormContext('form_focus', {
      timestamp: Date.now()
    });
  }, [processFormContext]);

  /**
   * Get suggestions for current form state
   */
  const getSuggestions = useCallback(async (currentData = {}) => {
    try {
      const result = await contextApiService.processContext(formType, 'get_suggestions', {
        currentData,
        formType,
        timestamp: Date.now()
      });
      
      return result?.data?.suggestions || [];
    } catch (error) {
      console.error('Error getting form suggestions:', error);
      return [];
    }
  }, [formType]);

  /**
   * Auto-detect form interactions when enabled
   */
  useEffect(() => {
    if (!enableAutoDetection || !formRef.current) return;

    const form = formRef.current;
    
    // Add event listeners for form interactions
    const handleInputChange = (event) => {
      const { name, value, type } = event.target;
      if (name) {
        trackFieldInteraction(name, value, type);
      }
    };

    const handleSelectChange = (event) => {
      const { name, value } = event.target;
      if (name) {
        trackFieldInteraction(name, value, 'select');
      }
    };

    const handleFormSubmit = (event) => {
      const formData = new FormData(form);
      const data = Object.fromEntries(formData.entries());
      trackFormSubmission(data);
    };

    const handleFormFocus = () => {
      trackFormFocus();
    };

    // Add listeners
    form.addEventListener('input', handleInputChange);
    form.addEventListener('change', handleSelectChange);
    form.addEventListener('submit', handleFormSubmit);
    form.addEventListener('focusin', handleFormFocus);

    // Cleanup
    return () => {
      form.removeEventListener('input', handleInputChange);
      form.removeEventListener('change', handleSelectChange);
      form.removeEventListener('submit', handleFormSubmit);
      form.removeEventListener('focusin', handleFormFocus);
      
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [enableAutoDetection, trackFieldInteraction, trackFormSubmission, trackFormFocus]);

  /**
   * Initialize form context detection
   */
  useEffect(() => {
    if (enableAutoDetection) {
      processFormContext('form_detected', {
        formType,
        timestamp: Date.now()
      });
    }
  }, [formType, enableAutoDetection, processFormContext]);

  return {
    formRef,
    trackFieldInteraction,
    trackFormSubmission,
    trackValidationErrors,
    trackFormFocus,
    getSuggestions,
    processFormContext
  };
};

/**
 * Hook specifically for leave request forms
 */
export const useLeaveRequestForm = (options = {}) => {
  return useContextAwareForm('leave_request_form', options);
};

/**
 * Hook specifically for user management forms
 */
export const useUserManagementForm = (options = {}) => {
  return useContextAwareForm('user_management_form', options);
};

/**
 * Hook specifically for task management forms
 */
export const useTaskManagementForm = (options = {}) => {
  return useContextAwareForm('task_management_form', options);
};

export default useContextAwareForm;
