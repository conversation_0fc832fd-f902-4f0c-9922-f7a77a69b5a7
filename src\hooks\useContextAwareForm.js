import { useEffect, useCallback, useRef } from 'react';
// import contextApiService from '../Services/ContextApiService';

/**
 * Hook for context-aware form functionality - DISABLED
 * Context-aware functionality removed - chatbot should not appear automatically
 */
export const useContextAwareForm = (formType, options = {}) => {
  console.log('⚠️ useContextAwareForm disabled - context-aware functionality removed');

  const formRef = useRef(null);

  // Return disabled stubs

  // All methods disabled - chatbot should not appear automatically
  const processFormContext = useCallback(async (action, data = {}) => { /* disabled */ }, []);
  const trackFieldInteraction = useCallback((fieldName, fieldValue, fieldType = 'input') => { /* disabled */ }, []);
  const trackFormSubmission = useCallback((formData = {}) => { /* disabled */ }, []);
  const trackValidationErrors = useCallback((errors = {}) => { /* disabled */ }, []);
  const trackFormFocus = useCallback(() => { /* disabled */ }, []);
  const getSuggestions = useCallback(async (currentData = {}) => { return []; }, []);

  return {
    formRef,
    trackFieldInteraction,
    trackFormSubmission,
    trackValidationErrors,
    trackFormFocus,
    getSuggestions,
    processFormContext
  };
};

// All implementation removed - context-aware functionality disabled

/**
 * Hook specifically for leave request forms
 */
export const useLeaveRequestForm = (options = {}) => {
  return useContextAwareForm('leave_request_form', options);
};

/**
 * Hook specifically for user management forms
 */
export const useUserManagementForm = (options = {}) => {
  return useContextAwareForm('user_management_form', options);
};

/**
 * Hook specifically for task management forms
 */
export const useTaskManagementForm = (options = {}) => {
  return useContextAwareForm('task_management_form', options);
};

export default useContextAwareForm;
