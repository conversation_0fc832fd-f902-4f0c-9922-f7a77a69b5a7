import { toast } from 'react-toastify';

// Toast notification categories
const TOAST_CATEGORIES = {
  AUTH: 'auth',
  DATA: 'data',
  ACTION: 'action',
  SYSTEM: 'system',
  NLP: 'nlp',
  EVALUATION: 'evaluation'
};

// Configuration for different toast types
const toastConfig = {
  // Default configuration
  default: {
    position: "top-right",
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    closeButton: true,
  },

  // Success toast configuration
  success: {
    position: "top-right",
    autoClose: 2000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    closeButton: true,
  },

  // Error toast configuration
  error: {
    position: "top-right",
    autoClose: 4000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    closeButton: true,
  },

  // Info toast configuration
  info: {
    position: "top-right",
    autoClose: 3000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    closeButton: true,
  },

  // Warning toast configuration
  warning: {
    position: "top-right",
    autoClose: 4000,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    closeButton: true,
  },

  // Loading toast configuration
  loading: {
    position: "top-right",
    autoClose: false,
    hideProgressBar: false,
    closeOnClick: false,
    pauseOnHover: true,
    draggable: false,
    progress: undefined,
    closeButton: true,
  }
};

// Essential notifications that should always be shown
const essentialNotifications = {
  // Authentication related notifications
  [TOAST_CATEGORIES.AUTH]: {
    login: true,
    logout: true,
    passwordReset: true,
    unauthorized: true,
  },

  // Data related notifications
  [TOAST_CATEGORIES.DATA]: {
    saveSuccess: true,
    saveFailed: true,
    loadFailed: true,
  },

  // Action related notifications
  [TOAST_CATEGORIES.ACTION]: {
    taskCompleted: true,
    leaveRequestSubmitted: true,
    leaveRequestStatusChanged: true,
    attendanceRecorded: true,
  },

  // System related notifications
  [TOAST_CATEGORIES.SYSTEM]: {
    criticalError: true,
  },

  // NLP related notifications - all are essential
  [TOAST_CATEGORIES.NLP]: {
    started: true,
    completed: true,
    failed: true,
    reportGenerated: true,
    reportViewed: true,
    cvProcessed: true,
    jobMatched: true,
  },

  // Evaluation related notifications - all are essential
  [TOAST_CATEGORIES.EVALUATION]: {
    created: true,
    updated: true,
    deleted: true,
    generated: true,
    viewed: true,
    submitted: true,
  }
};

/**
 * Show a success toast notification
 * @param {string} message - The message to display
 * @param {string} category - The category of the notification
 * @param {string} key - The specific key within the category
 * @param {Object} options - Additional toast options
 * @returns {string|number|null} Toast ID or null if not shown
 */
export const showSuccessToast = (message, category = null, key = null, options = {}) => {
  if (shouldShowNotification(category, key)) {
    return toast.success(message, { ...toastConfig.success, ...options });
  }
  return null;
};

/**
 * Show an error toast notification
 * @param {string} message - The message to display
 * @param {string} category - The category of the notification
 * @param {string} key - The specific key within the category
 * @param {Object} options - Additional toast options
 * @returns {string|number|null} Toast ID or null if not shown
 */
export const showErrorToast = (message, category = null, key = null, options = {}) => {
  if (shouldShowNotification(category, key)) {
    return toast.error(message, { ...toastConfig.error, ...options });
  }
  return null;
};

/**
 * Show an info toast notification
 * @param {string} message - The message to display
 * @param {string} category - The category of the notification
 * @param {string} key - The specific key within the category
 * @param {Object} options - Additional toast options
 * @returns {string|number|null} Toast ID or null if not shown
 */
export const showInfoToast = (message, category = null, key = null, options = {}) => {
  if (shouldShowNotification(category, key)) {
    return toast.info(message, { ...toastConfig.info, ...options });
  }
  return null;
};

/**
 * Show a warning toast notification
 * @param {string} message - The message to display
 * @param {string} category - The category of the notification
 * @param {string} key - The specific key within the category
 * @param {Object} options - Additional toast options
 * @returns {string|number|null} Toast ID or null if not shown
 */
export const showWarningToast = (message, category = null, key = null, options = {}) => {
  if (shouldShowNotification(category, key)) {
    return toast.warning(message, { ...toastConfig.warning, ...options });
  }
  return null;
};

/**
 * Show a loading toast notification
 * @param {string} message - The message to display
 * @param {Object} options - Additional toast options
 * @returns {string|number} Toast ID
 */
export const showLoadingToast = (message, options = {}) => {
  return toast.loading(message, { ...toastConfig.loading, ...options });
};

/**
 * Update an existing toast notification
 * @param {string|number} toastId - The ID of the toast to update
 * @param {Object} options - The options to update
 */
export const updateToast = (toastId, options) => {
  toast.update(toastId, options);
};

/**
 * Dismiss a toast notification
 * @param {string|number} toastId - The ID of the toast to dismiss
 */
export const dismissToast = (toastId) => {
  toast.dismiss(toastId);
};

/**
 * Determine if a notification should be shown based on category and key
 * @param {string} category - The category of the notification
 * @param {string} key - The specific key within the category
 * @returns {boolean} Whether the notification should be shown
 */
const shouldShowNotification = (category, key) => {
  // If no category or key is provided, show the notification
  if (!category || !key) {
    return true;
  }

  // If the category exists and the key is defined, check if it's essential
  if (essentialNotifications[category] && essentialNotifications[category][key] !== undefined) {
    return essentialNotifications[category][key];
  }

  // By default, don't show non-essential notifications
  return false;
};

export { TOAST_CATEGORIES };
