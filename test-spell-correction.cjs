const axios = require('axios');

async function testSpellCorrection() {
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'test123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.token;

    // Test spell correction and typo tolerance
    console.log('\n📝 Testing Spell Correction & Typo Tolerance...\n');
    
    const typoTests = [
      {
        message: "Can you chek my leav balanse? I ned to tak som tim of",
        description: "Multiple typos in leave balance request"
      },
      {
        message: "hey whats up? can u help me w/ my tasks plz?",
        description: "Casual language and abbreviations"
      },
      {
        message: "im realy stresed abt my performence reveiw tomoro",
        description: "Stress-related typos and informal language"
      },
      {
        message: "URGENT: I need to check in for work ASAP!",
        description: "Urgency with abbreviations"
      },
      {
        message: "i dont no how 2 submitt my timesheet",
        description: "Multiple casual language patterns"
      }
    ];

    for (let i = 0; i < typoTests.length; i++) {
      const test = typoTests[i];
      console.log(`\n📝 Test ${i + 1}: ${test.description}`);
      console.log(`👤 Original: "${test.message}"`);
      
      try {
        const response = await axios.post('http://localhost:5000/api/ai/chat/message', {
          message: test.message
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        const data = response.data.data;
        const classification = data.classification;

        if (classification.metadata?.hasTypos) {
          console.log(`✅ Corrected: "${classification.metadata.correctedText}"`);
        } else {
          console.log(`ℹ️  No corrections needed`);
        }

        console.log(`🎯 Intent: ${classification.intent} (${Math.round(classification.confidence * 100)}%)`);
        
        if (classification.metadata?.urgency !== 'normal') {
          console.log(`⚡ Urgency: ${classification.metadata.urgency}`);
        }

        console.log(`⚡ Response time: ${data.assistantMessage.metadata.responseTime}ms`);
        
      } catch (error) {
        console.error(`❌ Error in test ${i + 1}:`, error.response?.data?.message || error.message);
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n🎉 Spell Correction Testing Complete!');

  } catch (error) {
    console.error('❌ Error:', error.response?.data?.message || error.message);
  }
}

testSpellCorrection();
