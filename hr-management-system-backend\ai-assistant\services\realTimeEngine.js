/**
 * Real-Time Processing Engine for Jarvis
 * Handles real-time responses, streaming, and advanced processing
 */

class RealTimeEngine {
  constructor() {
    this.activeConnections = new Map();
    this.processingQueue = [];
    this.responseCache = new Map();
    this.contextBuffer = new Map();
    this.isProcessing = false;

    this.initializeRealTimeFeatures();
  }

  /**
   * Initialize real-time processing features
   */
  initializeRealTimeFeatures() {
    this.config = {
      maxConcurrentProcessing: 10,
      responseTimeout: parseInt(process.env.AI_RESPONSE_TIMEOUT) || 15000,
      cacheExpiry: 5 * 60 * 1000, // 5 minutes
      streamingEnabled: process.env.AI_REAL_TIME_PROCESSING === 'true',
      advancedAnalytics: process.env.AI_ADVANCED_ANALYTICS === 'true',
      emotionalIntelligence: process.env.AI_EMOTIONAL_INTELLIGENCE === 'true'
    };

    // Start processing queue
    this.startProcessingLoop();

    // Clean cache periodically
    setInterval(() => this.cleanCache(), 60000); // Every minute
  }

  /**
   * Process message with real-time optimizations
   */
  async processMessage(userId, message, context = {}) {
    const startTime = Date.now();
    const messageId = this.generateMessageId();

    try {
      // Check cache first for similar queries
      const cachedResponse = this.getCachedResponse(message, userId);
      if (cachedResponse) {
        return {
          ...cachedResponse,
          cached: true,
          responseTime: Date.now() - startTime
        };
      }

      // Add to processing queue with priority
      const priority = this.calculatePriority(message, context);
      const processingTask = {
        id: messageId,
        userId,
        message,
        context,
        priority,
        timestamp: startTime,
        resolve: null,
        reject: null
      };

      // Create promise for async processing
      const responsePromise = new Promise((resolve, reject) => {
        processingTask.resolve = resolve;
        processingTask.reject = reject;
      });

      // Add to queue
      this.addToQueue(processingTask);

      // Set timeout
      const timeoutId = setTimeout(() => {
        this.handleTimeout(messageId);
      }, this.config.responseTimeout);

      const response = await responsePromise;
      clearTimeout(timeoutId);

      // Cache successful response
      this.cacheResponse(message, userId, response);

      return {
        ...response,
        responseTime: Date.now() - startTime,
        messageId,
        metadata: {
          ...response.metadata,
          modelVersion: 'GPT-4.1',
          assistantName: 'Jarvis',
          realTimeProcessed: true,
          powered_by: 'OpenAI GPT-4.1'
        }
      };

    } catch (error) {
      console.error('Real-time processing error:', error);
      return {
        content: "I apologize, but I'm experiencing a temporary processing delay. Please try again in a moment.",
        type: 'error',
        responseTime: Date.now() - startTime,
        error: true
      };
    }
  }

  /**
   * Add task to processing queue with priority sorting
   */
  addToQueue(task) {
    this.processingQueue.push(task);

    // Sort by priority (higher priority first)
    this.processingQueue.sort((a, b) => b.priority - a.priority);

    // Trigger processing if not already running
    if (!this.isProcessing) {
      this.processNextInQueue();
    }
  }

  /**
   * Start the processing loop
   */
  startProcessingLoop() {
    setInterval(() => {
      if (!this.isProcessing && this.processingQueue.length > 0) {
        this.processNextInQueue();
      }
    }, 100); // Check every 100ms
  }

  /**
   * Process next task in queue
   */
  async processNextInQueue() {
    if (this.isProcessing || this.processingQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    const task = this.processingQueue.shift();

    try {
      // Get enhanced context
      const enhancedContext = await this.buildEnhancedContext(task.userId, task.context);

      // Process with appropriate service
      const response = await this.processWithIntelligence(
        task.message,
        enhancedContext,
        task.userId
      );

      // Update context buffer
      this.updateContextBuffer(task.userId, task.message, response);

      task.resolve(response);
    } catch (error) {
      console.error('Queue processing error:', error);
      task.reject(error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process message with enhanced intelligence
   */
  async processWithIntelligence(message, context, userId) {
    const openaiService = require('./openaiService');
    const intentClassifier = require('./intentClassifier');
    const emotionAnalyzer = require('./emotionAnalyzer');
    const personalityEngine = require('./personalityEngine');

    // Enhanced intent classification
    const classification = intentClassifier.classifyIntent(message, context.conversationHistory, context);

    // Emotional analysis if enabled
    let emotionalAnalysis = null;
    if (this.config.emotionalIntelligence) {
      emotionalAnalysis = emotionAnalyzer.analyzeComplete(message);
    }

    // Try OpenAI first for advanced responses
    if (openaiService.isAvailable()) {
      try {
        const aiResponse = await openaiService.generateResponse(
          message,
          context,
          classification.intent,
          classification.entities
        );

        // Enhance with personality if needed
        if (emotionalAnalysis?.summary?.needsEmotionalSupport) {
          const emotionalResponse = personalityEngine.generateEmpatheticResponse(
            emotionalAnalysis.primaryEmotion,
            message,
            context.conversationHistory,
            userId
          );

          // Blend AI response with emotional intelligence
          aiResponse.content = this.blendResponses(aiResponse.content, emotionalResponse.content);
          aiResponse.tone = emotionalResponse.tone;
        }

        return aiResponse;
      } catch (error) {
        console.warn('OpenAI processing failed, using enhanced fallback:', error.message);
      }
    }

    // Enhanced fallback with personality
    if (emotionalAnalysis?.summary?.needsEmotionalSupport) {
      return personalityEngine.generateEmpatheticResponse(
        emotionalAnalysis.primaryEmotion,
        message,
        context.conversationHistory,
        userId
      );
    }

    // Standard intelligent response
    return openaiService.getFallbackResponse(message, classification.intent);
  }

  /**
   * Build enhanced context with real-time data
   */
  async buildEnhancedContext(userId, baseContext) {
    const contextBuffer = this.contextBuffer.get(userId) || [];

    return {
      ...baseContext,
      recentInteractions: contextBuffer.slice(-5), // Last 5 interactions
      processingTime: Date.now(),
      realTimeFeatures: {
        streamingEnabled: this.config.streamingEnabled,
        advancedAnalytics: this.config.advancedAnalytics,
        emotionalIntelligence: this.config.emotionalIntelligence
      }
    };
  }

  /**
   * Update context buffer for user
   */
  updateContextBuffer(userId, message, response) {
    if (!this.contextBuffer.has(userId)) {
      this.contextBuffer.set(userId, []);
    }

    const buffer = this.contextBuffer.get(userId);
    buffer.push({
      timestamp: Date.now(),
      userMessage: message.substring(0, 100), // Truncate for memory
      assistantResponse: response.content.substring(0, 100),
      intent: response.metadata?.intent
    });

    // Keep only last 10 interactions
    if (buffer.length > 10) {
      buffer.splice(0, buffer.length - 10);
    }

    this.contextBuffer.set(userId, buffer);
  }

  /**
   * Calculate processing priority
   */
  calculatePriority(message, context) {
    let priority = 5; // Base priority

    // Urgent keywords increase priority
    const urgentKeywords = ['urgent', 'emergency', 'asap', 'immediately', 'critical'];
    if (urgentKeywords.some(keyword => message.toLowerCase().includes(keyword))) {
      priority += 3;
    }

    // User role affects priority
    if (context.userRole === 'HR' || context.userRole === 'Admin') {
      priority += 2;
    }

    // Emotional distress increases priority
    const distressKeywords = ['help', 'problem', 'issue', 'worried', 'stressed'];
    if (distressKeywords.some(keyword => message.toLowerCase().includes(keyword))) {
      priority += 1;
    }

    return priority;
  }

  /**
   * Cache response for similar queries
   */
  cacheResponse(message, userId, response) {
    const cacheKey = this.generateCacheKey(message, userId);
    this.responseCache.set(cacheKey, {
      response,
      timestamp: Date.now(),
      userId
    });
  }

  /**
   * Get cached response if available and valid
   */
  getCachedResponse(message, userId) {
    const cacheKey = this.generateCacheKey(message, userId);
    const cached = this.responseCache.get(cacheKey);

    if (cached && (Date.now() - cached.timestamp) < this.config.cacheExpiry) {
      return cached.response;
    }

    return null;
  }

  /**
   * Generate cache key for message
   */
  generateCacheKey(message, userId) {
    // Simple hash for caching similar messages
    const normalized = message.toLowerCase().trim().replace(/[^\w\s]/g, '');
    return `${userId}_${normalized.substring(0, 50)}`;
  }

  /**
   * Generate unique message ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Handle processing timeout
   */
  handleTimeout(messageId) {
    const taskIndex = this.processingQueue.findIndex(task => task.id === messageId);
    if (taskIndex !== -1) {
      const task = this.processingQueue.splice(taskIndex, 1)[0];
      task.reject(new Error('Processing timeout'));
    }
  }

  /**
   * Blend AI response with emotional response
   */
  blendResponses(aiResponse, emotionalResponse) {
    // Simple blending - can be enhanced with more sophisticated logic
    if (emotionalResponse.length > aiResponse.length) {
      return emotionalResponse + '\n\n' + aiResponse;
    }
    return aiResponse + '\n\n' + emotionalResponse;
  }

  /**
   * Clean expired cache entries
   */
  cleanCache() {
    const now = Date.now();
    for (const [key, value] of this.responseCache.entries()) {
      if (now - value.timestamp > this.config.cacheExpiry) {
        this.responseCache.delete(key);
      }
    }
  }

  /**
   * Get real-time statistics
   */
  getStats() {
    return {
      activeConnections: this.activeConnections.size,
      queueLength: this.processingQueue.length,
      cacheSize: this.responseCache.size,
      contextBuffers: this.contextBuffer.size,
      isProcessing: this.isProcessing,
      config: this.config
    };
  }
}

// Singleton instance
const realTimeEngine = new RealTimeEngine();

module.exports = realTimeEngine;
