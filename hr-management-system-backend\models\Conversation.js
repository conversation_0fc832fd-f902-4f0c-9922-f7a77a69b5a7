const mongoose = require('mongoose');

// Conversation Schema for AI Assistant
const conversationSchema = new mongoose.Schema({
  // User who owns this conversation
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Conversation title (auto-generated or user-defined)
  title: {
    type: String,
    default: 'New Conversation'
  },

  // Conversation status
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  },

  // Messages in the conversation
  messages: [{
    // Message ID for reference
    messageId: {
      type: String,
      required: true
    },

    // Role: 'user', 'assistant', 'system'
    role: {
      type: String,
      enum: ['user', 'assistant', 'system'],
      required: true
    },

    // Message content
    content: {
      type: String,
      required: true
    },

    // Message type for different UI rendering
    type: {
      type: String,
      enum: ['text', 'form', 'suggestion', 'action', 'error', 'loading', 'list', 'success', 'info', 'warning', 'casual_conversation', 'emotional_support'],
      default: 'text'
    },

    // Timestamp
    timestamp: {
      type: Date,
      default: Date.now
    },

    // Additional metadata
    metadata: {
      // Intent detected by AI
      intent: String,

      // Confidence score for intent
      confidence: Number,

      // Entities extracted from message
      entities: [{
        type: String,
        value: String,
        confidence: Number
      }],

      // Action performed (if any)
      action: {
        type: String,
        parameters: Object,
        result: Object
      },

      // Response time for assistant messages
      responseTime: Number,

      // Model used for generation
      model: String,

      // Token usage
      tokens: {
        prompt: Number,
        completion: Number,
        total: Number
      }
    }
  }],

  // Conversation context and state
  context: {
    // Current conversation topic/domain
    domain: {
      type: String,
      enum: ['general', 'leave_management', 'task_management', 'hr_policies', 'evaluations', 'applications', 'attendance'],
      default: 'general'
    },

    // User's current intent
    currentIntent: String,

    // Entities being tracked across messages
    trackedEntities: Object,

    // Form data being collected
    formData: Object,

    // Conversation state for multi-turn interactions
    state: {
      type: String,
      enum: ['idle', 'collecting_info', 'confirming_action', 'executing_action', 'waiting_approval'],
      default: 'idle'
    },

    // Last action timestamp
    lastActivity: {
      type: Date,
      default: Date.now
    }
  },

  // Conversation summary (for long conversations)
  summary: {
    type: String,
    default: ''
  },

  // Conversation tags for categorization
  tags: [String],

  // Feedback and ratings
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    timestamp: Date
  },

  // Privacy and sharing settings
  privacy: {
    isPrivate: {
      type: Boolean,
      default: true
    },
    sharedWith: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      permission: {
        type: String,
        enum: ['read', 'comment'],
        default: 'read'
      }
    }]
  }
}, {
  timestamps: true,
  // Optimize for queries
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
conversationSchema.index({ userId: 1, createdAt: -1 });
conversationSchema.index({ 'context.domain': 1 });
conversationSchema.index({ status: 1 });
conversationSchema.index({ tags: 1 });
conversationSchema.index({ 'context.lastActivity': -1 });

// Virtual for message count
conversationSchema.virtual('messageCount').get(function() {
  return this.messages.length;
});

// Virtual for last message
conversationSchema.virtual('lastMessage').get(function() {
  return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
});

// Virtual for conversation duration
conversationSchema.virtual('duration').get(function() {
  if (this.messages.length < 2) return 0;
  const firstMessage = this.messages[0];
  const lastMessage = this.messages[this.messages.length - 1];
  return lastMessage.timestamp - firstMessage.timestamp;
});

// Method to add a message
conversationSchema.methods.addMessage = function(messageData) {
  const { v4: uuidv4 } = require('uuid');
  const messageId = uuidv4();
  const message = {
    messageId,
    ...messageData,
    timestamp: new Date()
  };

  this.messages.push(message);
  this.context.lastActivity = new Date();

  // Auto-generate title from first user message
  if (this.messages.length === 1 && messageData.role === 'user' && this.title === 'New Conversation') {
    this.title = messageData.content.substring(0, 50) + (messageData.content.length > 50 ? '...' : '');
  }

  return message;
};

// Method to update conversation context
conversationSchema.methods.updateContext = function(contextUpdate) {
  this.context = { ...this.context, ...contextUpdate };
  this.context.lastActivity = new Date();
};

// Method to archive conversation
conversationSchema.methods.archive = function() {
  this.status = 'archived';
  return this.save();
};

// Static method to get active conversations for user
conversationSchema.statics.getActiveConversations = function(userId, limit = 10) {
  return this.find({
    userId,
    status: 'active'
  })
  .sort({ 'context.lastActivity': -1 })
  .limit(limit);
};

// Static method to search conversations
conversationSchema.statics.searchConversations = function(userId, query, options = {}) {
  const searchCriteria = {
    userId,
    status: { $ne: 'deleted' },
    $or: [
      { title: { $regex: query, $options: 'i' } },
      { 'messages.content': { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } }
    ]
  };

  return this.find(searchCriteria)
    .sort({ 'context.lastActivity': -1 })
    .limit(options.limit || 20);
};

// Pre-save middleware to update summary for long conversations
conversationSchema.pre('save', function(next) {
  // Update summary if conversation has more than 20 messages and no summary exists
  if (this.messages.length > 20 && !this.summary) {
    // Extract key topics and intents
    const intents = this.messages
      .filter(m => m.metadata && m.metadata.intent)
      .map(m => m.metadata.intent);

    const uniqueIntents = [...new Set(intents)];
    this.summary = `Conversation covering: ${uniqueIntents.join(', ')}`;
  }

  next();
});

const Conversation = mongoose.model('Conversation', conversationSchema);

module.exports = Conversation;
