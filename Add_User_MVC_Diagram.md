# Add User - MVC Architecture Diagram

```plantuml
@startuml Submit_Application_MVC_Diagram

!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 10
skinparam maxMessageSize 60

title Submit Application - MVC Pattern Flow

actor "👤 Job Applicant" as Applicant
participant "🖥️ **View**\n(Apply.jsx)" as View
participant "🎛️ **Controller**\n(applications.js)" as Controller
participant "📊 **Model**\n(Application.js)" as Model
participant "📋 **Job Model**\n(Job.js)" as JobModel
participant "� **Notification**\n(notificationController.js)" as NotificationCtrl
participant "�🗄️ **Database**\n(MongoDB)" as DB

== 🚀 User Interface Interaction ==

Applicant -> View : Navigate to Apply page
activate View
View -> View : Load available jobs
View -> Controller : GET /api/jobs
activate Controller
Controller -> JobModel : find({ endDate: { $gte: new Date() } })
activate JobModel
JobModel -> DB : Query active jobs
activate DB
DB --> JobModel : Return active jobs list
deactivate DB
JobModel --> Controller : Return jobs data
deactivate JobModel
Controller --> View : 200 OK - Jobs list
deactivate Controller
View -> View : Display job selection dropdown
View --> Applicant : Show application form
deactivate View

== 📝 Form Data Entry ==

Applicant -> View : Select job from dropdown
activate View
View -> View : Update jobId and jobTitle in state
note right of View
  setFormData({
    ...formData,
    jobId: selectedJob.id,
    jobTitle: selectedJob.title,
    position: selectedJob.jobType
  })
end note

Applicant -> View : Fill form fields (name, email, phone)
View -> View : Update form state on change

Applicant -> View : Upload CV file
View -> View : Validate file type (.pdf, .doc, .docx)
View -> View : Store file in form state

Applicant -> View : Submit application form
View -> View : Validate form data (client-side)

alt ❌ Form validation fails
    View -> View : Display validation alert
    View --> Applicant : Show "Please fill in all fields" message
    note right of View
      Validation checks:
      • All required fields filled
      • CV file uploaded
      • Valid file format
      • Job selected
    end note
else ✅ Form validation passes
    View -> View : Create FormData object
    note right of View
      FormData includes:
      • fullname, email, phone
      • position, jobId, jobTitle
      • cv (file upload)
    end note
    View -> Controller : POST /api/applications (multipart/form-data)
    activate Controller
end

== 🔒 Controller Processing ==

Controller -> Controller : Process file upload (multer)
Controller -> Controller : Validate request data

alt ❌ File upload fails
    Controller --> View : 400 Bad Request - "CV file is required"
    View --> Applicant : Display file upload error
else ❌ Required fields missing
    Controller --> View : 400 Bad Request - Missing fields details
    View --> Applicant : Display field validation errors
else ✅ All data valid
    Controller -> JobModel : Verify job exists
    activate JobModel
    JobModel -> DB : findById(jobId)
    activate DB
    DB --> JobModel : Return job or null
    deactivate DB

    alt ❌ Job not found
        JobModel --> Controller : Job not found
        Controller --> View : 404 Not Found - "Selected job not found"
        View --> Applicant : Display job selection error
    else ✅ Job exists
        JobModel --> Controller : Job data confirmed
        deactivate JobModel
        Controller -> Model : Create new Application
        activate Model
    end
end

== 📊 Model Operations ==

Model -> Model : Create Application instance
note right of Model
  new Application({
    fullname, email, phone,
    position, jobId, jobTitle,
    cv: normalizedFilePath,
    status: 'Pending',
    nlpProcessed: false,
    matchScore: 0
  })
end note

Model -> DB : save() - Insert application
activate DB
DB --> Model : Application saved successfully
deactivate DB

Model -> DB : findById() - Reload with all fields
activate DB
DB --> Model : Return complete application data
deactivate DB
Model --> Controller : Return saved application
deactivate Model

== 🔔 Notification System ==

Controller -> NotificationCtrl : Create application notification
activate NotificationCtrl
NotificationCtrl -> NotificationCtrl : Find HR users
NotificationCtrl -> DB : Create notification for HR
activate DB
note right of NotificationCtrl
  Notification: {
    type: 'NEW_APPLICATION',
    title: 'New Job Application',
    content: 'New application from [fullname] for [jobTitle]',
    userId: hrUserId
  }
end note
DB --> NotificationCtrl : Notification created
deactivate DB
NotificationCtrl --> Controller : Notification sent
deactivate NotificationCtrl

== 🎉 Success Response ==

Controller --> View : 201 Created - Application submitted
note right of Controller
  Response: {
    message: 'Application submitted successfully!',
    application: {
      id: applicationId,
      fullname: applicantName,
      jobTitle: jobTitle
    }
  }
end note
deactivate Controller

View -> View : Set success state
View -> View : Store application ID
View -> View : Reset form data
View -> View : Display success message with tracking ID
View --> Applicant : Show "Application submitted successfully!"
deactivate View

note over Applicant, DB : ✨ Application Successfully Submitted ✨

== 🔄 Alternative Flows ==

group Alternative 1: File Upload Error
    Applicant -> View : Upload invalid file type
    View -> View : Client-side file validation fails
    View --> Applicant : Display "Invalid file format" error
end

group Alternative 2: Network Error
    View -> Controller : POST /api/applications
    Controller --> View : 500 Server Error
    View --> Applicant : Display "Server error while submitting"
end

group Alternative 3: Job Selection Change
    Applicant -> View : Change job selection
    View -> View : Update jobId, jobTitle, and position
    View --> Applicant : Form updated with new job details
end

@enduml
```


