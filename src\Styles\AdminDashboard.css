/* File: src/styles/AdminDashboard.css */

body, html, #root {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  font-family: Arial, sans-serif;
}

.dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

.dashboard-header {
  background-color: #2c3e50;
  color: white;
  padding: 20px;
  text-align: center;
}

.dashboard-content {
  display: flex;
  flex: 1;
  height: calc(100vh - 80px);
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background-color: #34495e;
  color: white;
  padding: 15px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sidebar h2 {
  margin-bottom: 20px;
}

.sidebar button {
  background-color: #2c3e50;
  color: white;
  border: none;
  padding: 10px;
  text-align: left;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sidebar button:hover {
  background-color: #1a252f;
}

.logout-btn {
  background-color: #c0392b;
  margin-top: auto;
}

.logout-btn:hover {
  background-color: #a93226;
}

.main-section {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f6fa;
  box-sizing: border-box;
}

.main-section h2 {
  margin-bottom: 20px;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

th, td {
  padding: 12px;
  border: 1px solid #ddd;
  text-align: left;
}

th {
  background-color: #ecf0f1;
}

.edit-btn {
  background-color: #27ae60;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.edit-btn:hover {
  background-color: #1e8449;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 3px;
  cursor: pointer;
}

.delete-btn:hover {
  background-color: #c0392b;
}

.view-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 6px 12px;
  margin-right: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.view-btn:hover {
  background-color: #2980b9;
}

.add-user-btn {
  background-color: #2980b9;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 20px;
}

.add-user-btn:hover {
  background-color: #1f618d;
}

.users-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.modal-content h2 {
  margin-bottom: 20px;
  color: #2c3e50;
  border-bottom: 2px solid #ecf0f1;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.form-buttons button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-buttons button[type="submit"] {
  background-color: #27ae60;
  color: white;
}

.form-buttons button[type="submit"]:hover {
  background-color: #1e8449;
}

.form-buttons button[type="button"] {
  background-color: #7f8c8d;
  color: white;
}

.form-buttons button[type="button"]:hover {
  background-color: #6c7a7d;
}

/* User Detail Modal */
.user-detail-modal {
  width: 90%;
  max-width: 800px;
}

.user-detail-container {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.user-detail-container p {
  margin-bottom: 10px;
  line-height: 1.5;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .dashboard-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    margin-bottom: 15px;
  }

  .main-section {
    width: 100%;
  }

  table {
    font-size: 14px;
  }

  th, td {
    padding: 10px;
  }

  .modal-content {
    width: 95%;
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    padding: 10px;
  }

  .main-section {
    padding: 15px;
  }

  table {
    font-size: 12px;
  }
}
