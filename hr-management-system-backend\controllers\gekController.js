const User = require('../models/user');
const Task = require('../models/Task');
const Attendance = require('../models/Attendance');
const Evaluation = require('../models/Evaluation');
const GEKEstimate = require('../models/GEKEstimate');

/**
 * GEK Controller - Handles General Estimating Knowledge functionality
 * Provides methods for calculating fit scores, estimating task completion times,
 * and ranking employees for task assignments
 */

// ==================== HELPER FUNCTIONS ====================

/**
 * Calculate task completion metrics for a user
 * @param {string} userId - User ID
 * @param {string} taskCategory - Task category to filter by (optional)
 * @param {number} days - Number of days to look back (default: 90)
 * @returns {Promise<Object>} - Task metrics
 */
const calculateTaskMetrics = async (userId, taskCategory = null, days = 90) => {
  try {
    // Calculate date threshold
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);

    // Build query
    const query = {
      assignedTo: userId,
      createdAt: { $gte: dateThreshold }
    };

    // Add category filter if provided
    if (taskCategory) {
      query.category = taskCategory;
    }

    // Get all tasks for the user in the time period
    const tasks = await Task.find(query);

    // Initialize metrics
    const metrics = {
      totalTasks: tasks.length,
      completedTasks: 0,
      onTimeTasks: 0,
      lateCompletedTasks: 0,
      highPriorityTotal: 0,
      highPriorityCompleted: 0,
      averageCompletionTime: 0,
      taskIds: tasks.map(task => task._id),
      completionRate: 0,
      onTimeRate: 0,
      efficiencyScore: 0,
      qualityScore: 3.0 // Default
    };

    if (tasks.length === 0) {
      return metrics;
    }

    // Calculate total completion time in hours
    let totalCompletionTime = 0;
    let completionTimeCount = 0;
    let totalQualityScore = 0;
    let qualityScoreCount = 0;

    // Process each task
    tasks.forEach(task => {
      // Count high priority tasks
      if (task.priority === 'High' || task.priority === 'Urgent') {
        metrics.highPriorityTotal++;
        if (task.status === 'Completed') {
          metrics.highPriorityCompleted++;
        }
      }

      // Process completed tasks
      if (task.status === 'Completed') {
        metrics.completedTasks++;

        // Check if completed on time
        if (task.completedAt && task.deadline) {
          const completedDate = new Date(task.completedAt);
          const deadlineDate = new Date(task.deadline);

          if (completedDate <= deadlineDate) {
            metrics.onTimeTasks++;
          } else {
            metrics.lateCompletedTasks++;
          }

          // Calculate completion time in hours
          const assignedDate = new Date(task.createdAt);
          const completionTimeHours = (completedDate - assignedDate) / (1000 * 60 * 60);
          
          if (completionTimeHours > 0) {
            totalCompletionTime += completionTimeHours;
            completionTimeCount++;
          }
        }

        // Process feedback for quality score
        if (task.feedback) {
          // Simple sentiment analysis on feedback
          const feedback = task.feedback.toLowerCase();
          let score = 3.0; // Neutral default
          
          // Positive indicators
          const positiveTerms = ['excellent', 'great', 'good', 'well done', 'impressive', 'outstanding'];
          // Negative indicators
          const negativeTerms = ['poor', 'bad', 'inadequate', 'disappointing', 'needs improvement'];
          
          // Check for positive terms
          for (const term of positiveTerms) {
            if (feedback.includes(term)) {
              score += 0.5;
            }
          }
          
          // Check for negative terms
          for (const term of negativeTerms) {
            if (feedback.includes(term)) {
              score -= 0.5;
            }
          }
          
          // Ensure score is within range
          score = Math.min(Math.max(score, 1.0), 5.0);
          
          totalQualityScore += score;
          qualityScoreCount++;
        }
      }
    });

    // Calculate derived metrics
    metrics.completionRate = metrics.totalTasks > 0 ? (metrics.completedTasks / metrics.totalTasks) * 100 : 0;
    metrics.onTimeRate = metrics.completedTasks > 0 ? (metrics.onTimeTasks / metrics.completedTasks) * 100 : 0;
    
    // Calculate average completion time
    metrics.averageCompletionTime = completionTimeCount > 0 ? totalCompletionTime / completionTimeCount : 0;
    
    // Calculate quality score
    metrics.qualityScore = qualityScoreCount > 0 ? totalQualityScore / qualityScoreCount : 3.0;
    
    // Calculate efficiency score (based on completion time and on-time rate)
    metrics.efficiencyScore = (metrics.onTimeRate * 0.7) + (Math.min(100, 100 - (metrics.averageCompletionTime > 0 ? 0 : 100)) * 0.3);

    return metrics;
  } catch (error) {
    console.error('Error calculating task metrics:', error);
    throw error;
  }
};

/**
 * Calculate attendance reliability for a user
 * @param {string} userId - User ID
 * @param {number} days - Number of days to look back (default: 90)
 * @returns {Promise<Object>} - Attendance metrics
 */
const calculateAttendanceReliability = async (userId, days = 90) => {
  try {
    // Calculate date threshold
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);

    // Format date threshold as YYYY-MM-DD for string comparison
    const formattedThreshold = dateThreshold.toISOString().split('T')[0];

    // Get attendance records
    const attendanceRecords = await Attendance.find({
      userId,
      date: { $gte: formattedThreshold }
    });

    // Initialize metrics
    const metrics = {
      totalDays: 0,
      presentDays: 0,
      lateDays: 0,
      absentDays: 0,
      averageHoursWorked: 0,
      attendanceIds: attendanceRecords.map(record => record._id),
      attendanceReliability: 0
    };

    if (attendanceRecords.length === 0) {
      return metrics;
    }

    metrics.totalDays = attendanceRecords.length;
    let totalHoursWorked = 0;

    // Process each attendance record
    attendanceRecords.forEach(record => {
      if (record.status === 'Present') {
        metrics.presentDays++;
      } else if (record.status === 'Late') {
        metrics.lateDays++;
      } else if (record.status === 'Absent') {
        metrics.absentDays++;
      }

      // Add hours worked
      if (record.hoursWorked) {
        totalHoursWorked += record.hoursWorked;
      }
    });

    // Calculate average hours worked
    metrics.averageHoursWorked = metrics.totalDays > 0 ? totalHoursWorked / metrics.totalDays : 0;

    // Calculate attendance reliability score (0-100)
    // Formula: (Present days + Late days * 0.7) / Total days * 100
    metrics.attendanceReliability = metrics.totalDays > 0 
      ? ((metrics.presentDays + (metrics.lateDays * 0.7)) / metrics.totalDays) * 100 
      : 0;

    return metrics;
  } catch (error) {
    console.error('Error calculating attendance reliability:', error);
    throw error;
  }
};

/**
 * Get evaluation metrics for a user
 * @param {string} userId - User ID
 * @param {number} days - Number of days to look back (default: 90)
 * @returns {Promise<Object>} - Evaluation metrics
 */
const getEvaluationMetrics = async (userId, days = 90) => {
  try {
    // Calculate date threshold
    const dateThreshold = new Date();
    dateThreshold.setDate(dateThreshold.getDate() - days);

    // Get evaluations
    const evaluations = await Evaluation.find({
      userId,
      evaluationDate: { $gte: dateThreshold }
    }).sort({ evaluationDate: -1 });

    // Initialize metrics
    const metrics = {
      totalEvaluations: evaluations.length,
      averagePerformanceRating: 0,
      averageOverallRating: 0,
      latestEvaluation: null,
      evaluationIds: evaluations.map(eval => eval._id)
    };

    if (evaluations.length === 0) {
      return metrics;
    }

    // Calculate average ratings
    let totalPerformance = 0;
    let totalOverall = 0;

    evaluations.forEach(evaluation => {
      totalPerformance += evaluation.performanceRating || 0;
      totalOverall += evaluation.overallRating || 0;
    });

    metrics.averagePerformanceRating = totalPerformance / evaluations.length;
    metrics.averageOverallRating = totalOverall / evaluations.length;
    metrics.latestEvaluation = evaluations[0];

    return metrics;
  } catch (error) {
    console.error('Error getting evaluation metrics:', error);
    throw error;
  }
};

// ==================== MAIN CONTROLLER FUNCTIONS ====================

/**
 * Calculate fit score for a user and task category
 * @param {string} userId - User ID
 * @param {string} taskCategory - Task category
 * @param {string} taskPriority - Task priority (optional)
 * @returns {Promise<Object>} - Fit score and related metrics
 */
const calculateFitScore = async (userId, taskCategory, taskPriority = 'Medium') => {
  try {
    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Get metrics
    const taskMetrics = await calculateTaskMetrics(userId, taskCategory);
    const attendanceMetrics = await calculateAttendanceReliability(userId);
    const evaluationMetrics = await getEvaluationMetrics(userId);

    // Calculate fit score components
    // 1. Task performance (40%)
    const taskPerformanceScore = (
      (taskMetrics.completionRate * 0.4) +
      (taskMetrics.onTimeRate * 0.4) +
      (taskMetrics.efficiencyScore * 0.2)
    ) * 0.4;

    // 2. Task quality (20%)
    const qualityScore = ((taskMetrics.qualityScore - 1) / 4) * 100 * 0.2;

    // 3. Attendance reliability (20%)
    const attendanceScore = attendanceMetrics.attendanceReliability * 0.2;

    // 4. Evaluation ratings (20%)
    const evaluationScore = evaluationMetrics.totalEvaluations > 0
      ? ((evaluationMetrics.averagePerformanceRating - 1) / 4) * 100 * 0.2
      : 0;

    // Calculate overall fit score (0-100)
    const fitScore = taskPerformanceScore + qualityScore + attendanceScore + evaluationScore;

    // Calculate confidence score based on sample sizes
    const confidenceScore = Math.min(
      100,
      (Math.min(taskMetrics.totalTasks, 10) * 5) +
      (Math.min(attendanceMetrics.totalDays, 30) * 1) +
      (Math.min(evaluationMetrics.totalEvaluations, 4) * 10)
    );

    // Create or update GEK estimate
    const gekEstimate = {
      userId,
      taskCategory,
      taskPriority,
      fitScore,
      estimatedCompletionTime: taskMetrics.averageCompletionTime || 8, // Default to 8 hours if no data
      confidenceScore,
      historicalAccuracy: null, // Will be updated as predictions are verified
      performanceMetrics: {
        completionRate: taskMetrics.completionRate,
        onTimeRate: taskMetrics.onTimeRate,
        qualityScore: taskMetrics.qualityScore,
        efficiencyScore: taskMetrics.efficiencyScore,
        attendanceReliability: attendanceMetrics.attendanceReliability
      },
      sampleSize: taskMetrics.totalTasks,
      calculationBasis: {
        timePeriod: 90,
        taskIds: taskMetrics.taskIds,
        evaluationIds: evaluationMetrics.evaluationIds,
        attendanceIds: attendanceMetrics.attendanceIds
      }
    };

    // Save or update the estimate in the database
    await GEKEstimate.findOneAndUpdate(
      { userId, taskCategory, taskPriority },
      gekEstimate,
      { upsert: true, new: true }
    );

    return {
      ...gekEstimate,
      taskMetrics,
      attendanceMetrics,
      evaluationMetrics
    };
  } catch (error) {
    console.error('Error calculating fit score:', error);
    throw error;
  }
};

/**
 * Get ranked employees for a task
 * @param {Object} taskDetails - Task details including category and priority
 * @param {Array} userIds - List of user IDs to rank (optional)
 * @param {number} limit - Maximum number of results to return (default: 5)
 * @returns {Promise<Array>} - Ranked list of employees with fit scores
 */
const getRankedEmployeesForTask = async (taskDetails, userIds = null, limit = 5) => {
  try {
    const { category, priority = 'Medium' } = taskDetails;

    // Build query to find users
    const userQuery = { role: 'user', active: true };
    if (userIds && userIds.length > 0) {
      userQuery._id = { $in: userIds };
    }

    // Get all eligible users
    const users = await User.find(userQuery).select('-password');

    // Calculate fit scores for all users
    const userScores = await Promise.all(
      users.map(async (user) => {
        try {
          // Try to get existing estimate first
          let estimate = await GEKEstimate.findOne({
            userId: user._id,
            taskCategory: category,
            taskPriority: priority
          });

          // If no estimate exists or it's outdated, calculate a new one
          if (!estimate || !estimate.isRecent) {
            const fitScoreData = await calculateFitScore(user._id, category, priority);
            estimate = {
              fitScore: fitScoreData.fitScore,
              estimatedCompletionTime: fitScoreData.estimatedCompletionTime,
              confidenceScore: fitScoreData.confidenceScore,
              performanceMetrics: fitScoreData.performanceMetrics
            };
          }

          return {
            user: {
              _id: user._id,
              name: user.name,
              email: user.email,
              job: user.job,
              department: user.department
            },
            fitScore: estimate.fitScore,
            estimatedCompletionTime: estimate.estimatedCompletionTime,
            confidenceScore: estimate.confidenceScore,
            performanceMetrics: estimate.performanceMetrics
          };
        } catch (error) {
          console.error(`Error calculating fit score for user ${user._id}:`, error);
          return null;
        }
      })
    );

    // Filter out null results and sort by fit score
    const validScores = userScores
      .filter(score => score !== null)
      .sort((a, b) => b.fitScore - a.fitScore);

    // Return top results
    return validScores.slice(0, limit);
  } catch (error) {
    console.error('Error getting ranked employees:', error);
    throw error;
  }
};

module.exports = {
  calculateFitScore,
  getRankedEmployeesForTask,
  calculateTaskMetrics,
  calculateAttendanceReliability,
  getEvaluationMetrics
};
