/* Global transition styles */

/* Default transition for all interactive elements - faster for real-time feel */
button,
a,
.interactive-element,
.MuiButtonBase-root,
.MuiListItem-root,
.MuiMenuItem-root,
.MuiIconButton-root {
  transition: all 0.15s ease-out !important;
}

/* Hover effects for buttons and links - subtle for real-time feel */
button:hover,
a:hover,
.MuiButtonBase-root:hover,
.MuiListItem-root:hover,
.MuiMenuItem-root:hover,
.MuiIconButton-root:hover {
  transform: translateY(-1px);
}

/* Active state for buttons - minimal movement for real-time feel */
button:active,
.MuiButtonBase-root:active,
.MuiIconButton-root:active {
  transform: translateY(1px);
}

/* Card transitions - faster for real-time feel */
.MuiCard-root,
.MuiPaper-root {
  transition: transform 0.15s ease-out, box-shadow 0.15s ease-out !important;
}

.MuiCard-root:hover,
.MuiPaper-root:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* Navigation item transitions - faster for real-time feel */
.nav-item {
  position: relative;
  transition: all 0.15s ease-out;
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: width 0.15s ease-out;
}

.nav-item:hover::after,
.nav-item.active::after {
  width: 100%;
}

/* Page transition container */
.page-transition-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Loading indicator - faster animation for real-time feel */
.loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #D32F2F, transparent);
  z-index: 9999;
  animation: loading 0.8s infinite;
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Fade transitions - faster for real-time feel */
.fade-enter {
  opacity: 0.9;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 100ms;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0.9;
  transition: opacity 100ms;
}

/* Slide transitions - faster for real-time feel */
.slide-enter {
  transform: translateX(20px);
}

.slide-enter-active {
  transform: translateX(0);
  transition: transform 100ms;
}

.slide-exit {
  transform: translateX(0);
}

.slide-exit-active {
  transform: translateX(-20px);
  transition: transform 100ms;
}

/* Scale transitions - faster and more subtle for real-time feel */
.scale-enter {
  transform: scale(0.98);
  opacity: 0.9;
}

.scale-enter-active {
  transform: scale(1);
  opacity: 1;
  transition: all 100ms;
}

.scale-exit {
  transform: scale(1);
  opacity: 1;
}

.scale-exit-active {
  transform: scale(0.98);
  opacity: 0.9;
  transition: all 100ms;
}
