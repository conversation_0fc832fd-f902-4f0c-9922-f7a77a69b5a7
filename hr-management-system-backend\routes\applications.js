const express = require('express');
const router = express.Router();
const Application = require('../models/application');
const multer = require('multer');
const path = require('path');
const { createApplicationNotification } = require('../controllers/notificationController');

// Set up file upload with multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueName = Date.now() + path.extname(file.originalname);
    cb(null, uniqueName);
  },
});
const upload = multer({ storage });

// GET route to fetch all available jobs
router.get('/available-jobs', async (req, res) => {
  try {
    // Import the Job model
    const Job = require('../models/Job');
    const { search } = req.query;

    let query = {};

    // Add search functionality
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } },
        { jobType: { $regex: search, $options: 'i' } },
        { academicLevel: { $regex: search, $options: 'i' } }
      ];
    }

    // Fetch all active jobs
    const jobs = await Job.find(query).sort({ createdAt: -1 });

    res.status(200).json(jobs);
  } catch (err) {
    console.error('Error fetching available jobs:', err);
    res.status(500).json({ message: 'Server error while fetching available jobs.' });
  }
});

// GET route to check application status by ID (public route)
router.get('/status/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({ success: false, message: 'Application ID is required' });
    }

    console.log('Looking up application with ID:', id);

    // Find application by ID
    const application = await Application.findById(id).select('fullname jobTitle position status feedback createdAt');

    if (!application) {
      console.log('Application not found with ID:', id);
      return res.status(404).json({ success: false, message: 'Application not found. Please check your application ID and try again.' });
    }

    console.log('Found application:', application);

    // Return application status information
    return res.status(200).json({
      success: true,
      application: {
        id: application._id,
        fullname: application.fullname,
        jobTitle: application.jobTitle,
        position: application.position,
        status: application.status,
        feedback: application.feedback,
        appliedOn: application.createdAt
      }
    });
  } catch (err) {
    console.error('Error fetching application status:', err);
    return res.status(500).json({ success: false, message: 'Server error while fetching application status.' });
  }
});

// POST route for handling applications
router.post('/', upload.single('cv'), async (req, res) => {
  const { fullname, email, position, phone, jobId, jobTitle } = req.body;

  console.log('Received application data:', req.body);

  if (!req.file) {
    return res.status(400).json({ message: 'CV file is required!' });
  }

  const cv = req.file.path.replace(/\\/g, '/'); // Normalize path for Windows

  if (!fullname || !email || !position || !phone || !jobId || !jobTitle) {
    return res.status(400).json({
      message: 'All fields are required!',
      missing: {
        fullname: !fullname,
        email: !email,
        position: !position,
        phone: !phone,
        jobId: !jobId,
        jobTitle: !jobTitle
      }
    });
  }

  try {
    // Import the Job model to verify the job exists
    const Job = require('../models/Job');

    // Verify that the job exists
    const job = await Job.findById(jobId);
    if (!job) {
      return res.status(404).json({ message: 'Selected job not found!' });
    }

    const newApplication = new Application({
      fullname,
      email,
      position,
      phone,
      cv,
      jobId,
      jobTitle
    });

    await newApplication.save();

    // Reload the application to ensure we have all fields
    const savedApplication = await Application.findById(newApplication._id);
    console.log('Saved application with ID:', savedApplication._id);

    // Create notification for HR about the new application
    await createApplicationNotification(savedApplication, 'NEW_APPLICATION');

    res.status(201).json({
      message: 'Application submitted successfully!',
      application: {
        id: savedApplication._id,
        fullname: savedApplication.fullname,
        jobTitle: savedApplication.jobTitle
      }
    });
  } catch (err) {
    console.error('Error submitting application:', err);
    res.status(500).json({
      message: 'Server error while submitting the application.',
      error: err.message
    });
  }
});

module.exports = router;
