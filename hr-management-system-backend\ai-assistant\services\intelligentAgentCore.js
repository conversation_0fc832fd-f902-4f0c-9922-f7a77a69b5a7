/**
 * Intelligent Agent Core - Enhanced AI Agent with Full Project Knowledge
 * Jarvis - The Complete HR Intelligence System
 */

const User = require('../../models/user');
const Task = require('../../models/Task');
const LeaveRequest = require('../../models/LeaveRequest');
const Job = require('../../models/job');
const Application = require('../../models/application');

class IntelligentAgentCore {
  constructor() {
    this.agentName = 'Jarvis';
    this.agentPersonality = {
      name: '<PERSON>',
      role: 'Advanced HR Intelligence Agent',
      personality: 'Professional, intelligent, helpful, and comprehensive',
      capabilities: 'Complete HR system knowledge and advanced AI assistance',
      version: '3.0 - Full Intelligence Agent'
    };

    this.projectKnowledge = this.initializeProjectKnowledge();
    this.systemCapabilities = this.initializeSystemCapabilities();
    this.conversationContext = new Map();

    console.log(`🤖 ${this.agentName} Intelligence Agent Core initialized`);
  }

  /**
   * Initialize comprehensive project knowledge
   */
  initializeProjectKnowledge() {
    return {
      projectName: 'HR Management System',
      projectDescription: 'Comprehensive Human Resources Management Platform with AI Intelligence',

      // Core Modules
      modules: {
        userManagement: {
          name: 'User Management',
          description: 'Complete user lifecycle management with role-based access control',
          features: ['User creation', 'Role assignment', 'Profile management', 'Authentication', 'Authorization'],
          roles: ['admin', 'HR', 'user'],
          capabilities: {
            admin: ['Full system access', 'User management', 'System configuration', 'Analytics'],
            HR: ['Employee management', 'Leave approval', 'Task assignment', 'Reports', 'Job posting'],
            user: ['Profile management', 'Leave requests', 'Task viewing', 'Job applications']
          }
        },

        leaveManagement: {
          name: 'Leave Management System',
          description: 'Comprehensive leave request and approval workflow',
          features: ['Leave requests', 'Approval workflow', 'Balance tracking', 'Calendar integration'],
          leaveTypes: ['Annual Leave', 'Sick Leave', 'Personal Leave', 'Maternity/Paternity Leave', 'Bereavement Leave', 'Unpaid Leave'],
          workflow: ['Request submission', 'HR review', 'Approval/Rejection', 'Calendar update', 'Balance adjustment']
        },

        taskManagement: {
          name: 'Task Management',
          description: 'Advanced task assignment and tracking system',
          features: ['Task creation', 'Assignment', 'Progress tracking', 'Deadline management', 'Notifications'],
          priorities: ['Low', 'Medium', 'High'],
          statuses: ['Pending', 'In Progress', 'On Hold', 'Completed'],
          categories: ['General', 'Development', 'Design', 'Marketing', 'Research', 'Administration']
        },

        jobManagement: {
          name: 'Job Management & Recruitment',
          description: 'Complete recruitment and job posting system',
          features: ['Job posting', 'Application management', 'CV processing', 'Interview scheduling', 'Candidate evaluation'],
          departments: ['IT', 'HR', 'Finance', 'Marketing', 'Sales', 'Operations'],
          applicationProcess: ['Job posting', 'Application submission', 'CV screening', 'Interview', 'Decision']
        },

        reportingAnalytics: {
          name: 'Reporting & Analytics',
          description: 'Advanced reporting and business intelligence',
          features: ['User reports', 'Leave analytics', 'Task performance', 'Job statistics', 'Custom dashboards'],
          reportTypes: ['User Activity', 'Leave Patterns', 'Task Completion', 'Recruitment Metrics', 'Performance Analytics']
        },

        aiAssistant: {
          name: 'AI Assistant (Jarvis)',
          description: 'Advanced AI-powered assistance and automation',
          features: ['Natural language processing', 'Context awareness', 'Intelligent responses', 'System guidance'],
          capabilities: ['Chat assistance', 'Form guidance', 'Data analysis', 'Process automation', 'Smart recommendations']
        }
      },

      // Technical Architecture
      technology: {
        frontend: {
          framework: 'React.js',
          ui: 'Material-UI (MUI)',
          routing: 'React Router',
          stateManagement: 'React Hooks',
          animations: 'Framer Motion'
        },
        backend: {
          runtime: 'Node.js',
          framework: 'Express.js',
          database: 'MongoDB',
          authentication: 'JWT',
          ai: 'OpenAI GPT-4.1'
        },
        ai: {
          provider: 'OpenAI / OpenRouter',
          model: 'GPT-4.1',
          capabilities: ['Natural Language Processing', 'Intent Classification', 'Emotion Analysis', 'Context Awareness'],
          features: ['Conversational AI', 'Smart Responses', 'System Knowledge', 'Process Guidance']
        }
      },

      // Business Processes
      workflows: {
        leaveRequest: [
          'Employee submits leave request',
          'System validates dates and balance',
          'HR receives notification',
          'HR reviews and approves/rejects',
          'Employee receives notification',
          'Calendar and balance updated'
        ],
        userOnboarding: [
          'Admin creates user account',
          'System generates credentials',
          'Welcome email sent',
          'User completes profile',
          'Role-based access granted',
          'Onboarding tasks assigned'
        ],
        jobApplication: [
          'Job posted by HR',
          'Candidate applies online',
          'CV processed and scored',
          'HR reviews application',
          'Interview scheduled',
          'Decision communicated'
        ]
      }
    };
  }

  /**
   * Initialize system capabilities
   */
  initializeSystemCapabilities() {
    return {
      dataAccess: {
        users: 'Complete user database access',
        leaves: 'Leave request and balance information',
        tasks: 'Task assignment and progress data',
        jobs: 'Job postings and applications',
        reports: 'Analytics and reporting data'
      },

      actions: {
        query: 'Search and retrieve information',
        analyze: 'Perform data analysis and insights',
        guide: 'Provide step-by-step guidance',
        recommend: 'Suggest optimal actions',
        automate: 'Execute routine tasks'
      },

      intelligence: {
        nlp: 'Natural language understanding',
        context: 'Conversation and system context awareness',
        learning: 'Adaptive responses based on interactions',
        reasoning: 'Logical problem-solving capabilities',
        memory: 'Conversation and user preference memory'
      }
    };
  }

  /**
   * Process message with full intelligence
   */
  async processIntelligentMessage(userId, message, context = {}) {
    try {
      // Store conversation context
      this.updateConversationContext(userId, message, context);

      // Recognize agent name and respond appropriately
      const nameRecognition = this.recognizeAgentName(message);

      // Analyze message intent and extract entities
      const analysis = await this.analyzeMessage(message, context);

      // Generate intelligent response
      const response = await this.generateIntelligentResponse(
        userId,
        message,
        analysis,
        nameRecognition,
        context
      );

      return response;
    } catch (error) {
      console.error('Error in intelligent message processing:', error);
      return this.generateErrorResponse(error);
    }
  }

  /**
   * Recognize when user mentions agent name
   */
  recognizeAgentName(message) {
    const lowerMessage = message.toLowerCase();
    const nameVariations = [
      'jarvis', 'jarvis ai', 'hey jarvis', 'hi jarvis',
      'jarvis assistant', 'mr jarvis', 'agent jarvis'
    ];

    const mentioned = nameVariations.some(name => lowerMessage.includes(name));

    return {
      mentioned,
      greeting: lowerMessage.includes('hey') || lowerMessage.includes('hi') || lowerMessage.includes('hello'),
      direct: lowerMessage.startsWith('jarvis'),
      formal: lowerMessage.includes('mr jarvis') || lowerMessage.includes('agent jarvis')
    };
  }

  /**
   * Analyze message for intent and entities
   */
  async analyzeMessage(message, context) {
    const lowerMessage = message.toLowerCase();

    // Intent classification
    let intent = 'general_inquiry';
    let entities = {};
    let confidence = 0.8;

    // System knowledge intents
    if (this.matchesPattern(lowerMessage, ['what', 'how', 'explain', 'tell me about'])) {
      intent = 'knowledge_request';
      confidence = 0.9;
    }

    // Module-specific intents
    if (this.matchesPattern(lowerMessage, ['leave', 'vacation', 'time off'])) {
      intent = 'leave_inquiry';
      entities.module = 'leaveManagement';
    } else if (this.matchesPattern(lowerMessage, ['task', 'assignment', 'work'])) {
      intent = 'task_inquiry';
      entities.module = 'taskManagement';
    } else if (this.matchesPattern(lowerMessage, ['user', 'employee', 'staff'])) {
      intent = 'user_inquiry';
      entities.module = 'userManagement';
    } else if (this.matchesPattern(lowerMessage, ['job', 'position', 'hiring', 'recruitment'])) {
      intent = 'job_inquiry';
      entities.module = 'jobManagement';
    } else if (this.matchesPattern(lowerMessage, ['report', 'analytics', 'data', 'statistics'])) {
      intent = 'report_inquiry';
      entities.module = 'reportingAnalytics';
    }

    // Action intents
    if (this.matchesPattern(lowerMessage, ['create', 'add', 'new'])) {
      intent = 'create_action';
    } else if (this.matchesPattern(lowerMessage, ['update', 'edit', 'modify', 'change'])) {
      intent = 'update_action';
    } else if (this.matchesPattern(lowerMessage, ['delete', 'remove'])) {
      intent = 'delete_action';
    } else if (this.matchesPattern(lowerMessage, ['show', 'list', 'display', 'view'])) {
      intent = 'view_action';
    }

    return {
      intent,
      entities,
      confidence,
      messageType: this.classifyMessageType(message),
      complexity: this.assessComplexity(message)
    };
  }

  /**
   * Generate intelligent response based on analysis
   */
  async generateIntelligentResponse(userId, message, analysis, nameRecognition, context) {
    let response = {
      content: '',
      type: 'intelligent_response',
      metadata: {
        agentName: this.agentName,
        version: this.agentPersonality.version,
        intent: analysis.intent,
        confidence: analysis.confidence,
        nameRecognized: nameRecognition.mentioned
      }
    };

    // Handle name recognition
    if (nameRecognition.mentioned) {
      if (nameRecognition.greeting) {
        response.content = `Hello! I'm ${this.agentName}, your advanced HR Intelligence Agent. `;
      } else if (nameRecognition.direct) {
        response.content = `Yes, I'm ${this.agentName}. `;
      } else if (nameRecognition.formal) {
        response.content = `At your service! I'm ${this.agentName}, your comprehensive HR AI assistant. `;
      } else {
        response.content = `${this.agentName} here! `;
      }
    }

    // Generate content based on intent
    switch (analysis.intent) {
      case 'knowledge_request':
        response.content += await this.handleKnowledgeRequest(message, analysis.entities);
        break;

      case 'leave_inquiry':
        response.content += await this.handleLeaveInquiry(userId, message, analysis.entities);
        break;

      case 'task_inquiry':
        response.content += await this.handleTaskInquiry(userId, message, analysis.entities);
        break;

      case 'user_inquiry':
        response.content += await this.handleUserInquiry(userId, message, analysis.entities);
        break;

      case 'job_inquiry':
        response.content += await this.handleJobInquiry(userId, message, analysis.entities);
        break;

      case 'report_inquiry':
        response.content += await this.handleReportInquiry(userId, message, analysis.entities);
        break;

      default:
        response.content += await this.handleGeneralInquiry(userId, message, context);
    }

    // Add helpful suggestions
    response.suggestions = this.generateContextualSuggestions(analysis.intent, analysis.entities);

    return response;
  }

  /**
   * Helper method to match patterns in message
   */
  matchesPattern(message, patterns) {
    return patterns.some(pattern => message.includes(pattern));
  }

  /**
   * Classify message type
   */
  classifyMessageType(message) {
    if (message.includes('?')) return 'question';
    if (message.includes('!')) return 'exclamation';
    if (message.split(' ').length > 10) return 'detailed';
    return 'statement';
  }

  /**
   * Assess message complexity
   */
  assessComplexity(message) {
    const words = message.split(' ').length;
    if (words < 5) return 'simple';
    if (words < 15) return 'moderate';
    return 'complex';
  }

  /**
   * Update conversation context
   */
  updateConversationContext(userId, message, context) {
    if (!this.conversationContext.has(userId)) {
      this.conversationContext.set(userId, {
        messages: [],
        topics: [],
        preferences: {},
        lastActivity: Date.now()
      });
    }

    const userContext = this.conversationContext.get(userId);
    userContext.messages.push({
      content: message,
      timestamp: Date.now(),
      type: 'user'
    });

    // Keep only last 10 messages for context
    if (userContext.messages.length > 10) {
      userContext.messages = userContext.messages.slice(-10);
    }

    userContext.lastActivity = Date.now();
  }

  /**
   * Generate error response
   */
  generateErrorResponse(error) {
    return {
      content: `I apologize, but I encountered an issue processing your request. As ${this.agentName}, I'm designed to be highly reliable, but sometimes technical difficulties occur. Please try rephrasing your question, and I'll do my best to assist you.`,
      type: 'error_response',
      metadata: {
        agentName: this.agentName,
        error: error.message
      }
    };
  }

  /**
   * Handle knowledge requests about the system
   */
  async handleKnowledgeRequest(message, entities) {
    const lowerMessage = message.toLowerCase();

    // System overview
    if (this.matchesPattern(lowerMessage, ['what is', 'about this system', 'overview', 'what can you do'])) {
      return `I'm ${this.agentName}, your comprehensive HR Intelligence Agent! 🤖

**${this.projectKnowledge.projectName}** is a complete Human Resources Management Platform with advanced AI capabilities. Here's what I can help you with:

🏢 **Core Modules:**
• **User Management** - Complete employee lifecycle and role management
• **Leave Management** - Smart leave requests and approval workflows
• **Task Management** - Advanced task assignment and tracking
• **Job Management** - Full recruitment and hiring process
• **Reporting & Analytics** - Comprehensive business intelligence
• **AI Assistant** - That's me! Advanced AI-powered assistance

💡 **My Capabilities:**
• Answer questions about any system feature
• Guide you through processes step-by-step
• Analyze data and provide insights
• Help with form completion and workflows
• Provide intelligent recommendations

Ask me anything about the system, and I'll provide detailed, helpful information!`;
    }

    // Module-specific knowledge
    if (entities.module) {
      const module = this.projectKnowledge.modules[entities.module];
      if (module) {
        return `📋 **${module.name}**

${module.description}

**Key Features:**
${module.features.map(feature => `• ${feature}`).join('\n')}

${this.getModuleSpecificInfo(entities.module)}

Would you like me to explain any specific feature or guide you through a process?`;
      }
    }

    // Technology questions
    if (this.matchesPattern(lowerMessage, ['technology', 'tech stack', 'built with', 'architecture'])) {
      const tech = this.projectKnowledge.technology;
      return `🔧 **Technology Architecture**

**Frontend:**
• Framework: ${tech.frontend.framework}
• UI Library: ${tech.frontend.ui}
• Routing: ${tech.frontend.routing}
• Animations: ${tech.frontend.animations}

**Backend:**
• Runtime: ${tech.backend.runtime}
• Framework: ${tech.backend.framework}
• Database: ${tech.backend.database}
• Authentication: ${tech.backend.authentication}

**AI Intelligence:**
• Provider: ${tech.ai.provider}
• Model: ${tech.ai.model}
• My Capabilities: ${tech.ai.capabilities.join(', ')}

This modern tech stack ensures high performance, scalability, and advanced AI capabilities!`;
    }

    return `I have comprehensive knowledge about our HR Management System. Could you be more specific about what you'd like to know? I can explain:

• System features and modules
• How to use specific functions
• Business processes and workflows
• Technical architecture
• Best practices and recommendations

Just ask me anything!`;
  }

  /**
   * Handle leave-related inquiries
   */
  async handleLeaveInquiry(userId, message, entities) {
    const lowerMessage = message.toLowerCase();

    try {
      // Get user's leave data
      const userLeaves = await LeaveRequest.find({ userId }).sort({ createdAt: -1 }).limit(5);
      const leaveModule = this.projectKnowledge.modules.leaveManagement;

      if (this.matchesPattern(lowerMessage, ['how to', 'request', 'apply', 'submit'])) {
        return `🏖️ **Leave Request Process**

Here's how to request leave in our system:

**Step-by-Step Guide:**
1. Navigate to the Leave Management section
2. Click "Request Leave"
3. Select leave type: ${leaveModule.leaveTypes.join(', ')}
4. Choose start and end dates
5. Provide a clear reason
6. Submit for HR approval

**Workflow:**
${leaveModule.workflow.map((step, index) => `${index + 1}. ${step}`).join('\n')}

**Your Recent Leaves:**
${userLeaves.length > 0 ?
  userLeaves.map(leave => `• ${leave.leaveType} - ${leave.status} (${new Date(leave.startDate).toLocaleDateString()})`).join('\n') :
  'No recent leave requests found'
}

Would you like me to guide you through submitting a new leave request?`;
      }

      if (this.matchesPattern(lowerMessage, ['balance', 'remaining', 'available'])) {
        return `📊 **Leave Balance Information**

I can help you check your leave balance! Here's what you need to know:

**Available Leave Types:**
${leaveModule.leaveTypes.map(type => `• ${type}`).join('\n')}

**To check your current balance:**
1. Go to your Dashboard
2. Look for the "Leave Balance" section
3. View remaining days for each leave type

**Recent Activity:**
${userLeaves.length > 0 ?
  userLeaves.slice(0, 3).map(leave =>
    `• ${leave.leaveType}: ${leave.duration} days (${leave.status})`
  ).join('\n') :
  'No recent leave activity'
}

Need help planning your leave or checking specific balances?`;
      }

      return `🏖️ **Leave Management Help**

I can assist you with all aspects of leave management:

• **Request Leave** - Guide you through the application process
• **Check Balance** - Help you view remaining leave days
• **Track Status** - Monitor your pending/approved requests
• **Leave Policies** - Explain company leave policies
• **Calendar Planning** - Help plan leave around important dates

What specific leave-related help do you need?`;

    } catch (error) {
      return `I can help you with leave management! While I couldn't access your specific leave data right now, I can still guide you through:

• How to request different types of leave
• Understanding the approval process
• Checking leave balances
• Leave policies and guidelines

What would you like to know about leave management?`;
    }
  }

  /**
   * Handle task-related inquiries
   */
  async handleTaskInquiry(userId, message, entities) {
    const lowerMessage = message.toLowerCase();

    try {
      const userTasks = await Task.find({
        $or: [{ assignedTo: userId }, { createdBy: userId }]
      }).sort({ createdAt: -1 }).limit(5);

      const taskModule = this.projectKnowledge.modules.taskManagement;

      if (this.matchesPattern(lowerMessage, ['my tasks', 'assigned', 'current'])) {
        return `📋 **Your Tasks**

**Current Tasks:**
${userTasks.length > 0 ?
  userTasks.map(task =>
    `• **${task.title}** - ${task.status} (Priority: ${task.priority})`
  ).join('\n') :
  'No tasks currently assigned'
}

**Task Management Features:**
• **Priorities:** ${taskModule.priorities.join(', ')}
• **Statuses:** ${taskModule.statuses.join(', ')}
• **Categories:** ${taskModule.categories.join(', ')}

**Available Actions:**
• View task details and requirements
• Update task progress and status
• Add comments and updates
• Set reminders and deadlines

Need help with any specific task or want to update progress?`;
      }

      if (this.matchesPattern(lowerMessage, ['create', 'assign', 'new task'])) {
        return `➕ **Creating New Tasks**

Here's how to create and assign tasks:

**Step-by-Step Process:**
1. Navigate to Task Management
2. Click "Create New Task"
3. Fill in task details:
   - Title and description
   - Priority level (${taskModule.priorities.join(', ')})
   - Category (${taskModule.categories.join(', ')})
   - Assign to team member
   - Set deadline
4. Submit for assignment

**Best Practices:**
• Write clear, specific task descriptions
• Set realistic deadlines
• Choose appropriate priority levels
• Include all necessary resources and context

Would you like me to guide you through creating a specific task?`;
      }

      return `📋 **Task Management Assistant**

I can help you with all task-related activities:

• **View Tasks** - See your assigned and created tasks
• **Create Tasks** - Guide you through task creation
• **Update Progress** - Help update task status and progress
• **Manage Deadlines** - Assist with deadline planning
• **Team Collaboration** - Facilitate task communication

**Your Task Summary:**
• Total tasks: ${userTasks.length}
• Pending: ${userTasks.filter(t => t.status === 'Pending').length}
• In Progress: ${userTasks.filter(t => t.status === 'In Progress').length}
• Completed: ${userTasks.filter(t => t.status === 'Completed').length}

What task-related assistance do you need?`;

    } catch (error) {
      return `📋 **Task Management Help**

I can assist you with task management features:

• Creating and assigning new tasks
• Updating task progress and status
• Managing deadlines and priorities
• Team collaboration and communication
• Task categorization and organization

What specific task management help do you need?`;
    }
  }

  /**
   * Handle user management inquiries
   */
  async handleUserInquiry(userId, message, entities) {
    const lowerMessage = message.toLowerCase();

    try {
      const currentUser = await User.findById(userId);
      const userModule = this.projectKnowledge.modules.userManagement;

      if (this.matchesPattern(lowerMessage, ['profile', 'my account', 'personal info'])) {
        return `👤 **Your Profile Information**

**Current User:** ${currentUser.name}
**Role:** ${currentUser.role}
**Email:** ${currentUser.email}
**Department:** ${currentUser.department || 'Not specified'}

**Your Capabilities:**
${userModule.capabilities[currentUser.role]?.map(cap => `• ${cap}`).join('\n') || '• Basic user access'}

**Profile Management:**
• Update personal information
• Change password
• Manage notification preferences
• View activity history

**Available Actions:**
• Edit profile details
• Update contact information
• Manage account settings
• View access permissions

Need help updating any profile information?`;
      }

      if (this.matchesPattern(lowerMessage, ['roles', 'permissions', 'access'])) {
        return `🔐 **User Roles & Permissions**

**Available Roles:**
${Object.entries(userModule.capabilities).map(([role, caps]) =>
  `**${role.toUpperCase()}:**\n${caps.map(cap => `  • ${cap}`).join('\n')}`
).join('\n\n')}

**Your Current Role:** ${currentUser.role}
**Your Permissions:**
${userModule.capabilities[currentUser.role]?.map(cap => `• ${cap}`).join('\n') || '• Basic user access'}

**Role Management:**
• Only admins can assign roles
• Role changes require admin approval
• Each role has specific system access levels

Need clarification on any specific permissions or capabilities?`;
      }

      return `👥 **User Management Help**

I can assist you with user-related functions:

**Your Account:**
• Profile: ${currentUser.name} (${currentUser.role})
• Department: ${currentUser.department || 'Not specified'}
• Access Level: ${currentUser.role} permissions

**Available Services:**
• Profile management and updates
• Password and security settings
• Role and permission information
• Account activity and history
• Team and department information

What user management help do you need?`;

    } catch (error) {
      return `👥 **User Management Assistant**

I can help you with user management features:

• Profile management and updates
• Understanding roles and permissions
• Account security and settings
• Team and department information
• User onboarding and training

What specific user management assistance do you need?`;
    }
  }

  /**
   * Handle job and recruitment inquiries
   */
  async handleJobInquiry(userId, message, entities) {
    const lowerMessage = message.toLowerCase();

    try {
      const recentJobs = await Job.find().sort({ createdAt: -1 }).limit(5);
      const userApplications = await Application.find({ userId }).sort({ createdAt: -1 }).limit(3);
      const jobModule = this.projectKnowledge.modules.jobManagement;

      if (this.matchesPattern(lowerMessage, ['available jobs', 'open positions', 'job openings'])) {
        return `💼 **Available Job Opportunities**

**Current Open Positions:**
${recentJobs.length > 0 ?
  recentJobs.map(job =>
    `• **${job.title}** - ${job.department}\n  Location: ${job.location || 'Not specified'}\n  Posted: ${new Date(job.createdAt).toLocaleDateString()}`
  ).join('\n\n') :
  'No job openings currently available'
}

**Application Process:**
${jobModule.applicationProcess.map((step, index) => `${index + 1}. ${step}`).join('\n')}

**Your Applications:**
${userApplications.length > 0 ?
  userApplications.map(app => `• Applied for: ${app.jobTitle || 'Position'} - Status: ${app.status || 'Pending'}`).join('\n') :
  'No recent applications found'
}

Interested in applying for any position?`;
      }

      if (this.matchesPattern(lowerMessage, ['apply', 'application', 'how to apply'])) {
        return `📝 **Job Application Process**

**How to Apply:**
1. Browse available job openings
2. Select a position that matches your skills
3. Complete the online application form
4. Upload your CV and cover letter
5. Submit application for review

**Application Requirements:**
• Updated CV/Resume
• Cover letter (recommended)
• Relevant certifications or portfolios
• Contact information for references

**After Application:**
• HR team reviews your application
• CV is processed and scored automatically
• Qualified candidates are contacted for interviews
• Interview process and final decision

**Your Application History:**
${userApplications.length > 0 ?
  userApplications.map(app => `• ${app.jobTitle || 'Position'}: ${app.status || 'Pending'}`).join('\n') :
  'No previous applications'
}

Ready to apply for a position?`;
      }

      return `💼 **Job Management & Recruitment**

**Available Services:**
• Browse current job openings
• Submit job applications
• Track application status
• CV and profile optimization
• Interview preparation guidance

**Current Opportunities:**
• Open positions: ${recentJobs.length}
• Departments hiring: ${jobModule.departments.join(', ')}

**Your Activity:**
• Applications submitted: ${userApplications.length}
• Recent activity: ${userApplications.length > 0 ? 'Active' : 'None'}

How can I help with your job search or application process?`;

    } catch (error) {
      return `💼 **Job & Recruitment Help**

I can assist you with job-related services:

• Browsing available job openings
• Understanding the application process
• Preparing application materials
• Tracking application status
• Interview preparation tips

What job-related assistance do you need?`;
    }
  }

  /**
   * Handle reporting and analytics inquiries
   */
  async handleReportInquiry(userId, message, entities) {
    const reportModule = this.projectKnowledge.modules.reportingAnalytics;

    return `📊 **Reporting & Analytics**

**Available Reports:**
${reportModule.reportTypes.map(type => `• ${type}`).join('\n')}

**Key Features:**
${reportModule.features.map(feature => `• ${feature}`).join('\n')}

**Analytics Capabilities:**
• Real-time data visualization
• Custom dashboard creation
• Trend analysis and insights
• Performance metrics tracking
• Automated report generation

**Popular Reports:**
• Employee activity and engagement
• Leave patterns and trends
• Task completion rates
• Recruitment metrics and success rates
• Department performance analytics

**How to Access:**
1. Navigate to Reports section
2. Select report type
3. Choose date range and filters
4. Generate and view results
5. Export or share as needed

Which type of report or analytics would you like to explore?`;
  }

  /**
   * Handle general inquiries
   */
  async handleGeneralInquiry(userId, message, context) {
    const greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening'];
    const lowerMessage = message.toLowerCase();

    if (greetings.some(greeting => lowerMessage.includes(greeting))) {
      try {
        const user = await User.findById(userId);
        return `Hello ${user?.name || 'there'}! 👋

I'm ${this.agentName}, your advanced HR Intelligence Agent. I have comprehensive knowledge of our entire HR Management System and I'm here to provide intelligent assistance with:

🏢 **System Navigation & Guidance**
📋 **Process Automation & Workflows**
📊 **Data Analysis & Insights**
🤖 **Smart Recommendations & Tips**
💡 **Problem Solving & Support**

I can help you with anything related to:
• User management and profiles
• Leave requests and approvals
• Task assignment and tracking
• Job postings and applications
• Reports and analytics
• System features and capabilities

What would you like to explore or accomplish today?`;
      } catch (error) {
        return `Hello! I'm ${this.agentName}, your comprehensive HR Intelligence Agent. How can I assist you today?`;
      }
    }

    return `I'm ${this.agentName}, your intelligent HR assistant with complete system knowledge. I can help you with:

• **System Information** - Learn about features and capabilities
• **Process Guidance** - Step-by-step help with any workflow
• **Data Insights** - Analysis and reporting assistance
• **Problem Solving** - Troubleshooting and optimization
• **Smart Recommendations** - Personalized suggestions

What specific assistance do you need? Just ask me anything about the HR system!`;
  }

  /**
   * Get module-specific information
   */
  getModuleSpecificInfo(moduleName) {
    const module = this.projectKnowledge.modules[moduleName];

    switch (moduleName) {
      case 'leaveManagement':
        return `\n**Available Leave Types:**\n${module.leaveTypes.map(type => `• ${type}`).join('\n')}`;
      case 'taskManagement':
        return `\n**Priority Levels:** ${module.priorities.join(', ')}\n**Status Options:** ${module.statuses.join(', ')}`;
      case 'userManagement':
        return `\n**Available Roles:** ${Object.keys(module.capabilities).join(', ')}`;
      case 'jobManagement':
        return `\n**Departments:** ${module.departments.join(', ')}`;
      default:
        return '';
    }
  }

  /**
   * Generate contextual suggestions
   */
  generateContextualSuggestions(intent, entities) {
    const baseSuggestions = [
      'Tell me about the system',
      'How do I request leave?',
      'Show me my tasks',
      'What jobs are available?'
    ];

    switch (intent) {
      case 'leave_inquiry':
        return [
          'How do I request leave?',
          'Check my leave balance',
          'What are the leave types?',
          'Show leave approval process'
        ];
      case 'task_inquiry':
        return [
          'Show my current tasks',
          'How to create a new task?',
          'Update task progress',
          'View team tasks'
        ];
      case 'user_inquiry':
        return [
          'View my profile',
          'Update personal information',
          'Explain user roles',
          'Change password'
        ];
      case 'job_inquiry':
        return [
          'Show available jobs',
          'How to apply for jobs?',
          'Check application status',
          'View job requirements'
        ];
      default:
        return baseSuggestions;
    }
  }
}

module.exports = new IntelligentAgentCore();
