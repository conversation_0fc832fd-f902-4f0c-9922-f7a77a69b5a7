const fs = require('fs');
const path = require('path');

/**
 * Check if a file exists in the uploads directory
 * @param {string} filePath - The path to the file, can be relative to uploads directory
 * @returns {boolean} - True if the file exists, false otherwise
 */
const fileExists = (filePath) => {
  try {
    // Normalize the path to handle both formats: with or without 'uploads/' prefix
    let normalizedPath = filePath;

    // If the path doesn't include 'uploads/', add it
    if (!normalizedPath.includes('uploads/') && !normalizedPath.includes('uploads\\')) {
      normalizedPath = path.join('uploads', normalizedPath);
    }

    // Get the absolute path
    const absolutePath = path.resolve(normalizedPath);

    // Check if the file exists
    return fs.existsSync(absolutePath);
  } catch (error) {
    console.error('Error checking if file exists:', error);
    return false;
  }
};

/**
 * Get file information
 * @param {string} filePath - The path to the file, can be relative to uploads directory
 * @returns {Object|null} - File information or null if the file doesn't exist
 */
const getFileInfo = (filePath) => {
  try {
    if (!filePath) {
      console.error('No file path provided');
      return null;
    }

    // Clean the path - remove any leading/trailing whitespace and quotes
    let cleanPath = filePath.trim().replace(/^["']|["']$/g, '');

    // Normalize the path to handle both formats: with or without 'uploads/' prefix
    let normalizedPath = cleanPath;

    // If the path doesn't include 'uploads/', add it
    if (!normalizedPath.includes('uploads/') && !normalizedPath.includes('uploads\\')) {
      normalizedPath = path.join('uploads', normalizedPath);
    }

    // Replace backslashes with forward slashes for consistency
    normalizedPath = normalizedPath.replace(/\\/g, '/');

    // Get the absolute path
    const absolutePath = path.resolve(normalizedPath);

    console.log('Checking file existence at:', absolutePath);

    // Check if the file exists
    if (!fs.existsSync(absolutePath)) {
      console.log('File does not exist at:', absolutePath);
      return null;
    }

    // Get file stats
    const stats = fs.statSync(absolutePath);

    // Get file extension
    const ext = path.extname(absolutePath).toLowerCase();

    return {
      path: normalizedPath,
      absolutePath,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      extension: ext,
      contentType: getContentType(absolutePath),
      exists: true
    };
  } catch (error) {
    console.error('Error getting file info:', error);
    return null;
  }
};

/**
 * Get the content type for a file based on its extension
 * @param {string} filePath - Path to the file
 * @returns {string} - Content type
 */
const getContentType = (filePath) => {
  const ext = path.extname(filePath).toLowerCase();

  const contentTypes = {
    '.pdf': 'application/pdf',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.txt': 'text/plain',
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.xml': 'application/xml',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  };

  return contentTypes[ext] || 'application/octet-stream';
};

/**
 * Check if a file exists and get its information
 * @param {string} filePath - Path to the file
 * @returns {Object} - Object with exists flag and file info
 */
const checkFileExists = (filePath) => {
  try {
    const fileInfo = getFileInfo(filePath);

    if (fileInfo) {
      return {
        exists: true,
        fileInfo
      };
    } else {
      return {
        exists: false,
        error: 'File not found'
      };
    }
  } catch (error) {
    console.error('Error checking file existence:', error);
    return {
      exists: false,
      error: error.message
    };
  }
};

module.exports = {
  fileExists,
  getFileInfo,
  getContentType,
  checkFileExists
};
