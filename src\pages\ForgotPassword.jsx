import { useState } from "react";
import {
  <PERSON>,
  <PERSON>ton,
  CircularProgress,
  Container,
  Paper,
  TextField,
  Typography,
  Alert
} from "@mui/material";
import { toast } from "react-toastify";
import axios from 'axios';
import { sendPasswordResetEmail } from '../utils/emailNotifications';

// Simple ForgotPassword component
function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  // Function to handle the forgot password request
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess(false);

    try {
      console.log("Sending forgot password request for email:", email);

      // Use the forgot-password endpoint with proper error handling
      const response = await axios.post(`http://localhost:5000/api/auth/forgot-password`,
        { email },
        {
          timeout: 8000,
          validateStatus: (status) => status < 500 // Accept all status codes except server errors
        }
      );

      console.log("Forgot password response:", response.data);

      // Handle different response statuses
      if (response.status === 404) {
        setError("Email not found. Please check and try again.");
        toast.error("Email not found. Please check and try again.");
        return;
      }

      if (response.status === 400) {
        setError(response.data.message || "Invalid request. Please check your email.");
        toast.error(response.data.message || "Invalid request. Please check your email.");
        return;
      }

      if (response.status !== 200) {
        setError("Error processing your request. Please try again.");
        toast.error("Error processing your request. Please try again.");
        return;
      }

      if (response.data && (response.data.success || response.status === 200)) {
        // Get the new password from the response - handle different response structures
        const resetPassword = response.data.newPassword || '00000000'; // Default to 00000000 if not provided

        // Get the name from the email (part before @)
        const userName = email.split('@')[0] || 'User';

        console.log("Sending email with EmailJS");

        try {
          // Send the email using our utility function
          const emailResponse = await sendPasswordResetEmail({
            email: email,
            password: resetPassword,
            to_name: userName
          });

          console.log("EmailJS response:", emailResponse);

          // Handle the response from EmailJS
          if (emailResponse.status === 200) {
            setSuccess(true);
            toast.success("Your password has been reset and sent to your email!");
          } else {
            setError("Error sending email. Please try again or contact support.");
            toast.error("Error sending email. Please try again or contact support.");
          }
        } catch (emailError) {
          console.error("EmailJS error:", emailError);
          // Don't show the password, just inform about the email issue
          setSuccess(true);
          setError("Email could not be sent, but your password has been reset.");
          toast.warning("Email could not be sent, but your password has been reset.");
        }
      } else {
        setError("Error resetting password. Please try again.");
        toast.error("Error resetting password. Please try again.");
      }
    } catch (err) {
      console.error("Error in forgot password process:", err);

      if (err.response) {
        console.error("Error response:", err.response.data);

        if (err.response.status === 404) {
          setError("Email not found. Please check and try again.");
          toast.error("Email not found. Please check and try again.");
        } else {
          setError(err.response.data.message || "Something went wrong. Please try again.");
          toast.error(err.response.data.message || "Something went wrong. Please try again.");
        }
      } else if (err.code === 'ERR_NETWORK') {
        setError("Network error. Please check if the server is running and try again.");
        toast.error("Network error. Please check if the server is running and try again.");
      } else if (err.code === 'ECONNABORTED') {
        setError("Request timed out. Please try again later.");
        toast.error("Request timed out. Please try again later.");
      } else {
        setError("An unexpected error occurred. Please try again later.");
        toast.error("An unexpected error occurred. Please try again later.");
        console.error("Detailed error:", err);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8, mb: 4 }}>
        <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
          <Typography variant="h5" align="center" gutterBottom>
            Forgot Password
          </Typography>
          <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
            Enter your email address and we'll send you your password.
          </Typography>

          {error && !success && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Your password recovery was successful! Check your email for the new password.
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <TextField
              label="Enter your email"
              variant="outlined"
              fullWidth
              margin="normal"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              type="email"
              error={!!error}
              disabled={success}
            />
            <Button
              type="submit"
              variant="contained"
              color="primary"
              fullWidth
              disabled={loading || success}
              sx={{ mt: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : "Submit"}
            </Button>

            {success && (
              <Button
                variant="outlined"
                color="primary"
                fullWidth
                sx={{ mt: 2 }}
                onClick={() => window.location.href = '/login'}
              >
                Back to Login
              </Button>
            )}
          </form>
        </Paper>
      </Box>
    </Container>
  );
}

export default ForgotPassword;
