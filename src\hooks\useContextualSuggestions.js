/**
 * Hook for managing contextual AI suggestions
 * Provides automatic suggestions based on user actions
 */

import { useState, useCallback } from 'react';
import api from '../Services/ApiService';

export const useContextualSuggestions = () => {
  const [activeSuggestions, setActiveSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);

  // Generate suggestions based on action and context
  const generateSuggestions = useCallback(async (context, actionType, data = {}) => {
    try {
      setLoading(true);
      console.log('Hook generating suggestions for:', { context, actionType, data });

      // Generate suggestions directly based on logic
      const suggestions = generateDirectSuggestions(context, actionType, data);

      if (suggestions.length > 0) {
        setActiveSuggestions(suggestions);
        console.log('Hook set active suggestions:', suggestions);
        return suggestions;
      }

      return [];
    } catch (error) {
      console.error('Error generating contextual suggestions:', error);
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Generate suggestions directly based on context and data
  const generateDirectSuggestions = (context, actionType, data) => {
    const suggestions = [];
    console.log('Direct suggestions for:', { context, actionType, data });

    // Leave request suggestions
    if (context === 'leave_request') {
      suggestions.push({
        id: `leave_basic_${Date.now()}`,
        title: 'Leave Request Tips',
        description: 'Submit your leave request at least 3 days in advance for better approval chances.',
        type: 'tip',
        priority: 'medium',
        context,
        actionType
      });

      if (data.leaveType && data.leaveType.toLowerCase().includes('sick')) {
        suggestions.push({
          id: `sick_leave_${Date.now()}`,
          title: 'Medical Documentation',
          description: 'For sick leave, consider attaching medical documentation to speed up approval.',
          type: 'reminder',
          priority: 'high',
          context,
          actionType
        });
      }

      if (data.duration && data.duration > 5) {
        suggestions.push({
          id: `extended_leave_${Date.now()}`,
          title: 'Extended Leave Notice',
          description: 'For leaves longer than 5 days, consider giving at least 2 weeks notice.',
          type: 'best_practice',
          priority: 'medium',
          context,
          actionType
        });
      }
    }

    // Task management suggestions
    if (context === 'task_management') {
      if (actionType === 'create' && data.priority === 'High') {
        suggestions.push({
          id: `high_priority_${Date.now()}`,
          title: 'High Priority Task Alert',
          description: 'Consider setting a clear deadline and notifying the assignee immediately.',
          type: 'alert',
          priority: 'high',
          context,
          actionType
        });
      }

      if (actionType === 'create') {
        suggestions.push({
          id: `task_creation_${Date.now()}`,
          title: 'Task Management Tip',
          description: 'Provide clear instructions and set realistic deadlines for better task completion.',
          type: 'tip',
          priority: 'medium',
          context,
          actionType
        });
      }
    }

    // Attendance suggestions
    if (context === 'attendance') {
      if (actionType === 'check_in' && new Date().getHours() > 9) {
        suggestions.push({
          id: `late_checkin_${Date.now()}`,
          title: 'Late Check-in Detected',
          description: 'You\'re checking in after 9 AM. Consider setting an earlier alarm for tomorrow.',
          type: 'tip',
          priority: 'low',
          context,
          actionType
        });
      }

      if (actionType === 'check_in' && new Date().getHours() < 8) {
        suggestions.push({
          id: `early_checkin_${Date.now()}`,
          title: 'Early Bird!',
          description: 'Great job arriving early! This shows excellent dedication.',
          type: 'recognition',
          priority: 'low',
          context,
          actionType
        });
      }

      if (actionType === 'check_out' && data.hoursWorked && data.hoursWorked > 8) {
        suggestions.push({
          id: `overtime_${Date.now()}`,
          title: 'Overtime Detected',
          description: `You've worked ${data.hoursWorked} hours today. Great dedication!`,
          type: 'recognition',
          priority: 'medium',
          context,
          actionType
        });
      }
    }

    return suggestions.slice(0, 3);
  };

  // Create contextual message for different scenarios
  const createContextualMessage = (context, actionType, data) => {
    const messages = {
      leave_request: {
        create: `I'm submitting a leave request for ${data.leaveType || 'leave'} from ${data.startDate || 'start date'} to ${data.endDate || 'end date'}. Duration: ${data.duration || 'unknown'} days. Reason: ${data.reason || 'not specified'}. Can you provide smart suggestions to improve my request?`,
        update: `I'm updating my leave request. Can you suggest improvements?`,
        review: `I'm reviewing a leave request. What should I consider?`
      },
      attendance: {
        check_in: `I'm checking in to work at ${new Date().toLocaleTimeString()}. Any suggestions for productivity today?`,
        check_out: `I'm checking out after working ${data.hoursWorked || 'unknown'} hours. Any feedback on my work day?`,
        review: `I'm reviewing attendance records. What patterns should I look for?`
      },
      task_management: {
        create: `I'm creating a new task: "${data.title || 'task'}" with priority ${data.priority || 'normal'} assigned to ${data.assignedTo || 'someone'} due ${data.deadline || 'soon'}. Any suggestions for better task management?`,
        update: `I'm updating a task. What should I consider?`,
        complete: `I'm marking a task as complete. Any suggestions for follow-up?`
      },
      user_management: {
        create: `I'm adding a new user to the system. What onboarding steps should I consider?`,
        update: `I'm updating user information. Any best practices?`,
        review: `I'm reviewing user profiles. What should I look for?`
      },
      recruitment: {
        create: `I'm posting a new job for ${data.title || 'position'} in ${data.location || 'location'}. Any suggestions to attract better candidates?`,
        review: `I'm reviewing job applications. What criteria should I prioritize?`,
        interview: `I'm scheduling interviews. Any tips for effective interviewing?`
      },
      evaluation: {
        create: `I'm creating a performance evaluation. What key areas should I focus on?`,
        review: `I'm reviewing performance evaluations. Any insights on improvement areas?`,
        feedback: `I'm providing feedback to an employee. How can I make it constructive?`
      }
    };

    return messages[context]?.[actionType] || `I'm performing ${actionType} in ${context}. Can you provide relevant suggestions?`;
  };

  // Parse Jarvis response and extract actionable suggestions
  const parseJarvisResponse = (jarvisResponse, context, actionType, data) => {
    const suggestions = [];
    
    if (!jarvisResponse || !jarvisResponse.response) {
      return suggestions;
    }

    const content = jarvisResponse.response.content || '';
    const lowerContent = content.toLowerCase();

    // Extract suggestions based on context and content analysis
    if (context === 'leave_request') {
      if (lowerContent.includes('documentation') || lowerContent.includes('medical')) {
        suggestions.push({
          id: `leave_doc_${Date.now()}`,
          title: 'Documentation Reminder',
          description: 'Consider attaching supporting documentation for faster approval.',
          type: 'reminder',
          priority: 'medium',
          context,
          actionType
        });
      }

      if (lowerContent.includes('advance') || lowerContent.includes('notice')) {
        suggestions.push({
          id: `leave_notice_${Date.now()}`,
          title: 'Advance Notice',
          description: 'Providing advance notice improves approval chances and helps with planning.',
          type: 'tip',
          priority: 'low',
          context,
          actionType
        });
      }

      if (data.duration > 7) {
        suggestions.push({
          id: `leave_long_${Date.now()}`,
          title: 'Extended Leave',
          description: 'For leaves longer than a week, consider discussing with your manager first.',
          type: 'best_practice',
          priority: 'high',
          context,
          actionType
        });
      }
    }

    if (context === 'attendance') {
      const currentHour = new Date().getHours();
      
      if (actionType === 'check_in' && currentHour > 9) {
        suggestions.push({
          id: `attendance_late_${Date.now()}`,
          title: 'Late Check-in',
          description: 'Consider setting an earlier alarm to maintain consistent attendance.',
          type: 'tip',
          priority: 'low',
          context,
          actionType
        });
      }

      if (actionType === 'check_out' && data.hoursWorked > 8) {
        suggestions.push({
          id: `attendance_overtime_${Date.now()}`,
          title: 'Overtime Recognition',
          description: 'Great dedication! Make sure to track overtime hours properly.',
          type: 'recognition',
          priority: 'medium',
          context,
          actionType
        });
      }
    }

    if (context === 'task_management') {
      if (data.priority === 'High' || data.priority === 'high') {
        suggestions.push({
          id: `task_priority_${Date.now()}`,
          title: 'High Priority Task',
          description: 'Consider notifying the assignee immediately and setting clear expectations.',
          type: 'alert',
          priority: 'high',
          context,
          actionType
        });
      }

      if (data.deadline && new Date(data.deadline) < new Date(Date.now() + 24 * 60 * 60 * 1000)) {
        suggestions.push({
          id: `task_urgent_${Date.now()}`,
          title: 'Urgent Deadline',
          description: 'This task has a deadline within 24 hours. Immediate action required.',
          type: 'warning',
          priority: 'high',
          context,
          actionType
        });
      }
    }

    if (context === 'user_management' && actionType === 'create') {
      suggestions.push({
        id: `user_onboarding_${Date.now()}`,
        title: 'New User Onboarding',
        description: 'Create an onboarding checklist to ensure smooth integration.',
        type: 'best_practice',
        priority: 'medium',
        context,
        actionType
      });
    }

    if (context === 'recruitment') {
      if (actionType === 'create') {
        suggestions.push({
          id: `job_posting_${Date.now()}`,
          title: 'Job Posting Optimization',
          description: 'Include clear requirements and company culture information to attract better candidates.',
          type: 'tip',
          priority: 'medium',
          context,
          actionType
        });
      }

      if (actionType === 'review') {
        suggestions.push({
          id: `application_review_${Date.now()}`,
          title: 'Application Review',
          description: 'Review applications within 48 hours for better candidate experience.',
          type: 'best_practice',
          priority: 'medium',
          context,
          actionType
        });
      }
    }

    // Add general suggestions based on Jarvis content
    if (lowerContent.includes('recommend') || lowerContent.includes('suggest')) {
      const lines = content.split('\n').filter(line => 
        line.includes('recommend') || 
        line.includes('suggest') || 
        line.includes('consider') ||
        line.includes('tip') ||
        line.includes('advice')
      );

      lines.forEach((line, index) => {
        if (line.trim() && suggestions.length < 3) { // Limit to 3 suggestions
          suggestions.push({
            id: `general_${Date.now()}_${index}`,
            title: 'AI Recommendation',
            description: line.trim(),
            type: 'tip',
            priority: 'medium',
            context,
            actionType
          });
        }
      });
    }

    return suggestions.slice(0, 3); // Limit to 3 suggestions max
  };

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    setActiveSuggestions([]);
  }, []);

  // Remove specific suggestion
  const removeSuggestion = useCallback((suggestionId) => {
    setActiveSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  }, []);

  // Track suggestion feedback
  const trackFeedback = useCallback(async (suggestionId, feedback, context, actionType) => {
    try {
      await api.post('/api/ai/feedback', {
        suggestionId,
        feedback,
        context,
        actionType,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error tracking suggestion feedback:', error);
    }
  }, []);

  return {
    activeSuggestions,
    loading,
    generateSuggestions,
    clearSuggestions,
    removeSuggestion,
    trackFeedback
  };
};
