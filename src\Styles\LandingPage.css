.landing-page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fc;
  }

  .landing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #007bff;
    color: white;
  }

  .landing-header h1 {
    margin: 0;
    font-size: 24px;
  }

  .login-button {
    padding: 10px 20px;
    background-color: white;
    color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: transform 0.3s ease;
  }

  .login-button:hover {
    transform: scale(1.1);
  }

  .jobs-section {
    flex: 1;
    padding: 20px;
  }

  .jobs-section h2 {
    margin-bottom: 10px;
    font-size: 22px;
  }

  .jobs-section ul {
    list-style-type: none;
    padding: 0;
  }

  .job-item {
    margin-bottom: 20px;
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
  }

  .job-item:hover {
    transform: scale(1.05);
  }

  .job-description {
    color: #555;
    font-size: 16px;
    margin: 10px 0;
  }

  .details-button {
    padding: 8px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: transform 0.3s ease;
  }

  .details-button:hover {
    transform: scale(1.1);
  }

  .news-section {
    padding: 20px;
    background-color: #e9f4fd;
  }

  .news-section h2 {
    margin-bottom: 10px;
    font-size: 20px;
  }

  .news-section p {
    color: #333;
    font-size: 16px;
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 70%;
    max-width: 700px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  .modal h2 {
    margin-bottom: 15px;
    color: #007bff;
    border-bottom: 2px solid #eaeaea;
    padding-bottom: 10px;
  }

  .modal h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: #333;
    font-size: 18px;
  }

  .modal p {
    margin-bottom: 10px;
    line-height: 1.5;
  }

  .job-details-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
  }

  .job-details-section ul {
    padding-left: 20px;
    margin-bottom: 15px;
  }

  .job-details-section li {
    margin-bottom: 8px;
    line-height: 1.4;
  }

  .modal-buttons {
    display: flex;
    margin-top: 25px;
    justify-content: flex-end;
  }

  .modal button {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.2s ease, background-color 0.2s ease;
  }

  .modal button:hover {
    transform: scale(1.05);
  }

  .apply-button {
    background-color: #28a745 !important;
  }

  .apply-button:hover {
    background-color: #218838 !important;
  }

  .close-button:hover {
    background-color: #0069d9;
  }

  .landing-footer {
    text-align: center;
    padding: 15px;
    background-color: #007bff;
    color: white;
    margin-top: 30px;
  }
