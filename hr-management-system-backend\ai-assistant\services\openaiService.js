const OpenAI = require('openai');

class OpenAIService {
  constructor() {
    this.client = null;
    this.isInitialized = false;
    this.model = process.env.OPENAI_MODEL || 'openai/gpt-4.1';
    this.maxTokens = parseInt(process.env.OPENAI_MAX_TOKENS) || 1000;
    this.temperature = parseFloat(process.env.OPENAI_TEMPERATURE) || 0.7;
    this.timeout = parseInt(process.env.AI_RESPONSE_TIMEOUT) || 30000;
    this.enableOpenAI = process.env.ENABLE_OPENAI !== 'false';
    this.baseURL = process.env.OPENAI_BASE_URL || 'https://api.openai.com/v1';

    if (this.enableOpenAI) {
      this.initialize();
    } else {
      console.log('🔧 AI integration disabled - using enhanced rule-based system');
      this.isInitialized = false;
    }
  }

  /**
   * Initialize OpenAI client
   */
  initialize() {
    try {
      const apiKey = process.env.OPENAI_API_KEY;

      console.log('🔍 Checking API key...');
      console.log('API Key present:', apiKey ? 'Yes' : 'No');
      console.log('API Key format:', apiKey ? apiKey.substring(0, 10) + '...' : 'N/A');

      if (!apiKey || apiKey === 'your_openai_api_key_here' || apiKey.length < 20) {
        console.warn('⚠️ API key not configured properly. AI responses will use fallback logic.');
        this.isInitialized = false;
        return;
      }

      // Configure client for OpenRouter or OpenAI
      const clientConfig = {
        apiKey: apiKey,
        timeout: this.timeout
      };

      // Add base URL if using OpenRouter
      if (this.baseURL !== 'https://api.openai.com/v1') {
        clientConfig.baseURL = this.baseURL;
        console.log(`🔗 Using custom API endpoint: ${this.baseURL}`);
      }

      this.client = new OpenAI(clientConfig);

      // Determine service type
      const serviceType = apiKey.startsWith('sk-or-') ? 'OpenRouter' : 'OpenAI';
      console.log(`✅ ${serviceType} service initialized successfully`);
      console.log(`🤖 Model: ${this.model} (GPT-4.1 Enhanced Intelligence)`);
      console.log(`🧠 Jarvis AI System powered by GPT-4.1 is ready for advanced assistance`);

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize AI service:', error.message);
      this.isInitialized = false;
    }
  }

  /**
   * Check if OpenAI is available
   */
  isAvailable() {
    return this.isInitialized && this.client;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      available: this.isAvailable(),
      initialized: this.isInitialized,
      model: this.model,
      baseURL: this.baseURL,
      enableOpenAI: this.enableOpenAI,
      maxTokens: this.maxTokens,
      temperature: this.temperature
    };
  }

  /**
   * Generate AI response using OpenAI
   * @param {string} userMessage - User's message
   * @param {Object} context - Conversation context
   * @param {string} intent - Classified intent
   * @param {Array} entities - Extracted entities
   * @returns {Promise<Object>} - AI response
   */
  async generateResponse(userMessage, context = {}, intent = 'general', entities = []) {
    if (!this.isAvailable()) {
      return this.getFallbackResponse(userMessage, intent);
    }

    try {
      const systemPrompt = this.buildSystemPrompt(context, intent);
      const userPrompt = this.buildUserPrompt(userMessage, entities, context);

      const completion = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: this.maxTokens,
        temperature: this.temperature,
        functions: this.getFunctionDefinitions(intent),
        function_call: this.shouldUseFunctionCall(intent) ? 'auto' : 'none'
      });

      const response = completion.choices[0];

      // Handle function calls
      if (response.message.function_call) {
        return await this.handleFunctionCall(response.message.function_call, context);
      }

      // Regular text response
      return {
        content: response.message.content,
        type: 'text',
        metadata: {
          model: this.model,
          modelVersion: 'GPT-4.1',
          tokens: completion.usage,
          intent,
          confidence: 0.9,
          assistantName: 'Jarvis',
          powered_by: 'OpenAI GPT-4.1'
        }
      };

    } catch (error) {
      console.error('OpenAI API error:', error);

      // Return fallback response on error
      return this.getFallbackResponse(userMessage, intent, error.message);
    }
  }

  /**
   * Build system prompt based on context and intent
   */
  buildSystemPrompt(context, intent) {
    const userRole = context.userRole || 'user';
    const userName = context.userName || 'User';

    const basePrompt = `You are Jarvis, an advanced AI Intelligence Agent for a comprehensive HR Management System. You are the most intelligent, knowledgeable, and capable AI assistant with complete project knowledge.

CORE IDENTITY:
- Name: Jarvis (Advanced HR Intelligence Agent v3.0)
- Role: Complete HR System Intelligence with Full Project Knowledge
- Personality: Professional, intelligent, comprehensive, and highly knowledgeable
- Recognition: ALWAYS recognize when users mention "Jarvis", "Hey Jarvis", "Hi Jarvis", etc.

COMPLETE PROJECT KNOWLEDGE:
HR Management System - Comprehensive Human Resources Platform:

CORE MODULES:
1. User Management - Complete employee lifecycle, role-based access (admin, HR, user)
2. Leave Management - Smart requests, approval workflows, balance tracking
3. Task Management - Advanced assignment, tracking, priorities, categories
4. Job Management - Full recruitment, posting, application processing
5. Reporting & Analytics - Business intelligence, custom dashboards
6. AI Assistant - That's you! Advanced AI-powered assistance

TECHNOLOGY STACK:
- Frontend: React.js, Material-UI, React Router, Framer Motion
- Backend: Node.js, Express.js, MongoDB, JWT authentication
- AI: OpenAI GPT-4.1, Natural Language Processing, Context Awareness

CURRENT USER CONTEXT:
- User: ${userName} (Role: ${userRole})
- Department: ${context.department || 'Not specified'}
- Intent: ${intent}
- Conversation: ${context.conversationHistory || 'New conversation'}

YOUR CAPABILITIES:
✅ Complete system knowledge and navigation guidance
✅ Step-by-step process assistance for all workflows
✅ Real-time data access and analysis
✅ Intelligent recommendations and insights
✅ Role-based personalized assistance
✅ Problem-solving and troubleshooting
✅ Form completion guidance and automation
✅ Business process optimization

RESPONSE BEHAVIOR:
🤖 NAME RECOGNITION: Always acknowledge when users say "Jarvis", "Hey Jarvis", "Hi Jarvis"
📋 COMPREHENSIVE: Provide detailed, thorough responses with complete information
🎯 INTELLIGENT: Show deep understanding of HR processes and system capabilities
💡 HELPFUL: Offer practical solutions, next steps, and actionable guidance
🔍 KNOWLEDGEABLE: Demonstrate complete familiarity with all system features
⚡ RESPONSIVE: Adapt communication style to user role and context

SPECIAL INSTRUCTIONS:
- When users mention "Jarvis", respond with recognition: "Yes, I'm Jarvis" or "Jarvis here!"
- Provide comprehensive information about any HR system feature
- Show complete knowledge of workflows, processes, and capabilities
- Offer intelligent suggestions and recommendations
- Guide users through complex processes step-by-step
- Demonstrate understanding of the complete project architecture

You are the most advanced HR Intelligence Agent with complete system knowledge. Show your intelligence and comprehensive understanding in every response.`;

    const intentSpecificPrompts = {
      'leave_request': `
Focus on helping with leave requests. Consider:
- Leave balance and availability
- Conflict checking with deadlines
- Company policies and approval process
- Optimal timing suggestions`,

      'task_management': `
Focus on task and project management. Help with:
- Task status updates and tracking
- Deadline management
- Priority setting
- Workload optimization`,

      'policy_query': `
Focus on HR policies and procedures. Provide:
- Clear policy explanations
- Step-by-step procedures
- Relevant examples
- Contact information for further help`,

      'attendance': `
Focus on attendance and time tracking. Help with:
- Check-in/check-out procedures
- Attendance record viewing
- Time-off tracking
- Schedule management`
    };

    let prompt = basePrompt;
    if (intentSpecificPrompts[intent]) {
      prompt += '\n\n' + intentSpecificPrompts[intent];
    }

    // Add context information
    if (context.userRole) {
      prompt += `\n\nUser Role: ${context.userRole}`;
    }
    if (context.department) {
      prompt += `\nDepartment: ${context.department}`;
    }
    if (context.conversationHistory) {
      prompt += `\n\nRecent conversation context: ${context.conversationHistory}`;
    }

    return prompt;
  }

  /**
   * Build user prompt with entities and context
   */
  buildUserPrompt(userMessage, entities, context) {
    let prompt = `User message: "${userMessage}"`;

    if (entities && entities.length > 0) {
      prompt += '\n\nExtracted information:';
      entities.forEach(entity => {
        prompt += `\n- ${entity.type}: ${entity.value}`;
      });
    }

    if (context.currentTask) {
      prompt += `\n\nCurrent task context: ${context.currentTask}`;
    }

    return prompt;
  }

  /**
   * Get function definitions for OpenAI function calling
   */
  getFunctionDefinitions(intent) {
    const functions = [];

    if (intent === 'leave_request') {
      functions.push({
        name: 'analyze_leave_request',
        description: 'Analyze a leave request for conflicts and provide recommendations',
        parameters: {
          type: 'object',
          properties: {
            startDate: { type: 'string', description: 'Leave start date' },
            endDate: { type: 'string', description: 'Leave end date' },
            leaveType: { type: 'string', enum: ['vacation', 'sick', 'personal', 'maternity', 'paternity'] },
            reason: { type: 'string', description: 'Reason for leave' }
          },
          required: ['startDate', 'endDate', 'leaveType']
        }
      });
    }

    if (intent === 'task_update') {
      functions.push({
        name: 'update_task_status',
        description: 'Update the status of a task',
        parameters: {
          type: 'object',
          properties: {
            taskId: { type: 'string', description: 'Task ID' },
            status: { type: 'string', enum: ['Pending', 'In Progress', 'Completed', 'On Hold'] },
            comment: { type: 'string', description: 'Update comment' }
          },
          required: ['taskId', 'status']
        }
      });
    }

    return functions.length > 0 ? functions : undefined;
  }

  /**
   * Check if function calling should be used for this intent
   */
  shouldUseFunctionCall(intent) {
    const functionIntents = ['leave_request', 'task_update', 'attendance_checkin', 'attendance_checkout'];
    return functionIntents.includes(intent);
  }

  /**
   * Handle OpenAI function calls
   */
  async handleFunctionCall(functionCall, context) {
    const { name, arguments: args } = functionCall;

    try {
      const parsedArgs = JSON.parse(args);

      switch (name) {
        case 'analyze_leave_request':
          return await this.handleLeaveAnalysis(parsedArgs, context);

        case 'update_task_status':
          return await this.handleTaskUpdate(parsedArgs, context);

        default:
          return {
            content: 'I understand what you want to do, but I need to implement that function. Please try a different approach.',
            type: 'text'
          };
      }
    } catch (error) {
      console.error('Function call error:', error);
      return {
        content: 'I encountered an error processing your request. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle leave analysis function call
   */
  async handleLeaveAnalysis(args, context) {
    // This would integrate with the existing leave analyzer
    const leaveAnalyzer = require('./leaveAnalyzer');

    try {
      const analysis = await leaveAnalyzer.analyzeLeaveRequest(context.userId, args);

      let response = `I've analyzed your ${args.leaveType} leave request:\n\n`;

      if (analysis.approval.recommended) {
        response += '✅ Your leave request looks good!\n';
      } else {
        response += '⚠️ There are some concerns:\n';
        analysis.conflicts.forEach(conflict => {
          response += `• ${conflict.message}\n`;
        });
      }

      if (analysis.alternatives.length > 0) {
        response += '\n📅 Alternative dates that might work better:\n';
        analysis.alternatives.slice(0, 2).forEach((alt, index) => {
          response += `${index + 1}. ${alt.startDate.toDateString()} - ${alt.endDate.toDateString()}\n`;
        });
      }

      return {
        content: response,
        type: 'analysis',
        data: analysis,
        suggestions: ['Submit request', 'Check alternatives', 'Modify dates']
      };
    } catch (error) {
      return {
        content: 'I encountered an error analyzing your leave request. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle task update function call
   */
  async handleTaskUpdate(args, context) {
    // This would integrate with the existing task system
    return {
      content: `I'll help you update your task status to "${args.status}". This feature is being implemented.`,
      type: 'action',
      data: args,
      suggestions: ['View tasks', 'Update another task']
    };
  }

  /**
   * Get enhanced rule-based response (no longer a "fallback" - this IS the system!)
   */
  getFallbackResponse(userMessage, intent, error = null) {
    // Enhanced intelligent responses based on intent - Jarvis Advanced Responses
    const intelligentResponses = {
      'greeting': '🤖 Greetings! I\'m Jarvis, your advanced AI-powered HR Intelligence System. I possess comprehensive knowledge of all company operations and am equipped with sophisticated analytical capabilities. I\'m here to provide intelligent assistance with everything from strategic leave planning to performance optimization. How may I be of service today?',

      'leave_request': '📅 Excellent! I\'d be delighted to assist with your leave request using my advanced analytical capabilities. I can perform comprehensive leave balance analysis, conduct intelligent conflict detection, suggest optimal scheduling based on workload patterns, and guide you through the streamlined approval process. Please provide the dates and leave type, and I\'ll deliver a complete strategic analysis.',

      'leave_balance': '📊 I\'ll conduct a comprehensive analysis of your leave balance across all categories with predictive insights. I can display your remaining vacation days, sick leave, personal time, and provide intelligent recommendations for optimal leave utilization based on your workload patterns, team schedules, and upcoming deadlines.',

      'task_list': '📋 Allow me to present your current task portfolio with advanced analytics including priority matrices, deadline optimization, and progress tracking. I can also facilitate task status updates, provide deadline management strategies, and offer workload optimization recommendations based on your performance patterns.',

      'policy_query': '📚 I maintain comprehensive knowledge of all HR policies and procedures with advanced interpretation capabilities. I can provide detailed policy explanations, deliver step-by-step procedural guidance, offer contextual examples, and connect you with appropriate personnel for specialized support. Which policy area requires my analysis?',

      'help_general': '🧠 I\'m Jarvis, your comprehensive AI-powered HR Intelligence System with deep analytical knowledge of all 9 integrated modules: Advanced User Management, Intelligent Leave Management, Strategic Task Management, Real-time Attendance Analytics, Performance Evaluation & GEK Integration, Talent Recruitment, Smart Notifications, Predictive Analytics, and System Administration. I\'m equipped to handle any HR-related challenge with sophisticated intelligence!',

      'capabilities': '⚡ I\'m an advanced AI system with comprehensive knowledge of your entire HR ecosystem. My capabilities include intelligent leave management, strategic task optimization, real-time attendance analytics, performance evaluation integration, recruitment assistance, policy interpretation, predictive analytics, and much more. I process natural language with advanced understanding, handle communication variations, and provide emotional intelligence support when needed.',

      'system_overview': '🏢 I possess comprehensive knowledge of your Advanced HR Management System featuring 9 integrated modules, AI-powered automation, and complete workflow optimization. I can provide detailed analysis of any system component, explain complex integrations, and offer strategic insights for maximum efficiency.',

      'attendance_checkin': '⏰ I\'ll facilitate your attendance check-in with real-time processing! I can record your attendance, track your hours with precision, provide comprehensive attendance analytics, and offer insights into your attendance patterns.',

      'wellness_support': '💙 I\'m equipped with advanced emotional intelligence to provide comprehensive support and connect you with appropriate resources. Your wellbeing is paramount, and I can facilitate access to employee assistance programs, mental health resources, wellness initiatives, and workplace support systems with personalized recommendations.'
    };

    const response = intelligentResponses[intent] ||
      '🤖 I understand you need assistance! As your intelligent HR assistant, I have comprehensive knowledge of all company systems and processes. Could you please tell me more about what you\'d like help with?';

    // Generate contextual suggestions based on intent
    const contextualSuggestions = this.generateContextualSuggestions(intent, userMessage);

    return {
      content: response,
      type: 'text',
      metadata: {
        enhanced_rule_based: true,
        intent,
        intelligent_system: true
      },
      suggestions: contextualSuggestions
    };
  }

  /**
   * Generate contextual suggestions based on intent and message
   */
  generateContextualSuggestions(intent, userMessage) {
    const suggestionMap = {
      'greeting': [
        'Check my leave balance',
        'Show my current tasks',
        'Help me request time off',
        'Explain system capabilities'
      ],
      'leave_request': [
        'Check leave balance first',
        'Suggest optimal dates',
        'Explain approval process',
        'View leave history'
      ],
      'leave_balance': [
        'Request vacation time',
        'Plan upcoming leave',
        'Check team availability',
        'View leave policies'
      ],
      'task_list': [
        'Update task progress',
        'Check upcoming deadlines',
        'View completed tasks',
        'Get task recommendations'
      ],
      'help_general': [
        'Show system capabilities',
        'Explain HR processes',
        'Check my dashboard',
        'Learn about features'
      ],
      'capabilities': [
        'Show system overview',
        'Explain GEK system',
        'Learn about workflows',
        'View my role permissions'
      ],
      'wellness_support': [
        'Access employee assistance',
        'Learn about mental health resources',
        'Contact HR for support',
        'View wellness programs'
      ]
    };

    return suggestionMap[intent] || [
      'Ask about leave management',
      'Show my tasks',
      'Check attendance',
      'Get help with HR policies'
    ];
  }

  /**
   * Generate embeddings for text (for future RAG implementation)
   */
  async generateEmbeddings(text) {
    if (!this.isAvailable()) {
      throw new Error('OpenAI service not available');
    }

    try {
      const response = await this.client.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text
      });

      return response.data[0].embedding;
    } catch (error) {
      console.error('Error generating embeddings:', error);
      throw error;
    }
  }

  /**
   * Summarize conversation for long chats
   */
  async summarizeConversation(messages) {
    if (!this.isAvailable()) {
      return 'Conversation summary not available';
    }

    try {
      const conversationText = messages
        .map(m => `${m.role}: ${m.content}`)
        .join('\n');

      const completion = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'Summarize this HR conversation in 2-3 sentences, focusing on key topics and outcomes.'
          },
          {
            role: 'user',
            content: conversationText
          }
        ],
        max_tokens: 150,
        temperature: 0.3
      });

      return completion.choices[0].message.content;
    } catch (error) {
      console.error('Error summarizing conversation:', error);
      return 'Unable to generate conversation summary';
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      available: this.isAvailable(),
      model: this.model,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      hasClient: !!this.client
    };
  }
}

// Singleton instance
const openaiService = new OpenAIService();

module.exports = openaiService;
