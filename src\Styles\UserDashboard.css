/* General Layout */
.dashboard-container {
    display: flex;
    height: 100vh;
    background-color: #f4f6f8;
    font-family: '<PERSON>o', sans-serif;
    position: relative;
    max-width: 1200px;  /* Limit the max-width of the entire layout */
    margin: 0 auto;     /* Center the layout */
  }
  
  /* Sidebar Styling */
  .sidebar {
    width: 200px;  /* Sidebar width */
    background-color: #1976D2;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  
  .sidebar .MuiButton-root {
    margin-bottom: 15px;
    color: white;
    text-transform: none;
    font-weight: 600;
    width: 100%;
  }
  
  /* Main Content Styling */
  .main-content {
    margin-left: 220px;  /* Adjusted for the sidebar */
    width: calc(100% - 220px);
    background-color: #fff;
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 1500px; /* Limit the width of the main content */
    margin: 0 auto;    /* Center the content */
  }
  
  /* Card Styling */
  .MuiCard-root {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .MuiCardContent-root {
    padding: 20px;
  }
  
  .MuiButton-root {
    text-transform: none;
    font-weight: 600;
    padding: 12px 20px;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  
  /* Sidebar Logout Button Styling */
  .sidebar .logout-button {
    background-color: #D32F2F;  /* Red background for logout */
    color: white;
    font-weight: 600;
    margin-top: auto;  /* Push to the bottom of the sidebar */
    text-transform: none;
  }
  
  /* Dialog Styling */
  .MuiDialog-root {
    padding: 20px;
    max-width: 500px;
    width: 150%;
  }
  
  .MuiDialogTitle-root {
    background-color: #1976D2;
    color: white;
    padding: 16px;
    font-size: 1.6rem;
    font-weight: bold;
  }
  
  .MuiDialogContent-root {
    padding: 20px;
  }
  
  .MuiDialogActions-root {
    padding: 20px;
    justify-content: center;
  }
  
  .MuiTextField-root {
    margin-bottom: 20px;
    width: 150%;
  }
  
  .MuiDialogActions-root button {
    padding: 10px 20px;
    font-weight: 600;
  }
  
  /* Responsive Styles */
  @media (max-width: 800px) {
    .dashboard-container {
      flex-direction: column;
      width: 100%;  /* Make the layout 100% width on smaller screens */
      padding: 0 10px;
    }
  
    .sidebar {
      width: 100%;
      position: relative;
      margin-bottom: 20px;
    }
  
    .main-content {
      margin-left: 0;
      width: 100%;
    }
  }
  