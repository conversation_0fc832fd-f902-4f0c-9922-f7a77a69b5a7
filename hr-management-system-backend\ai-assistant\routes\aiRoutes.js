const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middleware/authmiddleware');
const conversationService = require('../services/conversationService');
const intentClassifier = require('../services/intentClassifier');
const leaveAnalyzer = require('../services/leaveAnalyzer');
const openaiService = require('../services/openaiService');
const chatController = require('../controllers/chatController');
const smartAssistantRoutes = require('./smartAssistantRoutes');
// Removed context-aware service - handled by separate context agent


// Chat routes
router.use('/chat', chatController);

// Smart Assistant routes
router.use('/smart-assistant', smartAssistantRoutes);

// Conversation management routes
router.get('/conversations', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { status, domain, limit, offset } = req.query;

    const conversations = await conversationService.getUserConversations(userId, {
      status,
      domain,
      limit: parseInt(limit) || 10,
      offset: parseInt(offset) || 0
    });

    res.json({
      success: true,
      data: conversations
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching conversations',
      error: error.message
    });
  }
});

router.get('/conversations/:conversationId', authenticate, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    const conversation = await conversationService.getConversation(conversationId, userId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    res.json({
      success: true,
      data: conversation
    });
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching conversation',
      error: error.message
    });
  }
});

router.post('/conversations', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { initialMessage, domain } = req.body;

    const conversation = await conversationService.createConversation(userId, initialMessage, domain);

    res.status(201).json({
      success: true,
      data: conversation
    });
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating conversation',
      error: error.message
    });
  }
});

router.put('/conversations/:conversationId/archive', authenticate, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    const conversation = await conversationService.archiveConversation(conversationId, userId);

    res.json({
      success: true,
      data: conversation
    });
  } catch (error) {
    console.error('Error archiving conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Error archiving conversation',
      error: error.message
    });
  }
});

router.delete('/conversations/:conversationId', authenticate, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    const success = await conversationService.deleteConversation(conversationId, userId);

    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    res.json({
      success: true,
      message: 'Conversation deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting conversation',
      error: error.message
    });
  }
});

// Search conversations
router.get('/conversations/search/:query', authenticate, async (req, res) => {
  try {
    const { query } = req.params;
    const userId = req.user.id;
    const { limit } = req.query;

    const results = await conversationService.searchConversations(userId, query, {
      limit: parseInt(limit) || 10
    });

    res.json({
      success: true,
      data: results
    });
  } catch (error) {
    console.error('Error searching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Error searching conversations',
      error: error.message
    });
  }
});

// Intent classification routes
router.post('/classify-intent', authenticate, async (req, res) => {
  try {
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Text is required for intent classification'
      });
    }

    const classification = intentClassifier.classifyIntent(text);
    const entities = intentClassifier.extractEntities(text, classification.intent);

    res.json({
      success: true,
      data: {
        classification,
        entities
      }
    });
  } catch (error) {
    console.error('Error classifying intent:', error);
    res.status(500).json({
      success: false,
      message: 'Error classifying intent',
      error: error.message
    });
  }
});

router.get('/intents', authenticate, async (req, res) => {
  try {
    const intents = intentClassifier.getAvailableIntents();
    const intentDescriptions = intents.map(intent => ({
      intent,
      description: intentClassifier.getIntentDescription(intent)
    }));

    res.json({
      success: true,
      data: intentDescriptions
    });
  } catch (error) {
    console.error('Error fetching intents:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching intents',
      error: error.message
    });
  }
});

router.get('/suggestions/:partialText', authenticate, async (req, res) => {
  try {
    const { partialText } = req.params;
    const suggestions = intentClassifier.getSuggestions(partialText);

    res.json({
      success: true,
      data: suggestions
    });
  } catch (error) {
    console.error('Error getting suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting suggestions',
      error: error.message
    });
  }
});

// Leave analysis routes
router.post('/analyze-leave', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, leaveType, reason } = req.body;

    if (!startDate || !endDate || !leaveType) {
      return res.status(400).json({
        success: false,
        message: 'Start date, end date, and leave type are required'
      });
    }

    const analysis = await leaveAnalyzer.analyzeLeaveRequest(userId, {
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      leaveType,
      reason
    });

    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    console.error('Error analyzing leave request:', error);
    res.status(500).json({
      success: false,
      message: 'Error analyzing leave request',
      error: error.message
    });
  }
});

router.get('/leave-suggestions', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { leaveType, duration, timeframe } = req.query;

    const suggestions = await leaveAnalyzer.getOptimalLeaveSuggestions(userId, {
      leaveType: leaveType || 'vacation',
      duration: parseInt(duration) || 5,
      timeframe: timeframe || 'next_3_months'
    });

    res.json({
      success: true,
      data: suggestions
    });
  } catch (error) {
    console.error('Error getting leave suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting leave suggestions',
      error: error.message
    });
  }
});

// Analytics routes
router.get('/analytics/conversations', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = req.query;

    const dateRange = {};
    if (startDate) dateRange.startDate = new Date(startDate);
    if (endDate) dateRange.endDate = new Date(endDate);

    const analytics = await conversationService.getAnalytics(userId, dateRange);

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching conversation analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching analytics',
      error: error.message
    });
  }
});

// Export conversation
router.get('/conversations/:conversationId/export', authenticate, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;
    const { format } = req.query;

    const exportData = await conversationService.exportConversation(conversationId, userId, format);

    if (exportData.format === 'text') {
      res.setHeader('Content-Type', 'text/plain');
      res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);
      res.send(exportData.content);
    } else {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);
      res.json(exportData.content);
    }
  } catch (error) {
    console.error('Error exporting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Error exporting conversation',
      error: error.message
    });
  }
});

// OpenAI configuration and testing routes
router.post('/openai/test', authenticate, async (req, res) => {
  try {
    if (!openaiService.isAvailable()) {
      return res.status(503).json({
        success: false,
        message: 'OpenAI service is not available. Please check your API key configuration.'
      });
    }

    const testResponse = await openaiService.generateResponse(
      'Hello, this is a test message',
      { userRole: req.user.role },
      'greeting',
      []
    );

    res.json({
      success: true,
      message: 'OpenAI test successful',
      response: testResponse
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'OpenAI test failed',
      error: error.message
    });
  }
});

router.get('/openai/status', authenticate, (req, res) => {
  const status = openaiService.getStatus();
  res.json({
    success: true,
    data: status
  });
});

// Context-aware assistance endpoints removed - handled by separate context agent



// Health check
router.get('/health', (req, res) => {
  const openaiStatus = openaiService.getStatus();

  res.json({
    success: true,
    message: 'Jarvis AI Assistant service is running (Powered by GPT-4.1)',
    timestamp: new Date().toISOString(),
    services: {
      conversationService: 'active',
      intentClassifier: intentClassifier.isTrained ? 'trained' : 'not_trained',
      leaveAnalyzer: 'active',
      openaiService: {
        status: openaiStatus.available ? 'available' : 'unavailable',
        model: openaiStatus.model,
        initialized: openaiStatus.initialized
      }
    },
    capabilities: {
      basicChat: true,
      realTimeProcessing: true,
      intentClassification: intentClassifier.isTrained,
      leaveAnalysis: true,
      advancedAI: openaiStatus.available,
      functionCalling: openaiStatus.available
    }
  });
});

module.exports = router;
