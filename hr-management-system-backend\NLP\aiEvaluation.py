import sys
import json
import re
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.corpus import stopwords
import os
import datetime

# Import Flair
try:
    from flair.data import Sentence
    from flair.models import SequenceTagger
    from flair.embeddings import WordEmbeddings, DocumentPoolEmbeddings, Embeddings
    from flair.embeddings import TransformerDocumentEmbeddings
    from sklearn.metrics.pairwise import cosine_similarity
    import torch
    import numpy as np
except ImportError:
    print("Flair not installed. Please run install_nltk_packages.py first.", file=sys.stderr)
    sys.exit(1)

# Download NLTK resources
def download_nltk_resources():
    try:
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        nltk.download('averaged_perceptron_tagger', quiet=True)
        nltk.download('maxent_ne_chunker', quiet=True)
        nltk.download('words', quiet=True)
        print("NLTK resources downloaded successfully", file=sys.stderr)
    except Exception as e:
        print(f"Error downloading NLTK resources: {str(e)}", file=sys.stderr)

# Download NLTK resources
download_nltk_resources()

# Initialize Flair models
print("Loading Flair models...", file=sys.stderr)
try:
    # Word embeddings for semantic similarity
    embeddings = WordEmbeddings('glove')
    document_embeddings = DocumentPoolEmbeddings([embeddings])
    print("Flair models loaded successfully", file=sys.stderr)
except Exception as e:
    print(f"Error loading Flair models: {str(e)}", file=sys.stderr)
    # Continue without Flair if models fail to load

# Define evaluation criteria and their descriptions
EVALUATION_CRITERIA = {
    "performance": "Work quality, meeting deadlines, and achieving goals",
    "attitude": "Positive approach, willingness to learn, and adaptability",
    "communication": "Clarity in written and verbal communication, listening skills",
    "teamwork": "Collaboration, supporting colleagues, and contributing to team goals",
    "initiative": "Proactivity, problem-solving, and taking ownership"
}

# Define positive and negative indicators for each criterion
PERFORMANCE_INDICATORS = {
    "performance": {
        "positive": ["completed", "achieved", "delivered", "successful", "on time", "quality", "excellent", "exceeded", "improved"],
        "negative": ["late", "missed", "failed", "incomplete", "poor", "delayed", "errors", "issues", "problems"]
    },
    "attitude": {
        "positive": ["positive", "enthusiastic", "willing", "adaptable", "flexible", "eager", "motivated", "committed"],
        "negative": ["negative", "resistant", "unwilling", "inflexible", "unmotivated", "disinterested", "complained"]
    },
    "communication": {
        "positive": ["clear", "articulate", "responsive", "timely", "detailed", "thorough", "informative", "proactive"],
        "negative": ["unclear", "unresponsive", "delayed", "vague", "incomplete", "misunderstood", "confusing"]
    },
    "teamwork": {
        "positive": ["collaborative", "supportive", "helpful", "shared", "assisted", "contributed", "team player"],
        "negative": ["uncooperative", "isolated", "unhelpful", "selfish", "conflict", "disagreement", "tension"]
    },
    "initiative": {
        "positive": ["proactive", "suggested", "proposed", "initiated", "solved", "identified", "improved", "innovative"],
        "negative": ["passive", "waited", "required direction", "needed prompting", "hesitant", "reluctant"]
    }
}

def analyze_task_performance(tasks):
    """
    Analyze task performance using NLP
    
    Args:
        tasks (list): List of task objects with status, updates, deadline, etc.
        
    Returns:
        dict: Analysis results with scores and insights
    """
    if not tasks or len(tasks) == 0:
        return {
            "performance": 0,
            "attitude": 0,
            "communication": 0,
            "teamwork": 0,
            "initiative": 0,
            "insights": {
                "performance": "No task data available for analysis.",
                "attitude": "No task data available for analysis.",
                "communication": "No task data available for analysis.",
                "teamwork": "No task data available for analysis.",
                "initiative": "No task data available for analysis."
            }
        }
    
    # Initialize scores
    scores = {
        "performance": 0,
        "attitude": 0,
        "communication": 0,
        "teamwork": 0,
        "initiative": 0
    }
    
    # Initialize insights
    insights = {
        "performance": [],
        "attitude": [],
        "communication": [],
        "teamwork": [],
        "initiative": []
    }
    
    # Analyze each task
    completed_tasks = 0
    on_time_tasks = 0
    high_quality_tasks = 0
    
    for task in tasks:
        # Check task completion status
        if task.get('status') == 'Completed':
            completed_tasks += 1
            
            # Check if completed on time
            if task.get('completedAt') and task.get('deadline'):
                completed_date = datetime.datetime.fromisoformat(task.get('completedAt').replace('Z', '+00:00'))
                deadline_date = datetime.datetime.fromisoformat(task.get('deadline').replace('Z', '+00:00'))
                
                if completed_date <= deadline_date:
                    on_time_tasks += 1
                    insights["performance"].append(f"Completed task '{task.get('title')}' on time.")
                else:
                    insights["performance"].append(f"Completed task '{task.get('title')}' after the deadline.")
        
        # Analyze task updates and feedback
        updates_text = ""
        for update in task.get('updates', []):
            updates_text += update.get('message', '') + " "
        
        feedback = task.get('feedback', '')
        
        # Combine all text for analysis
        all_text = task.get('title', '') + " " + task.get('description', '') + " " + updates_text + " " + feedback
        
        # Use Flair to analyze sentiment and extract insights
        try:
            # Create a Flair sentence
            sentence = Sentence(all_text)
            document_embeddings.embed(sentence)
            
            # Analyze for each criterion
            for criterion, indicators in PERFORMANCE_INDICATORS.items():
                # Check for positive indicators
                positive_score = 0
                for indicator in indicators["positive"]:
                    if indicator.lower() in all_text.lower():
                        positive_score += 1
                        if criterion == "performance" and indicator in ["quality", "excellent", "exceeded"]:
                            high_quality_tasks += 1
                
                # Check for negative indicators
                negative_score = 0
                for indicator in indicators["negative"]:
                    if indicator.lower() in all_text.lower():
                        negative_score += 1
                
                # Calculate net score for this criterion and task
                net_score = positive_score - negative_score
                scores[criterion] += net_score
                
                # Add insights based on indicators found
                if positive_score > negative_score:
                    insights[criterion].append(f"Demonstrated good {criterion} in task '{task.get('title')}'.")
                elif negative_score > positive_score:
                    insights[criterion].append(f"Could improve {criterion} in task '{task.get('title')}'.")
        
        except Exception as e:
            print(f"Error analyzing task with Flair: {str(e)}", file=sys.stderr)
    
    # Normalize scores based on number of tasks
    task_count = len(tasks)
    for criterion in scores:
        # Normalize to a 1-5 scale
        normalized_score = min(max(3 + (scores[criterion] / task_count), 1), 5)
        scores[criterion] = round(normalized_score, 1)
    
    # Calculate performance score based on completion rate and on-time rate
    if task_count > 0:
        completion_rate = completed_tasks / task_count
        on_time_rate = on_time_tasks / task_count if completed_tasks > 0 else 0
        quality_rate = high_quality_tasks / task_count if completed_tasks > 0 else 0
        
        performance_score = (completion_rate * 0.4) + (on_time_rate * 0.4) + (quality_rate * 0.2)
        scores["performance"] = min(max(round(performance_score * 5, 1), 1), 5)
    
    # Summarize insights
    summarized_insights = {}
    for criterion, criterion_insights in insights.items():
        if criterion_insights:
            summarized_insights[criterion] = ". ".join(criterion_insights[:3])
        else:
            summarized_insights[criterion] = f"Not enough data to evaluate {criterion}."
    
    return {
        "performance": scores["performance"],
        "attitude": scores["attitude"],
        "communication": scores["communication"],
        "teamwork": scores["teamwork"],
        "initiative": scores["initiative"],
        "insights": summarized_insights
    }

def analyze_attendance(attendance_records):
    """
    Analyze attendance records
    
    Args:
        attendance_records (list): List of attendance records
        
    Returns:
        dict: Analysis with score and insights
    """
    if not attendance_records or len(attendance_records) == 0:
        return {
            "score": 0,
            "insights": "No attendance data available for analysis."
        }
    
    # Count different attendance statuses
    present_count = 0
    late_count = 0
    absent_count = 0
    total_hours = 0
    
    for record in attendance_records:
        status = record.get('status', '')
        
        if status == 'Present':
            present_count += 1
        elif status == 'Late':
            late_count += 1
        elif status == 'Absent':
            absent_count += 1
        
        # Sum up hours worked
        total_hours += record.get('hoursWorked', 0)
    
    total_records = len(attendance_records)
    
    # Calculate attendance score (1-5 scale)
    if total_records > 0:
        present_rate = present_count / total_records
        late_rate = late_count / total_records
        absent_rate = absent_count / total_records
        
        # Weight: Present (70%), Late (20%), Absent (10%)
        attendance_score = (present_rate * 0.7) + ((1 - late_rate) * 0.2) + ((1 - absent_rate) * 0.1)
        attendance_score = min(max(round(attendance_score * 5, 1), 1), 5)
    else:
        attendance_score = 0
    
    # Generate insights
    insights = []
    
    if present_count > 0:
        present_percentage = (present_count / total_records) * 100
        insights.append(f"Present for {present_percentage:.1f}% of workdays.")
    
    if late_count > 0:
        late_percentage = (late_count / total_records) * 100
        insights.append(f"Late for {late_percentage:.1f}% of workdays.")
    
    if absent_count > 0:
        absent_percentage = (absent_count / total_records) * 100
        insights.append(f"Absent for {absent_percentage:.1f}% of workdays.")
    
    if total_hours > 0:
        avg_hours = total_hours / total_records
        insights.append(f"Averaged {avg_hours:.1f} hours per workday.")
    
    return {
        "score": attendance_score,
        "insights": ". ".join(insights)
    }

def generate_evaluation(user_data, tasks, attendance_records, previous_evaluations):
    """
    Generate an AI evaluation based on tasks, attendance, and previous evaluations
    
    Args:
        user_data (dict): User information
        tasks (list): List of tasks assigned to the user
        attendance_records (list): List of attendance records
        previous_evaluations (list): List of previous evaluations
        
    Returns:
        dict: Generated evaluation with ratings and insights
    """
    # Analyze task performance
    task_analysis = analyze_task_performance(tasks)
    
    # Analyze attendance
    attendance_analysis = analyze_attendance(attendance_records)
    
    # Initialize evaluation data
    evaluation = {
        "userId": user_data.get('_id'),
        "evaluationPeriod": "Monthly",
        "evaluationDate": datetime.datetime.now().isoformat(),
        "performanceRating": task_analysis["performance"],
        "attitudeRating": task_analysis["attitude"],
        "communicationRating": task_analysis["communication"],
        "teamworkRating": task_analysis["teamwork"],
        "initiativeRating": task_analysis["initiative"],
        "strengths": "",
        "areasForImprovement": "",
        "goals": "",
        "comments": "",
        "status": "Draft",
        "aiGenerated": True,
        "insights": {
            "tasks": task_analysis["insights"],
            "attendance": attendance_analysis["insights"]
        }
    }
    
    # Generate strengths based on highest scores
    strengths = []
    for criterion in ["performance", "attitude", "communication", "teamwork", "initiative"]:
        if task_analysis[criterion] >= 4:
            strengths.append(f"{criterion.capitalize()}: {task_analysis['insights'][criterion]}")
    
    if strengths:
        evaluation["strengths"] = "\n\n".join(strengths)
    else:
        evaluation["strengths"] = "AI could not identify specific strengths based on available data."
    
    # Generate areas for improvement based on lowest scores
    improvements = []
    for criterion in ["performance", "attitude", "communication", "teamwork", "initiative"]:
        if task_analysis[criterion] <= 3:
            improvements.append(f"{criterion.capitalize()}: Consider focusing on improving {EVALUATION_CRITERIA[criterion]}.")
    
    if improvements:
        evaluation["areasForImprovement"] = "\n\n".join(improvements)
    else:
        evaluation["areasForImprovement"] = "AI could not identify specific areas for improvement based on available data."
    
    # Generate goals based on areas for improvement
    goals = []
    for criterion in ["performance", "attitude", "communication", "teamwork", "initiative"]:
        if task_analysis[criterion] <= 3:
            goals.append(f"Improve {criterion} by focusing on {EVALUATION_CRITERIA[criterion]}.")
    
    if goals:
        evaluation["goals"] = "\n\n".join(goals)
    else:
        evaluation["goals"] = "Continue maintaining current performance levels across all areas."
    
    # Generate overall comments
    comments = [
        f"This AI-generated evaluation is based on {len(tasks)} tasks and {len(attendance_records)} attendance records.",
        f"Attendance score: {attendance_analysis['score']}/5 - {attendance_analysis['insights']}",
        "This evaluation is generated by AI and should be reviewed by HR before finalizing."
    ]
    
    evaluation["comments"] = "\n\n".join(comments)
    
    return evaluation

# Main function
if __name__ == "__main__":
    try:
        # Check if we have the right number of arguments
        if len(sys.argv) < 2:
            print(json.dumps({
                "error": "Missing required arguments. Usage: python aiEvaluation.py <user_data_json> [tasks_json] [attendance_json] [previous_evaluations_json]"
            }))
            sys.exit(1)
        
        # Parse input data
        user_data = json.loads(sys.argv[1])
        
        tasks = []
        if len(sys.argv) > 2:
            tasks = json.loads(sys.argv[2])
        
        attendance_records = []
        if len(sys.argv) > 3:
            attendance_records = json.loads(sys.argv[3])
        
        previous_evaluations = []
        if len(sys.argv) > 4:
            previous_evaluations = json.loads(sys.argv[4])
        
        # Generate evaluation
        evaluation = generate_evaluation(user_data, tasks, attendance_records, previous_evaluations)
        
        # Output as JSON
        print(json.dumps(evaluation))
        
    except Exception as e:
        error_data = {
            "error": str(e),
            "traceback": str(sys.exc_info())
        }
        print(json.dumps(error_data))
        sys.exit(1)
