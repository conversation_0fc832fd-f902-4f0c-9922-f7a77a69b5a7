const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middleware/authmiddleware');
const conversationService = require('../services/conversationService');
const intentClassifier = require('../services/intentClassifier');
const leaveAnalyzer = require('../services/leaveAnalyzer');
const openaiService = require('../services/openaiService');
const actionService = require('../services/actionService');
const personalityEngine = require('../services/personalityEngine');
const conversationMemory = require('../services/conversationMemory');
const emotionAnalyzer = require('../services/emotionAnalyzer');
const systemKnowledge = require('../services/systemKnowledge');
const realTimeEngine = require('../services/realTimeEngine');
const intelligentAgentCore = require('../services/intelligentAgentCore');
const AIInsight = require('../../models/AIInsight');

// Import existing models
const User = require('../../models/user');
const Task = require('../../models/Task');
const LeaveRequest = require('../../models/LeaveRequest');
const Attendance = require('../../models/Attendance');

class ChatController {
  /**
   * Send a message to the AI assistant
   */
  static async sendMessage(req, res) {
    try {
      const { conversationId, message, context = {} } = req.body;
      const userId = req.user.id;

      if (!message || !message.trim()) {
        return res.status(400).json({
          success: false,
          message: 'Message content is required'
        });
      }

      let conversation;

      // Get or create conversation
      if (conversationId) {
        conversation = await conversationService.getConversation(conversationId, userId);
        if (!conversation) {
          return res.status(404).json({
            success: false,
            message: 'Conversation not found'
          });
        }
      } else {
        conversation = await conversationService.createConversation(userId, message);
      }

      // Add user message
      const userMessage = await conversationService.addMessage(conversation._id, {
        userId,
        role: 'user',
        content: message.trim(),
        type: 'text'
      });

      // Get basic user context for intent classification
      const user = await User.findById(userId).select('name role department');
      const basicContext = {
        userId,
        userRole: user?.role,
        department: user?.department,
        userName: user?.name
      };

      // Get conversation context and memory
      const conversationContext = conversationMemory.getConversationContext(userId, 5);

      // Check for casual conversation first
      const casualResponse = personalityEngine.handleCasualConversation(
        message,
        conversationContext.recentTurns,
        userId
      );

      // If it's casual conversation, handle it with personality
      if (casualResponse) {
        // Store conversation turn in memory
        conversationMemory.storeConversationTurn(userId, message, casualResponse.content, {
          emotion: 'neutral',
          intent: 'casual_conversation',
          confidence: 1.0
        });

        // Add AI response to conversation
        const assistantMessage = await conversationService.addMessage(conversation._id, {
          userId,
          role: 'assistant',
          content: casualResponse.content,
          type: casualResponse.type || 'text',
          metadata: {
            intent: 'casual_conversation',
            confidence: 1.0,
            responseTime: Date.now() - Date.now(),
            model: 'personality-engine'
          }
        });

        return res.json({
          success: true,
          data: {
            conversationId: conversation._id,
            userMessage: userMessage.message,
            assistantMessage: assistantMessage.message,
            classification: { intent: 'casual_conversation', confidence: 1.0 },
            entities: [],
            suggestions: casualResponse.suggestions || []
          }
        });
      }

      // Classify intent using advanced NLP with emotion detection and context awareness
      const classification = intentClassifier.classifyIntent(message, conversation.messages, basicContext);
      const entities = intentClassifier.extractEntities(message, classification.intent);

      // Get detailed emotional analysis
      const emotionalAnalysis = emotionAnalyzer.analyzeComplete(message);

      // Log advanced analysis for debugging
      console.log('🧠 Advanced Analysis:', {
        originalText: classification.metadata?.originalText,
        correctedText: classification.metadata?.correctedText,
        hasTypos: classification.metadata?.hasTypos,
        emotionalState: classification.metadata?.emotionalState,
        primaryEmotion: emotionalAnalysis.emotions.primaryEmotion,
        sentiment: emotionalAnalysis.sentiment.sentiment,
        urgency: classification.metadata?.urgency,
        confidence: classification.confidence,
        reason: classification.reason
      });

      // Update personality engine with user interaction
      personalityEngine.updateUserProfile(
        userId,
        message,
        emotionalAnalysis.emotions.primaryEmotion,
        classification.intent
      );

      // Update message metadata
      userMessage.message.metadata = {
        intent: classification.intent,
        confidence: classification.confidence,
        entities
      };

      // Generate AI response with enhanced real-time processing
      const enhancedContext = {
        ...context,
        ...basicContext,
        conversationHistory: conversation.messages.slice(-3).map(m => `${m.role}: ${m.content}`).join('\n'),
        emotionalAnalysis,
        classification,
        entities
      };

      // Use intelligent agent core for enhanced processing
      const intelligentResponse = await intelligentAgentCore.processIntelligentMessage(userId, message, enhancedContext);

      // If intelligent agent provides a response, use it; otherwise fall back to real-time engine
      const aiResponse = intelligentResponse.content ? intelligentResponse : await realTimeEngine.processMessage(userId, message, enhancedContext);

      // Add AI response to conversation
      const assistantMessage = await conversationService.addMessage(conversation._id, {
        userId,
        role: 'assistant',
        content: aiResponse.content,
        type: aiResponse.type || 'text',
        metadata: {
          intent: classification.intent,
          confidence: classification.confidence,
          responseTime: aiResponse.responseTime,
          model: intelligentResponse.content ? 'jarvis-intelligent-agent-v3.0' : 'jarvis-v2.0',
          modelVersion: 'GPT-4.1',
          intelligentAgent: intelligentResponse.content ? true : false,
          realTimeProcessed: !intelligentResponse.content,
          cached: aiResponse.cached || false,
          assistantName: 'Jarvis',
          agentVersion: intelligentResponse.metadata?.version || 'v2.0',
          nameRecognized: intelligentResponse.metadata?.nameRecognized || false,
          powered_by: 'OpenAI GPT-4.1 + Intelligent Agent Core'
        }
      });

      // Update conversation context if needed
      if (aiResponse.contextUpdate) {
        await conversationService.updateContext(conversation._id, aiResponse.contextUpdate);
      }

      res.json({
        success: true,
        data: {
          conversationId: conversation._id,
          userMessage: userMessage.message,
          assistantMessage: assistantMessage.message,
          classification,
          entities,
          suggestions: aiResponse.suggestions || []
        }
      });

    } catch (error) {
      console.error('Error in sendMessage:', error);
      res.status(500).json({
        success: false,
        message: 'Error processing message',
        error: error.message
      });
    }
  }

  /**
   * Generate AI response based on intent and context with emotional intelligence
   */
  static async generateResponse(userId, message, classification, entities, conversation, context, emotionalAnalysis, conversationContext) {
    const startTime = Date.now();

    try {
      const { intent, confidence } = classification;
      const primaryEmotion = emotionalAnalysis?.emotions?.primaryEmotion;

      // Check if user is referencing previous conversation
      const previousReference = conversationMemory.isReferencingPrevious(message, userId);

      // Get enhanced user context for OpenAI
      const user = await User.findById(userId).select('name role department');
      const enhancedContext = {
        userId,
        userRole: user?.role,
        department: user?.department,
        userName: user?.name,
        conversationHistory: conversation.messages.slice(-3).map(m => `${m.role}: ${m.content}`).join('\n'),
        currentIntent: intent,
        confidence,
        // Enhanced context from advanced analysis
        emotionalState: classification.metadata?.emotionalState,
        primaryEmotion: primaryEmotion,
        sentiment: emotionalAnalysis?.sentiment?.sentiment,
        urgency: classification.metadata?.urgency,
        hasTypos: classification.metadata?.hasTypos,
        correctedText: classification.metadata?.correctedText,
        empathyResponse: classification.empathyResponse,
        conversationStage: classification.metadata?.conversationStage,
        // Memory and context
        conversationMemory: conversationContext,
        previousReference: previousReference,
        userFamiliarity: conversationContext.memoryInsights?.relationshipLevel || 0,
        // Emotional intelligence
        needsEmotionalSupport: emotionalAnalysis?.summary?.needsEmotionalSupport,
        stressLevel: emotionalAnalysis?.stress?.stressLevel
      };

      // Handle emotional responses first if needed
      if (primaryEmotion && ['sadness', 'anxiety', 'frustration', 'anger'].includes(primaryEmotion)) {
        const emotionalResponse = personalityEngine.generateEmpatheticResponse(
          primaryEmotion,
          message,
          conversationContext.recentTurns,
          userId
        );

        // If this is primarily an emotional expression, respond with empathy
        if (intent === 'wellness_support' || emotionalAnalysis?.summary?.needsEmotionalSupport) {
          // Store conversation turn in memory
          conversationMemory.storeConversationTurn(userId, message, emotionalResponse.content, {
            emotion: primaryEmotion,
            intent: 'wellness_support',
            confidence: 0.9,
            topics: ['emotional_support'],
            entities: entities
          });

          return {
            content: emotionalResponse.content,
            type: 'text',
            suggestions: emotionalResponse.suggestions,
            responseTime: Date.now() - startTime,
            emotionalSupport: true,
            tone: emotionalResponse.tone
          };
        }
      }

      // Check if OpenAI is enabled and available
      const enableOpenAI = process.env.ENABLE_OPENAI !== 'false';

      if (enableOpenAI && openaiService.isAvailable()) {
        try {
          const aiResponse = await openaiService.generateResponse(
            message,
            enhancedContext,
            intent,
            entities
          );

          // Store conversation turn in memory
          conversationMemory.storeConversationTurn(userId, message, aiResponse.content, {
            emotion: primaryEmotion,
            intent: intent,
            confidence: confidence,
            topics: conversationContext.currentTopics,
            entities: entities
          });

          // Add response time and return
          aiResponse.responseTime = Date.now() - startTime;
          return aiResponse;
        } catch (openaiError) {
          console.warn('OpenAI failed, falling back to rule-based responses:', openaiError.message);
        }
      } else if (!enableOpenAI) {
        console.log('🧠 Using enhanced rule-based system (OpenAI disabled)');
      }

      // Fallback to rule-based responses
      let response = {
        content: '',
        type: 'text',
        suggestions: [],
        contextUpdate: {}
      };

      // Handle different intents with rule-based logic and action service
      switch (intent) {
        // Greeting and Help
        case 'greeting':
          response = await ChatController.handleGreeting(userId, enhancedContext, primaryEmotion);
          break;
        case 'help_general':
          response = await ChatController.handleHelp(conversationContext.userRole);
          break;
        case 'capabilities':
          response = await ChatController.handleCapabilities(conversationContext.userRole);
          break;
        case 'gratitude':
          response = await ChatController.handleGratitude(userId, enhancedContext, primaryEmotion);
          break;
        case 'casual_conversation':
          response = await ChatController.handleCasualConversation(userId, message, enhancedContext, primaryEmotion);
          break;
        case 'farewell':
          response = await ChatController.handleFarewell(userId, enhancedContext);
          break;

        // User Management
        case 'user_create':
          response = await ChatController.handleUserCreate(userId, entities, message, conversationContext);
          break;
        case 'user_list':
        case 'user_search':
          response = await ChatController.handleUserList(userId, entities, message, conversationContext);
          break;
        case 'user_update':
          response = await ChatController.handleUserUpdate(userId, entities, message, conversationContext);
          break;
        case 'user_delete':
          response = await ChatController.handleUserDelete(userId, entities, message, conversationContext);
          break;
        case 'password_change':
          response = await ChatController.handlePasswordChange(userId, entities, message, conversationContext);
          break;

        // Leave Management
        case 'leave_request':
          response = await ChatController.handleLeaveRequest(userId, entities, conversation);
          break;
        case 'leave_balance':
          response = await ChatController.handleLeaveBalance(userId);
          break;
        case 'leave_list':
          response = await ChatController.handleLeaveList(userId, entities, message, conversationContext);
          break;
        case 'leave_approve':
        case 'leave_reject':
        case 'leave_status_update':
          response = await ChatController.handleLeaveStatusUpdate(userId, entities, message, conversationContext);
          break;
        case 'leave_suggestion':
          response = await ChatController.handleLeaveSuggestion(userId, entities);
          break;

        // Task Management
        case 'task_list':
        case 'task_search':
          response = await ChatController.handleTaskListEnhanced(userId, entities, message, conversationContext);
          break;
        case 'task_create':
          response = await ChatController.handleTaskCreate(userId, entities, message, conversationContext);
          break;
        case 'task_update':
          response = await ChatController.handleTaskUpdate(userId, entities, message);
          break;
        case 'task_delete':
          response = await ChatController.handleTaskDelete(userId, entities, message, conversationContext);
          break;

        // Job Management
        case 'job_create':
          response = await ChatController.handleJobCreate(userId, entities, message, conversationContext);
          break;
        case 'job_list':
        case 'job_search':
          response = await ChatController.handleJobList(userId, entities, message, conversationContext);
          break;
        case 'job_update':
          response = await ChatController.handleJobUpdate(userId, entities, message, conversationContext);
          break;
        case 'job_delete':
          response = await ChatController.handleJobDelete(userId, entities, message, conversationContext);
          break;

        // Application Management
        case 'application_list':
        case 'application_search':
          response = await ChatController.handleApplicationList(userId, entities, message, conversationContext);
          break;
        case 'application_approve':
        case 'application_reject':
        case 'application_status_update':
          response = await ChatController.handleApplicationStatusUpdate(userId, entities, message, conversationContext);
          break;
        case 'application_delete':
          response = await ChatController.handleApplicationDelete(userId, entities, message, conversationContext);
          break;

        // Attendance
        case 'attendance_checkin':
          response = await ChatController.handleAttendanceCheckin(userId);
          break;
        case 'attendance_checkout':
          response = await ChatController.handleAttendanceCheckout(userId);
          break;
        case 'attendance_view':
          response = await ChatController.handleAttendanceView(userId, entities, message, conversationContext);
          break;

        // Notifications
        case 'notification_list':
          response = await ChatController.handleNotificationList(userId, entities, message, conversationContext);
          break;
        case 'notification_read':
        case 'notification_delete':
          response = await ChatController.handleNotificationAction(userId, entities, message, conversationContext);
          break;

        // Search and Analytics
        case 'search_users':
          response = await ChatController.handleUserSearch(userId, entities, message, conversationContext);
          break;
        case 'analytics_view':
        case 'report_generate':
          response = await ChatController.handleAnalytics(userId, entities, message, conversationContext);
          break;

        // Policy and Information
        case 'policy_query':
          response = await ChatController.handlePolicyQuery(message);
          break;

        // Wellness and Emotional Support
        case 'wellness_support':
          response = await ChatController.handleWellnessSupport(userId, entities, message, conversationContext, classification);
          break;

        // Enhanced System Knowledge Handlers
        case 'system_overview':
          response = await ChatController.handleSystemOverview(conversationContext.userRole);
          break;
        case 'gek_explanation':
          response = await ChatController.handleGEKExplanation(conversationContext.userRole);
          break;
        case 'task_assignment_help':
          response = await ChatController.handleTaskAssignmentHelp(conversationContext.userRole);
          break;
        case 'recruitment_help':
          response = await ChatController.handleRecruitmentHelp(conversationContext.userRole);
          break;
        case 'nlp_explanation':
          response = await ChatController.handleNLPExplanation(conversationContext.userRole);
          break;
        case 'leave_workflow_help':
          response = await ChatController.handleLeaveWorkflowHelp(conversationContext.userRole);
          break;
        case 'evaluation_workflow_help':
          response = await ChatController.handleEvaluationWorkflowHelp(conversationContext.userRole);
          break;
        case 'notification_help':
          response = await ChatController.handleNotificationHelp(conversationContext.userRole);
          break;
        case 'analytics_help':
          response = await ChatController.handleAnalyticsHelp(conversationContext.userRole);
          break;
        case 'role_capabilities':
          response = await ChatController.handleRoleCapabilities(conversationContext.userRole);
          break;

        // Unclear or Unknown
        case 'unclear':
          response = await ChatController.handleUnclearIntent(classification.alternatives);
          break;

        default:
          response = await ChatController.handleUnknownIntent(message, intent);
      }

      // Store conversation turn in memory for rule-based responses
      conversationMemory.storeConversationTurn(userId, message, response.content, {
        emotion: primaryEmotion,
        intent: intent,
        confidence: confidence,
        topics: conversationContext.currentTopics,
        entities: entities
      });

      response.responseTime = Date.now() - startTime;
      return response;

    } catch (error) {
      console.error('Error generating response:', error);
      return {
        content: 'I apologize, but I encountered an error while processing your request. Please try again or contact support if the issue persists.',
        type: 'error',
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * Handle greeting intent with advanced personality and emotional intelligence
   */
  static async handleGreeting(userId, enhancedContext = null, primaryEmotion = null) {
    try {
      const user = await User.findById(userId).select('name');
      const userName = user ? user.name : null;

      // Use personality engine for personalized greeting
      const personalizedGreeting = personalityEngine.getPersonalizedGreeting(userId, userName);

      // If there's a strong emotional state, generate empathetic response
      if (primaryEmotion && ['sadness', 'anxiety', 'frustration', 'anger'].includes(primaryEmotion)) {
        const emotionalResponse = personalityEngine.generateEmpatheticResponse(
          primaryEmotion,
          enhancedContext?.correctedText || 'Hello',
          enhancedContext?.conversationMemory?.recentTurns || [],
          userId
        );

        return {
          content: `${personalizedGreeting}\n\n${emotionalResponse.content}`,
          type: 'text',
          suggestions: emotionalResponse.suggestions,
          tone: emotionalResponse.tone,
          emotionalSupport: true
        };
      }

      // Generate contextual suggestions based on user history and emotion
      let suggestions = conversationMemory.generateContextualSuggestions(
        userId,
        primaryEmotion,
        'greeting'
      );

      // Fallback suggestions if none generated
      if (suggestions.length === 0) {
        suggestions = [
          'Check my leave balance',
          'Show my tasks',
          'Request time off',
          'Check attendance',
          'Ask about HR policies'
        ];

        // Adapt suggestions based on emotion
        if (primaryEmotion === 'joy' || primaryEmotion === 'excitement') {
          suggestions = ['Celebrate achievements', 'Share good news', 'Plan vacation', 'Check my tasks'];
        } else if (primaryEmotion === 'sadness' || primaryEmotion === 'anxiety') {
          suggestions = ['Talk to someone', 'Check wellness resources', 'Request time off', 'Get support'];
        }
      }

      return {
        content: personalizedGreeting,
        type: 'text',
        suggestions: suggestions,
        tone: 'friendly',
        personalized: true
      };
    } catch (error) {
      console.error('Error in handleGreeting:', error);
      return {
        content: 'Greetings! I\'m Jarvis, your advanced AI-powered HR Intelligence System. I\'m operating at peak efficiency and ready to assist you with sophisticated analysis and support. How may I be of service today? 🤖',
        type: 'text',
        suggestions: [
          'Analyze my leave balance',
          'Show my task portfolio',
          'Request strategic time off',
          'Explain system capabilities'
        ]
      };
    }
  }

  /**
   * Handle wellness and emotional support
   */
  static async handleWellnessSupport(userId, entities, message, context, classification) {
    try {
      const user = await User.findById(userId).select('name');
      const userName = user ? user.name : '';

      let response = {
        content: '',
        type: 'text',
        suggestions: []
      };

      // Get emotional analysis from classification
      const emotionalAnalysis = classification.analysis?.emotional;
      const primaryEmotion = emotionalAnalysis?.emotions?.primaryEmotion;
      const empathyResponse = classification.empathyResponse;

      // Provide empathetic response based on emotional state
      if (empathyResponse && empathyResponse.supportive.length > 0) {
        response.content = empathyResponse.supportive[0] + '\n\n';
      } else {
        response.content = `${userName}, I can sense you might need some support right now. I'm here to help. 💙\n\n`;
      }

      // Provide specific support based on emotion
      switch (primaryEmotion) {
        case 'sadness':
          response.content += `🤗 **You're not alone.** It's completely normal to have difficult days. Here are some resources that might help:\n\n`;
          response.content += `• **Employee Assistance Program (EAP)**: Free confidential counseling\n`;
          response.content += `• **Mental Health Days**: You can request time off for mental health\n`;
          response.content += `• **Flexible Work Options**: We can discuss remote work or adjusted hours\n`;
          response.content += `• **Manager Support**: I can help you schedule a private conversation with your manager\n\n`;
          response.content += `Would you like me to help you access any of these resources?`;

          response.suggestions = [
            'Contact EAP counseling',
            'Request mental health day',
            'Talk to my manager',
            'Learn about flexible work'
          ];
          break;

        case 'anxiety':
          response.content += `🌱 **Take a deep breath.** Anxiety is manageable, and you have support. Here's what I can help with:\n\n`;
          response.content += `• **Stress Management Resources**: Techniques and tools for managing anxiety\n`;
          response.content += `• **Workload Assessment**: Let's review your current tasks and priorities\n`;
          response.content += `• **Time Management**: I can help you organize your schedule better\n`;
          response.content += `• **Professional Support**: Connect you with counseling services\n\n`;
          response.content += `What would be most helpful for you right now?`;

          response.suggestions = [
            'Get stress management tips',
            'Review my workload',
            'Organize my schedule',
            'Access counseling services'
          ];
          break;

        case 'anger':
        case 'frustration':
          response.content += `🤝 **Your feelings are valid.** Let's work together to address what's bothering you:\n\n`;
          response.content += `• **Conflict Resolution**: I can guide you through addressing workplace conflicts\n`;
          response.content += `• **HR Consultation**: Schedule a confidential meeting with HR\n`;
          response.content += `• **Policy Review**: Check if any policies or procedures need clarification\n`;
          response.content += `• **Mediation Services**: Professional mediation for workplace disputes\n\n`;
          response.content += `Would you like to discuss what's causing your frustration?`;

          response.suggestions = [
            'Report a workplace issue',
            'Schedule HR meeting',
            'Learn about conflict resolution',
            'Request mediation'
          ];
          break;

        case 'fatigue':
          response.content += `😌 **Rest is important.** Burnout is real, and taking care of yourself is a priority:\n\n`;
          response.content += `• **Work-Life Balance Assessment**: Review your current workload and hours\n`;
          response.content += `• **Time Off Options**: Explore vacation, personal days, or sabbatical options\n`;
          response.content += `• **Wellness Programs**: Access to fitness, mindfulness, and health resources\n`;
          response.content += `• **Schedule Adjustment**: Discuss flexible hours or reduced responsibilities\n\n`;
          response.content += `What would help you feel more balanced?`;

          response.suggestions = [
            'Check work-life balance',
            'Request time off',
            'Access wellness programs',
            'Discuss schedule changes'
          ];
          break;

        default:
          response.content += `🌟 **I'm here to support you.** Whether you're dealing with stress, workplace challenges, or just need someone to talk to, here are your options:\n\n`;
          response.content += `• **Confidential Support**: Employee Assistance Program with professional counselors\n`;
          response.content += `• **HR Consultation**: Private discussion about any workplace concerns\n`;
          response.content += `• **Wellness Resources**: Mental health, stress management, and work-life balance tools\n`;
          response.content += `• **Policy Information**: Understanding your rights and available support\n\n`;
          response.content += `What kind of support would be most helpful for you?`;

          response.suggestions = [
            'Talk to a counselor',
            'Schedule HR meeting',
            'Get wellness resources',
            'Learn about my rights'
          ];
      }

      // Add actionable items if available from empathy response
      if (empathyResponse && empathyResponse.actionable.length > 0) {
        response.content += `\n\n💡 **I can also help you:**\n${empathyResponse.actionable[0]}`;
      }

      return response;
    } catch (error) {
      return {
        content: 'I want to support you, but I encountered an error. Please reach out to HR directly or try again. Your wellbeing is important to us.',
        type: 'error',
        suggestions: ['Contact HR directly', 'Try again', 'Call employee assistance']
      };
    }
  }

  /**
   * Handle leave request intent
   */
  static async handleLeaveRequest(userId, entities, conversation) {
    try {
      // Extract leave details from entities
      const leaveType = entities.find(e => e.type === 'leave_type')?.value || 'vacation';
      const duration = entities.find(e => e.type === 'duration')?.value;
      const dateEntity = entities.find(e => e.type === 'date')?.value;

      // If we have all required information, analyze the request
      if (duration && dateEntity) {
        // Parse duration and date (simplified parsing)
        const days = parseInt(duration.match(/\d+/)?.[0]) || 1;
        const startDate = new Date(); // This would need proper date parsing
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + days - 1);

        const analysis = await leaveAnalyzer.analyzeLeaveRequest(userId, {
          startDate,
          endDate,
          leaveType
        });

        let content = `I've analyzed your ${leaveType} leave request for ${days} day(s).\n\n`;

        if (analysis.approval.recommended) {
          content += '✅ Your leave request looks good! ';
        } else {
          content += '⚠️ There are some concerns with your leave request:\n';
          analysis.conflicts.forEach(conflict => {
            content += `• ${conflict.message}\n`;
          });
        }

        if (analysis.alternatives.length > 0) {
          content += '\n📅 Alternative dates that might work better:\n';
          analysis.alternatives.slice(0, 2).forEach((alt, index) => {
            content += `${index + 1}. ${alt.startDate.toDateString()} - ${alt.endDate.toDateString()}\n`;
          });
        }

        return {
          content,
          type: 'text',
          suggestions: [
            'Submit this request',
            'Check alternative dates',
            'Modify request',
            'Check leave balance'
          ],
          contextUpdate: {
            state: 'confirming_action',
            formData: { leaveType, duration: days, analysis }
          }
        };
      } else {
        // Need more information
        return {
          content: `I'd be happy to help you request ${leaveType} leave! To get started, I need a few details:\n\n` +
                  `• When would you like to start your leave?\n` +
                  `• How many days do you need?\n` +
                  `• What's the reason for your leave?\n\n` +
                  `You can say something like: "I need 3 days off starting next Monday for personal reasons"`,
          type: 'form',
          contextUpdate: {
            state: 'collecting_info',
            currentIntent: 'leave_request',
            formData: { leaveType }
          }
        };
      }
    } catch (error) {
      return {
        content: 'I encountered an error while processing your leave request. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle leave balance inquiry
   */
  static async handleLeaveBalance(userId) {
    try {
      const currentYear = new Date().getFullYear();
      const yearStart = new Date(currentYear, 0, 1);
      const yearEnd = new Date(currentYear, 11, 31);

      // Get all leave requests for current year
      const leaveRequests = await LeaveRequest.find({
        userId,
        status: { $in: ['Approved', 'Pending'] },
        startDate: { $gte: yearStart, $lte: yearEnd }
      });

      // Calculate used days by leave type
      const leaveTypes = ['vacation', 'sick', 'personal'];
      const balances = {};

      leaveTypes.forEach(type => {
        const maxDays = { vacation: 25, sick: 10, personal: 5 }[type];
        const usedRequests = leaveRequests.filter(req => req.leaveType === type);
        const usedDays = usedRequests.reduce((total, req) => {
          const days = Math.ceil((new Date(req.endDate) - new Date(req.startDate)) / (1000 * 60 * 60 * 24)) + 1;
          return total + days;
        }, 0);

        balances[type] = {
          used: usedDays,
          remaining: maxDays - usedDays,
          total: maxDays
        };
      });

      let content = '📊 Your Leave Balance for ' + currentYear + ':\n\n';

      Object.entries(balances).forEach(([type, balance]) => {
        const percentage = (balance.remaining / balance.total) * 100;
        const emoji = percentage > 50 ? '🟢' : percentage > 20 ? '🟡' : '🔴';

        content += `${emoji} **${type.charAt(0).toUpperCase() + type.slice(1)}**: ${balance.remaining}/${balance.total} days remaining\n`;
      });

      content += '\n💡 Would you like me to suggest optimal times to use your remaining leave?';

      return {
        content,
        type: 'text',
        suggestions: [
          'Suggest optimal leave dates',
          'Request vacation leave',
          'View leave history',
          'Check team availability'
        ]
      };
    } catch (error) {
      return {
        content: 'I encountered an error while checking your leave balance. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle leave suggestion request
   */
  static async handleLeaveSuggestion(userId, entities) {
    try {
      const duration = entities.find(e => e.type === 'duration')?.value;
      const days = duration ? parseInt(duration.match(/\d+/)?.[0]) || 5 : 5;

      const suggestions = await leaveAnalyzer.getOptimalLeaveSuggestions(userId, {
        duration: days,
        leaveType: 'vacation',
        timeframe: 'next_3_months'
      });

      if (suggestions.length === 0) {
        return {
          content: 'I couldn\'t find any optimal leave periods in the next 3 months. This might be due to high workload or team commitments. Would you like me to check a longer timeframe?',
          type: 'text',
          suggestions: ['Check next 6 months', 'Check current workload', 'Request specific dates']
        };
      }

      let content = `🎯 Here are the best times for your ${days}-day leave:\n\n`;

      suggestions.slice(0, 3).forEach((suggestion, index) => {
        content += `${index + 1}. **${suggestion.startDate.toDateString()}** - ${suggestion.endDate.toDateString()}\n`;
        content += `   Confidence: ${Math.round(suggestion.confidence * 100)}%\n`;
        content += `   Benefits: ${suggestion.benefits.join(', ')}\n\n`;
      });

      content += 'Would you like me to help you request leave for any of these periods?';

      return {
        content,
        type: 'text',
        suggestions: [
          'Request option 1',
          'Request option 2',
          'See more options',
          'Check different duration'
        ]
      };
    } catch (error) {
      return {
        content: 'I encountered an error while generating leave suggestions. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle task list request
   */
  static async handleTaskList(userId) {
    try {
      const tasks = await Task.find({
        assignedTo: userId,
        status: { $in: ['Pending', 'In Progress'] }
      }).sort({ deadline: 1 }).limit(10);

      if (tasks.length === 0) {
        return {
          content: '🎉 Great news! You don\'t have any pending tasks at the moment.',
          type: 'text',
          suggestions: ['Check completed tasks', 'View team tasks', 'Create new task']
        };
      }

      let content = `📋 Your Current Tasks (${tasks.length}):\n\n`;

      tasks.forEach((task, index) => {
        const daysUntilDeadline = Math.ceil((new Date(task.deadline) - new Date()) / (1000 * 60 * 60 * 24));
        const urgencyEmoji = daysUntilDeadline <= 1 ? '🔴' : daysUntilDeadline <= 3 ? '🟡' : '🟢';

        content += `${urgencyEmoji} **${task.title}**\n`;
        content += `   Priority: ${task.priority} | Due: ${task.deadline.toDateString()}\n`;
        content += `   Status: ${task.status}\n\n`;
      });

      return {
        content,
        type: 'text',
        suggestions: [
          'Update task status',
          'Mark task complete',
          'Request deadline extension',
          'Get task details'
        ]
      };
    } catch (error) {
      return {
        content: 'I encountered an error while fetching your tasks. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle attendance check-in
   */
  static async handleAttendanceCheckin(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Check if already checked in today
      const existingAttendance = await Attendance.findOne({
        userId,
        date: today
      });

      if (existingAttendance && existingAttendance.checkInTime) {
        return {
          content: `You've already checked in today at ${existingAttendance.checkInTime.toLocaleTimeString()}. Have a productive day!`,
          type: 'text',
          suggestions: ['Check out', 'View attendance', 'Show my tasks']
        };
      }

      // Create or update attendance record
      const checkInTime = new Date();
      await Attendance.findOneAndUpdate(
        { userId, date: today },
        {
          userId,
          date: today,
          checkInTime,
          status: 'Present'
        },
        { upsert: true, new: true }
      );

      return {
        content: `✅ Successfully checked in at ${checkInTime.toLocaleTimeString()}. Welcome to work!`,
        type: 'text',
        suggestions: ['Show my tasks', 'Check schedule', 'View team status']
      };
    } catch (error) {
      return {
        content: 'I encountered an error while checking you in. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle attendance check-out
   */
  static async handleAttendanceCheckout(userId) {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Find today's attendance record
      const existingAttendance = await Attendance.findOne({
        userId,
        date: today
      });

      if (!existingAttendance || !existingAttendance.checkInTime) {
        return {
          content: `❌ You haven't checked in today yet. Please check in first before checking out.`,
          type: 'text',
          suggestions: ['Check in now', 'View attendance', 'Show my tasks']
        };
      }

      if (existingAttendance.checkOutTime) {
        return {
          content: `You've already checked out today at ${existingAttendance.checkOutTime.toLocaleTimeString()}. Have a great rest of your day!`,
          type: 'text',
          suggestions: ['View attendance', 'Show my tasks', 'Check tomorrow\'s schedule']
        };
      }

      // Update attendance record with check-out time
      const checkOutTime = new Date();
      const hoursWorked = (checkOutTime - existingAttendance.checkInTime) / (1000 * 60 * 60);

      await Attendance.findOneAndUpdate(
        { userId, date: today },
        {
          checkOutTime,
          hoursWorked: Math.round(hoursWorked * 100) / 100,
          status: 'Present'
        },
        { new: true }
      );

      return {
        content: `✅ Successfully checked out at ${checkOutTime.toLocaleTimeString()}.\n\n` +
                `⏱️ **Hours worked today**: ${Math.round(hoursWorked * 100) / 100} hours\n` +
                `🕐 **Check-in time**: ${existingAttendance.checkInTime.toLocaleTimeString()}\n\n` +
                `Great work today! Have a wonderful evening! 🌟`,
        type: 'text',
        suggestions: ['View attendance summary', 'Show my tasks', 'Plan tomorrow']
      };
    } catch (error) {
      return {
        content: 'I encountered an error while checking you out. Please try again.',
        type: 'error'
      };
    }
  }

  /**
   * Handle help request with comprehensive system knowledge
   */
  static async handleHelp(userRole = 'user') {
    try {
      // Get comprehensive system overview
      const systemOverview = systemKnowledge.getSystemOverview();
      const roleCapabilities = systemKnowledge.getCapabilitiesForRole(userRole);
      const smartSuggestions = systemKnowledge.getSmartSuggestions(userRole, {});

      let content = `🤖 **${systemOverview.name} - Your Advanced AI Intelligence System**\n\n`;
      content += `⚡ **Greetings! I'm Jarvis**, your sophisticated AI-powered HR Intelligence System with comprehensive analytical capabilities.\n\n`;

      // Role-specific capabilities
      content += `🎯 **Your Role**: ${roleCapabilities.title}\n`;
      content += `${roleCapabilities.description}\n\n`;

      content += `✨ **What I Can Help You With:**\n\n`;

      // Show role-specific capabilities
      roleCapabilities.capabilities.forEach((capability, index) => {
        content += `${index + 1}. ${capability}\n`;
      });

      content += `\n🏢 **Complete System Features:**\n\n`;

      // Show available modules based on role
      Object.entries(systemKnowledge.systemModules).forEach(([moduleKey, module]) => {
        if (module.permissions[userRole]) {
          const emoji = {
            'user_management': '👥',
            'leave_management': '📅',
            'task_management': '📋',
            'attendance_system': '⏰',
            'evaluation_system': '📊',
            'recruitment_system': '💼',
            'notification_system': '🔔',
            'analytics_system': '📈',
            'gek_system': '🧠'
          }[moduleKey] || '⚙️';

          content += `${emoji} **${module.description}**\n`;
          content += `   Permissions: ${module.permissions[userRole].join(', ')}\n\n`;
        }
      });

      content += `🎨 **Advanced AI Features:**\n\n`;
      systemOverview.keyCapabilities.forEach((capability, index) => {
        content += `• ${capability}\n`;
      });

      content += `\n💬 **Natural Language Interface:**\n`;
      content += `Just talk to me naturally! I understand:\n`;
      content += `• Casual language and typos\n`;
      content += `• Emotional context and provide support\n`;
      content += `• Complex requests across multiple systems\n`;
      content += `• Follow-up questions and references\n\n`;

      content += `📝 **Example Commands:**\n`;
      const examples = {
        'user': [
          '"What\'s my leave balance?"',
          '"Show me my tasks for this week"',
          '"I need to check in for work"',
          '"I\'m feeling overwhelmed with my workload"'
        ],
        'hr': [
          '"Create a new user account for Sarah Johnson"',
          '"Show me all pending leave requests"',
          '"Assign a high-priority task to the development team"',
          '"Generate an attendance report for last month"'
        ],
        'admin': [
          '"Show me system analytics for this quarter"',
          '"Who has been most active in the system?"',
          '"Generate a comprehensive user activity report"',
          '"Show me security audit logs"'
        ]
      };

      const roleExamples = examples[userRole] || examples['user'];
      roleExamples.forEach(example => {
        content += `• ${example}\n`;
      });

      return {
        content,
        type: 'text',
        suggestions: smartSuggestions,
        systemOverview: true,
        roleInfo: roleCapabilities
      };
    } catch (error) {
      console.error('Error in enhanced handleHelp:', error);

      // Fallback to basic help
      return {
        content: `🤖 **HR Assistant Help**\n\n` +
                `I'm your intelligent HR assistant. I can help you with:\n\n` +
                `• Leave management and requests\n` +
                `• Task assignment and tracking\n` +
                `• Attendance monitoring\n` +
                `• Employee evaluations\n` +
                `• Job postings and applications\n` +
                `• System analytics and reports\n\n` +
                `Just ask me anything in natural language!`,
        type: 'text',
        suggestions: [
          'What can you do?',
          'Show my tasks',
          'Check leave balance',
          'Help with attendance'
        ]
      };
    }
  }

  // ==================== USER MANAGEMENT HANDLERS ====================

  /**
   * Handle user creation
   */
  static async handleUserCreate(userId, entities, message, context) {
    try {
      // Extract user details from message
      const nameEntity = entities.find(e => e.type === 'person_name');
      const emailEntity = entities.find(e => e.type === 'email');
      const roleEntity = entities.find(e => e.type === 'role');

      if (!nameEntity || !emailEntity) {
        return {
          content: 'To create a new user, I need their name and email address. Please provide:\n\n' +
                  '• Full name\n' +
                  '• Email address\n' +
                  '• Role (admin, hr, user)\n' +
                  '• Job title\n' +
                  '• Department\n\n' +
                  'Example: "Create user John Doe <NAME_EMAIL> as HR manager in HR department"',
          type: 'form',
          contextUpdate: {
            state: 'collecting_info',
            currentIntent: 'user_create'
          }
        };
      }

      const result = await actionService.executeAction('create_user', {
        name: nameEntity.value,
        email: emailEntity.value,
        role: roleEntity?.value || 'user',
        password: 'TempPass123!', // Temporary password
        job: 'Employee',
        department: 'Other'
      }, context);

      return {
        content: `✅ ${result.message}\n\n` +
                `📧 **Email**: ${result.data.email}\n` +
                `👤 **Role**: ${result.data.role}\n` +
                `🏢 **Department**: ${result.data.department}\n\n` +
                `🔒 **Temporary Password**: TempPass123!\n` +
                `⚠️ The user should change their password on first login.`,
        type: 'success',
        suggestions: [
          'Create another user',
          'View all users',
          'Send welcome email'
        ]
      };
    } catch (error) {
      return {
        content: `❌ Error creating user: ${error.message}`,
        type: 'error',
        suggestions: ['Try again', 'View existing users']
      };
    }
  }

  /**
   * Handle user listing/searching
   */
  static async handleUserList(userId, entities, message, context) {
    try {
      const searchEntity = entities.find(e => e.type === 'search_term');
      const roleEntity = entities.find(e => e.type === 'role');

      const result = await actionService.executeAction('get_users', {
        search: searchEntity?.value,
        role: roleEntity?.value,
        limit: 10
      }, context);

      if (result.data.length === 0) {
        return {
          content: '📭 No users found matching your criteria.',
          type: 'text',
          suggestions: ['Show all users', 'Create new user']
        };
      }

      let content = `👥 **Found ${result.data.length} users:**\n\n`;
      result.data.forEach((user, index) => {
        content += `${index + 1}. **${user.name}**\n`;
        content += `   📧 ${user.email}\n`;
        content += `   👤 ${user.role} | 🏢 ${user.department}\n`;
        content += `   💼 ${user.job}\n\n`;
      });

      return {
        content,
        type: 'list',
        data: result.data,
        suggestions: [
          'Create new user',
          'Search users',
          'Update user info'
        ]
      };
    } catch (error) {
      return {
        content: `❌ Error retrieving users: ${error.message}`,
        type: 'error'
      };
    }
  }

  // ==================== LEAVE MANAGEMENT HANDLERS ====================

  /**
   * Handle leave request listing
   */
  static async handleLeaveList(userId, entities, message, context) {
    try {
      const statusEntity = entities.find(e => e.type === 'status');
      const searchEntity = entities.find(e => e.type === 'search_term');

      const result = await actionService.executeAction('get_leave_requests', {
        status: statusEntity?.value,
        search: searchEntity?.value,
        limit: 10
      }, context);

      if (result.data.length === 0) {
        return {
          content: '📭 No leave requests found.',
          type: 'text',
          suggestions: context.userRole === 'user' ?
            ['Request new leave', 'Check leave balance'] :
            ['View all requests', 'Pending approvals']
        };
      }

      let content = `📅 **Found ${result.data.length} leave requests:**\n\n`;
      result.data.forEach((leave, index) => {
        const startDate = new Date(leave.startDate).toDateString();
        const endDate = new Date(leave.endDate).toDateString();
        const statusEmoji = leave.status === 'Approved' ? '✅' :
                           leave.status === 'Rejected' ? '❌' : '⏳';

        content += `${index + 1}. **${leave.employeeName}** ${statusEmoji}\n`;
        content += `   📅 ${startDate} - ${endDate}\n`;
        content += `   🏷️ ${leave.leaveType}\n`;
        content += `   📝 ${leave.reason}\n\n`;
      });

      return {
        content,
        type: 'list',
        data: result.data,
        suggestions: context.userRole === 'user' ?
          ['Request new leave', 'Check leave balance'] :
          ['Approve requests', 'Reject requests', 'View details']
      };
    } catch (error) {
      return {
        content: `❌ Error retrieving leave requests: ${error.message}`,
        type: 'error'
      };
    }
  }

  // ==================== TASK MANAGEMENT HANDLERS ====================

  /**
   * Handle task creation
   */
  static async handleTaskCreate(userId, entities, message, context) {
    try {
      const titleEntity = entities.find(e => e.type === 'task_title');
      const assigneeEntity = entities.find(e => e.type === 'person_name');
      const priorityEntity = entities.find(e => e.type === 'priority');
      const deadlineEntity = entities.find(e => e.type === 'date');

      if (!titleEntity || !assigneeEntity) {
        return {
          content: 'To create a task, I need:\n\n' +
                  '• Task title/description\n' +
                  '• Assignee name or ID\n' +
                  '• Deadline (optional)\n' +
                  '• Priority (optional)\n\n' +
                  'Example: "Create task Review Q4 Reports for John Doe with high priority due next Friday"',
          type: 'form',
          contextUpdate: {
            state: 'collecting_info',
            currentIntent: 'task_create'
          }
        };
      }

      // Find assignee by name
      const users = await actionService.executeAction('get_users', {
        search: assigneeEntity.value
      }, context);

      if (users.data.length === 0) {
        return {
          content: `❌ No user found with name "${assigneeEntity.value}". Please check the name and try again.`,
          type: 'error',
          suggestions: ['Show all users', 'Try different name']
        };
      }

      const assignee = users.data[0];
      const deadline = deadlineEntity ? new Date(deadlineEntity.value) : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

      const result = await actionService.executeAction('create_task', {
        title: titleEntity.value,
        description: titleEntity.value,
        assignedTo: assignee._id,
        priority: priorityEntity?.value || 'Medium',
        deadline: deadline.toISOString(),
        category: 'General'
      }, context);

      return {
        content: `✅ ${result.message}\n\n` +
                `📋 **Task**: ${result.data.title}\n` +
                `👤 **Assigned to**: ${result.data.assignedTo.name}\n` +
                `⚡ **Priority**: ${result.data.priority}\n` +
                `📅 **Deadline**: ${new Date(result.data.deadline).toDateString()}\n\n` +
                `The assignee will be notified automatically.`,
        type: 'success',
        suggestions: [
          'Create another task',
          'View all tasks',
          'Assign to different person'
        ]
      };
    } catch (error) {
      return {
        content: `❌ Error creating task: ${error.message}`,
        type: 'error'
      };
    }
  }

  // ==================== ATTENDANCE HANDLERS ====================

  /**
   * Handle attendance viewing
   */
  static async handleAttendanceView(userId, entities, message, context) {
    try {
      const dateEntity = entities.find(e => e.type === 'date');
      const userEntity = entities.find(e => e.type === 'person_name');

      const result = await actionService.executeAction('get_attendance', {
        startDate: dateEntity?.value,
        limit: 10
      }, context);

      if (result.data.length === 0) {
        return {
          content: '📭 No attendance records found for the specified period.',
          type: 'text',
          suggestions: ['Check in now', 'View different period']
        };
      }

      let content = `⏰ **Attendance Records (${result.data.length}):**\n\n`;
      result.data.forEach((record, index) => {
        const date = new Date(record.date).toDateString();
        const checkIn = record.checkInTime ? new Date(record.checkInTime).toLocaleTimeString() : 'Not checked in';
        const checkOut = record.checkOutTime ? new Date(record.checkOutTime).toLocaleTimeString() : 'Not checked out';
        const hours = record.hoursWorked || 0;

        content += `${index + 1}. **${date}**\n`;
        if (record.userId?.name) content += `   👤 ${record.userId.name}\n`;
        content += `   🕐 In: ${checkIn}\n`;
        content += `   🕕 Out: ${checkOut}\n`;
        content += `   ⏱️ Hours: ${hours}\n\n`;
      });

      return {
        content,
        type: 'list',
        data: result.data,
        suggestions: [
          'Check in now',
          'Check out now',
          'View different period'
        ]
      };
    } catch (error) {
      return {
        content: `❌ Error retrieving attendance: ${error.message}`,
        type: 'error'
      };
    }
  }

  // ==================== NOTIFICATION HANDLERS ====================

  /**
   * Handle notification listing
   */
  static async handleNotificationList(userId, entities, message, context) {
    try {
      const result = await actionService.executeAction('get_notifications', {}, context);

      if (result.data.length === 0) {
        return {
          content: '📭 You have no notifications.',
          type: 'text',
          suggestions: ['Check tasks', 'View leave requests']
        };
      }

      let content = `🔔 **Your Notifications (${result.data.length}):**\n\n`;
      result.data.slice(0, 5).forEach((notification, index) => {
        const date = new Date(notification.createdAt).toLocaleDateString();
        const typeEmoji = {
          'TASK_ASSIGNED': '📋',
          'LEAVE_REQUEST_APPROVED': '✅',
          'LEAVE_REQUEST_REJECTED': '❌',
          'NEW_APPLICATION': '📄'
        }[notification.type] || '🔔';

        content += `${index + 1}. ${typeEmoji} **${notification.title}**\n`;
        content += `   📅 ${date}\n`;
        content += `   📝 ${notification.content.substring(0, 100)}...\n\n`;
      });

      return {
        content,
        type: 'list',
        data: result.data,
        suggestions: [
          'Mark all as read',
          'Delete old notifications',
          'View details'
        ]
      };
    } catch (error) {
      return {
        content: `❌ Error retrieving notifications: ${error.message}`,
        type: 'error'
      };
    }
  }

  // ==================== PLACEHOLDER HANDLERS ====================
  // These handlers will be implemented as needed

  static async handleUserUpdate(userId, entities, message, context) {
    return { content: 'User update functionality coming soon!', type: 'text' };
  }

  static async handleUserDelete(userId, entities, message, context) {
    return { content: 'User deletion functionality coming soon!', type: 'text' };
  }

  static async handlePasswordChange(userId, entities, message, context) {
    return { content: 'Password change functionality coming soon!', type: 'text' };
  }

  static async handleLeaveStatusUpdate(userId, entities, message, context) {
    return { content: 'Leave status update functionality coming soon!', type: 'text' };
  }

  static async handleTaskListEnhanced(userId, entities, message, context) {
    try {
      const statusEntity = entities.find(e => e.type === 'status');
      const searchEntity = entities.find(e => e.type === 'search_term');

      const result = await actionService.executeAction('get_tasks', {
        status: statusEntity?.value,
        search: searchEntity?.value,
        limit: 10
      }, context);

      if (result.data.length === 0) {
        return {
          content: '📭 No tasks found matching your criteria.',
          type: 'text',
          suggestions: context.userRole === 'user' ?
            ['Check my tasks', 'Update task status'] :
            ['Create new task', 'Assign tasks']
        };
      }

      let content = `📋 **Found ${result.data.length} tasks:**\n\n`;
      result.data.forEach((task, index) => {
        const deadline = new Date(task.deadline).toDateString();
        const statusEmoji = task.status === 'Completed' ? '✅' :
                           task.status === 'In Progress' ? '🔄' :
                           task.status === 'On Hold' ? '⏸️' : '⏳';

        content += `${index + 1}. **${task.title}** ${statusEmoji}\n`;
        content += `   👤 ${task.assignedTo?.name || 'Unassigned'}\n`;
        content += `   📅 Due: ${deadline}\n`;
        content += `   ⚡ Priority: ${task.priority}\n\n`;
      });

      return {
        content,
        type: 'list',
        data: result.data,
        suggestions: context.userRole === 'user' ?
          ['Update task status', 'Mark as complete'] :
          ['Create new task', 'Assign tasks', 'Update priorities']
      };
    } catch (error) {
      return {
        content: `❌ Error retrieving tasks: ${error.message}`,
        type: 'error'
      };
    }
  }

  static async handleTaskDelete(userId, entities, message, context) {
    return { content: 'Task deletion functionality coming soon!', type: 'text' };
  }

  static async handleJobCreate(userId, entities, message, context) {
    return { content: 'Job creation functionality coming soon!', type: 'text' };
  }

  static async handleJobList(userId, entities, message, context) {
    return { content: 'Job listing functionality coming soon!', type: 'text' };
  }

  static async handleJobUpdate(userId, entities, message, context) {
    return { content: 'Job update functionality coming soon!', type: 'text' };
  }

  static async handleJobDelete(userId, entities, message, context) {
    return { content: 'Job deletion functionality coming soon!', type: 'text' };
  }

  static async handleApplicationList(userId, entities, message, context) {
    return { content: 'Application listing functionality coming soon!', type: 'text' };
  }

  static async handleApplicationStatusUpdate(userId, entities, message, context) {
    return { content: 'Application status update functionality coming soon!', type: 'text' };
  }

  static async handleApplicationDelete(userId, entities, message, context) {
    return { content: 'Application deletion functionality coming soon!', type: 'text' };
  }

  static async handleNotificationAction(userId, entities, message, context) {
    return { content: 'Notification actions functionality coming soon!', type: 'text' };
  }

  static async handleUserSearch(userId, entities, message, context) {
    return await ChatController.handleUserList(userId, entities, message, context);
  }

  static async handleAnalytics(userId, entities, message, context) {
    return { content: 'Analytics functionality coming soon!', type: 'text' };
  }

  /**
   * Handle unclear intent
   */
  static async handleUnclearIntent(alternatives) {
    let content = 'I\'m not quite sure what you\'re asking for. ';

    if (alternatives && alternatives.length > 0) {
      content += 'Did you mean:\n\n';
      alternatives.forEach((alt, index) => {
        content += `${index + 1}. ${intentClassifier.getIntentDescription(alt.label)}\n`;
      });
    } else {
      content += 'Could you please rephrase your question or try one of these common requests?';
    }

    return {
      content,
      type: 'text',
      suggestions: [
        'Check leave balance',
        'Show my tasks',
        'Request time off',
        'Get help'
      ]
    };
  }

  /**
   * Handle capabilities request with comprehensive system knowledge
   */
  static async handleCapabilities(userRole = 'user') {
    try {
      const systemOverview = systemKnowledge.getSystemOverview();
      const roleCapabilities = systemKnowledge.getCapabilitiesForRole(userRole);
      const smartSuggestions = systemKnowledge.getSmartSuggestions(userRole, {});

      let content = `🏢 **${systemOverview.name}**\n\n`;
      content += `🤖 **I'm Alex**, your intelligent AI assistant with comprehensive knowledge of all HR systems.\n\n`;

      content += `🎯 **System Overview:**\n`;
      content += `• **${systemOverview.totalFeatures}+ Features** across **${systemOverview.modules.length} Modules**\n`;
      content += `• **Advanced AI** with emotional intelligence\n`;
      content += `• **Role-based access** for ${Object.keys(systemKnowledge.userRoles).join(', ')}\n`;
      content += `• **Natural language** understanding\n\n`;

      content += `🧠 **Core Capabilities:**\n\n`;
      systemOverview.keyCapabilities.forEach((capability, index) => {
        content += `${index + 1}. ${capability}\n`;
      });

      content += `\n📋 **Available Modules:**\n\n`;
      Object.entries(systemKnowledge.systemModules).forEach(([moduleKey, module]) => {
        const emoji = {
          'user_management': '👥',
          'leave_management': '📅',
          'task_management': '📋',
          'attendance_system': '⏰',
          'evaluation_system': '📊',
          'recruitment_system': '💼',
          'notification_system': '🔔',
          'analytics_system': '📈',
          'gek_system': '🧠'
        }[moduleKey] || '⚙️';

        content += `${emoji} **${module.description}**\n`;
        if (module.permissions[userRole]) {
          content += `   Your access: ${module.permissions[userRole].join(', ')}\n`;
        } else {
          content += `   Access: Limited (contact admin for permissions)\n`;
        }
        content += `\n`;
      });

      content += `💬 **How to Use:**\n`;
      content += `Just talk to me naturally! I understand:\n`;
      content += `• Casual language and typos\n`;
      content += `• Emotional context and provide support\n`;
      content += `• Complex multi-step requests\n`;
      content += `• Follow-up questions and references\n\n`;

      content += `🌟 **Try asking me:**\n`;
      smartSuggestions.slice(0, 4).forEach(suggestion => {
        content += `• "${suggestion}"\n`;
      });

      return {
        content,
        type: 'text',
        suggestions: smartSuggestions,
        systemOverview: true,
        comprehensive: true
      };
    } catch (error) {
      console.error('Error in handleCapabilities:', error);
      return {
        content: `🤖 **HR System Capabilities**\n\nI'm your intelligent HR assistant with access to:\n\n• Complete employee management\n• Leave and attendance tracking\n• Task assignment and monitoring\n• Performance evaluations\n• Job postings and applications\n• Analytics and reporting\n• Emotional support and wellness\n\nJust ask me anything in natural language!`,
        type: 'text',
        suggestions: [
          'Show my tasks',
          'Check leave balance',
          'View attendance',
          'Get help'
        ]
      };
    }
  }

  /**
   * Handle gratitude with warm response
   */
  static async handleGratitude(userId, enhancedContext, primaryEmotion) {
    try {
      const user = await User.findById(userId).select('name');
      const userName = user ? user.name : '';

      const gratitudeResponses = [
        `You're most welcome, ${userName}! 🤖 It's my primary directive to ensure your success and efficiency.`,
        `The pleasure is entirely mine, ${userName}! Optimal service delivery is what I'm designed for. ⚡`,
        `Your appreciation is most gratifying, ${userName}! My analytical circuits find great satisfaction in successful assistance. 💙`,
        `Excellent! I'm delighted to have provided valuable service, ${userName}. This is precisely what I was engineered for. 🌟`,
        `Thank you for the positive feedback, ${userName}! Such acknowledgment enhances my performance optimization algorithms. ✨`
      ];

      const response = gratitudeResponses[Math.floor(Math.random() * gratitudeResponses.length)];

      return {
        content: response,
        type: 'text',
        suggestions: [
          'What else can you help me with?',
          'Show me my tasks',
          'Check my leave balance',
          'Tell me about system features'
        ],
        tone: 'warm',
        emotional: true
      };
    } catch (error) {
      return {
        content: `You're very welcome! I'm always here to help. 😊`,
        type: 'text',
        suggestions: [
          'What else can you help me with?',
          'Show me system features',
          'Get help',
          'Ask me anything'
        ]
      };
    }
  }

  /**
   * Handle casual conversation
   */
  static async handleCasualConversation(userId, message, enhancedContext, primaryEmotion) {
    try {
      const user = await User.findById(userId).select('name');
      const userName = user ? user.name : '';

      // Use personality engine for natural responses
      const personalityResponse = personalityEngine.generateCasualResponse(message, userName, primaryEmotion);

      return {
        content: personalityResponse.content,
        type: 'casual_conversation',
        suggestions: personalityResponse.suggestions || [
          'Tell me about your day',
          'What can I help you with?',
          'How are things at work?',
          'Show me my tasks'
        ],
        tone: 'casual',
        conversational: true
      };
    } catch (error) {
      // Fallback casual responses
      const casualResponses = [
        "I'm doing well! What about you - how are things going?",
        "Going well! Always excited to help out. What's going on with you today?",
        "Pretty good! Just here ready to assist with whatever you need. How's your day?",
        "I'm great, thanks for asking! How can I make your day better?"
      ];

      return {
        content: casualResponses[Math.floor(Math.random() * casualResponses.length)],
        type: 'casual_conversation',
        suggestions: [
          'Tell me about your day',
          'What can I help you with?',
          'How are things at work?',
          'Show me my tasks'
        ],
        tone: 'casual'
      };
    }
  }

  /**
   * Handle farewell
   */
  static async handleFarewell(userId, enhancedContext) {
    try {
      const user = await User.findById(userId).select('name');
      const userName = user ? user.name : '';

      const farewellResponses = [
        `Goodbye, ${userName}! Have a wonderful day! 🌟`,
        `Take care, ${userName}! I'm here whenever you need me. 👋`,
        `See you later, ${userName}! Hope the rest of your day goes great! ✨`,
        `Bye for now, ${userName}! Don't hesitate to reach out if you need anything. 😊`,
        `Have a great day, ${userName}! I'll be here when you get back. 🤗`
      ];

      const response = farewellResponses[Math.floor(Math.random() * farewellResponses.length)];

      return {
        content: response,
        type: 'text',
        suggestions: [],
        tone: 'warm',
        farewell: true
      };
    } catch (error) {
      return {
        content: `Goodbye! Have a wonderful day! 👋`,
        type: 'text',
        suggestions: [],
        tone: 'warm'
      };
    }
  }

  /**
   * Handle unknown intent with system knowledge
   */
  static async handleUnknownIntent(message, intent) {
    try {
      // Use system knowledge to provide intelligent suggestions
      const relevantModules = systemKnowledge.identifyRelevantModules(message);

      let content = `I'm still learning and don't fully understand "${message}" yet. `;

      if (relevantModules.length > 0) {
        const topModule = relevantModules[0];
        content += `However, I noticed you might be asking about **${topModule.info.description}**.\n\n`;

        content += `Here's what I can help you with in this area:\n\n`;
        topModule.info.features.slice(0, 4).forEach((feature, index) => {
          content += `• ${feature}\n`;
        });

        content += `\nTry asking something like:\n`;
        const suggestions = systemKnowledge.generateSystemAwareSuggestions(topModule);
        suggestions.slice(0, 3).forEach(suggestion => {
          content += `• ${suggestion}\n`;
        });
      } else {
        content += `Let me help you explore what I can do!\n\n`;

        const systemOverview = systemKnowledge.getSystemOverview();
        content += `I'm your intelligent HR assistant with these capabilities:\n\n`;
        systemOverview.keyCapabilities.slice(0, 4).forEach(capability => {
          content += `• ${capability}\n`;
        });

        content += `\nYou can ask me about:\n`;
        content += `• Leave requests and balance\n`;
        content += `• Task assignments and deadlines\n`;
        content += `• Attendance tracking\n`;
        content += `• Employee evaluations\n`;
        content += `• Job applications and postings\n`;
        content += `• System analytics and reports\n`;
      }

      content += `\n💡 **Tip**: I understand natural language, so just tell me what you need!`;

      return {
        content,
        type: 'text',
        suggestions: relevantModules.length > 0
          ? systemKnowledge.generateSystemAwareSuggestions(relevantModules[0]).slice(0, 4)
          : [
              'What can you help me with?',
              'Show my leave balance',
              'List my tasks',
              'Help me check in'
            ],
        systemAware: true,
        relevantModules: relevantModules.slice(0, 2)
      };
    } catch (error) {
      console.error('Error in enhanced handleUnknownIntent:', error);

      // Fallback to basic response
      return {
        content: `I'm still learning and don't understand "${message}" yet. Could you try rephrasing or ask me about leave, tasks, attendance, or HR policies?`,
        type: 'text',
        suggestions: [
          'Check leave balance',
          'Show my tasks',
          'Ask about policies',
          'Get help'
        ]
      };
    }
  }
}

// Routes
router.post('/message', authenticate, ChatController.sendMessage);

module.exports = router;
