/**
 * Context Detection Service for Jarvis
 * Monitors user actions and provides contextual assistance
 */

class ContextDetectionService {
  constructor() {
    this.observers = new Map();
    this.contextHistory = [];
    this.currentContext = null;
    this.isActive = true;
    this.callbacks = new Set();
    
    this.initializeContextDetection();
  }

  /**
   * Initialize context detection system
   */
  initializeContextDetection() {
    // Monitor form interactions
    this.observeFormInteractions();
    
    // Monitor navigation changes
    this.observeNavigationChanges();
    
    // Monitor dialog/modal openings
    this.observeDialogChanges();
    
    // Monitor input focus events
    this.observeInputFocus();
    
    // Monitor button clicks
    this.observeButtonClicks();
    
    console.log('🔍 Jarvis Context Detection System initialized');
  }

  /**
   * Register callback for context changes
   */
  onContextChange(callback) {
    this.callbacks.add(callback);
    return () => this.callbacks.delete(callback);
  }

  /**
   * Notify all callbacks of context change
   */
  notifyContextChange(context) {
    this.callbacks.forEach(callback => {
      try {
        callback(context);
      } catch (error) {
        console.error('Error in context change callback:', error);
      }
    });
  }

  /**
   * Observe form interactions
   */
  observeFormInteractions() {
    const formObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check for form elements
              const forms = node.querySelectorAll ? node.querySelectorAll('form') : [];
              if (node.tagName === 'FORM') forms.push(node);
              
              forms.forEach(form => this.analyzeForm(form));
              
              // Check for dialog content
              if (node.querySelector && node.querySelector('[role="dialog"]')) {
                this.analyzeDialog(node);
              }
            }
          });
        }
      });
    });

    formObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.observers.set('forms', formObserver);
  }

  /**
   * Observe navigation changes
   */
  observeNavigationChanges() {
    // Listen for route changes
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = (...args) => {
      originalPushState.apply(history, args);
      this.handleNavigationChange();
    };

    history.replaceState = (...args) => {
      originalReplaceState.apply(history, args);
      this.handleNavigationChange();
    };

    window.addEventListener('popstate', () => {
      this.handleNavigationChange();
    });
  }

  /**
   * Observe dialog/modal changes
   */
  observeDialogChanges() {
    const dialogObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'open') {
          const dialog = mutation.target;
          if (dialog.tagName === 'DIALOG' || dialog.getAttribute('role') === 'dialog') {
            if (dialog.open || dialog.getAttribute('aria-hidden') === 'false') {
              this.analyzeDialog(dialog);
            }
          }
        }
      });
    });

    dialogObserver.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: ['open', 'aria-hidden', 'style']
    });

    this.observers.set('dialogs', dialogObserver);
  }

  /**
   * Observe input focus events
   */
  observeInputFocus() {
    document.addEventListener('focusin', (event) => {
      const element = event.target;
      if (this.isFormElement(element)) {
        this.analyzeInputFocus(element);
      }
    });
  }

  /**
   * Observe button clicks
   */
  observeButtonClicks() {
    document.addEventListener('click', (event) => {
      const element = event.target.closest('button, [role="button"]');
      if (element) {
        this.analyzeButtonClick(element);
      }
    });
  }

  /**
   * Analyze form for context
   */
  analyzeForm(form) {
    const formContext = this.extractFormContext(form);
    if (formContext) {
      this.updateContext(formContext);
    }
  }

  /**
   * Analyze dialog for context
   */
  analyzeDialog(dialog) {
    const dialogContext = this.extractDialogContext(dialog);
    if (dialogContext) {
      this.updateContext(dialogContext);
    }
  }

  /**
   * Analyze input focus for context
   */
  analyzeInputFocus(input) {
    const inputContext = this.extractInputContext(input);
    if (inputContext) {
      this.updateContext(inputContext);
    }
  }

  /**
   * Analyze button click for context
   */
  analyzeButtonClick(button) {
    const buttonContext = this.extractButtonContext(button);
    if (buttonContext) {
      this.updateContext(buttonContext);
    }
  }

  /**
   * Extract context from form
   */
  extractFormContext(form) {
    const formId = form.id || form.getAttribute('data-form-type');
    const inputs = form.querySelectorAll('input, select, textarea');
    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
    
    // Analyze form purpose based on inputs and labels
    const inputTypes = Array.from(inputs).map(input => ({
      type: input.type || input.tagName.toLowerCase(),
      name: input.name,
      label: this.getInputLabel(input),
      placeholder: input.placeholder
    }));

    let contextType = 'form_interaction';
    let action = 'form_filling';
    let suggestions = [];

    // Detect specific form types
    if (this.isLeaveRequestForm(inputTypes, form)) {
      contextType = 'leave_request';
      action = 'creating_leave_request';
      suggestions = this.getLeaveRequestSuggestions();
    } else if (this.isUserManagementForm(inputTypes, form)) {
      contextType = 'user_management';
      action = 'managing_user';
      suggestions = this.getUserManagementSuggestions();
    } else if (this.isTaskForm(inputTypes, form)) {
      contextType = 'task_management';
      action = 'creating_task';
      suggestions = this.getTaskManagementSuggestions();
    } else if (this.isApplicationForm(inputTypes, form)) {
      contextType = 'job_application';
      action = 'applying_for_job';
      suggestions = this.getApplicationSuggestions();
    }

    return {
      type: contextType,
      action,
      element: form,
      data: {
        formId,
        inputCount: inputs.length,
        hasSubmitButton: !!submitButton,
        inputTypes
      },
      suggestions,
      priority: this.calculatePriority(contextType),
      timestamp: Date.now()
    };
  }

  /**
   * Extract context from dialog
   */
  extractDialogContext(dialog) {
    const title = dialog.querySelector('[role="heading"], h1, h2, h3, .MuiDialogTitle-root')?.textContent;
    const content = dialog.querySelector('.MuiDialogContent-root, [role="document"]');
    
    let contextType = 'dialog_interaction';
    let action = 'viewing_dialog';
    let suggestions = [];

    if (title) {
      const titleLower = title.toLowerCase();
      
      if (titleLower.includes('leave') || titleLower.includes('request leave')) {
        contextType = 'leave_request_dialog';
        action = 'opening_leave_dialog';
        suggestions = this.getLeaveRequestSuggestions();
      } else if (titleLower.includes('user') || titleLower.includes('add') || titleLower.includes('edit')) {
        contextType = 'user_management_dialog';
        action = 'opening_user_dialog';
        suggestions = this.getUserManagementSuggestions();
      } else if (titleLower.includes('task')) {
        contextType = 'task_dialog';
        action = 'opening_task_dialog';
        suggestions = this.getTaskManagementSuggestions();
      }
    }

    return {
      type: contextType,
      action,
      element: dialog,
      data: {
        title,
        hasContent: !!content
      },
      suggestions,
      priority: this.calculatePriority(contextType),
      timestamp: Date.now()
    };
  }

  /**
   * Extract context from input focus
   */
  extractInputContext(input) {
    const inputName = input.name || input.id;
    const inputType = input.type;
    const label = this.getInputLabel(input);
    const form = input.closest('form');

    let contextType = 'input_focus';
    let action = 'focusing_input';
    let suggestions = [];

    // Provide specific suggestions based on input type
    if (inputName && inputName.includes('email')) {
      suggestions = ['Use your work email address', 'Ensure email format is correct'];
    } else if (inputType === 'date' || inputName.includes('date')) {
      suggestions = ['Select appropriate dates', 'Check for conflicts with existing schedules'];
    } else if (inputName.includes('reason') || inputName.includes('description')) {
      suggestions = ['Be specific and clear', 'Provide necessary details'];
    }

    return {
      type: contextType,
      action,
      element: input,
      data: {
        inputName,
        inputType,
        label,
        hasForm: !!form
      },
      suggestions,
      priority: 1,
      timestamp: Date.now()
    };
  }

  /**
   * Extract context from button click
   */
  extractButtonContext(button) {
    const buttonText = button.textContent || button.getAttribute('aria-label');
    const buttonType = button.type;
    
    let contextType = 'button_click';
    let action = 'clicking_button';
    let suggestions = [];

    if (buttonText) {
      const textLower = buttonText.toLowerCase();
      
      if (textLower.includes('submit') || textLower.includes('save')) {
        contextType = 'form_submission';
        action = 'submitting_form';
        suggestions = ['Review all fields before submitting', 'Ensure required information is complete'];
      } else if (textLower.includes('add') || textLower.includes('create')) {
        contextType = 'creation_action';
        action = 'creating_item';
        suggestions = ['Fill out all required fields', 'Double-check information accuracy'];
      }
    }

    return {
      type: contextType,
      action,
      element: button,
      data: {
        buttonText,
        buttonType
      },
      suggestions,
      priority: 2,
      timestamp: Date.now()
    };
  }

  /**
   * Handle navigation changes
   */
  handleNavigationChange() {
    const path = window.location.pathname;
    const contextType = this.getContextFromPath(path);
    
    if (contextType) {
      this.updateContext({
        type: 'navigation',
        action: 'page_navigation',
        data: { path, contextType },
        suggestions: this.getNavigationSuggestions(contextType),
        priority: 3,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Update current context
   */
  updateContext(newContext) {
    if (!this.isActive) return;

    // Add to history
    this.contextHistory.push(newContext);
    
    // Keep only last 10 contexts
    if (this.contextHistory.length > 10) {
      this.contextHistory = this.contextHistory.slice(-10);
    }

    // Update current context
    this.currentContext = newContext;
    
    // Notify callbacks
    this.notifyContextChange(newContext);
  }

  /**
   * Helper methods for form detection
   */
  isLeaveRequestForm(inputTypes, form) {
    const hasLeaveType = inputTypes.some(input => 
      input.name?.includes('leave') || input.label?.toLowerCase().includes('leave')
    );
    const hasDateInputs = inputTypes.filter(input => input.type === 'date').length >= 2;
    const hasReason = inputTypes.some(input => 
      input.name?.includes('reason') || input.label?.toLowerCase().includes('reason')
    );
    
    return hasLeaveType || (hasDateInputs && hasReason);
  }

  isUserManagementForm(inputTypes, form) {
    const hasEmail = inputTypes.some(input => input.type === 'email');
    const hasName = inputTypes.some(input => 
      input.name?.includes('name') || input.label?.toLowerCase().includes('name')
    );
    const hasRole = inputTypes.some(input => 
      input.name?.includes('role') || input.label?.toLowerCase().includes('role')
    );
    
    return hasEmail && hasName && (hasRole || form.textContent?.includes('user'));
  }

  isTaskForm(inputTypes, form) {
    const hasTitle = inputTypes.some(input => 
      input.name?.includes('title') || input.label?.toLowerCase().includes('title')
    );
    const hasDescription = inputTypes.some(input => 
      input.name?.includes('description') || input.label?.toLowerCase().includes('description')
    );
    const hasPriority = inputTypes.some(input => 
      input.name?.includes('priority') || input.label?.toLowerCase().includes('priority')
    );
    
    return hasTitle && (hasDescription || hasPriority);
  }

  isApplicationForm(inputTypes, form) {
    const hasCV = inputTypes.some(input => 
      input.type === 'file' && (input.name?.includes('cv') || input.name?.includes('resume'))
    );
    const hasPersonalInfo = inputTypes.some(input => input.type === 'email') &&
                           inputTypes.some(input => input.type === 'tel');
    
    return hasCV || (hasPersonalInfo && form.textContent?.includes('application'));
  }

  /**
   * Get suggestions for different contexts
   */
  getLeaveRequestSuggestions() {
    return [
      'Check your leave balance before requesting',
      'Ensure dates don\'t conflict with important deadlines',
      'Provide clear reason for leave request',
      'Submit request at least 2 weeks in advance'
    ];
  }

  getUserManagementSuggestions() {
    return [
      'Use work email addresses for new users',
      'Assign appropriate roles based on responsibilities',
      'Ensure all required fields are completed',
      'Verify user information before saving'
    ];
  }

  getTaskManagementSuggestions() {
    return [
      'Set realistic deadlines for tasks',
      'Provide clear and detailed descriptions',
      'Assign appropriate priority levels',
      'Consider employee workload when assigning'
    ];
  }

  getApplicationSuggestions() {
    return [
      'Upload CV in PDF format for best compatibility',
      'Fill out all required personal information',
      'Review job requirements before applying',
      'Double-check contact information'
    ];
  }

  getNavigationSuggestions(contextType) {
    const suggestions = {
      'dashboard': ['Check your recent activities', 'Review pending tasks'],
      'leave': ['View your leave balance', 'Check leave history'],
      'users': ['Search for specific users', 'Use filters to narrow results'],
      'tasks': ['Review task priorities', 'Check upcoming deadlines'],
      'reports': ['Generate relevant reports', 'Export data for analysis']
    };
    
    return suggestions[contextType] || ['Explore available features', 'Use the search function'];
  }

  /**
   * Utility methods
   */
  isFormElement(element) {
    return ['INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName);
  }

  getInputLabel(input) {
    const label = input.labels?.[0]?.textContent ||
                  document.querySelector(`label[for="${input.id}"]`)?.textContent ||
                  input.getAttribute('aria-label') ||
                  input.getAttribute('placeholder');
    return label?.trim();
  }

  getContextFromPath(path) {
    if (path.includes('dashboard')) return 'dashboard';
    if (path.includes('leave')) return 'leave';
    if (path.includes('user')) return 'users';
    if (path.includes('task')) return 'tasks';
    if (path.includes('report')) return 'reports';
    if (path.includes('apply')) return 'application';
    return null;
  }

  calculatePriority(contextType) {
    const priorities = {
      'leave_request': 5,
      'user_management': 4,
      'task_management': 4,
      'job_application': 3,
      'form_submission': 5,
      'creation_action': 4,
      'dialog_interaction': 3,
      'input_focus': 1,
      'navigation': 2
    };
    
    return priorities[contextType] || 1;
  }

  /**
   * Control methods
   */
  activate() {
    this.isActive = true;
  }

  deactivate() {
    this.isActive = false;
  }

  getCurrentContext() {
    return this.currentContext;
  }

  getContextHistory() {
    return [...this.contextHistory];
  }

  clearHistory() {
    this.contextHistory = [];
    this.currentContext = null;
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.callbacks.clear();
  }
}

// Create singleton instance
const contextDetectionService = new ContextDetectionService();

export default contextDetectionService;
