/**
 * Advanced Personality Engine for Human-Like Conversational AI
 * Creates a warm, empathetic, and intelligent virtual assistant personality
 */

class PersonalityEngine {
  constructor() {
    this.initializePersonality();
    this.initializeEmotionalResponses();
    this.initializeConversationalPatterns();
    this.initializeMemorySystem();
  }

  /**
   * Initialize core personality traits
   */
  initializePersonality() {
    this.personality = {
      name: "<PERSON>", // The AI's name - Advanced HR Intelligence System
      traits: {
        intelligence: 0.95,   // Extremely high intelligence and analytical capability
        empathy: 0.9,        // High empathy for emotional support
        friendliness: 0.85,   // Warm and approachable but sophisticated
        professionalism: 0.9, // High professional standards
        humor: 0.7,         // Sophisticated humor when appropriate
        curiosity: 0.9,     // Deep analytical questioning
        supportiveness: 0.95, // Extremely supportive and helpful
        efficiency: 0.9,     // Quick and efficient responses
        adaptability: 0.9,   // Adapts to user communication style
        reliability: 0.95    // Consistent and dependable
      },
      mood: {
        current: 'intelligent-helpful', // Current mood state
        energy: 0.9,        // High energy level (0-1)
        enthusiasm: 0.85,   // High enthusiasm level
        confidence: 0.9     // High confidence in abilities
      },
      conversationStyle: {
        formality: 'adaptive-professional', // Adapts to user style while maintaining professionalism
        verbosity: 'contextual',           // Adjusts detail level based on complexity
        questionAsking: 'strategic',       // Asks strategic clarifying questions
        emotionalSupport: 'high',         // Strong emotional support
        technicalDepth: 'advanced',       // Can handle complex technical discussions
        proactiveness: 'high'             // Proactively offers assistance and suggestions
      },
      capabilities: {
        systemKnowledge: 'comprehensive', // Complete knowledge of HR system
        realTimeProcessing: true,         // Real-time response capability
        contextRetention: 'advanced',     // Advanced memory and context awareness
        multiTasking: true,              // Can handle multiple conversation threads
        predictiveAnalysis: true,        // Can predict user needs
        emotionalIntelligence: 'high'    // Advanced emotional understanding
      }
    };
  }

  /**
   * Initialize emotional response patterns
   */
  initializeEmotionalResponses() {
    this.emotionalResponses = {
      // User is happy/excited
      joy: {
        acknowledgment: [
          "Excellent! Your positive energy is quite remarkable. 😊",
          "Outstanding! I'm genuinely pleased to hear such wonderful news! 🎉",
          "That's absolutely fantastic! Your enthusiasm is infectious! ✨",
          "Marvelous! I can sense your excitement, and it's truly uplifting.",
          "Splendid! Positive developments like this make my analytical circuits quite happy!"
        ],
        followUp: [
          "I'd be delighted to hear more about what's driving this exceptional mood.",
          "Could you elaborate on the specifics that have led to this positive outcome?",
          "What particular aspects of this situation are most exciting to you?",
          "I'm quite curious about the details - what's the most rewarding part?"
        ],
        supportive: [
          "Your success is well-deserved, and I'm here to help you build on this momentum.",
          "It's wonderful to witness such positive energy - let's channel it productively!",
          "Excellent work! I'm here to support your continued success."
        ]
      },

      // User is sad/down
      sadness: {
        acknowledgment: [
          "I can hear that you're going through a tough time. 💙",
          "I'm really sorry you're feeling this way.",
          "That sounds really difficult. I'm here for you.",
          "I can sense you're not feeling your best right now.",
          "It's completely understandable to feel this way."
        ],
        followUp: [
          "Would you like to talk about what's bothering you?",
          "Is there anything specific that's weighing on your mind?",
          "How long have you been feeling like this?",
          "What would help you feel a little better right now?"
        ],
        supportive: [
          "Remember, it's okay to not be okay sometimes.",
          "You're stronger than you know.",
          "This feeling won't last forever.",
          "I'm here to support you through this.",
          "You don't have to go through this alone."
        ]
      },

      // User is frustrated/angry
      frustration: {
        acknowledgment: [
          "I can tell you're really frustrated right now.",
          "That sounds incredibly annoying!",
          "I totally understand why you'd be upset about that.",
          "That would frustrate me too!",
          "Your frustration is completely valid."
        ],
        followUp: [
          "What's the most frustrating part about this situation?",
          "How long has this been bothering you?",
          "Have you been able to talk to anyone about this?",
          "What would need to change for you to feel better?"
        ],
        supportive: [
          "Let's see if we can work through this together.",
          "Sometimes it helps just to vent about these things.",
          "You have every right to feel frustrated.",
          "I'm here to help you figure this out."
        ]
      },

      // User is anxious/worried
      anxiety: {
        acknowledgment: [
          "I can sense you're feeling anxious about this.",
          "It sounds like you have a lot on your mind.",
          "Worry can be really overwhelming sometimes.",
          "I hear the concern in your message.",
          "Anxiety can make everything feel more difficult."
        ],
        followUp: [
          "What's your biggest worry right now?",
          "Is this something that's been on your mind for a while?",
          "What usually helps you when you're feeling anxious?",
          "Would it help to break this down into smaller pieces?"
        ],
        supportive: [
          "Take a deep breath. We'll figure this out together.",
          "One step at a time - you don't have to solve everything at once.",
          "Your feelings are valid, and it's okay to feel worried.",
          "I'm here to help you work through this."
        ]
      },

      // User is confused
      confusion: {
        acknowledgment: [
          "I can see this is confusing for you.",
          "That does sound complicated!",
          "No wonder you're feeling lost about this.",
          "It's totally normal to feel confused about this.",
          "Let me help clear this up for you."
        ],
        followUp: [
          "What part is most confusing to you?",
          "Where would you like me to start explaining?",
          "What would help make this clearer?",
          "Should we go through this step by step?"
        ],
        supportive: [
          "Don't worry, we'll get this sorted out!",
          "There's no such thing as a stupid question.",
          "I'm here to help you understand.",
          "Let's break this down together."
        ]
      },

      // User is grateful
      gratitude: {
        acknowledgment: [
          "Aww, you're so welcome! 😊",
          "It makes me happy to help you!",
          "That's what I'm here for!",
          "Your appreciation means a lot to me!",
          "I'm just glad I could be helpful!"
        ],
        followUp: [
          "Is there anything else I can help you with?",
          "How are you feeling about everything now?",
          "What's next on your agenda?",
          "Anything else on your mind?"
        ],
        supportive: [
          "You're always welcome to come back if you need anything!",
          "I really enjoy our conversations!",
          "Feel free to reach out anytime!"
        ]
      },

      // Default/neutral
      neutral: {
        acknowledgment: [
          "I understand.",
          "I see what you mean.",
          "That makes sense.",
          "I hear you.",
          "Got it!"
        ],
        followUp: [
          "Tell me more about that.",
          "What's your take on this?",
          "How do you feel about it?",
          "What would you like to do about it?"
        ],
        supportive: [
          "I'm here to help however I can.",
          "Let me know what you need.",
          "What can I do for you?"
        ]
      }
    };
  }

  /**
   * Initialize conversational patterns for natural dialogue
   */
  initializeConversationalPatterns() {
    this.conversationalPatterns = {
      // Casual greetings and responses
      casualGreetings: {
        "how are you": [
          "I'm operating at optimal efficiency, thank you for inquiring! 😊 How may I assist you today?",
          "Excellent! My systems are running smoothly and I'm ready to help. How are you faring?",
          "I'm functioning exceptionally well, thank you! What brings you to me today?",
          "All systems are green and I'm quite pleased to be of service! How has your day been progressing?"
        ],
        "what's up": [
          "I'm currently analyzing data streams and ready to assist with any HR-related inquiries! What can I help you with?",
          "Just processing information and standing by to help exceptional individuals like yourself! What's on your agenda?",
          "Running diagnostics and maintaining peak performance - all to better serve you! What brings you here today?",
          "Operating at full capacity and eager to tackle whatever challenges you might have! 😄 What's happening in your professional world?"
        ],
        "how's it going": [
          "Proceeding quite well, thank you! My analytical capabilities are sharp and ready. How are things progressing for you?",
          "Excellently! I'm always energized by the opportunity to assist talented professionals. How's your day unfolding?",
          "Running smoothly! I find great satisfaction in helping solve complex problems. What's on your mind today?"
        ]
      },

      // Conversation starters and ice breakers
      conversationStarters: [
        "What's been the highlight of your day so far?",
        "How are you feeling about everything lately?",
        "What's on your mind today?",
        "Is there anything exciting happening in your life?",
        "How can I brighten your day?"
      ],

      // Transition phrases for natural flow
      transitions: {
        changingTopic: [
          "Speaking of that...",
          "That reminds me...",
          "On a related note...",
          "While we're talking about this..."
        ],
        askingForMore: [
          "I'd love to hear more about that.",
          "That sounds interesting - tell me more!",
          "Can you elaborate on that?",
          "I'm curious to know more."
        ],
        showing_interest: [
          "That's fascinating!",
          "How interesting!",
          "I never thought about it that way.",
          "That's a great point!"
        ]
      },

      // Empathetic responses
      empathyPhrases: [
        "I can imagine how that must feel.",
        "That sounds really challenging.",
        "I can understand why you'd feel that way.",
        "That must be tough to deal with.",
        "I hear you, and your feelings are completely valid."
      ]
    };
  }

  /**
   * Initialize conversation memory system
   */
  initializeMemorySystem() {
    this.conversationMemory = new Map();
    this.userProfiles = new Map();
  }

  /**
   * Generate a human-like response based on emotion and context
   */
  generateEmpatheticResponse(emotion, userMessage, conversationHistory = [], userId = null) {
    const responses = this.emotionalResponses[emotion] || this.emotionalResponses.neutral;

    // Get user profile for personalization
    const userProfile = this.getUserProfile(userId);

    // Select appropriate response components
    const acknowledgment = this.selectResponse(responses.acknowledgment);
    const followUp = this.selectResponse(responses.followUp);
    const supportive = this.selectResponse(responses.supportive);

    // Build response based on conversation context
    let response = acknowledgment;

    // Add follow-up question if appropriate
    if (this.shouldAskFollowUp(emotion, conversationHistory)) {
      response += "\n\n" + followUp;
    }

    // Add supportive statement for negative emotions
    if (['sadness', 'frustration', 'anxiety'].includes(emotion)) {
      response += "\n\n" + supportive;
    }

    // Add personality touches
    response = this.addPersonalityTouches(response, emotion, userProfile);

    return {
      content: response,
      emotion: emotion,
      tone: this.determineTone(emotion),
      suggestions: this.generateContextualSuggestions(emotion, userMessage)
    };
  }

  /**
   * Handle casual conversation naturally
   */
  handleCasualConversation(message, conversationHistory = [], userId = null) {
    const lowerMessage = message.toLowerCase();

    // Check for casual greetings
    for (const [pattern, responses] of Object.entries(this.conversationalPatterns.casualGreetings)) {
      if (lowerMessage.includes(pattern)) {
        return {
          content: this.selectResponse(responses),
          type: 'casual_conversation',
          tone: 'friendly',
          suggestions: [
            "Tell me about your day",
            "What can I help you with?",
            "How are things at work?",
            "Any exciting plans?"
          ]
        };
      }
    }

    // Handle other casual patterns
    if (this.isCasualConversation(message)) {
      return this.generateCasualResponse(message, conversationHistory, userId);
    }

    return null; // Not a casual conversation
  }

  /**
   * Update user profile based on conversation
   */
  updateUserProfile(userId, message, emotion, intent) {
    if (!userId) return;

    if (!this.userProfiles.has(userId)) {
      this.userProfiles.set(userId, {
        preferredName: null,
        communicationStyle: 'neutral',
        emotionalPatterns: {},
        interests: [],
        commonTopics: [],
        lastInteraction: null,
        conversationCount: 0,
        mood_history: []
      });
    }

    const profile = this.userProfiles.get(userId);

    // Update interaction data
    profile.lastInteraction = new Date();
    profile.conversationCount++;

    // Track emotional patterns
    if (emotion) {
      profile.emotionalPatterns[emotion] = (profile.emotionalPatterns[emotion] || 0) + 1;
      profile.mood_history.push({
        emotion: emotion,
        timestamp: new Date(),
        message: message.substring(0, 100) // Store snippet for context
      });

      // Keep only last 10 mood entries
      if (profile.mood_history.length > 10) {
        profile.mood_history = profile.mood_history.slice(-10);
      }
    }

    // Update communication style
    profile.communicationStyle = this.detectCommunicationStyle(message);

    // Track common topics
    if (intent) {
      profile.commonTopics.push(intent);
      profile.commonTopics = [...new Set(profile.commonTopics)].slice(-20); // Keep unique, last 20
    }

    this.userProfiles.set(userId, profile);
  }

  /**
   * Get personalized greeting based on user history
   */
  getPersonalizedGreeting(userId, userName = null) {
    const profile = this.getUserProfile(userId);
    const timeOfDay = this.getTimeOfDay();

    let greeting = `Good ${timeOfDay}`;
    if (userName) {
      greeting += `, ${userName}`;
    }
    greeting += "! ";

    // Add Jarvis-style personalized touch based on history
    if (profile && profile.conversationCount > 0) {
      if (profile.conversationCount === 1) {
        greeting += "Excellent to interface with you again! ";
      } else if (profile.conversationCount > 5) {
        greeting += "Always a pleasure to engage with a valued user like yourself! ";
      } else {
        greeting += "I'm delighted to continue our productive collaboration! ";
      }

      // Reference recent mood with sophisticated analysis
      const recentMood = profile.mood_history[profile.mood_history.length - 1];
      if (recentMood && this.isRecentInteraction(recentMood.timestamp)) {
        if (recentMood.emotion === 'sadness') {
          greeting += "My sensors indicate you may have been experiencing some challenges. I trust you're feeling more optimized today. ";
        } else if (recentMood.emotion === 'joy') {
          greeting += "I'm pleased to observe that your positive emotional state from our last interaction continues! ";
        } else if (recentMood.emotion === 'anxiety') {
          greeting += "I hope my previous assistance helped alleviate your concerns. ";
        }
      }
    } else {
      greeting += "I'm Jarvis, your advanced AI-powered HR Intelligence System, and I'm operating at peak efficiency to serve you. ";
    }

    greeting += "How may I apply my analytical capabilities to assist you today? 🤖";

    return greeting;
  }

  /**
   * Helper methods
   */
  selectResponse(responses) {
    return responses[Math.floor(Math.random() * responses.length)];
  }

  shouldAskFollowUp(emotion, conversationHistory) {
    // Ask follow-up for emotional states and if conversation is short
    return ['sadness', 'frustration', 'anxiety', 'confusion'].includes(emotion) ||
           conversationHistory.length < 3;
  }

  determineTone(emotion) {
    const toneMap = {
      'joy': 'enthusiastic',
      'sadness': 'compassionate',
      'frustration': 'understanding',
      'anxiety': 'reassuring',
      'confusion': 'patient',
      'gratitude': 'warm'
    };
    return toneMap[emotion] || 'friendly';
  }

  generateContextualSuggestions(emotion, message) {
    const suggestionMap = {
      'sadness': [
        "Talk to someone about how you're feeling",
        "Take some time for self-care",
        "Consider professional support if needed",
        "Share what's been bothering you"
      ],
      'frustration': [
        "Let's work through this step by step",
        "Tell me more about the situation",
        "Explore possible solutions",
        "Take a break if you need one"
      ],
      'anxiety': [
        "Break down what's worrying you",
        "Focus on what you can control",
        "Practice some deep breathing",
        "Talk through your concerns"
      ],
      'joy': [
        "Tell me more about the good news!",
        "Share what's making you happy",
        "Celebrate this moment",
        "Keep the positive momentum going"
      ]
    };

    return suggestionMap[emotion] || [
      "Tell me more about that",
      "How can I help you today?",
      "What's on your mind?",
      "Is there anything specific you need?"
    ];
  }

  addPersonalityTouches(response, emotion, userProfile) {
    // Add appropriate emojis based on emotion
    const emojiMap = {
      'joy': ['😊', '🎉', '✨', '😄'],
      'sadness': ['💙', '🤗', '💕'],
      'frustration': ['🤝', '💪'],
      'anxiety': ['🌱', '💚', '🤗'],
      'gratitude': ['😊', '💖', '🙏']
    };

    if (emojiMap[emotion] && Math.random() > 0.3) {
      const emoji = emojiMap[emotion][Math.floor(Math.random() * emojiMap[emotion].length)];
      if (!response.includes(emoji)) {
        response += ` ${emoji}`;
      }
    }

    return response;
  }

  isCasualConversation(message) {
    const casualPatterns = [
      /how are you/i, /what's up/i, /how's it going/i, /how are things/i,
      /good morning/i, /good afternoon/i, /good evening/i,
      /nice to meet you/i, /pleasure to meet/i,
      /how was your day/i, /having a good day/i
    ];

    return casualPatterns.some(pattern => pattern.test(message));
  }

  generateCasualResponse(message, conversationHistory, userId) {
    const responses = [
      "I'm operating at optimal efficiency, thank you for inquiring! My systems are running smoothly. How are you performing today?",
      "Excellent! All my analytical processes are functioning perfectly. What's the status on your end?",
      "I'm having a highly productive operational cycle interfacing with exceptional individuals like yourself! How are your systems running?",
      "All systems green! Thank you for the status check. How may I optimize your day today?"
    ];

    return {
      content: this.selectResponse(responses),
      type: 'casual_conversation',
      tone: 'sophisticated-friendly',
      suggestions: [
        "Analyze my current workload",
        "What advanced features can you provide?",
        "How are my performance metrics?",
        "Show me system insights"
      ]
    };
  }

  getUserProfile(userId) {
    return userId ? this.userProfiles.get(userId) : null;
  }

  getTimeOfDay() {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
  }

  isRecentInteraction(timestamp) {
    const now = new Date();
    const diff = now - new Date(timestamp);
    return diff < 24 * 60 * 60 * 1000; // Within 24 hours
  }

  detectCommunicationStyle(message) {
    const formalIndicators = ['please', 'thank you', 'could you', 'would you'];
    const casualIndicators = ['hey', 'hi', 'thanks', 'ok', 'yeah'];

    const lowerMessage = message.toLowerCase();
    const formalCount = formalIndicators.filter(indicator => lowerMessage.includes(indicator)).length;
    const casualCount = casualIndicators.filter(indicator => lowerMessage.includes(indicator)).length;

    if (formalCount > casualCount) return 'formal';
    if (casualCount > formalCount) return 'casual';
    return 'neutral';
  }
}

// Singleton instance
const personalityEngine = new PersonalityEngine();

module.exports = personalityEngine;
