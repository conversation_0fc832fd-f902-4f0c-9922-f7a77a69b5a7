/**
 * User Activity Model
 * Tracks user interactions and activities for AI analysis
 */

const mongoose = require('mongoose');

const userActivitySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  activityType: {
    type: String,
    enum: [
      'leave_request',
      'task_creation',
      'task_completion',
      'job_posting',
      'application_review',
      'user_update',
      'attendance_checkin',
      'attendance_checkout',
      'evaluation_created',
      'evaluation_completed',
      'login',
      'logout',
      'page_view',
      'search',
      'export',
      'report_generation'
    ],
    required: true,
    index: true
  },
  category: {
    type: String,
    enum: [
      'user_management',
      'recruitment',
      'workflow',
      'leave_management',
      'productivity',
      'task_management',
      'analytics',
      'performance',
      'attendance',
      'system'
    ],
    required: true,
    index: true
  },
  resourceType: {
    type: String,
    enum: ['user', 'task', 'leave_request', 'application', 'job', 'evaluation', 'report'],
    index: true
  },
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    index: true
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  sessionId: {
    type: String,
    index: true
  },
  userAgent: String,
  ipAddress: String,
  duration: {
    type: Number, // in milliseconds
    default: 0
  },
  success: {
    type: Boolean,
    default: true
  },
  errorMessage: String
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
userActivitySchema.index({ userId: 1, createdAt: -1 });
userActivitySchema.index({ userId: 1, activityType: 1 });
userActivitySchema.index({ userId: 1, category: 1 });
userActivitySchema.index({ activityType: 1, createdAt: -1 });
userActivitySchema.index({ sessionId: 1, createdAt: -1 });
userActivitySchema.index({ createdAt: -1 }); // For cleanup

// Virtual for activity age
userActivitySchema.virtual('ageInHours').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60));
});

// Virtual for activity age in days
userActivitySchema.virtual('ageInDays').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24));
});

// Static methods for analytics
userActivitySchema.statics.getUserActivityStats = function(userId, timeRange = 30) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$activityType',
        count: { $sum: 1 },
        avgDuration: { $avg: '$duration' },
        successRate: {
          $avg: {
            $cond: ['$success', 1, 0]
          }
        },
        lastActivity: { $max: '$createdAt' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

userActivitySchema.statics.getActivityByCategory = function(userId, category, timeRange = 7) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  return this.find({
    userId,
    category,
    createdAt: { $gte: startDate }
  })
  .sort({ createdAt: -1 })
  .limit(50);
};

userActivitySchema.statics.getRecentActivity = function(userId, limit = 20) {
  return this.find({ userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('resourceId', 'title name');
};

userActivitySchema.statics.getActivityPatterns = function(userId, timeRange = 30) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          hour: { $hour: '$createdAt' },
          dayOfWeek: { $dayOfWeek: '$createdAt' },
          activityType: '$activityType'
        },
        count: { $sum: 1 },
        avgDuration: { $avg: '$duration' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

userActivitySchema.statics.getProductivityMetrics = function(userId, timeRange = 7) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);
  
  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate },
        activityType: { $in: ['task_creation', 'task_completion', 'leave_request', 'application_review'] }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
        },
        taskCreations: {
          $sum: {
            $cond: [{ $eq: ['$activityType', 'task_creation'] }, 1, 0]
          }
        },
        taskCompletions: {
          $sum: {
            $cond: [{ $eq: ['$activityType', 'task_completion'] }, 1, 0]
          }
        },
        leaveRequests: {
          $sum: {
            $cond: [{ $eq: ['$activityType', 'leave_request'] }, 1, 0]
          }
        },
        applicationReviews: {
          $sum: {
            $cond: [{ $eq: ['$activityType', 'application_review'] }, 1, 0]
          }
        },
        totalActivities: { $sum: 1 },
        avgDuration: { $avg: '$duration' }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
};

userActivitySchema.statics.findSimilarActivities = function(userId, activityType, resourceType, timeWindow = 24) {
  const startTime = new Date(Date.now() - timeWindow * 60 * 60 * 1000);
  
  return this.find({
    userId,
    activityType,
    resourceType,
    createdAt: { $gte: startTime }
  })
  .sort({ createdAt: -1 });
};

userActivitySchema.statics.cleanupOldActivities = function(daysToKeep = 90) {
  const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
  
  return this.deleteMany({
    createdAt: { $lt: cutoffDate }
  });
};

// Instance methods
userActivitySchema.methods.updateDuration = function(duration) {
  this.duration = duration;
  return this.save();
};

userActivitySchema.methods.markAsError = function(errorMessage) {
  this.success = false;
  this.errorMessage = errorMessage;
  return this.save();
};

// Pre-save middleware
userActivitySchema.pre('save', function(next) {
  // Ensure metadata is an object
  if (!this.metadata) {
    this.metadata = {};
  }
  
  next();
});

// Post-save middleware for logging (only in development)
userActivitySchema.post('save', function(doc) {
  if (process.env.NODE_ENV === 'development') {
    console.log(`📊 Activity logged: ${doc.activityType} by user ${doc.userId}`);
  }
});

module.exports = mongoose.model('UserActivity', userActivitySchema);
