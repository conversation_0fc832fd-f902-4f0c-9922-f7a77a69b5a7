import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  EventNote as EventNoteIcon,
  Assignment as AssignmentIcon,
  AccessTime as AccessTimeIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CheckCircleIcon,
  HourglassEmpty as HourglassEmptyIcon,
  CalendarToday as CalendarTodayIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationOnIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  AutoAwesome,
} from '@mui/icons-material';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Button,
  useTheme,
  Card,
  CardContent,
  CardActions,
  Divider,
  Avatar,
  LinearProgress,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  InputAdornment,
  IconButton,
  Alert,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { jwtDecode } from 'jwt-decode';
import api from '../Services/ApiService';
import DashboardLayout from '../components/layout/DashboardLayout';
import StatCard from '../components/dashboard/StatCard';
import TaskList from '../components/TaskList';
import AttendanceTracker from '../components/AttendanceTracker';
import UserEvaluations from '../components/UserEvaluations';
import ContextualSuggestions from '../components/ai-assistant/ContextualSuggestions';
import { useContextualSuggestions } from '../hooks/useContextualSuggestions';

const ProfileCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  overflow: 'hidden',
}));

const ProfileHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
  padding: theme.spacing(3),
  color: theme.palette.common.white,
  textAlign: 'center',
  position: 'relative',
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 100,
  height: 100,
  margin: '0 auto',
  border: `4px solid ${theme.palette.common.white}`,
  boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
}));

const TaskCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.05)',
  overflow: 'hidden',
}));

const TaskHeader = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, ${theme.palette.secondary.dark} 0%, ${theme.palette.secondary.main} 100%)`,
  padding: theme.spacing(2),
  color: theme.palette.common.white,
}));

const NewUserDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [profile, setProfile] = useState({});
  const [tasks, setTasks] = useState([]);
  const [leaveHistory, setLeaveHistory] = useState([]);
  const [attendanceRecords, setAttendanceRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogContent, setDialogContent] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authChecking, setAuthChecking] = useState(true);

  // Leave request form states
  const [leaveType, setLeaveType] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [reason, setReason] = useState('');
  const [leaveSearchQuery, setLeaveSearchQuery] = useState('');

  // Contextual AI suggestions
  const { generateSuggestions, activeSuggestions, clearSuggestions } = useContextualSuggestions();



  // Check authentication on component mount - instant validation
  useEffect(() => {
    setAuthChecking(true);
    const token = localStorage.getItem('token');

    if (!token) {
      toast.error('You must be logged in to access this page');
      navigate('/', { replace: true });
      return;
    }

    try {
      const decodedToken = jwtDecode(token);

      // Check if token is expired
      if (decodedToken.exp * 1000 <= Date.now()) {
        toast.error('Session expired. Please log in again.');
        localStorage.removeItem('token');
        navigate('/', { replace: true });
        return;
      }

      // Check role authorization
      if (decodedToken.role !== 'user') {
        toast.error('You do not have permission to access this page');
        navigate('/', { replace: true });
        return;
      }

      // Authentication successful - proceed immediately
      setIsAuthenticated(true);
      setAuthChecking(false);
      fetchUserData();

    } catch (error) {
      console.error('Token decode error:', error);
      toast.error('Authentication failed. Please log in again.');
      localStorage.removeItem('token');
      navigate('/', { replace: true });
    }
  }, [navigate]);

  // Component-specific search timeout reference
  const leaveSearchTimeoutRef = useRef(null);

  // Cleanup function to clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (leaveSearchTimeoutRef.current) {
        clearTimeout(leaveSearchTimeoutRef.current);
      }
    };
  }, []);

  // Real-time search for leave requests
  useEffect(() => {
    // Clear any existing timeout
    if (leaveSearchTimeoutRef.current) {
      clearTimeout(leaveSearchTimeoutRef.current);
    }

    // Debounced search
    leaveSearchTimeoutRef.current = setTimeout(() => {
      if (activeSection === 'leave') {
        fetchLeaveHistory(leaveSearchQuery);
      }
    }, 300); // 300ms delay for more responsive feel
  }, [leaveSearchQuery, activeSection]);

  const fetchUserData = async () => {
    setLoading(true);
    try {
      // Create an array to store all the promises
      const promises = [];
      const results = { profile: null, tasks: null, leaveHistory: null, attendance: null };

      // Profile data - try both endpoints
      promises.push(
        (async () => {
          try {
            const response = await api.get('/user/profile');
            console.log('Profile data from primary endpoint:', response.data);
            results.profile = response.data;
          } catch (primaryError) {
            console.warn('Primary endpoint failed for profile:', primaryError);
            try {
              const altResponse = await api.get('/normaluser/profile');
              console.log('Profile data from alternative endpoint:', altResponse.data);
              results.profile = altResponse.data;
            } catch (altError) {
              console.error('Alternative endpoint also failed for profile:', altError);
              throw altError;
            }
          }
        })()
      );

      // Tasks data - try both endpoints
      promises.push(
        (async () => {
          try {
            const response = await api.get('/tasks/user/assigned');
            console.log('Tasks data from primary endpoint:', response.data);
            if (response.data && Array.isArray(response.data.tasks)) {
              results.tasks = response.data.tasks;
            } else if (Array.isArray(response.data)) {
              results.tasks = response.data;
            } else {
              console.warn('Unexpected tasks data structure from primary endpoint:', response.data);
              results.tasks = [];
            }
          } catch (primaryError) {
            console.warn('Primary endpoint failed for tasks:', primaryError);
            try {
              const altResponse = await api.get('/normaluser/tasks');
              console.log('Tasks data from alternative endpoint:', altResponse.data);
              if (altResponse.data && Array.isArray(altResponse.data.tasks)) {
                results.tasks = altResponse.data.tasks;
              } else if (Array.isArray(altResponse.data)) {
                results.tasks = altResponse.data;
              } else {
                console.warn('Unexpected tasks data structure from alternative endpoint:', altResponse.data);
                results.tasks = [];
              }
            } catch (altError) {
              console.error('Alternative endpoint also failed for tasks:', altError);
              results.tasks = [];
            }
          }
        })()
      );

      // Leave history data - try both endpoints
      promises.push(
        (async () => {
          try {
            const response = await api.get('/user/leave-requests');
            console.log('Leave history data from primary endpoint:', response.data);
            if (Array.isArray(response.data)) {
              results.leaveHistory = response.data;
            } else if (response.data && Array.isArray(response.data.leaveRequests)) {
              results.leaveHistory = response.data.leaveRequests;
            } else {
              console.warn('Unexpected leave history data structure from primary endpoint:', response.data);
              // Try alternative endpoint
              throw new Error('Invalid data structure');
            }
          } catch (primaryError) {
            console.warn('Primary endpoint failed for leave history:', primaryError);
            try {
              const altResponse = await api.get('/normaluser/leave-requests');
              console.log('Leave history data from alternative endpoint:', altResponse.data);
              if (Array.isArray(altResponse.data)) {
                results.leaveHistory = altResponse.data;
              } else if (altResponse.data && Array.isArray(altResponse.data.leaveRequests)) {
                results.leaveHistory = altResponse.data.leaveRequests;
              } else {
                console.warn('Unexpected leave history data structure from alternative endpoint:', altResponse.data);
                results.leaveHistory = [];
              }
            } catch (altError) {
              console.error('Alternative endpoint also failed for leave history:', altError);
              results.leaveHistory = [];
            }
          }
        })()
      );

      // Attendance data - try both endpoints
      promises.push(
        (async () => {
          try {
            const response = await api.get('/user/attendance');
            console.log('Attendance data from primary endpoint:', response.data);
            if (Array.isArray(response.data)) {
              results.attendance = response.data;
            } else if (response.data && Array.isArray(response.data.records)) {
              results.attendance = response.data.records;
            } else {
              console.warn('Unexpected attendance data structure from primary endpoint:', response.data);
              // Try alternative endpoint
              throw new Error('Invalid data structure');
            }
          } catch (primaryError) {
            console.warn('Primary endpoint failed for attendance:', primaryError);
            try {
              const altResponse = await api.get('/normaluser/attendance');
              console.log('Attendance data from alternative endpoint:', altResponse.data);
              if (Array.isArray(altResponse.data)) {
                results.attendance = altResponse.data;
              } else if (altResponse.data && Array.isArray(altResponse.data.records)) {
                results.attendance = altResponse.data.records;
              } else {
                console.warn('Unexpected attendance data structure from alternative endpoint:', altResponse.data);
                results.attendance = [];
              }
            } catch (altError) {
              console.error('Alternative endpoint also failed for attendance:', altError);
              results.attendance = [];
            }
          }
        })()
      );

      // Wait for all promises to resolve
      await Promise.all(promises);

      // Set the data
      if (results.profile) {
        setProfile(results.profile);
      }

      setTasks(results.tasks || []);
      setLeaveHistory(results.leaveHistory || []);
      setAttendanceRecords(results.attendance || []);

    } catch (error) {
      console.error('Error fetching user data:', error);
      toast.error('Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    // Instant logout - no API calls needed
    localStorage.removeItem('token');
    toast.info('You have been logged out');
    navigate('/', { replace: true });
  };

  const handleDialogOpen = (content) => {
    setDialogContent(content);
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
    setDialogContent(null);
  };

  const handleLeaveSubmit = async (e) => {
    if (e) e.preventDefault();

    if (!leaveType || !startDate || !endDate || !reason) {
      toast.error('Please fill in all fields');
      return;
    }

    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    const today = new Date();

    // Remove time part for comparison
    today.setHours(0, 0, 0, 0);

    if (start < today) {
      toast.error('Start date cannot be in the past');
      return;
    }

    if (end < start) {
      toast.error('End date cannot be before start date');
      return;
    }

    // Calculate duration in days
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days

    // Check if the leave duration is reasonable (e.g., not more than 30 days)
    if (diffDays > 30) {
      if (!window.confirm(`You are requesting ${diffDays} days of leave. Are you sure this is correct?`)) {
        return;
      }
    }

    const requestData = {
      leaveType,
      startDate,
      endDate,
      reason
    };

    try {
      console.log('Submitting leave request:', requestData);

      // Try all possible endpoints in sequence
      let response = null;
      let success = false;

      // Try endpoint 1
      try {
        response = await api.post('/user/leave-requests', requestData);
        console.log('Leave request response from endpoint 1:', response.data);
        success = true;
      } catch (error1) {
        console.warn('Endpoint 1 failed for leave request:', error1);
      }

      // Try endpoint 2 if endpoint 1 failed
      if (!success) {
        try {
          response = await api.post('/user/leave-request', requestData);
          console.log('Leave request response from endpoint 2:', response.data);
          success = true;
        } catch (error2) {
          console.warn('Endpoint 2 failed for leave request:', error2);
        }
      }

      // Try endpoint 3 if endpoints 1 and 2 failed
      if (!success) {
        try {
          response = await api.post('/normaluser/leave-requests', requestData);
          console.log('Leave request response from endpoint 3:', response.data);
          success = true;
        } catch (error3) {
          console.warn('Endpoint 3 failed for leave request:', error3);
        }
      }

      // Try endpoint 4 if endpoints 1, 2, and 3 failed
      if (!success) {
        try {
          response = await api.post('/normaluser/leave-request', requestData);
          console.log('Leave request response from endpoint 4:', response.data);
          success = true;
        } catch (error4) {
          console.warn('Endpoint 4 failed for leave request:', error4);
          throw error4; // Throw the last error if all endpoints failed
        }
      }

      toast.success('Leave request submitted successfully');

      // Clear suggestions after successful submission
      clearSuggestions();

      handleDialogClose();

      // Reset form
      setLeaveType('');
      setStartDate('');
      setEndDate('');
      setReason('');

      // Refresh leave history
      fetchLeaveHistory();
    } catch (error) {
      console.error('Error submitting leave request:', error);

      // Log detailed error information for debugging
      if (error.response) {
        console.error('Error response:', error.response.data);
        console.error('Status code:', error.response.status);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }

      toast.error('Failed to submit leave request: ' + (error.response?.data?.message || 'Unknown error'));
    }
  };

  const fetchLeaveHistory = async (searchQuery = '') => {
    try {
      // Try all possible endpoints in sequence
      let success = false;

      // Try endpoint 1
      try {
        const response = await api.get('/user/leave-requests', {
          params: searchQuery ? { search: searchQuery } : {}
        });
        console.log('Fetched leave history from endpoint 1:', response.data);

        // Ensure leave history is properly formatted
        let leaveData = response.data;
        if (Array.isArray(leaveData)) {
          setLeaveHistory(leaveData);
          success = true;
        } else if (leaveData && Array.isArray(leaveData.leaveRequests)) {
          setLeaveHistory(leaveData.leaveRequests);
          success = true;
        } else {
          console.warn('Leave history data from endpoint 1 is not in expected format:', leaveData);
        }
      } catch (error1) {
        console.warn('Endpoint 1 failed for leave history:', error1);
      }

      // Try endpoint 2 if endpoint 1 failed
      if (!success) {
        try {
          const response = await api.get('/user/leave-request', {
            params: searchQuery ? { search: searchQuery } : {}
          });
          console.log('Fetched leave history from endpoint 2:', response.data);

          // Ensure leave history is properly formatted
          let leaveData = response.data;
          if (Array.isArray(leaveData)) {
            setLeaveHistory(leaveData);
            success = true;
          } else if (leaveData && Array.isArray(leaveData.leaveRequests)) {
            setLeaveHistory(leaveData.leaveRequests);
            success = true;
          } else {
            console.warn('Leave history data from endpoint 2 is not in expected format:', leaveData);
          }
        } catch (error2) {
          console.warn('Endpoint 2 failed for leave history:', error2);
        }
      }

      // Try endpoint 3 if endpoints 1 and 2 failed
      if (!success) {
        try {
          const response = await api.get('/normaluser/leave-requests', {
            params: searchQuery ? { search: searchQuery } : {}
          });
          console.log('Fetched leave history from endpoint 3:', response.data);

          // Ensure leave history is properly formatted
          let leaveData = response.data;
          if (Array.isArray(leaveData)) {
            setLeaveHistory(leaveData);
            success = true;
          } else if (leaveData && Array.isArray(leaveData.leaveRequests)) {
            setLeaveHistory(leaveData.leaveRequests);
            success = true;
          } else {
            console.warn('Leave history data from endpoint 3 is not in expected format:', leaveData);
          }
        } catch (error3) {
          console.warn('Endpoint 3 failed for leave history:', error3);
        }
      }

      // Try endpoint 4 if endpoints 1, 2, and 3 failed
      if (!success) {
        try {
          const response = await api.get('/normaluser/leave-request', {
            params: searchQuery ? { search: searchQuery } : {}
          });
          console.log('Fetched leave history from endpoint 4:', response.data);

          // Ensure leave history is properly formatted
          let leaveData = response.data;
          if (Array.isArray(leaveData)) {
            setLeaveHistory(leaveData);
            success = true;
          } else if (leaveData && Array.isArray(leaveData.leaveRequests)) {
            setLeaveHistory(leaveData.leaveRequests);
            success = true;
          } else {
            console.warn('Leave history data from endpoint 4 is not in expected format:', leaveData);
            setLeaveHistory([]); // Set empty array if all endpoints failed or returned unexpected data
          }
        } catch (error4) {
          console.warn('Endpoint 4 failed for leave history:', error4);
          setLeaveHistory([]); // Set empty array if all endpoints failed
        }
      }

      if (!success) {
        console.error('All endpoints failed for leave history');
        toast.error('Failed to refresh leave history');
      }
    } catch (error) {
      console.error('Error in fetchLeaveHistory:', error);
      toast.error('Failed to refresh leave history');
      setLeaveHistory([]);
    }
  };

  // Menu items for the sidebar
  const menuItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      onClick: () => setActiveSection('dashboard'),
      active: activeSection === 'dashboard',
    },
    {
      text: 'My Tasks',
      icon: <AssignmentIcon />,
      onClick: () => setActiveSection('tasks'),
      active: activeSection === 'tasks',
    },
    {
      text: 'Leave Requests',
      icon: <EventNoteIcon />,
      onClick: () => setActiveSection('leave'),
      active: activeSection === 'leave',
    },
    {
      text: 'Attendance',
      icon: <AccessTimeIcon />,
      onClick: () => setActiveSection('attendance'),
      active: activeSection === 'attendance',
    },
    {
      text: 'Evaluations',
      icon: <AssessmentIcon />,
      onClick: () => setActiveSection('evaluations'),
      active: activeSection === 'evaluations',
    },

  ];

  // Calculate stats
  const stats = {
    pendingTasks: tasks.filter(task => task.status !== 'Completed').length,
    completedTasks: tasks.filter(task => task.status === 'Completed').length,
    pendingLeave: leaveHistory.filter(leave => leave.status === 'Pending').length,
    approvedLeave: leaveHistory.filter(leave => leave.status === 'Approved').length,
    attendanceRate: attendanceRecords.length > 0
      ? Math.round((attendanceRecords.filter(record => record.status === 'Present').length / attendanceRecords.length) * 100)
      : 0
  };

  // Render the dashboard content based on the active section
  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              Welcome, {profile.name || 'User'}!
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Here's an overview of your activities and tasks.
            </Typography>

            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Active Tasks"
                  value={stats.pendingTasks}
                  icon={<AssignmentIcon style={{ fontSize: 80 }} />}
                  color="primary"
                  subtitle="Tasks assigned to you"
                  onClick={() => setActiveSection('tasks')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Completed Tasks"
                  value={stats.completedTasks}
                  icon={<CheckCircleIcon style={{ fontSize: 80 }} />}
                  color="success"
                  subtitle="Tasks you've completed"
                  onClick={() => setActiveSection('tasks')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Attendance Rate"
                  value={`${stats.attendanceRate}%`}
                  icon={<AccessTimeIcon style={{ fontSize: 80 }} />}
                  color="info"
                  subtitle="Your attendance this month"
                  onClick={() => setActiveSection('attendance')}
                />
              </Grid>
            </Grid>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <ProfileCard>
                  <ProfileHeader>
                    <ProfileAvatar src={profile.avatar}>
                      {profile.name ? profile.name.charAt(0).toUpperCase() : 'U'}
                    </ProfileAvatar>
                    <Typography variant="h5" sx={{ mt: 2, fontWeight: 600 }}>
                      {profile.name || 'User'}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      {profile.job || 'Employee'}
                    </Typography>
                  </ProfileHeader>
                  <CardContent>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Email
                      </Typography>
                      <Typography variant="body1">
                        {profile.email || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Job Title
                      </Typography>
                      <Typography variant="body1">
                        {profile.job || 'N/A'}
                      </Typography>
                    </Box>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Birthdate
                      </Typography>
                      <Typography variant="body1">
                        {profile.birthdate ? new Date(profile.birthdate).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Account Created
                      </Typography>
                      <Typography variant="body1">
                        {profile.creationDate ? new Date(profile.creationDate).toLocaleDateString() : 'N/A'}
                      </Typography>
                    </Box>
                  </CardContent>
                  <Box sx={{ flexGrow: 1 }} />
                  <CardActions sx={{ p: 2, justifyContent: 'center' }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => setActiveSection('profile')}
                    >
                      View Full Profile
                    </Button>
                  </CardActions>
                </ProfileCard>
              </Grid>

              <Grid item xs={12} md={6}>
                <TaskCard>
                  <TaskHeader>
                    <Typography variant="h6" fontWeight={600}>
                      Recent Tasks
                    </Typography>
                  </TaskHeader>
                  <CardContent sx={{ flexGrow: 1, p: 0 }}>
                    {tasks.length > 0 ? (
                      <Box>
                        {tasks.slice(0, 3).map((task) => (
                          <Box key={task._id} sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                              <Typography variant="subtitle1" fontWeight={500}>
                                {task.title}
                              </Typography>
                              <Chip
                                label={task.status}
                                size="small"
                                color={
                                  task.status === 'Completed' ? 'success' :
                                  task.status === 'In Progress' ? 'warning' : 'default'
                                }
                              />
                            </Box>
                            <Typography variant="body2" color="text.secondary" noWrap sx={{ mb: 1 }}>
                              {task.description}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                                Progress:
                              </Typography>
                              <Box sx={{ flexGrow: 1, mr: 1 }}>
                                <LinearProgress
                                  variant="determinate"
                                  value={task.progress}
                                  color={task.status === 'Completed' ? 'success' : 'primary'}
                                />
                              </Box>
                              <Typography variant="caption" color="text.secondary">
                                {task.progress}%
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography variant="caption" color="text.secondary">
                                Due: {new Date(task.deadline).toLocaleDateString()}
                              </Typography>
                              <Chip
                                label={task.priority}
                                size="small"
                                color={
                                  task.priority === 'High' || task.priority === 'Urgent' ? 'error' :
                                  task.priority === 'Medium' ? 'warning' : 'success'
                                }
                                variant="outlined"
                              />
                            </Box>
                          </Box>
                        ))}
                      </Box>
                    ) : (
                      <Box sx={{ p: 4, textAlign: 'center' }}>
                        <Typography variant="body1" color="text.secondary">
                          No tasks assigned to you yet.
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                  <CardActions sx={{ p: 2, justifyContent: 'center' }}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={() => setActiveSection('tasks')}
                    >
                      View All Tasks
                    </Button>
                  </CardActions>
                </TaskCard>
              </Grid>
            </Grid>
          </Box>
        );

      case 'tasks':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              My Tasks
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Manage and track your assigned tasks.
            </Typography>

            {/* Pass the tasks data to the TaskList component */}
            <TaskList />
          </Box>
        );

      case 'attendance':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              Attendance Tracker
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Track your attendance and check-in/check-out times.
            </Typography>

            <AttendanceTracker />
          </Box>
        );

      case 'leave':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <div>
                <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
                  Leave Requests
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  Manage your leave requests and view their status.
                </Typography>
              </div>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleDialogOpen('requestLeave')}
              >
                Request Leave
              </Button>
            </Box>

            <Paper sx={{ p: 0, borderRadius: 2, overflow: 'hidden', mb: 4 }}>
              <Box sx={{ p: 2, bgcolor: theme.palette.primary.main, color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" fontWeight={600}>
                  Leave Request History
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TextField
                    placeholder="Search leave requests..."
                    size="small"
                    value={leaveSearchQuery}
                    onChange={(e) => setLeaveSearchQuery(e.target.value)}
                    InputProps={{
                      style: { backgroundColor: 'white', borderRadius: '4px' },
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon fontSize="small" sx={{ color: 'text.secondary' }} />
                        </InputAdornment>
                      ),
                      endAdornment: leaveSearchQuery ? (
                        <InputAdornment position="end">
                          <IconButton
                            size="small"
                            onClick={() => {
                              setLeaveSearchQuery('');
                            }}
                            aria-label="clear search"
                            sx={{ color: 'text.secondary' }}
                          >
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        </InputAdornment>
                      ) : null
                    }}
                    sx={{ width: 280 }}
                  />
                </Box>
              </Box>

              {leaveHistory.length > 0 ? (
                <Box sx={{ overflowX: 'auto' }}>
                  <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <thead>
                      <tr style={{ backgroundColor: theme.palette.grey[100] }}>
                        <th style={{ padding: '12px 16px', textAlign: 'left', fontWeight: 600 }}>Leave Type</th>
                        <th style={{ padding: '12px 16px', textAlign: 'left', fontWeight: 600 }}>Start Date</th>
                        <th style={{ padding: '12px 16px', textAlign: 'left', fontWeight: 600 }}>End Date</th>
                        <th style={{ padding: '12px 16px', textAlign: 'left', fontWeight: 600 }}>Duration</th>
                        <th style={{ padding: '12px 16px', textAlign: 'left', fontWeight: 600 }}>Reason</th>
                        <th style={{ padding: '12px 16px', textAlign: 'left', fontWeight: 600 }}>Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {leaveHistory.map((leave) => {
                        // Calculate duration in days
                        const start = new Date(leave.startDate);
                        const end = new Date(leave.endDate);
                        const diffTime = Math.abs(end - start);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days

                        return (
                          <tr key={leave._id} style={{ borderBottom: `1px solid ${theme.palette.divider}` }}>
                            <td style={{ padding: '12px 16px' }}>{leave.leaveType}</td>
                            <td style={{ padding: '12px 16px' }}>{new Date(leave.startDate).toLocaleDateString()}</td>
                            <td style={{ padding: '12px 16px' }}>{new Date(leave.endDate).toLocaleDateString()}</td>
                            <td style={{ padding: '12px 16px' }}>{diffDays} day{diffDays !== 1 ? 's' : ''}</td>
                            <td style={{ padding: '12px 16px' }}>
                              <Tooltip title={leave.reason} arrow>
                                <Typography
                                  variant="body2"
                                  sx={{
                                    maxWidth: 200,
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                  }}
                                >
                                  {leave.reason}
                                </Typography>
                              </Tooltip>
                            </td>
                            <td style={{ padding: '12px 16px' }}>
                              <Chip
                                label={leave.status}
                                size="small"
                                color={
                                  leave.status === 'Approved' ? 'success' :
                                  leave.status === 'Rejected' ? 'error' : 'warning'
                                }
                              />
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </Box>
              ) : (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {leaveSearchQuery
                      ? `No leave requests found matching "${leaveSearchQuery}".`
                      : "You haven't made any leave requests yet."}
                  </Typography>
                  {!leaveSearchQuery ? (
                    <Button
                      variant="contained"
                      color="primary"
                      sx={{ mt: 2 }}
                      onClick={() => handleDialogOpen('requestLeave')}
                    >
                      Request Leave
                    </Button>
                  ) : (
                    <Button
                      variant="outlined"
                      color="primary"
                      sx={{ mt: 2 }}
                      onClick={() => {
                        setLeaveSearchQuery('');
                      }}
                    >
                      Clear Search
                    </Button>
                  )}
                </Box>
              )}
            </Paper>

            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom fontWeight={600}>
                Leave Policy
              </Typography>
              <Typography variant="body2" paragraph>
                Employees are entitled to various types of leave as per company policy. Please make sure to request leave at least 3 days in advance for proper planning.
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{ p: 2, bgcolor: theme.palette.grey[100], borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom color="primary">
                      Sick Leave
                    </Typography>
                    <Typography variant="body2">
                      Up to 10 days per year for illness or medical appointments.
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{ p: 2, bgcolor: theme.palette.grey[100], borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom color="primary">
                      Vacation
                    </Typography>
                    <Typography variant="body2">
                      Up to 20 days per year for personal time off.
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{ p: 2, bgcolor: theme.palette.grey[100], borderRadius: 1 }}>
                    <Typography variant="subtitle2" gutterBottom color="primary">
                      Personal Leave
                    </Typography>
                    <Typography variant="body2">
                      Up to 5 days per year for personal matters.
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Box>
        );

      case 'evaluations':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              My Performance Evaluations
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              View your performance evaluations and feedback from management.
            </Typography>

            <UserEvaluations />
          </Box>
        );



      // Add other sections as needed

      default:
        return (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h5" color="text.secondary">
              Section under development
            </Typography>
          </Box>
        );
    }
  };

  // Show nothing while checking authentication
  if (authChecking) {
    return null; // Return nothing while checking authentication to prevent flash
  }

  return (
    <>
      <DashboardLayout
        title={
          activeSection === 'dashboard' ? 'Dashboard' :
          activeSection === 'profile' ? 'My Profile' :
          activeSection === 'tasks' ? 'My Tasks' :
          activeSection === 'leave' ? 'Leave Requests' :
          activeSection === 'attendance' ? 'Attendance' :
          activeSection === 'evaluations' ? 'Performance Evaluations' :
          'Dashboard'
        }
        menuItems={menuItems}
        userName={profile.name || 'User'}
        userRole={profile.job || 'Employee'}
        onLogout={handleLogout}
      >

        {renderContent()}
      </DashboardLayout>

      {/* Dialog for Leave Requests, Profile Edit, etc. */}
      <Dialog
        open={openDialog}
        onClose={handleDialogClose}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          {dialogContent === 'requestLeave' && 'Request Leave'}
          {dialogContent === 'profile' && 'Edit Profile'}
        </DialogTitle>
        <DialogContent>
          {dialogContent === 'requestLeave' && (
            <form onSubmit={handleLeaveSubmit}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Leave Type</InputLabel>
                <Select
                  value={leaveType}
                  onChange={(e) => setLeaveType(e.target.value)}
                  label="Leave Type"
                  required
                >
                  <MenuItem value="Annual Leave">Annual Leave</MenuItem>
                  <MenuItem value="Sick Leave">Sick Leave</MenuItem>
                  <MenuItem value="Personal Leave">Personal Leave</MenuItem>
                  <MenuItem value="Maternity/Paternity Leave">Maternity/Paternity Leave</MenuItem>
                  <MenuItem value="Bereavement Leave">Bereavement Leave</MenuItem>
                  <MenuItem value="Unpaid Leave">Unpaid Leave</MenuItem>
                </Select>
              </FormControl>
              <TextField
                name="startDate"
                label="Start Date"
                type="date"
                fullWidth
                margin="normal"
                required
                InputLabelProps={{ shrink: true }}
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
              <TextField
                name="endDate"
                label="End Date"
                type="date"
                fullWidth
                margin="normal"
                required
                InputLabelProps={{ shrink: true }}
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
              <TextField
                name="reason"
                label="Reason"
                multiline
                rows={4}
                fullWidth
                margin="normal"
                required
                placeholder="Please provide a detailed reason for your leave request..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />

              {/* AI Suggestions for Leave Request - Automatic Display */}
              {(leaveType || startDate || endDate || reason.length > 5) && (
                <Box sx={{ mt: 2, mb: 1 }}>
                  <ContextualSuggestions
                    context="leave_request"
                    actionType="create"
                    data={{
                      leaveType,
                      startDate,
                      endDate,
                      duration: startDate && endDate ? Math.ceil(Math.abs(new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1 : 0,
                      reason: reason.substring(0, 100)
                    }}
                    position="inline"
                    onSuggestionApplied={(suggestion) => {
                      console.log('Applied suggestion:', suggestion);
                      // Handle suggestion application if needed
                    }}
                  />
                </Box>
              )}
            </form>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} color="inherit">Cancel</Button>
          {dialogContent === 'requestLeave' && (
            <Button onClick={handleLeaveSubmit} color="primary" variant="contained">
              Submit Request
            </Button>
          )}
        </DialogActions>
      </Dialog>


    </>
  );
};

export default NewUserDashboard;
