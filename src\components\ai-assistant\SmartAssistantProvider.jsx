/**
 * Smart Assistant Provider
 * Manages proactive AI suggestions and notifications
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Box, Snackbar, Portal } from '@mui/material';
import SmartAssistantNotification from './SmartAssistantNotification';
import smartAssistantService from '../../Services/SmartAssistantService';
import { toast } from 'react-toastify';

const SmartAssistantContext = createContext();

export const useSmartAssistant = () => {
  const context = useContext(SmartAssistantContext);
  if (!context) {
    throw new Error('useSmartAssistant must be used within a SmartAssistantProvider');
  }
  return context;
};

const SmartAssistantProvider = ({ children }) => {
  const [insights, setInsights] = useState([]);
  const [preferences, setPreferences] = useState(null);
  const [isEnabled, setIsEnabled] = useState(true);
  const [lastAnalysis, setLastAnalysis] = useState(null);

  // Load initial data
  useEffect(() => {
    loadPreferences();
    loadActiveInsights();
  }, []);

  // Periodic analysis trigger
  useEffect(() => {
    if (!isEnabled) return;

    const interval = setInterval(() => {
      triggerPeriodicAnalysis();
    }, 5 * 60 * 1000); // Every 5 minutes

    return () => clearInterval(interval);
  }, [isEnabled]);

  const loadPreferences = async () => {
    try {
      const response = await smartAssistantService.getPreferences();
      setPreferences(response.data);
    } catch (error) {
      console.error('Error loading preferences:', error);
    }
  };

  const loadActiveInsights = async () => {
    try {
      const response = await smartAssistantService.getInsights({ 
        status: 'active',
        limit: 5 
      });
      setInsights(response.data.insights || []);
    } catch (error) {
      console.error('Error loading insights:', error);
    }
  };

  const triggerPeriodicAnalysis = async () => {
    try {
      // Don't trigger too frequently
      if (lastAnalysis && Date.now() - lastAnalysis < 10 * 60 * 1000) {
        return; // Wait at least 10 minutes between analyses
      }

      const result = await smartAssistantService.analyzeAndSuggest();
      
      if (result.data.suggestions.length > 0) {
        setLastAnalysis(Date.now());
        await loadActiveInsights(); // Refresh insights
      }
    } catch (error) {
      console.error('Error in periodic analysis:', error);
    }
  };

  const trackOperation = useCallback(async (operation, resourceType, resourceId, metadata) => {
    if (!isEnabled) return;

    try {
      await smartAssistantService.trackOperation(operation, resourceType, resourceId, metadata);
      
      // Trigger analysis for high-impact operations
      const highImpactOps = ['USER_UPDATE', 'TASK_CREATE', 'LEAVE_REQUEST_CREATE', 'JOB_CREATE'];
      if (highImpactOps.includes(operation)) {
        setTimeout(() => {
          triggerPeriodicAnalysis();
        }, 3000); // 3-second delay
      }
    } catch (error) {
      console.error('Error tracking operation:', error);
    }
  }, [isEnabled]);

  const dismissInsight = useCallback(async (insightId) => {
    try {
      await smartAssistantService.dismissInsight(insightId);
      setInsights(prev => prev.filter(insight => insight.id !== insightId));
    } catch (error) {
      console.error('Error dismissing insight:', error);
      toast.error('Failed to dismiss suggestion');
    }
  }, []);

  const provideFeedback = useCallback(async (insightId, rating, comment) => {
    try {
      await smartAssistantService.provideFeedback(insightId, rating, comment);
      
      // Update local state
      setInsights(prev => prev.map(insight => 
        insight.id === insightId 
          ? { ...insight, status: rating === 'up' ? 'acted_upon' : 'dismissed' }
          : insight
      ));

      // Show appropriate message
      if (rating === 'up') {
        toast.success('Thanks for the positive feedback!');
      } else {
        toast.info('Thanks for the feedback. I\'ll learn from this.');
      }
    } catch (error) {
      console.error('Error providing feedback:', error);
      toast.error('Failed to submit feedback');
    }
  }, []);

  const updatePreferences = useCallback(async (newPreferences) => {
    try {
      const response = await smartAssistantService.updatePreferences(newPreferences);
      setPreferences(response.data);
      toast.success('Preferences updated successfully');
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast.error('Failed to update preferences');
    }
  }, []);

  const triggerAnalysis = useCallback(async () => {
    try {
      const result = await smartAssistantService.analyzeAndSuggest();
      setLastAnalysis(Date.now());
      
      if (result.data.suggestions.length > 0) {
        await loadActiveInsights();
        toast.success(`Generated ${result.data.suggestions.length} new suggestions!`);
      } else {
        toast.info('No new suggestions at this time.');
      }
      
      return result;
    } catch (error) {
      console.error('Error triggering analysis:', error);
      toast.error('Failed to analyze patterns');
      throw error;
    }
  }, []);

  // Helper methods for common operations
  const trackUserUpdate = useCallback((userId, changes) => {
    return trackOperation('USER_UPDATE', 'USER', userId, { changes });
  }, [trackOperation]);

  const trackTaskCreation = useCallback((taskId, taskData) => {
    return trackOperation('TASK_CREATE', 'TASK', taskId, { taskData });
  }, [trackOperation]);

  const trackLeaveRequest = useCallback((leaveId, leaveData) => {
    return trackOperation('LEAVE_REQUEST_CREATE', 'LEAVE_REQUEST', leaveId, { leaveData });
  }, [trackOperation]);

  const trackJobPosting = useCallback((jobId, jobData) => {
    return trackOperation('JOB_CREATE', 'JOB', jobId, { jobData });
  }, [trackOperation]);

  const trackApplicationUpdate = useCallback((applicationId, status) => {
    return trackOperation('APPLICATION_UPDATE', 'APPLICATION', applicationId, { status });
  }, [trackOperation]);

  // Filter insights based on preferences
  const visibleInsights = insights.filter(insight => {
    if (!preferences) return true;
    return smartAssistantService.shouldShowSuggestion(insight, preferences);
  });

  const contextValue = {
    insights: visibleInsights,
    preferences,
    isEnabled,
    setIsEnabled,
    trackOperation,
    trackUserUpdate,
    trackTaskCreation,
    trackLeaveRequest,
    trackJobPosting,
    trackApplicationUpdate,
    dismissInsight,
    provideFeedback,
    updatePreferences,
    triggerAnalysis,
    loadActiveInsights
  };

  return (
    <SmartAssistantContext.Provider value={contextValue}>
      {children}
      
      {/* Render notifications */}
      {isEnabled && (
        <Portal>
          <Box
            sx={{
              position: 'fixed',
              top: 80,
              right: 20,
              zIndex: 9999,
              maxWidth: 400,
              maxHeight: '80vh',
              overflow: 'auto'
            }}
          >
            {visibleInsights.map((insight) => (
              <SmartAssistantNotification
                key={insight.id}
                insight={insight}
                onDismiss={dismissInsight}
                onFeedback={provideFeedback}
                autoHide={preferences?.frequency === 'low'}
                hideDelay={15000}
              />
            ))}
          </Box>
        </Portal>
      )}
    </SmartAssistantContext.Provider>
  );
};

export default SmartAssistantProvider;
