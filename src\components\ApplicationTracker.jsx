import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  CircularProgress,
  Alert,
  Divider,
  Chip,
  Grid,
  Card,
  CardContent,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,
  AccessTime as AccessTimeIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  HourglassEmpty as HourglassEmptyIcon
} from '@mui/icons-material';
import axios from 'axios';
import { format } from 'date-fns';

const ApplicationTracker = () => {
  const theme = useTheme();
  const [applicationId, setApplicationId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [applicationData, setApplicationData] = useState(null);

  const handleInputChange = (e) => {
    setApplicationId(e.target.value.trim());
    // Clear previous results when input changes
    if (applicationData) setApplicationData(null);
    if (error) setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!applicationId) {
      setError('Please enter an application ID');
      return;
    }

    setLoading(true);
    setError('');
    setApplicationData(null);

    try {
      console.log('Fetching application status for ID:', applicationId);
      const response = await axios.get(`http://localhost:5000/api/applications/status/${applicationId}`);
      console.log('Application status response:', response.data);

      if (response.data.success) {
        setApplicationData(response.data.application);
      } else {
        setError(response.data.message || 'Failed to retrieve application status');
      }
    } catch (err) {
      console.error('Error fetching application status:', err);
      setError(err.response?.data?.message || 'Failed to retrieve application status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get status color and icon
  const getStatusInfo = (status) => {
    switch (status) {
      case 'Approved':
        return {
          color: theme.palette.success.main,
          icon: <CheckCircleIcon />,
          label: 'Approved'
        };
      case 'Rejected':
        return {
          color: theme.palette.error.main,
          icon: <CancelIcon />,
          label: 'Rejected'
        };
      case 'Pending':
      default:
        return {
          color: theme.palette.warning.main,
          icon: <HourglassEmptyIcon />,
          label: 'Pending'
        };
    }
  };

  return (
    <Box sx={{ py: 4 }}>
      <Typography
        variant="h4"
        component="h2"
        align="center"
        gutterBottom
        fontWeight={700}
        sx={{ mb: 3 }}
      >
        Track Your Application
      </Typography>

      <Typography
        variant="body1"
        align="center"
        color="text.secondary"
        sx={{ mb: 4, maxWidth: 600, mx: 'auto' }}
      >
        Enter your application ID to check the current status of your job application.
      </Typography>

      <Paper
        component="form"
        onSubmit={handleSubmit}
        elevation={2}
        sx={{
          p: 3,
          maxWidth: 600,
          mx: 'auto',
          borderRadius: 2,
          mb: applicationData ? 4 : 0
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
          <TextField
            fullWidth
            label="Application ID"
            placeholder="e.g. 60f1a2b3c4d5e6f7g8h9i0j1"
            variant="outlined"
            value={applicationId}
            onChange={handleInputChange}
            error={!!error}
            helperText={error}
            InputProps={{
              sx: { borderRadius: 1 }
            }}
          />
          <Button
            variant="contained"
            color="primary"
            size="large"
            type="submit"
            disabled={loading || !applicationId}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SearchIcon />}
            sx={{
              height: 56,
              px: 3,
              whiteSpace: 'nowrap'
            }}
          >
            Track
          </Button>
        </Box>
      </Paper>

      {/* Application Status Result */}
      {applicationData && (
        <Card
          elevation={3}
          sx={{
            maxWidth: 600,
            mx: 'auto',
            borderRadius: 2,
            overflow: 'hidden',
            border: `1px solid ${theme.palette.divider}`
          }}
        >
          <Box
            sx={{
              p: 2,
              bgcolor: theme.palette.primary.main,
              color: 'white'
            }}
          >
            <Typography variant="h6" fontWeight={600}>
              Application Status
            </Typography>
          </Box>

          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Status
                  </Typography>
                  <Chip
                    icon={getStatusInfo(applicationData.status).icon}
                    label={getStatusInfo(applicationData.status).label}
                    sx={{
                      color: 'white',
                      bgcolor: getStatusInfo(applicationData.status).color,
                      fontWeight: 600
                    }}
                  />
                </Box>
                <Divider />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Applicant Name
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {applicationData.fullname}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Applied On
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {applicationData.appliedOn ? format(new Date(applicationData.appliedOn), 'PPP') : 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  Position
                </Typography>
                <Typography variant="body1" fontWeight={500}>
                  {applicationData.jobTitle} - {applicationData.position}
                </Typography>
              </Grid>

              {applicationData.feedback && (
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Feedback
                  </Typography>
                  <Alert
                    severity={applicationData.status === 'Approved' ? 'success' : applicationData.status === 'Rejected' ? 'error' : 'info'}
                    sx={{ mt: 1 }}
                  >
                    {applicationData.feedback}
                  </Alert>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ApplicationTracker;
