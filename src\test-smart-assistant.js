/**
 * Test file to verify Smart Assistant integration
 * This file can be used to test the smart assistant functionality
 */

import SmartAssistantService from './services/SmartAssistantService';

// Test the smart assistant service
const testSmartAssistant = async () => {
  try {
    console.log('Testing Smart Assistant Service...');
    
    const service = new SmartAssistantService();
    
    // Test activity tracking
    console.log('Testing activity tracking...');
    await service.trackActivity(
      'leave_request',
      'leave_management',
      'leave_request',
      'test-leave-id',
      { leaveType: 'vacation', duration: 5 },
      1000
    );
    
    // Test insights fetching
    console.log('Testing insights fetching...');
    const insights = await service.getInsights({
      category: 'leave_management',
      limit: 5
    });
    console.log('Insights:', insights);
    
    // Test statistics
    console.log('Testing statistics...');
    const stats = await service.getStatistics(30);
    console.log('Statistics:', stats);
    
    console.log('Smart Assistant Service test completed successfully!');
    
  } catch (error) {
    console.error('Smart Assistant Service test failed:', error);
  }
};

// Export for use in development
export default testSmartAssistant;

// Auto-run in development mode
if (process.env.NODE_ENV === 'development') {
  // Uncomment the line below to auto-test when this file is imported
  // testSmartAssistant();
}
