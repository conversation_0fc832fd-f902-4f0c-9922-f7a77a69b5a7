import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';
import {
  showSuccessToast,
  showErrorToast,
  showInfoToast,
  showWarningToast,
  showLoadingToast,
  dismissToast,
  TOAST_CATEGORIES
} from '../Utils/toastUtils';
import GEKDashboard from '../components/hr/GEKDashboard';
import GEKTaskAssignment from '../components/hr/GEKTaskAssignment';
import HRAttendanceManagement from '../components/hr/HRAttendanceManagement';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Work as WorkIcon,
  Assignment as AssignmentIcon,
  EventNote as EventNoteIcon,
  Assessment as AssessmentIcon,
  BarChart as BarChartIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Person as PersonIcon,
  Description as DescriptionIcon,
  Email as EmailIcon,
  AccessTime as AccessTimeIcon,
  Phone as PhoneIcon,
  CalendarToday as CalendarTodayIcon,
  LocationOn as LocationOnIcon,
  Business as BusinessIcon,
  School as SchoolIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  PlayArrow as PlayArrowIcon,
  Save as SaveIcon,
  Close as CloseIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  MoreVert as MoreVertIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  StarHalf as StarHalfIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  HourglassEmpty as HourglassEmptyIcon,
  Flag as FlagIcon,
  Code as CodeIcon,
  Refresh as RefreshIcon,
  HelpOutline as HelpOutlineIcon,
  SmartToy as SmartToyIcon,
  Timeline as TimelineIcon,
  Insights as InsightsIcon,
  AssignmentTurnedIn as AssignmentTurnedInIcon,
} from '@mui/icons-material';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Button,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Divider,
  IconButton,
  Tooltip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  LinearProgress,
  CircularProgress,
  AppBar,
  Toolbar
} from '@mui/material';
import api from '../Services/ApiService';
import DashboardLayout from '../components/layout/DashboardLayout';
import StatCard from '../components/dashboard/StatCard';
import DataTable from '../components/dashboard/DataTable';
import EvaluationHistory from '../components/EvaluationHistory';

const NewHRDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authChecking, setAuthChecking] = useState(true);

  // Function to handle section changes
  const handleSectionChange = (section) => {
    // Reset NLP processing result when navigating away from applications
    if (activeSection === 'applications' && section !== 'applications') {
      setNlpProcessingResult(null);
    }
    setActiveSection(section);
  };

  // Search handlers for each section
  const handleUserSearch = async (query) => {
    // Update the search query state
    setUserSearchQuery(query);

    // Clear any existing timeout
    if (userSearchTimeoutRef.current) {
      clearTimeout(userSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    userSearchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await api.get('/hr/users', {
          params: {
            role: 'user',
            search: query
          }
        });

        if (Array.isArray(response.data)) {
          setUsers(response.data);
        } else if (response.data && Array.isArray(response.data.users)) {
          setUsers(response.data.users);
        } else {
          console.warn('User search data is not in expected format:', response.data);
        }
      } catch (error) {
        console.error('Error searching users:', error);
        showErrorToast('Error searching users', TOAST_CATEGORIES.DATA, 'searchFailed');
      }
    }, 300); // 300ms delay for more responsive feel
  };

  const handleApplicationSearch = async (query) => {
    // Update the search query state
    setApplicationSearchQuery(query);

    // Clear any existing timeout
    if (applicationSearchTimeoutRef.current) {
      clearTimeout(applicationSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    applicationSearchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await api.get('/hr/applications', {
          params: { search: query }
        });

        let newApplications = [];
        if (Array.isArray(response.data)) {
          newApplications = response.data;
        } else if (response.data && Array.isArray(response.data.applications)) {
          newApplications = response.data.applications;
        } else {
          console.warn('Application search data is not in expected format:', response.data);
          return;
        }

        // Set the applications directly
        setApplications(newApplications);
      } catch (error) {
        console.error('Error searching applications:', error);
        showErrorToast('Error searching applications', TOAST_CATEGORIES.DATA, 'searchFailed');
      }
    }, 300); // 300ms delay for more responsive feel
  };

  const handleLeaveRequestSearch = async (query) => {
    // Update the search query state
    setLeaveRequestSearchQuery(query);

    // Clear any existing timeout
    if (leaveRequestSearchTimeoutRef.current) {
      clearTimeout(leaveRequestSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    leaveRequestSearchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await api.get('/hr/leave-requests', {
          params: { search: query }
        });

        if (Array.isArray(response.data)) {
          setLeaveRequests(response.data);
        } else if (response.data && Array.isArray(response.data.leaveRequests)) {
          setLeaveRequests(response.data.leaveRequests);
        } else {
          console.warn('Leave request search data is not in expected format:', response.data);
        }
      } catch (error) {
        console.error('Error searching leave requests:', error);
        showErrorToast('Error searching leave requests', TOAST_CATEGORIES.DATA, 'searchFailed');
      }
    }, 300); // 300ms delay for more responsive feel
  };

  const handleTaskSearch = async (query) => {
    // Update the search query state
    setTaskSearchQuery(query);

    // Clear any existing timeout
    if (taskSearchTimeoutRef.current) {
      clearTimeout(taskSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    taskSearchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await api.get('/tasks/hr', {
          params: { search: query }
        });

        if (Array.isArray(response.data)) {
          setTasks(response.data);
        } else if (response.data && Array.isArray(response.data.tasks)) {
          setTasks(response.data.tasks);
        } else {
          console.warn('Task search data is not in expected format:', response.data);
        }
      } catch (error) {
        console.error('Error searching tasks:', error);
        showErrorToast('Error searching tasks', TOAST_CATEGORIES.DATA, 'searchFailed');
      }
    }, 300); // 300ms delay for more responsive feel
  };

  const handleJobSearch = async (query) => {
    // Update the search query state
    setJobSearchQuery(query);

    // Clear any existing timeout
    if (jobSearchTimeoutRef.current) {
      clearTimeout(jobSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    jobSearchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await api.get('/hr/jobs', {
          params: { search: query }
        });

        if (Array.isArray(response.data)) {
          setJobs(response.data);
        } else if (response.data && Array.isArray(response.data.jobs)) {
          setJobs(response.data.jobs);
        } else {
          console.warn('Job search data is not in expected format:', response.data);
        }
      } catch (error) {
        console.error('Error searching jobs:', error);
        showErrorToast('Error searching jobs', TOAST_CATEGORIES.DATA, 'searchFailed');
      }
    }, 300); // 300ms delay for more responsive feel
  };

  const handleEvaluationSearch = async (query) => {
    // Update the search query state
    setEvaluationSearchQuery(query);

    // Clear any existing timeout
    if (evaluationSearchTimeoutRef.current) {
      clearTimeout(evaluationSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    evaluationSearchTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await api.get('/hr/evaluations', {
          params: { search: query }
        });

        // For evaluations, we're searching users since the evaluations page shows users
        const userResponse = await api.get('/hr/users', {
          params: {
            role: 'user',
            search: query
          }
        });

        if (Array.isArray(userResponse.data)) {
          setUsers(userResponse.data);
        } else if (userResponse.data && Array.isArray(userResponse.data.users)) {
          setUsers(userResponse.data.users);
        } else {
          console.warn('User search data for evaluations is not in expected format:', userResponse.data);
        }
      } catch (error) {
        console.error('Error searching evaluations:', error);
        showErrorToast('Error searching evaluations', TOAST_CATEGORIES.DATA, 'searchFailed');
      }
    }, 300); // 300ms delay for more responsive feel
  };
  const [users, setUsers] = useState([]);
  const [applications, setApplications] = useState([]);
  const [leaveRequests, setLeaveRequests] = useState([]);
  const [jobs, setJobs] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalApplications: 0,
    totalJobs: 0,
    pendingLeaveRequests: 0,
    activeTasks: 0,
    completedTasks: 0,
  });
  const [loading, setLoading] = useState(true);
  const [openJobDialog, setOpenJobDialog] = useState(false);
  const [jobDialogType, setJobDialogType] = useState(null);
  const [selectedJob, setSelectedJob] = useState(null);
  const [jobData, setJobData] = useState({
    title: '',
    description: '',
    location: '',
    jobType: 'Full-Time',
    academicLevel: 'Bachelor',
    requirements: '',
    responsibilities: '',
    endDate: null
  });

  // Task management state
  const [openTaskDialog, setOpenTaskDialog] = useState(false);
  const [taskDialogType, setTaskDialogType] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [taskData, setTaskData] = useState({
    title: '',
    description: '',
    assignedTo: '',
    priority: 'Medium',
    category: 'General',
    deadline: '',
    status: 'Not Started'
  });

  // Application management state
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [openNLPReportDialog, setOpenNLPReportDialog] = useState(false);
  const [processingNLP, setProcessingNLP] = useState(false);
  const [processingMessage, setProcessingMessage] = useState('');
  const [nlpProcessingResult, setNlpProcessingResult] = useState(null); // 'success', 'failed', or null
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackAction, setFeedbackAction] = useState('');
  const [feedbackText, setFeedbackText] = useState('');
  const [selectedApplicationId, setSelectedApplicationId] = useState(null);

  // Evaluation management state
  const [showEvaluationHistory, setShowEvaluationHistory] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  // User profile dialog state
  const [showUserProfileDialog, setShowUserProfileDialog] = useState(false);
  const [selectedUserProfile, setSelectedUserProfile] = useState(null);

  // Component-specific search timeout references
  const userSearchTimeoutRef = useRef(null);
  const applicationSearchTimeoutRef = useRef(null);
  const leaveRequestSearchTimeoutRef = useRef(null);
  const taskSearchTimeoutRef = useRef(null);
  const jobSearchTimeoutRef = useRef(null);
  const evaluationSearchTimeoutRef = useRef(null);

  // State for search queries
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [applicationSearchQuery, setApplicationSearchQuery] = useState('');
  const [leaveRequestSearchQuery, setLeaveRequestSearchQuery] = useState('');
  const [taskSearchQuery, setTaskSearchQuery] = useState('');
  const [jobSearchQuery, setJobSearchQuery] = useState('');
  const [evaluationSearchQuery, setEvaluationSearchQuery] = useState('');

  // Cleanup function to clear timeouts when component unmounts
  useEffect(() => {
    return () => {
      if (userSearchTimeoutRef.current) clearTimeout(userSearchTimeoutRef.current);
      if (applicationSearchTimeoutRef.current) clearTimeout(applicationSearchTimeoutRef.current);
      if (leaveRequestSearchTimeoutRef.current) clearTimeout(leaveRequestSearchTimeoutRef.current);
      if (taskSearchTimeoutRef.current) clearTimeout(taskSearchTimeoutRef.current);
      if (jobSearchTimeoutRef.current) clearTimeout(jobSearchTimeoutRef.current);
      if (evaluationSearchTimeoutRef.current) clearTimeout(evaluationSearchTimeoutRef.current);
    };
  }, []);

  // Check authentication on component mount - instant validation
  useEffect(() => {
    setAuthChecking(true);
    const token = localStorage.getItem('token');

    if (!token) {
      showErrorToast('You must be logged in to access this page', TOAST_CATEGORIES.AUTH, 'unauthorized');
      navigate('/', { replace: true });
      return;
    }

    try {
      // Quick token validation without API call
      const decoded = jwtDecode(token);

      // Check if token is expired
      if (decoded.exp * 1000 <= Date.now()) {
        showErrorToast('Session expired. Please log in again.', TOAST_CATEGORIES.AUTH, 'failed');
        localStorage.removeItem('token');
        navigate('/', { replace: true });
        return;
      }

      // Check role authorization
      if (decoded.role !== 'hr') {
        showErrorToast('You do not have permission to access this page', TOAST_CATEGORIES.AUTH, 'failed');
        navigate('/', { replace: true });
        return;
      }

      // Authentication successful - proceed immediately
      setIsAuthenticated(true);
      setAuthChecking(false);
      fetchDashboardData();

    } catch (error) {
      console.error('Token validation error:', error);
      showErrorToast('Authentication failed. Please log in again.', TOAST_CATEGORIES.AUTH, 'failed');
      localStorage.removeItem('token');
      navigate('/', { replace: true });
    }
  }, [navigate]);

  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      console.log('Fetching dashboard data...');

      // Fetch all data in parallel with better error handling
      const [usersRes, applicationsRes, jobsRes, leaveRequestsRes, tasksRes] = await Promise.allSettled([
        api.get('/hr/users', { params: { role: 'user' } }),
        api.get('/hr/applications'),
        api.get('/hr/jobs'),
        api.get('/hr/leave-requests'),
        api.get('/tasks/hr')
      ]);

      console.log('Dashboard data fetched:', {
        users: usersRes,
        applications: applicationsRes,
        jobs: jobsRes,
        leaveRequests: leaveRequestsRes,
        tasks: tasksRes
      });

      // Process users data
      if (usersRes.status === 'fulfilled') {
        const userData = usersRes.value.data;
        if (Array.isArray(userData)) {
          setUsers(userData);
        } else if (userData && Array.isArray(userData.users)) {
          setUsers(userData.users);
        } else {
          console.warn('Users data is not in expected format:', userData);
          // Don't reset users array if it already has data
          if (users.length === 0) {
            setUsers([]);
          }
        }
      } else {
        console.error('Failed to fetch users:', usersRes.reason);
        // Don't reset users array if it already has data
        if (users.length === 0) {
          showErrorToast('Failed to load users data', TOAST_CATEGORIES.DATA, 'loadFailed');
        }
      }

      // Process applications data
      if (applicationsRes.status === 'fulfilled') {
        const appData = applicationsRes.value.data;
        let newApplications = [];

        if (Array.isArray(appData)) {
          console.log('Applications data:', appData);
          newApplications = appData;
        } else if (appData && Array.isArray(appData.applications)) {
          console.log('Applications data (nested):', appData.applications);
          newApplications = appData.applications;
        } else {
          console.warn('Applications data is not in expected format:', appData);
          newApplications = [];
        }

        // Check for applications that have nlpProcessed=true but no nlpResults
        const applicationsToUpdate = [];
        newApplications.forEach(app => {
          if (app.nlpProcessed && (!app.nlpResults || Object.keys(app.nlpResults).length === 0)) {
            console.log(`Application ${app._id} has nlpProcessed=true but no nlpResults, will fetch details`);
            applicationsToUpdate.push(app._id);
          }
        });

        // Set applications with preserved references
        setApplications(prevApplications => {
          // If there are no previous applications, just use the new ones
          if (prevApplications.length === 0) {
            return newApplications;
          }

          // Create a map of existing applications by ID for quick lookup
          const existingAppsMap = {};
          prevApplications.forEach(app => {
            if (app._id) {
              existingAppsMap[app._id] = app;
            }
          });

          // Merge new applications with existing ones to preserve references
          return newApplications.map(newApp => {
            if (newApp._id && existingAppsMap[newApp._id]) {
              // Preserve the reference number and other important fields
              return {
                ...newApp,
                referenceNumber: existingAppsMap[newApp._id].referenceNumber || newApp.referenceNumber
              };
            }
            return newApp;
          });
        });

        // Fetch detailed application data for those with missing NLP results
        if (applicationsToUpdate.length > 0) {
          console.log(`Fetching detailed data for ${applicationsToUpdate.length} applications with missing NLP results`);

          // Fetch each application's details in parallel
          Promise.allSettled(
            applicationsToUpdate.map(appId =>
              api.get(`/hr/applications/${appId}`)
                .then(response => {
                  console.log(`Fetched detailed data for application ${appId}:`, response.data);
                  return response.data;
                })
                .catch(error => {
                  console.error(`Error fetching application ${appId}:`, error);
                  throw error;
                })
            )
          ).then(results => {
            // Update applications with the fetched detailed data
            const successfulResults = results
              .filter(result => result.status === 'fulfilled')
              .map(result => result.value);

            if (successfulResults.length > 0) {
              setApplications(prevApplications => {
                return prevApplications.map(app => {
                  const updatedApp = successfulResults.find(updated => updated._id === app._id);
                  return updatedApp || app;
                });
              });
              console.log(`Updated ${successfulResults.length} applications with detailed data`);
            }
          });
        }
      } else {
        console.error('Failed to fetch applications:', applicationsRes.reason);
        showErrorToast('Failed to load applications data', TOAST_CATEGORIES.DATA, 'loadFailed');
        setApplications([]);
      }

      // Process jobs data
      if (jobsRes.status === 'fulfilled') {
        const jobData = jobsRes.value.data;
        if (Array.isArray(jobData)) {
          setJobs(jobData);
        } else if (jobData && Array.isArray(jobData.jobs)) {
          setJobs(jobData.jobs);
        } else {
          console.warn('Jobs data is not in expected format:', jobData);
          setJobs([]);
        }
      } else {
        console.error('Failed to fetch jobs:', jobsRes.reason);
        showErrorToast('Failed to load jobs data', TOAST_CATEGORIES.DATA, 'loadFailed');
        setJobs([]);
      }

      // Process leave requests data
      if (leaveRequestsRes.status === 'fulfilled') {
        const leaveData = leaveRequestsRes.value.data;
        if (Array.isArray(leaveData)) {
          setLeaveRequests(leaveData);
        } else if (leaveData && Array.isArray(leaveData.leaveRequests)) {
          setLeaveRequests(leaveData.leaveRequests);
        } else {
          console.warn('Leave requests data is not in expected format:', leaveData);
          setLeaveRequests([]);
        }
      } else {
        console.error('Failed to fetch leave requests:', leaveRequestsRes.reason);
        showErrorToast('Failed to load leave requests data', TOAST_CATEGORIES.DATA, 'loadFailed');
        setLeaveRequests([]);
      }

      // Process tasks data
      if (tasksRes.status === 'fulfilled') {
        const taskData = tasksRes.value.data;
        let processedTasks = [];

        if (Array.isArray(taskData)) {
          processedTasks = taskData;
        } else if (taskData && Array.isArray(taskData.tasks)) {
          processedTasks = taskData.tasks;
        } else {
          console.warn('Tasks data is not in expected format:', taskData);
        }

        // Process each task to ensure assignedTo is properly formatted
        const formattedTasks = processedTasks.map(task => {
          // Create a formatted task object
          const formattedTask = { ...task };

          // Handle assignedTo if it's an object
          if (formattedTask.assignedTo && typeof formattedTask.assignedTo === 'object') {
            formattedTask.assignedToName = formattedTask.assignedTo.name || formattedTask.assignedTo.email || 'Unassigned';
          }

          return formattedTask;
        });

        setTasks(formattedTasks);

        // Calculate task stats
        const activeTasks = processedTasks.filter(task => task.status !== 'Completed').length;
        const completedTasks = processedTasks.filter(task => task.status === 'Completed').length;

        // Calculate leave request stats
        const pendingLeaveRequests = leaveRequestsRes.status === 'fulfilled' ?
          (Array.isArray(leaveRequestsRes.value.data) ?
            leaveRequestsRes.value.data.filter(req => req.status === 'Pending').length :
            (leaveRequestsRes.value.data && Array.isArray(leaveRequestsRes.value.data.leaveRequests) ?
              leaveRequestsRes.value.data.leaveRequests.filter(req => req.status === 'Pending').length :
              0)) :
          0;

        // Calculate user stats
        const totalUsers = usersRes.status === 'fulfilled' ?
          (Array.isArray(usersRes.value.data) ?
            usersRes.value.data.length :
            (usersRes.value.data && Array.isArray(usersRes.value.data.users) ?
              usersRes.value.data.users.length :
              0)) :
          0;

        // Calculate application stats
        const totalApplications = applicationsRes.status === 'fulfilled' ?
          (Array.isArray(applicationsRes.value.data) ?
            applicationsRes.value.data.length :
            (applicationsRes.value.data && Array.isArray(applicationsRes.value.data.applications) ?
              applicationsRes.value.data.applications.length :
              0)) :
          0;

        // Calculate job stats
        const totalJobs = jobsRes.status === 'fulfilled' ?
          (Array.isArray(jobsRes.value.data) ?
            jobsRes.value.data.length :
            (jobsRes.value.data && Array.isArray(jobsRes.value.data.jobs) ?
              jobsRes.value.data.jobs.length :
              0)) :
          0;

        // Update stats
        setStats({
          totalUsers,
          totalApplications,
          totalJobs,
          pendingLeaveRequests,
          activeTasks,
          completedTasks,
        });
      } else {
        console.error('Failed to fetch tasks:', tasksRes.reason);
        showErrorToast('Failed to load tasks data', TOAST_CATEGORIES.DATA, 'loadFailed');
        setTasks([]);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      showErrorToast('Failed to load dashboard data', TOAST_CATEGORIES.DATA, 'loadFailed');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    // Instant logout - no API calls needed
    localStorage.removeItem('token');
    showInfoToast('You have been logged out', TOAST_CATEGORIES.AUTH, 'logout');
    navigate('/', { replace: true });
  };

  // Handle opening the evaluation history modal
  const handleOpenEvaluation = (user) => {
    setSelectedEmployee({
      id: user._id,
      name: user.name,
      job: user.job
    });
    setShowEvaluationHistory(true);
  };

  // Handle closing the evaluation history modal
  const handleCloseEvaluation = () => {
    setShowEvaluationHistory(false);
    setSelectedEmployee(null);
  };

  // Handle closing the user profile dialog
  const handleCloseUserProfile = () => {
    setShowUserProfileDialog(false);
    setSelectedUserProfile(null);
  };

  // Placeholder for leave request handlers - actual implementations are below

  // Handle opening job dialog
  const handleOpenJobDialog = (type, job = null) => {
    setJobDialogType(type);

    if (type === 'add') {
      // Reset form for new job
      setJobData({
        title: '',
        description: '',
        location: '',
        jobType: 'Full-Time',
        academicLevel: 'Bachelor',
        requirements: '',
        responsibilities: '',
        endDate: ''
      });
    } else if (type === 'edit' && job) {
      // Format the end date for the form (YYYY-MM-DD)
      let formattedEndDate = '';
      if (job.endDate) {
        const endDate = new Date(job.endDate);
        formattedEndDate = endDate.toISOString().split('T')[0];
      }

      // Populate form with existing job data
      setJobData({
        title: job.title || '',
        description: job.description || '',
        location: job.location || '',
        jobType: job.jobType || 'Full-Time',
        academicLevel: job.academicLevel || 'Bachelor',
        requirements: Array.isArray(job.requirements) ? job.requirements.join(', ') : job.requirements || '',
        responsibilities: Array.isArray(job.responsibilities) ? job.responsibilities.join(', ') : job.responsibilities || '',
        endDate: formattedEndDate
      });
      setSelectedJob(job);
    } else if (type === 'view' && job) {
      setSelectedJob(job);
    }

    setOpenJobDialog(true);
  };

  // Handle closing job dialog
  const handleCloseJobDialog = () => {
    setOpenJobDialog(false);
    setJobDialogType(null);
    setSelectedJob(null);
  };

  // Handle job form submission
  const handleJobFormSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!jobData.title || !jobData.description || !jobData.location) {
      showErrorToast('Please fill in all required fields', TOAST_CATEGORIES.VALIDATION, 'missingFields');
      return;
    }

    // Format requirements and responsibilities as arrays
    const formattedJobData = {
      ...jobData,
      requirements: jobData.requirements.split(',').map(item => item.trim()).filter(item => item),
      responsibilities: jobData.responsibilities.split(',').map(item => item.trim()).filter(item => item),
      // Handle endDate properly - ensure it's a valid date string or null
      endDate: jobData.endDate && jobData.endDate.trim() !== '' ? jobData.endDate : null
    };

    console.log('Submitting job data:', formattedJobData);

    try {
      let response;

      if (jobDialogType === 'edit' && selectedJob) {
        // Update existing job
        response = await api.put(`/hr/jobs/${selectedJob._id}`, formattedJobData);
        showSuccessToast('Job updated successfully!', TOAST_CATEGORIES.DATA, 'saveSuccess');

        // Update the job in the local state
        setJobs(jobs.map(job =>
          job._id === selectedJob._id ? { ...job, ...formattedJobData } : job
        ));
      } else {
        // Create new job
        response = await api.post('/hr/jobs', formattedJobData);
        showSuccessToast('Job created successfully!', TOAST_CATEGORIES.DATA, 'saveSuccess');

        // Add the new job to the local state
        const newJob = response.data;
        setJobs([...jobs, newJob]);
      }

      // Close the dialog
      handleCloseJobDialog();

      // Refresh dashboard data
      fetchDashboardData();
    } catch (error) {
      console.error('Error saving job:', error);

      // More detailed error logging
      if (error.response) {
        console.error('Error response:', error.response.data);
        console.error('Status code:', error.response.status);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }

      showErrorToast('Failed to save job: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.DATA, 'saveFailed');
    }
  };

  // Handle deleting a job
  const handleDeleteJob = (id) => {
    if (window.confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
      api.delete(`/hr/jobs/${id}`)
        .then(() => {
          showSuccessToast('Job deleted successfully!', TOAST_CATEGORIES.DATA, 'deleted');

          // Update the local state to remove the deleted job
          setJobs(jobs.filter(job => job._id !== id));

          // Refresh dashboard data to update stats
          fetchDashboardData();
        })
        .catch(error => {
          console.error('Error deleting job:', error);

          // More detailed error logging
          if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Status code:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }

          showErrorToast('Failed to delete job: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.DATA, 'deleteFailed');
        });
    }
  };

  // Generate a text version of the NLP report
  const generateNLPReportText = (application) => {
    if (!application || !application.nlpResults) {
      return 'No NLP results available for this application';
    }

    const nlp = application.nlpResults;
    const matchScore = application.matchScore !== undefined ?
      Math.round(application.matchScore) :
      (nlp.matchScore !== undefined ? Math.round(nlp.matchScore) : 'N/A');

    // Calculate a qualitative assessment based on match score
    let qualitativeAssessment = '';
    if (matchScore >= 90) {
      qualitativeAssessment = 'Excellent match for the position. Candidate exceeds most requirements.';
    } else if (matchScore >= 75) {
      qualitativeAssessment = 'Strong match for the position. Candidate meets most key requirements.';
    } else if (matchScore >= 60) {
      qualitativeAssessment = 'Good match for the position. Candidate meets many requirements but has some gaps.';
    } else if (matchScore >= 40) {
      qualitativeAssessment = 'Moderate match for the position. Candidate has relevant skills but significant gaps exist.';
    } else {
      qualitativeAssessment = 'Limited match for the position. Candidate does not meet many key requirements.';
    }

    // Format the report with clear sections and better organization
    let report = `COMPREHENSIVE NLP ANALYSIS REPORT\n`;
    report += `=================================\n\n`;

    // CANDIDATE INFORMATION SECTION
    report += `CANDIDATE INFORMATION\n`;
    report += `--------------------\n`;
    report += `Name: ${application.fullname}\n`;
    report += `Email: ${application.email}\n`;
    report += `Phone: ${application.phone || 'N/A'}\n`;
    report += `Position Applied For: ${application.position || 'N/A'}\n`;
    report += `Job ID: ${application.jobId || 'N/A'}\n`;
    report += `Application Date: ${new Date(application.createdAt).toLocaleDateString()}\n\n`;

    // EXECUTIVE SUMMARY SECTION
    report += `EXECUTIVE SUMMARY\n`;
    report += `----------------\n`;
    report += `Overall Match Score: ${matchScore}%\n`;
    report += `Assessment: ${qualitativeAssessment}\n\n`;

    // Add analysis summary if available
    if (nlp.analysis) {
      report += `Analysis: ${nlp.analysis}\n\n`;
    }

    // SKILLS ASSESSMENT SECTION
    report += `SKILLS ASSESSMENT\n`;
    report += `-----------------\n`;
    if (nlp.skills && nlp.skills.length > 0) {
      // Group skills by category if possible
      const technicalSkills = nlp.skills.filter(skill =>
        /\b(programming|software|technical|language|framework|database|cloud|development|engineering)\b/i.test(skill)
      );

      const softSkills = nlp.skills.filter(skill =>
        /\b(communication|teamwork|leadership|management|organization|problem.solving|creativity|adaptability)\b/i.test(skill)
      );

      const otherSkills = nlp.skills.filter(skill =>
        !technicalSkills.includes(skill) && !softSkills.includes(skill)
      );

      if (technicalSkills.length > 0) {
        report += `Technical Skills:\n`;
        technicalSkills.sort().forEach(skill => {
          report += `- ${skill}\n`;
        });
        report += '\n';
      }

      if (softSkills.length > 0) {
        report += `Soft Skills:\n`;
        softSkills.sort().forEach(skill => {
          report += `- ${skill}\n`;
        });
        report += '\n';
      }

      if (otherSkills.length > 0) {
        report += `Other Skills:\n`;
        otherSkills.sort().forEach(skill => {
          report += `- ${skill}\n`;
        });
        report += '\n';
      }
    } else {
      report += 'No skills detected in the CV\n\n';
    }

    // EDUCATION SECTION
    report += `EDUCATION BACKGROUND\n`;
    report += `-------------------\n`;
    if (nlp.education && nlp.education.length > 0) {
      nlp.education.forEach(edu => {
        report += `• ${edu}\n`;
      });
    } else {
      report += 'No education details detected in the CV\n';
    }
    report += '\n';

    // EXPERIENCE SECTION
    report += `PROFESSIONAL EXPERIENCE\n`;
    report += `----------------------\n`;
    if (nlp.experience && nlp.experience.length > 0) {
      nlp.experience.forEach(exp => {
        report += `• ${exp}\n`;
      });
    } else {
      report += 'No experience details detected in the CV\n';
    }
    report += '\n';

    // JOB REQUIREMENTS MATCH SECTION
    report += `JOB REQUIREMENTS MATCH ANALYSIS\n`;
    report += `------------------------------\n`;
    if (nlp.requirementsMatch) {
      const total = nlp.requirementsMatch.matched.length + nlp.requirementsMatch.missing.length;
      const matchPercentage = Math.round((nlp.requirementsMatch.matched.length / total) * 100);

      report += `Requirements Match Rate: ${matchPercentage}% (${nlp.requirementsMatch.matched.length} of ${total})\n\n`;

      report += `Matched Requirements:\n`;
      if (nlp.requirementsMatch.matched.length > 0) {
        nlp.requirementsMatch.matched.forEach(req => {
          report += `✓ ${req}\n`;
        });
      } else {
        report += 'No matched requirements\n';
      }
      report += '\n';

      report += `Missing Requirements:\n`;
      if (nlp.requirementsMatch.missing.length > 0) {
        nlp.requirementsMatch.missing.forEach(req => {
          report += `✗ ${req}\n`;
        });
      } else {
        report += 'No missing requirements\n';
      }
      report += '\n';
    } else {
      report += 'No requirements match data available\n\n';
    }

    // RECOMMENDATION SECTION
    report += `RECOMMENDATION\n`;
    report += `--------------\n`;
    if (matchScore >= 75) {
      report += `Recommendation: Proceed to interview stage. Candidate shows strong alignment with position requirements.\n`;
    } else if (matchScore >= 50) {
      report += `Recommendation: Consider for interview with focus on addressing skill gaps. Candidate shows potential but has some missing requirements.\n`;
    } else {
      report += `Recommendation: Not recommended for this position. Consider for other roles that better match candidate's profile or keep resume on file for future opportunities.\n`;
    }
    report += '\n';

    // Add specific areas to explore in interview if match score is high enough
    if (matchScore >= 50) {
      report += `Interview Focus Areas:\n`;

      // If there are missing requirements, suggest focusing on those
      if (nlp.requirementsMatch && nlp.requirementsMatch.missing && nlp.requirementsMatch.missing.length > 0) {
        report += `- Verify candidate's capabilities regarding: ${nlp.requirementsMatch.missing.slice(0, 3).join(', ')}\n`;
      }

      // Suggest exploring experience depth
      if (nlp.experience && nlp.experience.length > 0) {
        report += `- Explore depth of experience in: ${nlp.experience[0].split(' at ')[0]}\n`;
      }

      // Suggest technical assessment if technical skills are present
      const technicalSkillsPresent = nlp.skills && nlp.skills.some(skill =>
        /\b(programming|software|technical|language|framework|database|cloud|development|engineering)\b/i.test(skill)
      );

      if (technicalSkillsPresent) {
        report += `- Consider technical assessment to verify proficiency in claimed technical skills\n`;
      }
    }

    report += '\n=================================================\n';
    report += `Report generated on ${new Date().toLocaleString()}\n`;
    report += `This report was automatically generated using NLP analysis and should be used as a guide, not as the sole basis for hiring decisions.`;

    return report;
  };

  // Handle viewing a CV
  const handleViewCV = (application) => {
    console.log('Viewing CV for application:', application);

    // Check if the application has a CV field
    if (!application.cv) {
      showErrorToast('No CV file found for this application', TOAST_CATEGORIES.DATA, 'notFound');
      return;
    }

    // Format the CV path correctly
    let cvPath = application.cv;

    // If the path already includes 'uploads/', remove it to avoid duplication
    if (cvPath.startsWith('uploads/')) {
      cvPath = cvPath.substring(8);
    }

    console.log('Opening CV with path:', cvPath);

    // Use hardcoded API URL (same as in ApiService.jsx)
    const apiUrl = "http://localhost:5000";
    const url = `${apiUrl}/uploads/${cvPath}`;
    console.log('Full URL:', url);

    // Create a temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';

    // Simulate a click on the link
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
  };

  // Handle approving a leave request
  const handleApproveLeaveRequest = (id) => {
    // Find the current request to check its status
    const request = leaveRequests.find(req => req._id === id);
    const currentStatus = request ? request.status : null;

    // Customize confirmation message based on current status
    let confirmMessage = 'Are you sure you want to approve this leave request?';
    if (currentStatus === 'Rejected') {
      confirmMessage = 'This request is currently rejected. Are you sure you want to change it to approved?';
    }

    if (window.confirm(confirmMessage)) {
      // Use the general status update endpoint
      api.put(`/hr/leave-requests/${id}/status`, { status: 'Approved' })
        .then((response) => {
          console.log('Leave request approval response:', response.data);
          toast.success('Leave request approved successfully!');

          // Update the local state to reflect the change
          setLeaveRequests(leaveRequests.map(req =>
            req._id === id ? { ...req, status: 'Approved' } : req
          ));

          // Refresh dashboard data to update stats
          fetchDashboardData();
        })
        .catch(error => {
          console.error('Error approving leave request:', error);

          // More detailed error logging
          if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Status code:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }

          toast.error('Failed to approve leave request: ' + (error.response?.data?.message || error.message || 'Unknown error'));
        });
    }
  };

  // Handle rejecting a leave request
  const handleRejectLeaveRequest = (id) => {
    // Find the current request to check its status
    const request = leaveRequests.find(req => req._id === id);
    const currentStatus = request ? request.status : null;

    // Customize confirmation message based on current status
    let confirmMessage = 'Are you sure you want to reject this leave request?';
    if (currentStatus === 'Approved') {
      confirmMessage = 'This request is currently approved. Are you sure you want to change it to rejected?';
    }

    if (window.confirm(confirmMessage)) {
      // Use the general status update endpoint
      api.put(`/hr/leave-requests/${id}/status`, { status: 'Rejected' })
        .then((response) => {
          console.log('Leave request rejection response:', response.data);
          toast.success('Leave request rejected successfully!');

          // Update the local state to reflect the change
          setLeaveRequests(leaveRequests.map(req =>
            req._id === id ? { ...req, status: 'Rejected' } : req
          ));

          // Refresh dashboard data to update stats
          fetchDashboardData();
        })
        .catch(error => {
          console.error('Error rejecting leave request:', error);

          // More detailed error logging
          if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Status code:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }

          toast.error('Failed to reject leave request: ' + (error.response?.data?.message || error.message || 'Unknown error'));
        });
    }
  };

  // Handle deleting a leave request
  const handleDeleteLeaveRequest = (id) => {
    if (window.confirm('Are you sure you want to delete this leave request? This action cannot be undone.')) {
      api.delete(`/hr/leave-requests/${id}`)
        .then(() => {
          showSuccessToast('Leave request deleted successfully!', TOAST_CATEGORIES.DATA, 'deleted');

          // Update the local state to remove the deleted leave request
          setLeaveRequests(leaveRequests.filter(req => req._id !== id));

          // Refresh dashboard data to update stats
          fetchDashboardData();
        })
        .catch(error => {
          console.error('Error deleting leave request:', error);

          // More detailed error logging
          if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Status code:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }

          showErrorToast('Failed to delete leave request: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.DATA, 'deleteFailed');
        });
    }
  };

  // Handle running NLP on a CV
  const handleRunNLP = (application) => {
    console.log('Running NLP for application:', application);

    if (!application.cv) {
      showErrorToast('No CV file found for this application', TOAST_CATEGORIES.NLP, 'failed');
      return;
    }

    if (!application.jobId) {
      showErrorToast('No job associated with this application. Please assign a job first.', TOAST_CATEGORIES.NLP, 'failed');
      return;
    }

    // Show processing notification
    showInfoToast('Starting NLP processing...', TOAST_CATEGORIES.NLP, 'started', {
      closeButton: true
    });

    // Show a persistent loading toast that will stay visible during processing
    const loadingToastId = showLoadingToast('Processing CV with NLP. This may take a moment...', {
      autoClose: false,
      closeButton: false,
      isLoading: true
    });

    // Set processing state to show a message in the UI
    setProcessingNLP(true);
    setProcessingMessage(`Processing CV for ${application.fullname}. Please wait...`);
    setNlpProcessingResult(null); // Reset the result state

    // Format the CV path correctly
    let cvPath = application.cv;

    // If the path already includes 'uploads/', remove it to avoid duplication
    if (cvPath.startsWith('uploads/')) {
      cvPath = cvPath.substring(8);
    }

    console.log('Running NLP with CV path:', cvPath);

    // Run NLP on the CV
    api.post(`/api/hr/applications/${application._id}/nlp`, {
      cvPath: cvPath,
      jobId: application.jobId,
      applicationId: application._id
    })
      .then(response => {
        console.log('NLP response:', response.data);
        dismissToast(loadingToastId);

        // Reset processing state
        setProcessingNLP(false);
        setProcessingMessage('');
        setNlpProcessingResult('success'); // Set success result

        // Clear the result message after 5 seconds
        setTimeout(() => {
          setNlpProcessingResult(null);
        }, 5000);

        showSuccessToast('NLP processing completed successfully!', TOAST_CATEGORIES.NLP, 'completed', {
          closeButton: true
        });

        // Update the application in the local state with the match score
        const updatedApplication = {
          ...application,
          nlpProcessed: true,
          nlpResults: response.data,
          matchScore: response.data.matchScore || 0
        };

        console.log('Updated application with NLP results:', updatedApplication);

        setApplications(applications.map(app =>
          app._id === application._id ? updatedApplication : app
        ));

        // Close the NLP report dialog if it's open
        if (openNLPReportDialog) {
          setOpenNLPReportDialog(false);
        }

        // If the dialog is open, update the selected application
        if (selectedApplication && selectedApplication._id === application._id) {
          setSelectedApplication(updatedApplication);
        }

        // Show a notification to view the NLP report
        showSuccessToast('NLP processing completed. Click "View NLP Report" to see results.', TOAST_CATEGORIES.NLP, 'completed', {
          closeButton: true
        });
      })
      .catch(error => {
        console.error('Error running NLP:', error);
        dismissToast(loadingToastId);

        // Reset processing state
        setProcessingNLP(false);
        setProcessingMessage('');
        setNlpProcessingResult('failed'); // Set failed result

        // Clear the result message after 5 seconds
        setTimeout(() => {
          setNlpProcessingResult(null);
        }, 5000);

        // More detailed error logging
        if (error.response) {
          console.error('Error response:', error.response.data);
          console.error('Status code:', error.response.status);

          // If there's a detailed error message about the file path
          if (error.response.data && error.response.data.paths) {
            console.error('Path details:', error.response.data.paths);
          }
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else {
          console.error('Error setting up request:', error.message);
        }

        showErrorToast('Failed to process CV: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.NLP, 'failed', {
          closeButton: true
        });
      });
  };

  // Handle viewing NLP report
  const handleViewNLPReport = (application) => {
    console.log('Viewing NLP report for application:', application);

    // Check if NLP results exist by looking for nlpResults object
    if (!application.nlpResults || Object.keys(application.nlpResults).length === 0) {
      console.log('No NLP results found for application:', application._id);
      showInfoToast('Running NLP analysis...', TOAST_CATEGORIES.NLP, 'started', {
        closeButton: true
      });
      handleRunNLP(application);
      return;
    }

    // Create a dialog to show the NLP report
    setSelectedApplication(application);
    setOpenNLPReportDialog(true);

    // Show a notification that the NLP report is being viewed
    showInfoToast('Viewing NLP report', TOAST_CATEGORIES.NLP, 'reportViewed', {
      closeButton: true
    });
  };

  // Handle deleting an application
  const handleDeleteApplication = (id) => {
    if (window.confirm('Are you sure you want to delete this application? This action cannot be undone.')) {
      api.delete(`/api/hr/applications/${id}`)
        .then(() => {
          showSuccessToast('Application deleted successfully!', TOAST_CATEGORIES.DATA, 'deleted');

          // Update the local state to remove the deleted application
          setApplications(applications.filter(app => app._id !== id));

          // Refresh dashboard data to update stats
          fetchDashboardData();
        })
        .catch(error => {
          console.error('Error deleting application:', error);

          // More detailed error logging
          if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Status code:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }

          showErrorToast('Failed to delete application: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.DATA, 'deleteFailed');
        });
    }
  };

  // Handle opening feedback dialog for approve/reject
  const handleOpenFeedbackDialog = (action, applicationId) => {
    setFeedbackAction(action);
    setSelectedApplicationId(applicationId);
    setFeedbackText('');
    setFeedbackDialogOpen(true);
  };

  // Handle closing feedback dialog
  const handleCloseFeedbackDialog = () => {
    setFeedbackDialogOpen(false);
    setFeedbackAction('');
    setSelectedApplicationId(null);
    setFeedbackText('');
  };

  // Handle approving an application
  const handleApproveApplication = (id) => {
    handleOpenFeedbackDialog('approve', id);
  };

  // Handle rejecting an application
  const handleRejectApplication = (id) => {
    handleOpenFeedbackDialog('reject', id);
  };

  // Handle submitting application feedback and status change
  const handleSubmitFeedback = async () => {
    if (!selectedApplicationId) return;

    const status = feedbackAction === 'approve' ? 'Approved' : 'Rejected';

    try {
      const response = await api.put(`/hr/applications/${selectedApplicationId}/status`, {
        status,
        feedback: feedbackText
      });

      // Update the application in the local state
      setApplications(applications.map(app =>
        app._id === selectedApplicationId
          ? { ...app, status, feedback: feedbackText }
          : app
      ));

      showSuccessToast(
        `Application ${status.toLowerCase()} successfully!`,
        TOAST_CATEGORIES.ACTION,
        `application${status}`
      );

      // Close the dialog
      handleCloseFeedbackDialog();
    } catch (error) {
      console.error(`Error ${status.toLowerCase()} application:`, error);
      showErrorToast(
        `Failed to ${feedbackAction} application: ${error.response?.data?.message || error.message}`,
        TOAST_CATEGORIES.ACTION,
        'actionFailed'
      );
    }
  };

  // Handle opening task dialog
  const handleOpenTaskDialog = (type, task = null) => {
    setTaskDialogType(type);

    if (type === 'add') {
      // Reset form for new task
      setTaskData({
        title: '',
        description: '',
        assignedTo: '',
        priority: 'Medium',
        category: 'General',
        deadline: '',
        status: 'Not Started'
      });

      // Fetch users for assignment dropdown if not already loaded
      if (users.length === 0) {
        fetchUsers();
      }
    } else if (type === 'edit' && task) {
      // Populate form with existing task data
      setTaskData({
        title: task.title || '',
        description: task.description || '',
        assignedTo: task.assignedTo || '',
        priority: task.priority || 'Medium',
        category: task.category || 'General',
        deadline: task.deadline ? new Date(task.deadline).toISOString().split('T')[0] : '',
        status: task.status || 'Not Started'
      });
      setSelectedTask(task);

      // Fetch users for assignment dropdown if not already loaded
      if (users.length === 0) {
        fetchUsers();
      }
    } else if (type === 'view' && task) {
      setSelectedTask(task);
    }

    setOpenTaskDialog(true);
  };

  // Handle closing task dialog
  const handleCloseTaskDialog = () => {
    setOpenTaskDialog(false);
    setTaskDialogType(null);
    setSelectedTask(null);
  };

  // Fetch users for task assignment
  const fetchUsers = async () => {
    try {
      const response = await api.get('/api/hr/users');
      console.log('Fetched users:', response.data);

      // Ensure users is properly formatted
      let userData = response.data;
      if (Array.isArray(userData)) {
        setUsers(userData);
      } else if (userData && Array.isArray(userData.users)) {
        setUsers(userData.users);
      } else {
        setUsers([]);
        console.warn('User data is not in expected format:', userData);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      showErrorToast('Failed to fetch users for task assignment', TOAST_CATEGORIES.DATA, 'loadFailed');
    }
  };

  // Handle task form submission
  const handleTaskFormSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!taskData.title || !taskData.assignedTo || !taskData.deadline) {
      showErrorToast('Please fill in all required fields', TOAST_CATEGORIES.VALIDATION, 'missingFields');
      return;
    }

    // Ensure assignedTo is a single ID, not an array
    const formattedTaskData = {
      ...taskData,
      assignedTo: Array.isArray(taskData.assignedTo)
        ? taskData.assignedTo[0]
        : taskData.assignedTo
    };

    console.log('Submitting task data:', formattedTaskData);

    try {
      let response;

      if (taskDialogType === 'edit' && selectedTask) {
        // Update existing task
        response = await api.put(`/api/tasks/${selectedTask._id}`, formattedTaskData);
        showSuccessToast('Task updated successfully!', TOAST_CATEGORIES.TASK, 'updated');

        // Update the task in the local state
        setTasks(tasks.map(task =>
          task._id === selectedTask._id ? { ...task, ...formattedTaskData } : task
        ));
      } else {
        // Create new task
        response = await api.post('/api/tasks', formattedTaskData);
        showSuccessToast('Task created successfully!', TOAST_CATEGORIES.TASK, 'created');

        // Add the new task to the local state
        const newTask = response.data;
        setTasks([...tasks, newTask]);
      }

      // Close the dialog
      handleCloseTaskDialog();

      // Refresh dashboard data
      fetchDashboardData();
    } catch (error) {
      console.error('Error saving task:', error);

      // More detailed error logging
      if (error.response) {
        console.error('Error response:', error.response.data);
        console.error('Status code:', error.response.status);
      } else if (error.request) {
        console.error('No response received:', error.request);
      } else {
        console.error('Error setting up request:', error.message);
      }

      showErrorToast('Failed to save task: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.TASK, 'failed');
    }
  };

  // Handle deleting a task
  const handleDeleteTask = (id) => {
    if (window.confirm('Are you sure you want to delete this task? This action cannot be undone.')) {
      api.delete(`/api/tasks/${id}`)
        .then(() => {
          showSuccessToast('Task deleted successfully!', TOAST_CATEGORIES.TASK, 'deleted');

          // Update the local state to remove the deleted task
          setTasks(tasks.filter(task => task._id !== id));

          // Refresh dashboard data to update stats
          fetchDashboardData();
        })
        .catch(error => {
          console.error('Error deleting task:', error);

          // More detailed error logging
          if (error.response) {
            console.error('Error response:', error.response.data);
            console.error('Status code:', error.response.status);
          } else if (error.request) {
            console.error('No response received:', error.request);
          } else {
            console.error('Error setting up request:', error.message);
          }

          showErrorToast('Failed to delete task: ' + (error.response?.data?.message || error.message || 'Unknown error'), TOAST_CATEGORIES.TASK, 'deleteFailed');
        });
    }
  };

  // Menu items for the sidebar
  const menuItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      onClick: () => handleSectionChange('dashboard'),
      active: activeSection === 'dashboard',
    },
    {
      text: 'Users',
      icon: <PeopleIcon />,
      onClick: () => handleSectionChange('users'),
      active: activeSection === 'users',
    },
    {
      text: 'Applications',
      icon: <DescriptionIcon />,
      onClick: () => handleSectionChange('applications'),
      active: activeSection === 'applications',
    },
    {
      text: 'Leave Requests',
      icon: <EventNoteIcon />,
      onClick: () => handleSectionChange('leave-requests'),
      active: activeSection === 'leave-requests',
    },
    {
      text: 'Attendance',
      icon: <AccessTimeIcon />,
      onClick: () => handleSectionChange('attendance'),
      active: activeSection === 'attendance',
    },
    {
      text: 'Tasks',
      icon: <AssignmentIcon />,
      onClick: () => handleSectionChange('tasks'),
      active: activeSection === 'tasks',
    },
    {
      text: 'Jobs',
      icon: <WorkIcon />,
      onClick: () => handleSectionChange('jobs'),
      active: activeSection === 'jobs',
    },
    {
      text: 'Evaluations',
      icon: <AssessmentIcon />,
      onClick: () => handleSectionChange('evaluations'),
      active: activeSection === 'evaluations',
    },
    {
      text: 'GEK System',
      icon: <TimelineIcon />,
      items: [
        {
          text: 'GEK Dashboard',
          icon: <InsightsIcon />,
          onClick: () => handleSectionChange('gek-dashboard'),
          active: activeSection === 'gek-dashboard',
        },
        {
          text: 'Task Assignment',
          icon: <AssignmentTurnedInIcon />,
          onClick: () => handleSectionChange('gek-task-assignment'),
          active: activeSection === 'gek-task-assignment',
        }
      ]
    },
    {
      text: 'Reports',
      icon: <BarChartIcon />,
      onClick: () => navigate('/reports'),
      active: false,
    },
  ];

  // User table columns
  const userColumns = [
    { id: 'name', label: 'Name', minWidth: 150 },
    { id: 'email', label: 'Email', minWidth: 200 },
    { id: 'job', label: 'Job Title', minWidth: 150 },
    {
      id: 'creationDate',
      label: 'Joined',
      minWidth: 120,
      render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 100,
      render: (value) => (
        <Box
          sx={{
            display: 'inline-block',
            px: 1,
            py: 0.5,
            borderRadius: 1,
            backgroundColor: value === 'Active' ? 'success.light' : 'error.light',
            color: value === 'Active' ? 'success.dark' : 'error.dark',
            fontWeight: 500,
            fontSize: '0.75rem',
          }}
        >
          {value || 'Active'}
        </Box>
      )
    },
  ];

  // User table actions - only View Profile as requested by HR
  const userActions = [
    {
      name: 'View Profile',
      icon: <VisibilityIcon fontSize="small" />,
    },
  ];

  // Application table columns
  const applicationColumns = [
    { id: '_id', label: 'ID', minWidth: 120 },
    { id: 'fullname', label: 'Name', minWidth: 150 },
    { id: 'email', label: 'Email', minWidth: 200 },
    { id: 'position', label: 'Position', minWidth: 150 },
    { id: 'phone', label: 'Phone', minWidth: 120 },
    {
      id: 'createdAt',
      label: 'Applied On',
      minWidth: 120,
      render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
    {
      id: 'matchScore',
      label: 'Match Score',
      minWidth: 100,
      render: (value) => {
        if (value === undefined || value === null) return 'Not analyzed';
        return `${Math.round(value)}%`;
      }
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 100,
      render: (value) => (
        <Box
          sx={{
            display: 'inline-block',
            px: 1,
            py: 0.5,
            borderRadius: 1,
            backgroundColor:
              value === 'Approved' ? 'success.light' :
              value === 'Rejected' ? 'error.light' : 'warning.light',
            color:
              value === 'Approved' ? 'success.dark' :
              value === 'Rejected' ? 'error.dark' : 'warning.dark',
            fontWeight: 500,
            fontSize: '0.75rem',
          }}
        >
          {value || 'Pending'}
        </Box>
      )
    },
  ];

  // Application table actions
  const applicationActions = [
    {
      name: 'View CV',
      icon: <VisibilityIcon fontSize="small" />,
    },
    {
      name: 'Run NLP',
      icon: <PlayArrowIcon fontSize="small" />,
      condition: (row) => !row.nlpResults || Object.keys(row.nlpResults).length === 0,
    },
    {
      name: 'View NLP Report',
      icon: <AssessmentIcon fontSize="small" />,
      condition: (row) => row.nlpResults && Object.keys(row.nlpResults).length > 0,
    },
    {
      name: 'Approve',
      icon: <CheckCircleIcon fontSize="small" color="success" />,
      // Allow approving for both Pending and Rejected applications
      condition: (row) => row.status === 'Pending' || row.status === 'Rejected',
    },
    {
      name: 'Reject',
      icon: <CancelIcon fontSize="small" color="error" />,
      // Allow rejecting for both Pending and Approved applications
      condition: (row) => row.status === 'Pending' || row.status === 'Approved',
    },
    {
      name: 'Delete Application',
      icon: <DeleteIcon fontSize="small" />,
    },
  ];

  // Use GEK components imported at the top of the file

  // Render the dashboard content based on the active section
  const renderContent = () => {
    switch (activeSection) {
      case 'gek-dashboard':
        return <GEKDashboard />;

      case 'gek-task-assignment':
        return <GEKTaskAssignment />;

      case 'dashboard':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              HR Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Welcome to the HR Management Dashboard. Here's an overview of your organization.
            </Typography>

            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Total Employees"
                  value={stats.totalUsers}
                  icon={<PeopleIcon style={{ fontSize: 80 }} />}
                  color="primary"
                  subtitle="Active employees in the system"
                  onClick={() => handleSectionChange('users')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Applications"
                  value={stats.totalApplications}
                  icon={<DescriptionIcon style={{ fontSize: 80 }} />}
                  color="secondary"
                  subtitle="Total job applications received"
                  onClick={() => handleSectionChange('applications')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Open Positions"
                  value={stats.totalJobs}
                  icon={<WorkIcon style={{ fontSize: 80 }} />}
                  color="info"
                  subtitle="Currently open job positions"
                  onClick={() => handleSectionChange('jobs')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Leave Requests"
                  value={stats.pendingLeaveRequests}
                  icon={<EventNoteIcon style={{ fontSize: 80 }} />}
                  color="warning"
                  subtitle="Pending leave requests"
                  trend={{
                    icon: <TrendingUpIcon fontSize="small" />,
                  }}
                  trendValue="5 new this week"
                  onClick={() => handleSectionChange('leave-requests')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Active Tasks"
                  value={stats.activeTasks}
                  icon={<AssignmentIcon style={{ fontSize: 80 }} />}
                  color="error"
                  subtitle="Tasks in progress"
                  onClick={() => handleSectionChange('tasks')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={4}>
                <StatCard
                  title="Completed Tasks"
                  value={stats.completedTasks}
                  icon={<CheckCircleIcon style={{ fontSize: 80 }} />}
                  color="success"
                  subtitle="Tasks completed this month"
                  trend={{
                    icon: <TrendingUpIcon fontSize="small" />,
                  }}
                  trendValue="12% increase"
                  onClick={() => handleSectionChange('tasks')}
                />
              </Grid>
            </Grid>

            {/* Dashboard charts and analytics will go here */}
          </Box>
        );

      case 'users':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              User Management
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Manage your organization's employees and their information.
            </Typography>

            <DataTable
              title="Employees"
              columns={userColumns}
              data={users.map((user, index) => ({
                ...user,
                id: user._id || index,
                status: 'Active', // Assuming all users are active
              }))}
              actions={userActions}
              onActionClick={(action, row) => {
                console.log('Action:', action, 'Row:', row);
                if (action.name === 'View Profile') {
                  setSelectedUserProfile(row);
                  setShowUserProfileDialog(true);
                }
              }}
              searchable={true}
              selectable={true}
              onSearch={handleUserSearch}
              searchQuery={userSearchQuery}
              emptyMessage="No employees found"
            />
          </Box>
        );

      case 'applications':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              Applications Management
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Review and manage job applications from candidates.
            </Typography>

            {processingNLP && (
              <Box sx={{ mb: 3, p: 2, bgcolor: 'info.light', borderRadius: 1, display: 'flex', alignItems: 'center' }}>
                <CircularProgress size={24} sx={{ mr: 2, color: 'info.dark' }} />
                <Typography variant="body1" color="info.dark" fontWeight="medium">
                  {processingMessage}
                </Typography>
              </Box>
            )}

            {/* Display success or failure message after CV processing */}
            {!processingNLP && nlpProcessingResult && (
              <Box sx={{
                mb: 3,
                p: 2,
                bgcolor: nlpProcessingResult === 'success' ? 'success.light' : 'error.light',
                borderRadius: 1,
                display: 'flex',
                alignItems: 'center'
              }}>
                {nlpProcessingResult === 'success' ? (
                  <CheckCircleIcon sx={{ mr: 2, color: 'success.dark' }} />
                ) : (
                  <CancelIcon sx={{ mr: 2, color: 'error.dark' }} />
                )}
                <Typography variant="body1" color={nlpProcessingResult === 'success' ? 'success.dark' : 'error.dark'} fontWeight="medium">
                  {nlpProcessingResult === 'success' ? 'Successful' : 'Failed'} CV processing
                </Typography>
              </Box>
            )}

            <DataTable
              title="Job Applications"
              columns={applicationColumns}
              data={applications.map((app, index) => ({
                ...app,
                id: app._id || index,
                status: app.status || 'Pending',
              }))}
              actions={applicationActions}
              onActionClick={(action, row) => {
                console.log('Action:', action, 'Row:', row);
                if (action.name === 'View CV') {
                  handleViewCV(row);
                } else if (action.name === 'Run NLP') {
                  handleRunNLP(row);
                } else if (action.name === 'View NLP Report') {
                  handleViewNLPReport(row);
                } else if (action.name === 'Approve') {
                  handleApproveApplication(row._id);
                } else if (action.name === 'Reject') {
                  handleRejectApplication(row._id);
                } else if (action.name === 'Delete Application') {
                  handleDeleteApplication(row._id);
                }
              }}
              searchable={true}
              onSearch={handleApplicationSearch}
              searchQuery={applicationSearchQuery}
              emptyMessage="No applications found"
            />
          </Box>
        );

      case 'leave-requests':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              Leave Requests Management
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Review and manage employee leave requests.
            </Typography>

            <DataTable
              title="Leave Requests"
              columns={[
                { id: 'employeeName', label: 'Employee', minWidth: 150 },
                { id: 'leaveType', label: 'Type', minWidth: 120 },
                {
                  id: 'startDate',
                  label: 'Start Date',
                  minWidth: 120,
                  render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
                },
                {
                  id: 'endDate',
                  label: 'End Date',
                  minWidth: 120,
                  render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
                },
                {
                  id: 'duration',
                  label: 'Duration',
                  minWidth: 100,
                  render: (_, row) => {
                    if (!row.startDate || !row.endDate) return 'N/A';
                    const start = new Date(row.startDate);
                    const end = new Date(row.endDate);
                    const diffTime = Math.abs(end - start);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
                    return `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
                  }
                },
                {
                  id: 'reason',
                  label: 'Reason',
                  minWidth: 200,
                  render: (value) => value && value.length > 30 ? `${value.substring(0, 30)}...` : value
                },
                {
                  id: 'status',
                  label: 'Status',
                  minWidth: 100,
                  render: (value) => (
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        backgroundColor:
                          value === 'Approved' ? 'success.light' :
                          value === 'Rejected' ? 'error.light' : 'warning.light',
                        color:
                          value === 'Approved' ? 'success.dark' :
                          value === 'Rejected' ? 'error.dark' : 'warning.dark',
                        fontWeight: 500,
                        fontSize: '0.75rem',
                      }}
                    >
                      {value || 'Pending'}
                    </Box>
                  )
                },
              ]}
              data={leaveRequests.map((req, index) => ({
                ...req,
                id: req._id || index,
              }))}
              actions={[
                {
                  name: 'Approve',
                  icon: <CheckCircleIcon fontSize="small" color="success" />,
                  // Allow approving for both Pending and Rejected requests
                  condition: (row) => row.status === 'Pending' || row.status === 'Rejected',
                },
                {
                  name: 'Reject',
                  icon: <CancelIcon fontSize="small" color="error" />,
                  // Allow rejecting for both Pending and Approved requests
                  condition: (row) => row.status === 'Pending' || row.status === 'Approved',
                },
                {
                  name: 'Delete',
                  icon: <DeleteIcon fontSize="small" />,
                },
              ]}
              onActionClick={(action, row) => {
                console.log('Action:', action, 'Row:', row);
                if (action.name === 'Approve') {
                  handleApproveLeaveRequest(row._id);
                } else if (action.name === 'Reject') {
                  handleRejectLeaveRequest(row._id);
                } else if (action.name === 'Delete') {
                  handleDeleteLeaveRequest(row._id);
                }
              }}
              searchable={true}
              onSearch={handleLeaveRequestSearch}
              searchQuery={leaveRequestSearchQuery}
              filters={[
                { id: 'status', label: 'Status', options: ['All', 'Pending', 'Approved', 'Rejected'] },
                { id: 'leaveType', label: 'Leave Type', options: ['All', 'Annual Leave', 'Sick Leave', 'Personal Leave', 'Maternity/Paternity Leave', 'Bereavement Leave', 'Unpaid Leave'] },
              ]}
              emptyMessage="No leave requests found"
            />
          </Box>
        );

      case 'attendance':
        return <HRAttendanceManagement />;

      case 'tasks':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <div>
                <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
                  Task Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Create, assign, and manage tasks for your team.
                </Typography>
              </div>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleOpenTaskDialog('add')}
              >
                Create New Task
              </Button>
            </Box>

            <DataTable
              title="Tasks"
              columns={[
                { id: 'title', label: 'Task', minWidth: 200 },
                { id: 'assignedToName', label: 'Assigned To', minWidth: 150,
                  render: (value, row) => {
                    // Check if value is a string
                    if (typeof value === 'string') {
                      return value;
                    }
                    // Check if assignedTo is a string
                    if (typeof row.assignedTo === 'string') {
                      return row.assignedTo;
                    }
                    // If it's an object, extract the name or email
                    if (value && typeof value === 'object') {
                      return value.name || value.email || 'Unassigned';
                    }
                    if (row.assignedTo && typeof row.assignedTo === 'object') {
                      return row.assignedTo.name || row.assignedTo.email || 'Unassigned';
                    }
                    return 'Unassigned';
                  }
                },
                { id: 'category', label: 'Category', minWidth: 120 },
                { id: 'priority', label: 'Priority', minWidth: 100,
                  render: (value) => (
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        backgroundColor:
                          value === 'High' || value === 'Urgent' ? 'error.light' :
                          value === 'Medium' ? 'warning.light' : 'success.light',
                        color:
                          value === 'High' || value === 'Urgent' ? 'error.dark' :
                          value === 'Medium' ? 'warning.dark' : 'success.dark',
                        fontWeight: 500,
                        fontSize: '0.75rem',
                      }}
                    >
                      {value || 'Low'}
                    </Box>
                  )
                },
                {
                  id: 'deadline',
                  label: 'Deadline',
                  minWidth: 120,
                  render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
                },
                { id: 'status', label: 'Status', minWidth: 100,
                  render: (value) => (
                    <Box
                      sx={{
                        display: 'inline-block',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        backgroundColor:
                          value === 'Completed' ? 'success.light' :
                          value === 'In Progress' ? 'warning.light' : 'info.light',
                        color:
                          value === 'Completed' ? 'success.dark' :
                          value === 'In Progress' ? 'warning.dark' : 'info.dark',
                        fontWeight: 500,
                        fontSize: '0.75rem',
                      }}
                    >
                      {value || 'Not Started'}
                    </Box>
                  )
                },
              ]}
              data={tasks.map((task, index) => ({
                ...task,
                id: task._id || index,
              }))}
              actions={[
                {
                  name: 'View Task',
                  icon: <VisibilityIcon fontSize="small" />,
                },
                {
                  name: 'Edit Task',
                  icon: <EditIcon fontSize="small" />,
                },
                {
                  name: 'Delete Task',
                  icon: <DeleteIcon fontSize="small" />,
                },
              ]}
              onActionClick={(action, row) => {
                console.log('Action:', action, 'Row:', row);
                if (action.name === 'View Task') {
                  handleOpenTaskDialog('view', row);
                } else if (action.name === 'Edit Task') {
                  handleOpenTaskDialog('edit', row);
                } else if (action.name === 'Delete Task') {
                  handleDeleteTask(row._id);
                }
              }}
              searchable={true}
              onSearch={handleTaskSearch}
              searchQuery={taskSearchQuery}
              filters={[
                { id: 'status', label: 'Status', options: ['All', 'Not Started', 'In Progress', 'Completed'] },
                { id: 'priority', label: 'Priority', options: ['All', 'Low', 'Medium', 'High', 'Urgent'] },
              ]}
              emptyMessage="No tasks found"
            />
          </Box>
        );

      case 'jobs':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <div>
                <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
                  Job Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Create and manage job postings for your organization.
                </Typography>
              </div>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleOpenJobDialog('add')}
              >
                Add New Job
              </Button>
            </Box>

            <DataTable
              title="Job Postings"
              columns={[
                { id: 'title', label: 'Job Title', minWidth: 200 },
                { id: 'location', label: 'Location', minWidth: 150 },
                { id: 'jobType', label: 'Type', minWidth: 120 },
                { id: 'academicLevel', label: 'Academic Level', minWidth: 150 },
                {
                  id: 'createdAt',
                  label: 'Posted On',
                  minWidth: 120,
                  render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
                },
                {
                  id: 'endDate',
                  label: 'End Date',
                  minWidth: 120,
                  render: (value) => value ? new Date(value).toLocaleDateString() : 'No end date'
                },
              ]}
              data={jobs.map((job, index) => ({
                ...job,
                id: job._id || index,
              }))}
              actions={[
                {
                  name: 'View Job',
                  icon: <VisibilityIcon fontSize="small" />,
                },
                {
                  name: 'Edit Job',
                  icon: <EditIcon fontSize="small" />,
                },
                {
                  name: 'Delete Job',
                  icon: <DeleteIcon fontSize="small" />,
                },
              ]}
              onActionClick={(action, row) => {
                console.log('Action:', action, 'Row:', row);
                if (action.name === 'View Job') {
                  handleOpenJobDialog('view', row);
                } else if (action.name === 'Edit Job') {
                  handleOpenJobDialog('edit', row);
                } else if (action.name === 'Delete Job') {
                  handleDeleteJob(row._id);
                }
              }}
              searchable={true}
              onSearch={handleJobSearch}
              searchQuery={jobSearchQuery}
              emptyMessage="No job postings found"
            />
          </Box>
        );

      case 'evaluations':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <div>
                <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
                  Performance Evaluations
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage employee performance evaluations and feedback.
                </Typography>
              </div>
              <Tooltip title="Generate AI evaluations based on tasks, attendance, and performance">
                <Button
                  variant="contained"
                  color="secondary"
                  startIcon={<SmartToyIcon />}
                  onClick={() => showInfoToast('Select an employee and use the "Generate AI Evaluation" button in their evaluation history', TOAST_CATEGORIES.EVALUATION, 'info')}
                >
                  AI Evaluation System
                </Button>
              </Tooltip>
            </Box>

            <DataTable
              title="Employee Evaluations"
              columns={[
                { id: 'name', label: 'Name', minWidth: 150 },
                { id: 'job', label: 'Job Title', minWidth: 150 },
                { id: 'email', label: 'Email', minWidth: 200 },
                {
                  id: 'lastEvaluation',
                  label: 'Last Evaluation',
                  minWidth: 120,
                  render: (value) => value ? new Date(value).toLocaleDateString() : 'Not evaluated'
                },
              ]}
              data={users.map((user, index) => ({
                ...user,
                id: user._id || index,
                lastEvaluation: user.lastEvaluation || null
              }))}
              actions={[
                {
                  name: 'View Evaluations',
                  icon: <AssessmentIcon fontSize="small" />,
                },
                {
                  name: 'Generate AI Evaluation',
                  icon: <SmartToyIcon fontSize="small" />,
                  condition: (row) => row.role === 'user', // Only for normal users
                },
              ]}
              onActionClick={(action, row) => {
                console.log('Action:', action, 'Row:', row);
                if (action.name === 'View Evaluations') {
                  handleOpenEvaluation(row);
                } else if (action.name === 'Generate AI Evaluation') {
                  // Show loading toast
                  const loadingToastId = showLoadingToast('Generating AI evaluation...', { autoClose: false });

                  // Call the AI evaluation endpoint
                  api.post(`/api/hr/evaluations/ai/${row._id}`)
                    .then(response => {
                      // Close loading toast
                      dismissToast(loadingToastId);
                      // Show success toast
                      showSuccessToast('AI evaluation generated successfully!', TOAST_CATEGORIES.EVALUATION, 'generated');
                      // Open the evaluation history to show the new evaluation
                      handleOpenEvaluation(row);
                    })
                    .catch(error => {
                      // Close loading toast
                      dismissToast(loadingToastId);
                      // Show error toast
                      showErrorToast(`Failed to generate AI evaluation: ${error.response?.data?.message || error.message}`, TOAST_CATEGORIES.EVALUATION, 'failed');
                    });
                }
              }}
              searchable={true}
              onSearch={handleEvaluationSearch}
              searchQuery={evaluationSearchQuery}
              emptyMessage="No employees found"
            />
          </Box>
        );

      // Add other sections as needed

      default:
        return (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h5" color="text.secondary">
              Section under development
            </Typography>
          </Box>
        );
    }
  };

  // Show nothing while checking authentication
  if (authChecking) {
    return null; // Return nothing while checking authentication to prevent flash
  }

  return (
    <DashboardLayout
      title={
        activeSection === 'dashboard' ? 'HR Dashboard' :
        activeSection === 'users' ? 'User Management' :
        activeSection === 'applications' ? 'Applications Management' :
        activeSection === 'leave-requests' ? 'Leave Requests' :
        activeSection === 'attendance' ? 'Attendance Management' :
        activeSection === 'tasks' ? 'Task Management' :
        activeSection === 'jobs' ? 'Job Management' :
        activeSection === 'evaluations' ? 'Performance Evaluations' :
        activeSection === 'reports' ? 'Reports' :
        'HR Dashboard'
      }
      menuItems={menuItems}
      userName="HR Manager"
      userRole="Human Resources"
      onLogout={handleLogout}
    >

      {renderContent()}

      {/* Job Dialog */}
      <Dialog
        open={openJobDialog}
        onClose={handleCloseJobDialog}
        fullWidth
        maxWidth="md"
        // Fix accessibility issues with focus management
        keepMounted
        container={() => document.getElementById('dialog-container') || document.body}
        disableScrollLock={false}
        aria-labelledby="job-dialog-title"
      >
        {jobDialogType === 'view' && selectedJob ? (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography id="job-dialog-title" variant="h6">{selectedJob.title}</Typography>
                <IconButton onClick={handleCloseJobDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Location</Typography>
                  <Typography variant="body1" paragraph>{selectedJob.location}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Job Type</Typography>
                  <Typography variant="body1" paragraph>{selectedJob.jobType}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Academic Level</Typography>
                  <Typography variant="body1" paragraph>{selectedJob.academicLevel}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Posted On</Typography>
                  <Typography variant="body1" paragraph>
                    {selectedJob.createdAt ? new Date(selectedJob.createdAt).toLocaleDateString() : 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">End Date</Typography>
                  <Typography variant="body1" paragraph>
                    {selectedJob.endDate ? new Date(selectedJob.endDate).toLocaleDateString() : 'No end date'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                  <Typography variant="body1" paragraph>{selectedJob.description}</Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Requirements</Typography>
                  <List dense>
                    {Array.isArray(selectedJob.requirements) ? (
                      selectedJob.requirements.map((req, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <CheckCircleIcon color="primary" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText primary={req} />
                        </ListItem>
                      ))
                    ) : (
                      <Typography variant="body1">No requirements specified</Typography>
                    )}
                  </List>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Responsibilities</Typography>
                  <List dense>
                    {Array.isArray(selectedJob.responsibilities) ? (
                      selectedJob.responsibilities.map((resp, index) => (
                        <ListItem key={index}>
                          <ListItemIcon>
                            <CheckCircleIcon color="primary" fontSize="small" />
                          </ListItemIcon>
                          <ListItemText primary={resp} />
                        </ListItem>
                      ))
                    ) : (
                      <Typography variant="body1">No responsibilities specified</Typography>
                    )}
                  </List>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => handleOpenJobDialog('edit', selectedJob)} startIcon={<EditIcon />}>
                Edit
              </Button>
              <Button
                color="error"
                onClick={() => {
                  handleCloseJobDialog();
                  handleDeleteJob(selectedJob._id);
                }}
                startIcon={<DeleteIcon />}
              >
                Delete
              </Button>
            </DialogActions>
          </>
        ) : (jobDialogType === 'add' || jobDialogType === 'edit') ? (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography id="job-dialog-title" variant="h6">{jobDialogType === 'add' ? 'Add New Job' : 'Edit Job'}</Typography>
                <IconButton onClick={handleCloseJobDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <form id="job-form" onSubmit={handleJobFormSubmit}>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <TextField
                      label="Job Title"
                      fullWidth
                      required
                      value={jobData.title}
                      onChange={(e) => setJobData({ ...jobData, title: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Location"
                      fullWidth
                      required
                      value={jobData.location}
                      onChange={(e) => setJobData({ ...jobData, location: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>Job Type</InputLabel>
                      <Select
                        value={jobData.jobType}
                        label="Job Type"
                        onChange={(e) => setJobData({ ...jobData, jobType: e.target.value })}
                      >
                        <MenuItem value="Full-Time">Full-Time</MenuItem>
                        <MenuItem value="Part-Time">Part-Time</MenuItem>
                        <MenuItem value="Contract">Contract</MenuItem>
                        <MenuItem value="Internship">Internship</MenuItem>
                        <MenuItem value="Remote">Remote</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>Academic Level</InputLabel>
                      <Select
                        value={jobData.academicLevel}
                        label="Academic Level"
                        onChange={(e) => setJobData({ ...jobData, academicLevel: e.target.value })}
                      >
                        <MenuItem value="High School">High School</MenuItem>
                        <MenuItem value="Associate">Associate</MenuItem>
                        <MenuItem value="Bachelor">Bachelor</MenuItem>
                        <MenuItem value="Master">Master</MenuItem>
                        <MenuItem value="PhD">PhD</MenuItem>
                        <MenuItem value="Not Required">Not Required</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="End Date"
                      type="date"
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      value={jobData.endDate}
                      onChange={(e) => setJobData({ ...jobData, endDate: e.target.value })}
                      helperText="Job will be automatically deleted after this date"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      fullWidth
                      required
                      multiline
                      rows={4}
                      value={jobData.description}
                      onChange={(e) => setJobData({ ...jobData, description: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Requirements (comma separated)"
                      fullWidth
                      multiline
                      rows={3}
                      value={jobData.requirements}
                      onChange={(e) => setJobData({ ...jobData, requirements: e.target.value })}
                      helperText="Enter requirements separated by commas"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Responsibilities (comma separated)"
                      fullWidth
                      multiline
                      rows={3}
                      value={jobData.responsibilities}
                      onChange={(e) => setJobData({ ...jobData, responsibilities: e.target.value })}
                      helperText="Enter responsibilities separated by commas"
                    />
                  </Grid>
                </Grid>
              </form>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseJobDialog} color="inherit">Cancel</Button>
              <Button
                type="submit"
                form="job-form"
                variant="contained"
                color="primary"
              >
                {jobDialogType === 'add' ? 'Create Job' : 'Update Job'}
              </Button>
            </DialogActions>
          </>
        ) : null}
      </Dialog>

      {/* NLP Report Dialog */}
      <Dialog
        open={openNLPReportDialog}
        onClose={() => setOpenNLPReportDialog(false)}
        fullWidth
        maxWidth="lg"
        PaperProps={{
          sx: {
            bgcolor: '#f8f9fa',
            height: '90vh',
            maxHeight: '90vh'
          }
        }}
        // Fix accessibility issues with focus management
        keepMounted
        container={() => document.getElementById('dialog-container') || document.body}
        disableScrollLock={false}
        aria-labelledby="nlp-report-dialog-title"
      >
        <AppBar position="static" sx={{ bgcolor: '#e53935' }}>
          <Toolbar variant="dense">
            <Typography id="nlp-report-dialog-title" variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
              NLP Analysis Report
              {selectedApplication && (
                <Typography component="span" variant="subtitle1" sx={{ ml: 1, color: 'rgba(255, 255, 255, 0.9)' }}>
                  - {selectedApplication.fullname}
                </Typography>
              )}
            </Typography>

            {selectedApplication && selectedApplication.nlpResults && (
              <>
                <Button
                  onClick={() => {
                    // Run NLP again to refresh the results
                    handleRunNLP(selectedApplication);
                    setOpenNLPReportDialog(false);
                  }}
                  color="inherit"
                  startIcon={<RefreshIcon />}
                  size="small"
                  sx={{ mr: 1 }}
                >
                  Refresh
                </Button>
              </>
            )}

            <IconButton
              edge="end"
              color="inherit"
              onClick={() => setOpenNLPReportDialog(false)}
              aria-label="close"
              size="small"
            >
              <CloseIcon />
            </IconButton>
          </Toolbar>
        </AppBar>
        <DialogContent sx={{ p: 2 }}>
          {selectedApplication && selectedApplication.nlpResults ? (
            <Box sx={{ px: 1 }}>
              {/* Executive Summary Section */}
              <Paper elevation={0} sx={{ p: 1.5, mb: 2, bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                    Executive Summary
                  </Typography>
                  <Divider sx={{ flexGrow: 1 }} />
                </Box>

                <Grid container spacing={1}>
                  <Grid item xs={12} sm={3}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                      {/* Match Score Circle - Smaller */}
                      <Box
                        sx={{
                          position: 'relative',
                          width: 80,
                          height: 80,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: 'background.paper',
                          border: '6px solid',
                          borderColor: (theme) => {
                            const matchScore = selectedApplication.matchScore !== undefined ?
                              Math.round(selectedApplication.matchScore) :
                              (selectedApplication.nlpResults.matchScore !== undefined ?
                                Math.round(selectedApplication.nlpResults.matchScore) : 0);

                            if (matchScore >= 80) return theme.palette.success.main;
                            if (matchScore >= 60) return theme.palette.success.light;
                            if (matchScore >= 40) return theme.palette.warning.main;
                            return theme.palette.error.main;
                          },
                          mr: 2
                        }}
                      >
                        <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                          {selectedApplication.matchScore !== undefined ?
                            `${Math.round(selectedApplication.matchScore)}%` :
                            (selectedApplication.nlpResults.matchScore !== undefined ?
                              `${Math.round(selectedApplication.nlpResults.matchScore)}%` : 'N/A')}
                        </Typography>
                      </Box>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                          Match Score
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={9}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', justifyContent: 'space-between' }}>
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                          Assessment:
                        </Typography>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          {(() => {
                            const matchScore = selectedApplication.matchScore !== undefined ?
                              Math.round(selectedApplication.matchScore) :
                              (selectedApplication.nlpResults.matchScore !== undefined ?
                                Math.round(selectedApplication.nlpResults.matchScore) : 0);

                            if (matchScore >= 90) {
                              return 'Excellent match for the position. Candidate exceeds most requirements.';
                            } else if (matchScore >= 75) {
                              return 'Strong match for the position. Candidate meets most key requirements.';
                            } else if (matchScore >= 60) {
                              return 'Good match for the position. Candidate meets many requirements but has some gaps.';
                            } else if (matchScore >= 40) {
                              return 'Moderate match for the position. Candidate has relevant skills but significant gaps exist.';
                            } else {
                              return 'Limited match for the position. Candidate does not meet many key requirements.';
                            }
                          })()}
                        </Typography>
                      </Box>

                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                          Recommendation:
                        </Typography>
                        <Typography variant="body2">
                          {(() => {
                            const matchScore = selectedApplication.matchScore !== undefined ?
                              Math.round(selectedApplication.matchScore) :
                              (selectedApplication.nlpResults.matchScore !== undefined ?
                                Math.round(selectedApplication.nlpResults.matchScore) : 0);

                            if (matchScore >= 75) {
                              return 'Proceed to interview stage. Candidate shows strong alignment with position requirements.';
                            } else if (matchScore >= 50) {
                              return 'Consider for interview with focus on addressing skill gaps. Candidate shows potential but has some missing requirements.';
                            } else {
                              return 'Not recommended for this position. Consider for other roles that better match candidate\'s profile.';
                            }
                          })()}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Paper>

              {/* Candidate Information Section */}
              <Paper elevation={0} sx={{ p: 1.5, mb: 2, bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                    Candidate Information
                  </Typography>
                  <Divider sx={{ flexGrow: 1 }} />
                </Box>

                <Grid container spacing={1}>
                  <Grid item xs={12}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Application ID</Typography>
                    <Typography variant="body2" fontWeight="bold" color="primary.main">{selectedApplication._id || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={6} sm={3} md={2}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Name</Typography>
                    <Typography variant="body2">{selectedApplication.fullname}</Typography>
                  </Grid>
                  <Grid item xs={6} sm={3} md={2}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Email</Typography>
                    <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>{selectedApplication.email}</Typography>
                  </Grid>
                  <Grid item xs={6} sm={3} md={2}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Phone</Typography>
                    <Typography variant="body2">{selectedApplication.phone || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={6} sm={3} md={2}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Applied On</Typography>
                    <Typography variant="body2">{new Date(selectedApplication.createdAt).toLocaleDateString()}</Typography>
                  </Grid>
                  <Grid item xs={6} sm={6} md={2}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Position</Typography>
                    <Typography variant="body2">
                      {typeof selectedApplication.position === 'object'
                        ? (selectedApplication.position.title || 'N/A')
                        : (selectedApplication.position || 'N/A')}
                    </Typography>
                  </Grid>
                  <Grid item xs={6} sm={6} md={2}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>Job ID</Typography>
                    <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                      {typeof selectedApplication.jobId === 'object'
                        ? (selectedApplication.jobId._id || 'N/A')
                        : (selectedApplication.jobId || 'N/A')}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>

              {/* Skills Assessment Section */}
              <Paper elevation={0} sx={{ p: 1.5, mb: 2, bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                    Skills Assessment
                  </Typography>
                  <Divider sx={{ flexGrow: 1 }} />
                </Box>

                {selectedApplication.nlpResults.skills && selectedApplication.nlpResults.skills.length > 0 ? (
                  <Grid container spacing={1}>
                    {/* Group skills by category */}
                    {(() => {
                      const skills = selectedApplication.nlpResults.skills;

                      const technicalSkills = skills.filter(skill =>
                        /\b(programming|software|technical|language|framework|database|cloud|development|engineering)\b/i.test(skill)
                      );

                      const softSkills = skills.filter(skill =>
                        /\b(communication|teamwork|leadership|management|organization|problem.solving|creativity|adaptability)\b/i.test(skill)
                      );

                      const otherSkills = skills.filter(skill =>
                        !technicalSkills.includes(skill) && !softSkills.includes(skill)
                      );

                      return (
                        <>
                          {technicalSkills.length > 0 && (
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                                Technical Skills:
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {technicalSkills.sort().map((skill, index) => (
                                  <Chip
                                    key={index}
                                    label={skill}
                                    size="small"
                                    variant="outlined"
                                    icon={<CodeIcon fontSize="small" />}
                                    sx={{ mb: 0.5, fontSize: '0.75rem' }}
                                  />
                                ))}
                              </Box>
                            </Grid>
                          )}

                          {softSkills.length > 0 && (
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                                Soft Skills:
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {softSkills.sort().map((skill, index) => (
                                  <Chip
                                    key={index}
                                    label={skill}
                                    size="small"
                                    variant="outlined"
                                    icon={<PeopleIcon fontSize="small" />}
                                    sx={{ mb: 0.5, fontSize: '0.75rem' }}
                                  />
                                ))}
                              </Box>
                            </Grid>
                          )}

                          {otherSkills.length > 0 && (
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                                Other Skills:
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {otherSkills.sort().map((skill, index) => (
                                  <Chip
                                    key={index}
                                    label={skill}
                                    size="small"
                                    variant="outlined"
                                    icon={<CheckCircleIcon fontSize="small" />}
                                    sx={{ mb: 0.5, fontSize: '0.75rem' }}
                                  />
                                ))}
                              </Box>
                            </Grid>
                          )}
                        </>
                      );
                    })()}
                  </Grid>
                ) : (
                  <Typography variant="body2" color="text.secondary">No skills detected in the CV</Typography>
                )}
              </Paper>

              {/* Education & Experience Section */}
              <Grid container spacing={2}>
                {/* Education Section */}
                <Grid item xs={12} md={6}>
                  <Paper elevation={0} sx={{ p: 1.5, height: '100%', bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                        Education
                      </Typography>
                      <Divider sx={{ flexGrow: 1 }} />
                    </Box>

                    {selectedApplication.nlpResults.education && selectedApplication.nlpResults.education.length > 0 ? (
                      <List dense disablePadding>
                        {selectedApplication.nlpResults.education.map((edu, index) => (
                          <ListItem key={index} alignItems="flex-start" sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <SchoolIcon color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                  {edu.split(' at ')[0]}
                                </Typography>
                              }
                              secondary={
                                edu.includes(' at ') ? (
                                  <Typography variant="caption" color="text.secondary">
                                    {edu.split(' at ')[1]}
                                  </Typography>
                                ) : null
                              }
                              sx={{ m: 0 }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography variant="body2" color="text.secondary">No education details detected in the CV</Typography>
                    )}
                  </Paper>
                </Grid>

                {/* Experience Section */}
                <Grid item xs={12} md={6}>
                  <Paper elevation={0} sx={{ p: 1.5, height: '100%', bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                        Experience
                      </Typography>
                      <Divider sx={{ flexGrow: 1 }} />
                    </Box>

                    {selectedApplication.nlpResults.experience && selectedApplication.nlpResults.experience.length > 0 ? (
                      <List dense disablePadding>
                        {selectedApplication.nlpResults.experience.map((exp, index) => (
                          <ListItem key={index} alignItems="flex-start" sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 30 }}>
                              <WorkIcon color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={
                                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                  {exp.split(' at ')[0]}
                                </Typography>
                              }
                              secondary={
                                exp.includes(' at ') ? (
                                  <Typography variant="caption" color="text.secondary">
                                    {exp.split(' at ')[1]}
                                  </Typography>
                                ) : null
                              }
                              sx={{ m: 0 }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography variant="body2" color="text.secondary">No experience details detected in the CV</Typography>
                    )}
                  </Paper>
                </Grid>
              </Grid>

              {/* Job Requirements Match Section */}
              <Paper elevation={0} sx={{ p: 1.5, mt: 2, mb: 2, bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                    Requirements Match
                  </Typography>
                  <Divider sx={{ flexGrow: 1 }} />
                </Box>

                {selectedApplication.nlpResults.requirementsMatch ? (
                  <>
                    <Box sx={{ mb: 2 }}>
                      {(() => {
                        const matched = selectedApplication.nlpResults.requirementsMatch.matched.length;
                        const missing = selectedApplication.nlpResults.requirementsMatch.missing.length;
                        const total = matched + missing;
                        const matchPercentage = Math.round((matched / total) * 100);

                        return (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box sx={{ width: '100%', mr: 1 }}>
                              <LinearProgress
                                variant="determinate"
                                value={matchPercentage}
                                sx={{
                                  height: 8,
                                  borderRadius: 4,
                                  backgroundColor: 'error.light',
                                  '& .MuiLinearProgress-bar': {
                                    backgroundColor: 'success.main',
                                  }
                                }}
                              />
                            </Box>
                            <Box sx={{ minWidth: 35 }}>
                              <Typography variant="body2" color="text.secondary">{`${matchPercentage}%`}</Typography>
                            </Box>
                          </Box>
                        );
                      })()}
                      <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                        Matched {selectedApplication.nlpResults.requirementsMatch.matched.length} out of {
                          selectedApplication.nlpResults.requirementsMatch.matched.length +
                          selectedApplication.nlpResults.requirementsMatch.missing.length
                        } requirements
                      </Typography>
                    </Box>

                    <Grid container spacing={2}>
                      {/* Matched Requirements */}
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                          Matched Requirements:
                        </Typography>

                        {selectedApplication.nlpResults.requirementsMatch.matched.length > 0 ? (
                          <List dense disablePadding>
                            {selectedApplication.nlpResults.requirementsMatch.matched.map((req, index) => (
                              <ListItem key={index} sx={{ py: 0.25 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <CheckCircleIcon color="success" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={<Typography variant="body2">{req}</Typography>}
                                  sx={{ m: 0 }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        ) : (
                          <Typography variant="body2" color="text.secondary">No matched requirements</Typography>
                        )}
                      </Grid>

                      {/* Missing Requirements */}
                      <Grid item xs={12} md={6}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'text.secondary', mb: 0.5 }}>
                          Missing Requirements:
                        </Typography>

                        {selectedApplication.nlpResults.requirementsMatch.missing.length > 0 ? (
                          <List dense disablePadding>
                            {selectedApplication.nlpResults.requirementsMatch.missing.map((req, index) => (
                              <ListItem key={index} sx={{ py: 0.25 }}>
                                <ListItemIcon sx={{ minWidth: 28 }}>
                                  <CancelIcon color="error" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText
                                  primary={<Typography variant="body2">{req}</Typography>}
                                  sx={{ m: 0 }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        ) : (
                          <Typography variant="body2" color="text.secondary">No missing requirements</Typography>
                        )}
                      </Grid>
                    </Grid>
                  </>
                ) : (
                  <Typography variant="body2" color="text.secondary">No requirements match data available</Typography>
                )}
              </Paper>

              {/* Interview Focus Areas Section */}
              {(() => {
                const matchScore = selectedApplication.matchScore !== undefined ?
                  Math.round(selectedApplication.matchScore) :
                  (selectedApplication.nlpResults.matchScore !== undefined ?
                    Math.round(selectedApplication.nlpResults.matchScore) : 0);

                if (matchScore >= 50) {
                  return (
                    <Paper elevation={0} sx={{ p: 1.5, mb: 2, bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1" sx={{ color: 'primary.main', fontWeight: 'bold', mr: 1 }}>
                          Interview Focus Areas
                        </Typography>
                        <Divider sx={{ flexGrow: 1 }} />
                      </Box>

                      <List dense disablePadding>
                        {/* If there are missing requirements, suggest focusing on those */}
                        {selectedApplication.nlpResults.requirementsMatch &&
                         selectedApplication.nlpResults.requirementsMatch.missing &&
                         selectedApplication.nlpResults.requirementsMatch.missing.length > 0 && (
                          <ListItem sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 28 }}>
                              <HelpOutlineIcon color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={<Typography variant="body2" sx={{ fontWeight: 'medium' }}>Verify capabilities regarding missing requirements</Typography>}
                              secondary={
                                <Typography variant="caption" color="text.secondary">
                                  {selectedApplication.nlpResults.requirementsMatch.missing.slice(0, 3).join(', ')}
                                </Typography>
                              }
                              sx={{ m: 0 }}
                            />
                          </ListItem>
                        )}

                        {/* Suggest exploring experience depth */}
                        {selectedApplication.nlpResults.experience &&
                         selectedApplication.nlpResults.experience.length > 0 && (
                          <ListItem sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 28 }}>
                              <WorkIcon color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={<Typography variant="body2" sx={{ fontWeight: 'medium' }}>Explore depth of experience</Typography>}
                              secondary={
                                <Typography variant="caption" color="text.secondary">
                                  {selectedApplication.nlpResults.experience[0].split(' at ')[0]}
                                </Typography>
                              }
                              sx={{ m: 0 }}
                            />
                          </ListItem>
                        )}

                        {/* Suggest technical assessment if technical skills are present */}
                        {selectedApplication.nlpResults.skills &&
                         selectedApplication.nlpResults.skills.some(skill =>
                           /\b(programming|software|technical|language|framework|database|cloud|development|engineering)\b/i.test(skill)
                         ) && (
                          <ListItem sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 28 }}>
                              <CodeIcon color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={<Typography variant="body2" sx={{ fontWeight: 'medium' }}>Consider technical assessment</Typography>}
                              secondary={
                                <Typography variant="caption" color="text.secondary">
                                  Verify proficiency in claimed technical skills
                                </Typography>
                              }
                              sx={{ m: 0 }}
                            />
                          </ListItem>
                        )}
                      </List>
                    </Paper>
                  );
                }
                return null;
              })()}
            </Box>
          ) : (
            <Typography variant="body1" color="text.secondary">
              No NLP results available for this application
            </Typography>
          )}
        </DialogContent>

      </Dialog>

      {/* Evaluation Dialog */}
      {selectedEmployee && (
        <EvaluationHistory
          open={showEvaluationHistory}
          onClose={handleCloseEvaluation}
          userId={selectedEmployee.id}
          userName={selectedEmployee.name}
          userJob={selectedEmployee.job}
        />
      )}

      {/* Task Dialog */}
      <Dialog
        open={openTaskDialog}
        onClose={handleCloseTaskDialog}
        fullWidth
        maxWidth="md"
        // Fix accessibility issues with focus management
        keepMounted
        container={() => document.getElementById('dialog-container') || document.body}
        disableScrollLock={false}
        aria-labelledby="task-dialog-title"
      >
        {taskDialogType === 'view' && selectedTask ? (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography id="task-dialog-title" variant="h6">{selectedTask.title}</Typography>
                <IconButton onClick={handleCloseTaskDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Assigned To</Typography>
                  <Typography variant="body1" paragraph>
                    {(() => {
                      // Check if assignedToName is a string
                      if (typeof selectedTask.assignedToName === 'string') {
                        return selectedTask.assignedToName;
                      }
                      // Check if assignedTo is a string
                      if (typeof selectedTask.assignedTo === 'string') {
                        return selectedTask.assignedTo;
                      }
                      // If assignedToName is an object, extract the name or email
                      if (selectedTask.assignedToName && typeof selectedTask.assignedToName === 'object') {
                        return selectedTask.assignedToName.name || selectedTask.assignedToName.email || 'Unassigned';
                      }
                      // If assignedTo is an object, extract the name or email
                      if (selectedTask.assignedTo && typeof selectedTask.assignedTo === 'object') {
                        return selectedTask.assignedTo.name || selectedTask.assignedTo.email || 'Unassigned';
                      }
                      return 'Unassigned';
                    })()}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Category</Typography>
                  <Typography variant="body1" paragraph>{selectedTask.category || 'General'}</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Priority</Typography>
                  <Box
                    sx={{
                      display: 'inline-block',
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      backgroundColor:
                        selectedTask.priority === 'High' || selectedTask.priority === 'Urgent' ? theme.palette.error.light :
                        selectedTask.priority === 'Medium' ? theme.palette.warning.light : theme.palette.success.light,
                      color:
                        selectedTask.priority === 'High' || selectedTask.priority === 'Urgent' ? theme.palette.error.dark :
                        selectedTask.priority === 'Medium' ? theme.palette.warning.dark : theme.palette.success.dark,
                      fontWeight: 500,
                      fontSize: '0.75rem',
                    }}
                  >
                    {selectedTask.priority || 'Low'}
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Status</Typography>
                  <Box
                    sx={{
                      display: 'inline-block',
                      px: 1,
                      py: 0.5,
                      borderRadius: 1,
                      backgroundColor:
                        selectedTask.status === 'Completed' ? theme.palette.success.light :
                        selectedTask.status === 'In Progress' ? theme.palette.warning.light : theme.palette.info.light,
                      color:
                        selectedTask.status === 'Completed' ? theme.palette.success.dark :
                        selectedTask.status === 'In Progress' ? theme.palette.warning.dark : theme.palette.info.dark,
                      fontWeight: 500,
                      fontSize: '0.75rem',
                    }}
                  >
                    {selectedTask.status || 'Not Started'}
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Deadline</Typography>
                  <Typography variant="body1" paragraph>
                    {selectedTask.deadline ? new Date(selectedTask.deadline).toLocaleDateString() : 'No deadline'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Created On</Typography>
                  <Typography variant="body1" paragraph>
                    {selectedTask.createdAt ? new Date(selectedTask.createdAt).toLocaleDateString() : 'Unknown'}
                  </Typography>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                  <Typography variant="body1" paragraph>{selectedTask.description || 'No description provided'}</Typography>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => handleOpenTaskDialog('edit', selectedTask)} startIcon={<EditIcon />}>
                Edit
              </Button>
              <Button
                color="error"
                onClick={() => {
                  handleCloseTaskDialog();
                  handleDeleteTask(selectedTask._id);
                }}
                startIcon={<DeleteIcon />}
              >
                Delete
              </Button>
            </DialogActions>
          </>
        ) : (taskDialogType === 'add' || taskDialogType === 'edit') ? (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography id="task-dialog-title" variant="h6">{taskDialogType === 'add' ? 'Create New Task' : 'Edit Task'}</Typography>
                <IconButton onClick={handleCloseTaskDialog}>
                  <CloseIcon />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <form id="task-form" onSubmit={handleTaskFormSubmit}>
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={12}>
                    <TextField
                      label="Task Title"
                      fullWidth
                      required
                      value={taskData.title}
                      onChange={(e) => setTaskData({ ...taskData, title: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>Assigned To</InputLabel>
                      <Select
                        value={taskData.assignedTo}
                        label="Assigned To"
                        onChange={(e) => setTaskData({ ...taskData, assignedTo: e.target.value })}
                      >
                        {users.map((user) => (
                          <MenuItem key={user._id} value={user._id}>
                            {user.name} ({user.email})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>Category</InputLabel>
                      <Select
                        value={taskData.category}
                        label="Category"
                        onChange={(e) => setTaskData({ ...taskData, category: e.target.value })}
                      >
                        <MenuItem value="General">General</MenuItem>
                        <MenuItem value="Development">Development</MenuItem>
                        <MenuItem value="Design">Design</MenuItem>
                        <MenuItem value="Marketing">Marketing</MenuItem>
                        <MenuItem value="HR">HR</MenuItem>
                        <MenuItem value="Finance">Finance</MenuItem>
                        <MenuItem value="Operations">Operations</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>Priority</InputLabel>
                      <Select
                        value={taskData.priority}
                        label="Priority"
                        onChange={(e) => setTaskData({ ...taskData, priority: e.target.value })}
                      >
                        <MenuItem value="Low">Low</MenuItem>
                        <MenuItem value="Medium">Medium</MenuItem>
                        <MenuItem value="High">High</MenuItem>
                        <MenuItem value="Urgent">Urgent</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth required>
                      <InputLabel>Status</InputLabel>
                      <Select
                        value={taskData.status}
                        label="Status"
                        onChange={(e) => setTaskData({ ...taskData, status: e.target.value })}
                      >
                        <MenuItem value="Not Started">Not Started</MenuItem>
                        <MenuItem value="In Progress">In Progress</MenuItem>
                        <MenuItem value="Completed">Completed</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Deadline"
                      type="date"
                      fullWidth
                      required
                      InputLabelProps={{ shrink: true }}
                      value={taskData.deadline}
                      onChange={(e) => setTaskData({ ...taskData, deadline: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="Description"
                      fullWidth
                      multiline
                      rows={4}
                      value={taskData.description}
                      onChange={(e) => setTaskData({ ...taskData, description: e.target.value })}
                    />
                  </Grid>
                </Grid>
              </form>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseTaskDialog} color="inherit">Cancel</Button>
              <Button
                type="submit"
                form="task-form"
                variant="contained"
                color="primary"
              >
                {taskDialogType === 'add' ? 'Create Task' : 'Update Task'}
              </Button>
            </DialogActions>
          </>
        ) : null}
      </Dialog>

      {/* User Profile Dialog */}
      <Dialog
        open={showUserProfileDialog}
        onClose={handleCloseUserProfile}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">User Profile</Typography>
            <IconButton onClick={handleCloseUserProfile}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {selectedUserProfile && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                  <Avatar
                    sx={{
                      width: 120,
                      height: 120,
                      fontSize: 48,
                      mb: 2,
                      bgcolor: 'primary.main'
                    }}
                  >
                    {selectedUserProfile.name ? selectedUserProfile.name.charAt(0).toUpperCase() : 'U'}
                  </Avatar>
                  <Typography variant="h5" gutterBottom>
                    {selectedUserProfile.name || 'User'}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    {selectedUserProfile.job || 'Employee'}
                  </Typography>
                  <Chip
                    label="Active"
                    color="success"
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Grid>
              <Grid item xs={12} md={8}>
                <Typography variant="h6" gutterBottom>Personal Information</Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Full Name</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>{selectedUserProfile.name || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>{selectedUserProfile.email || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Job Title</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>{selectedUserProfile.job || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Department</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>{selectedUserProfile.department || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Birthdate</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedUserProfile.birthdate ? new Date(selectedUserProfile.birthdate).toLocaleDateString() : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Account Created</Typography>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                      {selectedUserProfile.creationDate ? new Date(selectedUserProfile.creationDate).toLocaleDateString() : 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUserProfile}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Application Feedback Dialog */}
      <Dialog
        open={feedbackDialogOpen}
        onClose={handleCloseFeedbackDialog}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              {feedbackAction === 'approve' ? 'Approve Application' : 'Reject Application'}
            </Typography>
            <IconButton onClick={handleCloseFeedbackDialog} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Typography variant="body1" sx={{ mb: 2 }}>
            {feedbackAction === 'approve'
              ? 'Please provide any feedback or notes for approving this application.'
              : 'Please provide a reason for rejecting this application.'}
          </Typography>
          <TextField
            label="Feedback"
            multiline
            rows={4}
            fullWidth
            value={feedbackText}
            onChange={(e) => setFeedbackText(e.target.value)}
            placeholder={feedbackAction === 'approve'
              ? 'Enter approval notes (optional)'
              : 'Enter rejection reason'}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseFeedbackDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitFeedback}
            variant="contained"
            color={feedbackAction === 'approve' ? 'success' : 'error'}
          >
            {feedbackAction === 'approve' ? 'Approve' : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
};

export default NewHRDashboard;
