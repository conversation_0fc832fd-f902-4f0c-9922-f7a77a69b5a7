import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  CircularProgress,
  Link,
  Box,
  Grid,
  Paper,
  InputAdornment,
  IconButton,
  Divider,
  useTheme,
  useMediaQuery
} from "@mui/material";
import {
  Lock,
  Email,
  Visibility,
  VisibilityOff,
  BusinessCenter
} from "@mui/icons-material";
import {
  showSuccessToast,
  showErrorToast,
  TOAST_CATEGORIES
} from "../Utils/toastUtils";
import { useNavigate } from "react-router-dom";
import "react-toastify/dist/ReactToastify.css";
import "../Styles/Login.css";
import api from "../Services/ApiService";
import { jwtDecode } from "jwt-decode";
import Logo from "../components/Logo";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");  // To display error messages
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();  // Hook to navigate
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Check if the user is already logged in - instant redirect
  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      try {
        const decoded = jwtDecode(token);
        // Check if token is expired
        if (decoded.exp * 1000 > Date.now()) {
          // Instant redirect based on role
          const roleRoutes = {
            "hr": "/hr-dashboard",
            "admin": "/dashboard",
            "user": "/user-dashboard"
          };
          const route = roleRoutes[decoded.role];
          if (route) {
            navigate(route, { replace: true });
            return;
          }
        } else {
          // Token expired, remove it
          localStorage.removeItem("token");
        }
      } catch (error) {
        // Invalid token, remove it
        localStorage.removeItem("token");
      }
    }
  }, [navigate]);

  // Handle login
  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");  // Clear previous error message

    try {
      console.log("Attempting login with email:", email);

      // Basic validation
      if (!email || !password) {
        setError("Please enter both email and password!");
        toast.error("Please enter both email and password!");
        setLoading(false);
        return;
      }

      // Log the login attempt details
      console.log("Sending login request with:", {
        email,
        password: password ? '********' : 'empty'
      });

      // Use async/await properly without mixing with .then()
      const response = await api.post("/auth/login", { email, password });
      const responseData = response.data;

      console.log("Login successful! Response data:", {
        success: responseData.success,
        role: responseData.role,
        name: responseData.name,
        userId: responseData.userId,
        tokenReceived: !!responseData.token
      });

      // Store the token in localStorage
      if (responseData.token) {
        console.log("Storing token in localStorage");
        localStorage.setItem("token", responseData.token);
      } else {
        console.error("No token received in response");
        throw new Error("No token received");
      }

      // Show success message (non-blocking)
      showSuccessToast("Login successful!", TOAST_CATEGORIES.AUTH, "login");

      // Instant redirect based on role - no delays
      const roleRoutes = {
        "admin": "/dashboard",
        "hr": "/hr-dashboard",
        "user": "/user-dashboard"
      };

      const route = roleRoutes[responseData.role];
      if (route) {
        console.log(`Redirecting to ${route}...`);
        navigate(route, { replace: true });
      } else {
        console.error("Unknown role:", responseData.role);
        setError("Unknown user role: " + responseData.role);
        toast.error("Unknown user role. Please contact an administrator.");
        setLoading(false);
      }

    } catch (err) {
      console.error("Login error:", err);

      if (err.response) {
        const errorData = err.response.data;
        console.error('Login error response:', errorData);

        // Handle specific error types
        if (errorData.errorType === 'INVALID_CREDENTIALS') {
          setError("Invalid email or password");
          showErrorToast("Invalid email or password", TOAST_CATEGORIES.AUTH, "unauthorized");
        } else if (errorData.errorType === 'INVALID_EMAIL') {
          setError("Email address not found");
          showErrorToast("Email address not found", TOAST_CATEGORIES.AUTH, "unauthorized");
        } else if (errorData.errorType === 'INVALID_PASSWORD') {
          setError("Incorrect password");
          showErrorToast("Incorrect password", TOAST_CATEGORIES.AUTH, "unauthorized");
        } else if (errorData.errorType === 'MISSING_FIELDS') {
          setError("Please enter both email and password");
          showErrorToast("Please enter both email and password", TOAST_CATEGORIES.AUTH, "unauthorized");
        } else if (errorData.errorType === 'ACCOUNT_INACTIVE') {
          setError("Your account is inactive. Please contact an administrator.");
          showErrorToast("Account inactive", TOAST_CATEGORIES.AUTH, "unauthorized");
        } else {
          setError(errorData.message || "Authentication failed");
          showErrorToast(errorData.message || "Authentication failed", TOAST_CATEGORIES.AUTH, "unauthorized");
        }
      } else {
        setError("Connection error. Please try again later.");
        showErrorToast("Connection error. Please try again later.", TOAST_CATEGORIES.SYSTEM, "criticalError");
      }

      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        bgcolor: 'background.default',
        position: 'relative',
        overflow: 'hidden',
      }}
    >


      {/* Background Design Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: -100,
          right: -100,
          width: 400,
          height: 400,
          borderRadius: '50%',
          background: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
          opacity: 0.1,
          zIndex: 0,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: -150,
          left: -150,
          width: 500,
          height: 500,
          borderRadius: '50%',
          background: `linear-gradient(135deg, ${theme.palette.secondary.light} 0%, ${theme.palette.secondary.main} 100%)`,
          opacity: 0.1,
          zIndex: 0,
        }}
      />

      <Grid container sx={{ height: '100%' }}>
        {/* Left Side - Brand Section */}
        {!isMobile && (
          <Grid
            item
            xs={false}
            sm={false}
            md={6}
            lg={7}
            sx={{
              position: 'relative',
              background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.primary.main} 100%)`,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              color: 'white',
              p: 4,
              textAlign: 'center',
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                opacity: 0.1,
                backgroundImage: 'url(/images/pattern.svg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                zIndex: 1,
              }}
            />

            <Box sx={{ position: 'relative', zIndex: 2, maxWidth: 500 }}>
              <Box sx={{ mb: 4 }}>
                <Logo size="large" variant="dark" />
              </Box>

              <Typography variant="h3" component="h1" gutterBottom fontWeight={700}>
                Welcome to MjayTrack
              </Typography>

              <Typography variant="h6" gutterBottom sx={{ mb: 4, opacity: 0.9 }}>
                The complete HR management solution for modern enterprises
              </Typography>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 2,
                  alignItems: 'center',
                  mt: 6,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    p: 2,
                    borderRadius: 2,
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                    width: '100%',
                    maxWidth: 400,
                  }}
                >
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                    }}
                  >
                    <BusinessCenter sx={{ color: 'white' }} />
                  </Box>
                  <Box sx={{ textAlign: 'left' }}>
                    <Typography variant="subtitle1" fontWeight={600}>
                      Streamlined HR Processes
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Manage recruitment, onboarding, and employee data efficiently
                    </Typography>
                  </Box>
                </Box>

                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2,
                    p: 2,
                    borderRadius: 2,
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                    width: '100%',
                    maxWidth: 400,
                  }}
                >
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                    }}
                  >
                    <BusinessCenter sx={{ color: 'white' }} />
                  </Box>
                  <Box sx={{ textAlign: 'left' }}>
                    <Typography variant="subtitle1" fontWeight={600}>
                      Smart Talent Acquisition
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      AI-powered CV matching and candidate evaluation
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>
          </Grid>
        )}

        {/* Right Side - Login Form */}
        <Grid
          item
          xs={12}
          sm={12}
          md={6}
          lg={5}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            p: { xs: 2, sm: 4, md: 6 },
            position: 'relative',
            zIndex: 1,
          }}
        >
          <Box
            sx={{
              maxWidth: 450,
              width: '100%',
              mx: 'auto',
            }}
          >
            {isMobile && (
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Logo size="large" />
              </Box>
            )}

            <Paper
              elevation={isMobile ? 0 : 6}
              sx={{
                p: { xs: 3, sm: 4 },
                borderRadius: 3,
                bgcolor: 'background.paper',
              }}
            >
              <Box sx={{ mb: 4, textAlign: 'center' }}>
                <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
                  Sign In
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Enter your credentials to access your account
                </Typography>
              </Box>

              <form onSubmit={handleLogin}>
                <TextField
                  label="Email Address"
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email color="action" />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                />

                <TextField
                  label="Password"
                  variant="outlined"
                  fullWidth
                  margin="normal"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                />

                {error && (
                  <Box
                    sx={{
                      p: 2,
                      mb: 3,
                      borderRadius: 1,
                      bgcolor: 'error.light',
                      color: 'error.dark',
                      border: '1px solid',
                      borderColor: 'error.main',
                    }}
                  >
                    <Typography variant="body2" fontWeight={500}>
                      {error}
                    </Typography>
                  </Box>
                )}

                <Box sx={{ textAlign: 'right', mb: 2 }}>
                  <Link
                    href="/forgot-password"
                    variant="body2"
                    color="primary"
                    underline="hover"
                  >
                    Forgot password?
                  </Link>
                </Box>

                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  fullWidth
                  size="large"
                  disabled={loading}
                  sx={{
                    py: 1.5,
                    fontSize: '1rem',
                    fontWeight: 600,
                    boxShadow: 4,
                  }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : "Sign In"}
                </Button>
              </form>

              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  Don't have an account?{' '}
                  <Link
                    href="/apply"
                    variant="body2"
                    color="primary"
                    underline="hover"
                    fontWeight={600}
                  >
                    Apply for a job
                  </Link>
                </Typography>
              </Box>
            </Paper>

            <Box sx={{ mt: 4, textAlign: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                &copy; {new Date().getFullYear()} MjayTrack. All rights reserved.
              </Typography>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Login;
