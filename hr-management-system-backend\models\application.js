const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
  fullname: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  phone: {
    type: String,
    required: true,
  },
  cv: {
    type: String,
    required: true,
  },
  // Reference to the specific job
  jobId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Job',
    required: true,
  },
  // Store the job title at the time of application for quick reference
  jobTitle: {
    type: String,
    required: true,
  },
  // Store the position/job type at the time of application
  position: {
    type: String,
    required: true,
  },
  // Application status
  status: {
    type: String,
    enum: ['Pending', 'Approved', 'Rejected'],
    default: 'Pending'
  },
  // Feedback or notes from HR
  feedback: {
    type: String,
    default: ''
  },
  // NLP analysis results
  nlpResults: {
    type: Object,
    default: function() {
      return {}; // Initialize as empty object instead of null
    }
  },
  // Flag to indicate if NLP has been run on this application
  nlpProcessed: {
    type: Boolean,
    default: false
  },
  // Job match score (0-100)
  matchScore: {
    type: Number,
    default: 0
  },
  // Store the job ID that was used for matching (may be different from the applied job)
  matchedJobId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Job',
    default: null
  },
  // Semantic similarity score (0-1)
  semanticScore: {
    type: Number,
    default: 0
  }
}, { timestamps: true });

// Capitalize the model name here:
// Check if the model already exists to prevent recompilation
const Application = mongoose.models.Application || mongoose.model('Application', applicationSchema);

module.exports = Application;
