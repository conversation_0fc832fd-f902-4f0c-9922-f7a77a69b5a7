/* File: src/styles/apply.css */
.apply-container {
  max-width: 700px;
  margin: 2rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.apply-container h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #333;
}

.apply-form {
  display: flex;
  flex-direction: column;
}

.apply-form input,
.apply-form select,
.apply-form button {
  display: block;
  width: 100%;
  margin-bottom: 1rem;
  padding: 0.7rem;
  border-radius: 8px;
  border: 1px solid #ccc;
  font-size: 1rem;
}

.apply-form select {
  cursor: pointer;
  background-color: #f9f9f9;
}

.apply-form button, .apply-button {
  background-color: #007bff;
  color: white;
  cursor: pointer;
  border: none;
  font-weight: bold;
  transition: background-color 0.3s;
  margin-top: 1rem;
  padding: 0.7rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
}

.apply-form button:hover, .apply-button:hover {
  background-color: #0056b3;
}

.apply-form button:disabled, .apply-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #555;
}

.file-input {
  margin-bottom: 1.5rem;
}

.file-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #555;
}

.job-details {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #007bff;
}

.job-details h3 {
  margin-top: 0;
  color: #007bff;
}

.job-details p {
  margin: 0.5rem 0;
}

.error-message {
  color: #d9534f;
  text-align: center;
  padding: 1rem;
  background-color: #f8d7da;
  border-radius: 8px;
  margin-bottom: 1rem;
}
