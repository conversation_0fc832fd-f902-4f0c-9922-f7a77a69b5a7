const mongoose = require('mongoose');

// Knowledge Base Schema for RAG (Retrieval-Augmented Generation)
const knowledgeBaseSchema = new mongoose.Schema({
  // Document identification
  title: {
    type: String,
    required: true
  },

  // Document type
  type: {
    type: String,
    enum: [
      'policy',
      'procedure',
      'handbook',
      'faq',
      'guideline',
      'form_template',
      'legal_document',
      'training_material',
      'announcement',
      'best_practice'
    ],
    required: true
  },

  // Document category for organization
  category: {
    type: String,
    enum: [
      'hr_policies',
      'leave_management',
      'performance',
      'recruitment',
      'compensation',
      'benefits',
      'compliance',
      'training',
      'safety',
      'general'
    ],
    required: true
  },

  // Document content
  content: {
    // Full text content
    fullText: {
      type: String,
      required: true
    },

    // Structured sections for better retrieval
    sections: [{
      title: String,
      content: String,
      order: Number,
      // Vector embeddings for this section
      embedding: [Number]
    }],

    // Key-value pairs for structured data
    metadata: {
      type: Map,
      of: String
    },

    // Document summary
    summary: String,

    // Keywords for search
    keywords: [String],

    // FAQ pairs if applicable
    faqs: [{
      question: String,
      answer: String,
      embedding: [Number]
    }]
  },

  // Document source information
  source: {
    // Original file information
    fileName: String,
    filePath: String,
    fileType: String,
    fileSize: Number,

    // Source URL if web-based
    url: String,

    // Author/creator information
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },

    // Department that owns this document
    department: {
      type: String,
      enum: ['HR', 'Legal', 'IT', 'Finance', 'Operations', 'Executive', 'All']
    },

    // Version information
    version: {
      type: String,
      default: '1.0'
    },

    // Last updated information
    lastUpdated: Date,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },

  // Access control
  access: {
    // Visibility level
    visibility: {
      type: String,
      enum: ['public', 'internal', 'restricted', 'confidential'],
      default: 'internal'
    },

    // Roles that can access this document
    allowedRoles: [{
      type: String,
      enum: ['admin', 'hr', 'user', 'manager']
    }],

    // Specific users with access
    allowedUsers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],

    // Departments with access
    allowedDepartments: [String]
  },

  // Document status
  status: {
    type: String,
    enum: ['draft', 'active', 'archived', 'deprecated'],
    default: 'active'
  },

  // Vector embeddings for semantic search
  embeddings: {
    // Main document embedding
    document: [Number],

    // Title embedding
    title: [Number],

    // Summary embedding
    summary: [Number],

    // Model used for embeddings
    model: {
      type: String,
      default: 'sentence-transformers/all-MiniLM-L6-v2'
    },

    // Embedding generation timestamp
    generatedAt: Date
  },

  // Usage analytics
  analytics: {
    // View count
    views: {
      type: Number,
      default: 0
    },

    // Search hits
    searchHits: {
      type: Number,
      default: 0
    },

    // User interactions
    interactions: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      action: {
        type: String,
        enum: ['view', 'search', 'download', 'share', 'bookmark']
      },
      timestamp: Date,
      metadata: Object
    }],

    // Feedback and ratings
    feedback: [{
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      rating: {
        type: Number,
        min: 1,
        max: 5
      },
      helpful: Boolean,
      comment: String,
      timestamp: Date
    }]
  },

  // Related documents
  relationships: [{
    documentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'KnowledgeBase'
    },
    relationship: {
      type: String,
      enum: ['related', 'supersedes', 'superseded_by', 'references', 'referenced_by']
    },
    strength: {
      type: Number,
      min: 0,
      max: 1
    }
  }],

  // Tags for categorization
  tags: [String],

  // Approval workflow
  approval: {
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'needs_review'],
      default: 'pending'
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date,
    reviewComments: String
  },

  // Expiration and review
  lifecycle: {
    // When this document expires
    expiresAt: Date,

    // Review schedule
    reviewSchedule: {
      frequency: {
        type: String,
        enum: ['monthly', 'quarterly', 'annually', 'biannually']
      },
      nextReview: Date,
      lastReview: Date,
      reviewer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
knowledgeBaseSchema.index({ type: 1, category: 1 });
knowledgeBaseSchema.index({ status: 1 });
knowledgeBaseSchema.index({ 'access.visibility': 1 });
knowledgeBaseSchema.index({ 'access.allowedRoles': 1 });
knowledgeBaseSchema.index({ tags: 1 });
knowledgeBaseSchema.index({ 'content.keywords': 1 });
knowledgeBaseSchema.index({ 'source.department': 1 });
knowledgeBaseSchema.index({ 'lifecycle.expiresAt': 1 });

// Text index for full-text search
knowledgeBaseSchema.index({
  title: 'text',
  'content.fullText': 'text',
  'content.summary': 'text',
  'content.keywords': 'text'
});

// Virtual for checking if document is expired
knowledgeBaseSchema.virtual('isExpired').get(function() {
  return this.lifecycle.expiresAt && this.lifecycle.expiresAt < new Date();
});

// Virtual for checking if document needs review
knowledgeBaseSchema.virtual('needsReview').get(function() {
  return this.lifecycle.reviewSchedule.nextReview && 
         this.lifecycle.reviewSchedule.nextReview < new Date();
});

// Method to record interaction
knowledgeBaseSchema.methods.recordInteraction = function(userId, action, metadata = {}) {
  this.analytics.interactions.push({
    userId,
    action,
    metadata,
    timestamp: new Date()
  });

  // Update counters
  if (action === 'view') {
    this.analytics.views += 1;
  } else if (action === 'search') {
    this.analytics.searchHits += 1;
  }

  return this.save();
};

// Method to add feedback
knowledgeBaseSchema.methods.addFeedback = function(userId, feedbackData) {
  this.analytics.feedback.push({
    userId,
    ...feedbackData,
    timestamp: new Date()
  });
  return this.save();
};

// Static method for semantic search
knowledgeBaseSchema.statics.semanticSearch = function(query, userRole, department, options = {}) {
  // This would integrate with vector database for semantic search
  // For now, using text search as fallback
  const searchCriteria = {
    status: 'active',
    $text: { $search: query },
    'access.allowedRoles': { $in: [userRole] }
  };

  if (department) {
    searchCriteria.$or = [
      { 'access.allowedDepartments': department },
      { 'source.department': 'All' }
    ];
  }

  return this.find(searchCriteria)
    .sort({ score: { $meta: 'textScore' } })
    .limit(options.limit || 10);
};

// Static method to get documents by category
knowledgeBaseSchema.statics.getByCategory = function(category, userRole, options = {}) {
  const query = {
    category,
    status: 'active',
    'access.allowedRoles': { $in: [userRole] }
  };

  return this.find(query)
    .sort({ 'analytics.views': -1, updatedAt: -1 })
    .limit(options.limit || 20);
};

// Static method to get popular documents
knowledgeBaseSchema.statics.getPopular = function(userRole, limit = 10) {
  return this.find({
    status: 'active',
    'access.allowedRoles': { $in: [userRole] }
  })
  .sort({ 'analytics.views': -1 })
  .limit(limit);
};

// Static method to get recently updated documents
knowledgeBaseSchema.statics.getRecentlyUpdated = function(userRole, limit = 10) {
  return this.find({
    status: 'active',
    'access.allowedRoles': { $in: [userRole] }
  })
  .sort({ updatedAt: -1 })
  .limit(limit);
};

// Pre-save middleware to update embeddings if content changed
knowledgeBaseSchema.pre('save', function(next) {
  if (this.isModified('content.fullText') || this.isModified('title')) {
    // Mark embeddings as needing regeneration
    this.embeddings.generatedAt = null;
  }
  next();
});

const KnowledgeBase = mongoose.model('KnowledgeBase', knowledgeBaseSchema);

module.exports = KnowledgeBase;
