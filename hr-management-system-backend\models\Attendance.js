const mongoose = require('mongoose');

const attendanceSchema = new mongoose.Schema({
  // The user who is checking in/out
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Date of the attendance record (stored as YYYY-MM-DD)
  date: {
    type: String,
    required: true
  },

  // Check-in time
  checkIn: {
    type: Date,
    required: true
  },

  // Check-out time (can be null if user hasn't checked out yet)
  checkOut: {
    type: Date,
    default: null
  },

  // Total hours worked (calculated when user checks out)
  hoursWorked: {
    type: Number,
    default: 0
  },

  // Status of the attendance (Present, Late, Absent, etc.)
  status: {
    type: String,
    enum: ['Present', 'Late', 'Absent', 'Half-Day', 'On Leave'],
    default: 'Present'
  },

  // Optional notes about the attendance
  notes: {
    type: String,
    default: ''
  },

  // Location data for check-in (optional) - can be string or object
  checkInLocation: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },

  // Location data for check-out (optional) - can be string or object
  checkOutLocation: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  }
}, { timestamps: true });

// Calculate hours worked when checking out
attendanceSchema.methods.calculateHoursWorked = function() {
  if (this.checkIn && this.checkOut) {
    const checkInTime = new Date(this.checkIn).getTime();
    const checkOutTime = new Date(this.checkOut).getTime();
    const diffMs = checkOutTime - checkInTime;
    const diffHrs = diffMs / (1000 * 60 * 60);
    this.hoursWorked = parseFloat(diffHrs.toFixed(2));
  }
  return this.hoursWorked;
};

// Create a compound index on userId and date to ensure a user can only have one attendance record per day
attendanceSchema.index({ userId: 1, date: 1 }, { unique: true });

// Check if the model already exists to prevent recompilation
const Attendance = mongoose.models.Attendance || mongoose.model('Attendance', attendanceSchema);

module.exports = Attendance;
