import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  People as PeopleIcon,
  Work as WorkIcon,
  EventNote as EventNoteIcon,
  Assignment as AssignmentIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  PieC<PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import api from '../../Services/ApiService';
import { toast } from 'react-toastify';

// Colors for charts
const COLORS = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];

// Stat card styles
const statCardStyle = {
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between'
};

const statIconStyle = {
  fontSize: 40,
  opacity: 0.7,
  color: 'primary.main'
};

const DashboardAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setMonth(new Date().getMonth() - 1)), // Last month
    endDate: new Date()
  });

  // Statistics state
  const [stats, setStats] = useState({
    users: {
      total: 0,
      byRole: []
    },
    logins: {
      total: 0,
      successful: 0,
      failed: 0,
      byDay: [],
      byBrowser: [],
      byDevice: []
    },
    applications: {
      total: 0,
      approved: 0,
      rejected: 0,
      pending: 0
    },
    leaveRequests: {
      total: 0,
      approved: 0,
      rejected: 0,
      pending: 0,
      byType: []
    },
    tasks: {
      total: 0,
      completed: 0,
      inProgress: 0,
      pending: 0,
      byDepartment: []
    },
    jobs: {
      total: 0,
      active: 0,
      byDepartment: []
    }
  });

  // Fetch statistics
  const fetchStatistics = async () => {
    setLoading(true);
    setError(null);

    // Record the start time of the fetch operation
    const fetchStartTime = new Date();

    try {
      // Format dates for API
      const startDate = dateRange.startDate.toISOString().split('T')[0];
      const endDate = dateRange.endDate.toISOString().split('T')[0];

      console.log('Fetching analytics data for date range:', startDate, 'to', endDate);

      try {
        // Use Promise.all to fetch all statistics in parallel for better performance
        const [
          loginStatsResponse,
          userStatsResponse,
          applicationStatsResponse,
          leaveStatsResponse,
          taskStatsResponse,
          jobStatsResponse
        ] = await Promise.all([
          // Fetch login statistics
          api.get('/api/statistics/login-statistics', {
            params: { startDate, endDate }
          }).catch(error => {
            console.error('Error fetching login statistics:', error);
            return { data: { summary: {}, loginsByDay: [], loginsByBrowser: [], loginsByDevice: [] } };
          }),

          // Fetch user statistics
          api.get('/api/statistics/user-statistics').catch(error => {
            console.error('Error fetching user statistics:', error);
            return { data: { totalUsers: 0, usersByRole: [] } };
          }),

          // Fetch application statistics
          api.get('/api/statistics/application-statistics', {
            params: { startDate, endDate }
          }).catch(error => {
            console.error('Error fetching application statistics:', error);
            // Log more details about the error
            if (error.response) {
              console.error('Response error data:', error.response.data);
              console.error('Response error status:', error.response.status);
            } else if (error.request) {
              console.error('Request error:', error.request);
            } else {
              console.error('Error message:', error.message);
            }
            return { data: { totalApplications: 0, approvedApplications: 0, rejectedApplications: 0, pendingApplications: 0 } };
          }),

          // Fetch leave request statistics
          api.get('/api/statistics/leave-statistics', {
            params: { startDate, endDate }
          }).catch(error => {
            console.error('Error fetching leave statistics:', error);
            // Log more details about the error
            if (error.response) {
              console.error('Response error data:', error.response.data);
              console.error('Response error status:', error.response.status);
            } else if (error.request) {
              console.error('Request error:', error.request);
            } else {
              console.error('Error message:', error.message);
            }
            return { data: { totalRequests: 0, approvedRequests: 0, rejectedRequests: 0, pendingRequests: 0, requestsByType: [] } };
          }),

          // Fetch task statistics
          api.get('/api/statistics/task-statistics', {
            params: { startDate, endDate }
          }).catch(error => {
            console.error('Error fetching task statistics:', error);
            return { data: { totalTasks: 0, completedTasks: 0, inProgressTasks: 0, pendingTasks: 0, tasksByDepartment: [] } };
          }),

          // Fetch job statistics
          api.get('/api/statistics/job-statistics').catch(error => {
            console.error('Error fetching job statistics:', error);
            return { data: { totalJobs: 0, activeJobs: 0, jobsByDepartment: [] } };
          })
        ]);

        // Log responses for debugging
        console.log('Login statistics response:', loginStatsResponse.data);
        console.log('User statistics response:', userStatsResponse.data);
        console.log('Application statistics response:', applicationStatsResponse.data);
        console.log('Leave statistics response:', leaveStatsResponse.data);
        console.log('Task statistics response:', taskStatsResponse.data);
        console.log('Job statistics response:', jobStatsResponse.data);

        // Log all responses for debugging
        console.log('All API responses:', {
          users: userStatsResponse.data,
          applications: applicationStatsResponse.data,
          leaves: leaveStatsResponse.data,
          jobs: jobStatsResponse.data
        });

        // Check if we have real data
        const hasRealData =
          userStatsResponse.data.totalUsers > 0 ||
          applicationStatsResponse.data.totalApplications > 0 ||
          leaveStatsResponse.data.totalRequests > 0 ||
          jobStatsResponse.data.totalJobs > 0;

        if (!hasRealData) {
          console.warn('No real data found in any of the API responses');
          toast.warning('No data found. Please make sure there is data in the database.');
        }

        // Log specific data for applications and leave requests
        console.log('Application data details:', {
          total: applicationStatsResponse.data.totalApplications,
          approved: applicationStatsResponse.data.approvedApplications,
          rejected: applicationStatsResponse.data.rejectedApplications,
          pending: applicationStatsResponse.data.pendingApplications
        });

        console.log('Leave request data details:', {
          total: leaveStatsResponse.data.totalRequests,
          approved: leaveStatsResponse.data.approvedRequests,
          rejected: leaveStatsResponse.data.rejectedRequests,
          pending: leaveStatsResponse.data.pendingRequests
        });

        // Combine all statistics
        setStats({
          users: {
            total: userStatsResponse.data.totalUsers || 0,
            byRole: userStatsResponse.data.usersByRole || []
          },
          logins: {
            total: loginStatsResponse.data.summary?.totalLogins || 0,
            successful: loginStatsResponse.data.summary?.successfulLogins || 0,
            failed: loginStatsResponse.data.summary?.failedLogins || 0,
            byDay: loginStatsResponse.data.loginsByDay || [],
            byBrowser: loginStatsResponse.data.loginsByBrowser || [],
            byDevice: loginStatsResponse.data.loginsByDevice || []
          },
          applications: {
            total: applicationStatsResponse.data.totalApplications || 0,
            approved: applicationStatsResponse.data.approvedApplications || 0,
            rejected: applicationStatsResponse.data.rejectedApplications || 0,
            pending: applicationStatsResponse.data.pendingApplications || 0
          },

          // Application data logged for debugging
          // console.log('Application data received:', applicationStatsResponse.data)
          leaveRequests: {
            total: leaveStatsResponse.data.totalRequests || 0,
            approved: leaveStatsResponse.data.approvedRequests || 0,
            rejected: leaveStatsResponse.data.rejectedRequests || 0,
            pending: leaveStatsResponse.data.pendingRequests || 0,
            byType: leaveStatsResponse.data.requestsByType || []
          },

          // Leave request data logged for debugging
          // console.log('Leave request data received:', leaveStatsResponse.data)
          tasks: {
            total: taskStatsResponse.data.totalTasks || 0,
            completed: taskStatsResponse.data.completedTasks || 0,
            inProgress: taskStatsResponse.data.inProgressTasks || 0,
            pending: taskStatsResponse.data.pendingTasks || 0,
            byDepartment: taskStatsResponse.data.tasksByDepartment || []
          },
          jobs: {
            total: jobStatsResponse.data.totalJobs || 0,
            active: jobStatsResponse.data.activeJobs || 0,
            byDepartment: jobStatsResponse.data.jobsByDepartment || []
          }
        });
      } catch (apiError) {
        console.error('API Error:', apiError);
        toast.error('Failed to fetch statistics data');
        setError('Failed to fetch statistics. Please try again later.');
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);

      // Provide more specific error message
      const errorMessage = error.response
        ? `Error: ${error.response.status} - ${error.response.data?.message || 'Unknown error'}`
        : 'Network error or server not responding. Please check your connection.';

      setError(`Failed to fetch statistics: ${errorMessage}`);
      toast.error('Failed to fetch statistics data');

      // Log detailed error information for debugging
      console.log('API Error Details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      // Keep any existing data that might be available
      console.log('Keeping existing data if available');

      // Don't use mock data - we want to show real data only
    } finally {
      setLoading(false);
      // Set the last updated timestamp
      setLastUpdated(new Date());
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStatistics();
  }, []);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Generate PDF report
  const generatePDFReport = () => {
    toast.info('Generating PDF report...');
    // Implementation would go here
  };

  return (
    <Box sx={{ overflow: 'auto', height: '100%' }}>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h2" fontWeight={600}>
          Dashboard Analytics
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={generatePDFReport}
            sx={{ mr: 1 }}
          >
            Export Report
          </Button>
          <Button
            startIcon={<RefreshIcon />}
            onClick={fetchStatistics}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Button
                variant="contained"
                onClick={fetchStatistics}
                startIcon={<RefreshIcon />}
                sx={{ mr: 2 }}
              >
                Refresh Dashboard Data
              </Button>
              {lastUpdated && (
                <Typography variant="body2" color="text.secondary">
                  Last updated: {lastUpdated.toLocaleTimeString()} on {lastUpdated.toLocaleDateString()}
                </Typography>
              )}
            </Box>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="body2" color="text.secondary" align="right">
              Showing data from {dateRange.startDate.toLocaleDateString()} to {dateRange.endDate.toLocaleDateString()}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : stats.users.total === 0 &&
          stats.applications.total === 0 &&
          stats.leaveRequests.total === 0 &&
          stats.jobs.total === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center', my: 3 }}>
          <Box sx={{ mb: 2 }}>
            <AssignmentIcon sx={{ fontSize: 60, color: 'text.secondary', opacity: 0.5 }} />
          </Box>
          <Typography variant="h5" gutterBottom>No Data Available</Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            There is no data in the system to display analytics. Start by adding users, jobs, applications, or tasks.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={fetchStatistics}
            startIcon={<RefreshIcon />}
          >
            Refresh Data
          </Button>
        </Paper>
      ) : (
        <>
          {/* Key Statistics Cards */}
          <Typography variant="h6" sx={{ mb: 2 }}>
            Key Statistics
          </Typography>

          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={statCardStyle}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box>
                      <Typography variant="h4" component="div" fontWeight={600}>
                        {stats.users.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Users
                      </Typography>
                    </Box>
                    <PeopleIcon sx={statIconStyle} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={statCardStyle}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box>
                      <Typography variant="h4" component="div" fontWeight={600}>
                        {stats.applications.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Applications
                      </Typography>
                    </Box>
                    <AssignmentIcon sx={statIconStyle} />
                  </Box>
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="caption" color="success.main">
                      Approved: {stats.applications.approved}
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      Rejected: {stats.applications.rejected}
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      Pending: {stats.applications.pending}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={statCardStyle}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box>
                      <Typography variant="h4" component="div" fontWeight={600}>
                        {stats.leaveRequests.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Leave Requests
                      </Typography>
                    </Box>
                    <EventNoteIcon sx={statIconStyle} />
                  </Box>
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="caption" color="success.main">
                      Approved: {stats.leaveRequests.approved}
                    </Typography>
                    <Typography variant="caption" color="error.main">
                      Rejected: {stats.leaveRequests.rejected}
                    </Typography>
                    <Typography variant="caption" color="info.main">
                      Pending: {stats.leaveRequests.pending}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={statCardStyle}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Box>
                      <Typography variant="h4" component="div" fontWeight={600}>
                        {stats.jobs.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Jobs Posted
                      </Typography>
                    </Box>
                    <WorkIcon sx={statIconStyle} />
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="caption" color="success.main">
                      Active: {stats.jobs.active}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Charts */}
          <Typography variant="h6" sx={{ mb: 2, mt: 4 }}>
            Analytics
          </Typography>

          <Grid container spacing={3} sx={{ pb: 4 }}>
            {/* Users Chart */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="User Distribution" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {stats.users.byRole && stats.users.byRole.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={stats.users.byRole}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="count"
                            nameKey="role"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {stats.users.byRole.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <RechartsTooltip formatter={(value, name) => [value, `${name.charAt(0).toUpperCase() + name.slice(1)}s`]} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    ) : (
                      <Box sx={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: 'text.secondary'
                      }}>
                        <PeopleIcon sx={{ fontSize: 40, mb: 1, opacity: 0.5 }} />
                        <Typography variant="body1">No user data available</Typography>
                        <Typography variant="caption">Add users to see distribution</Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Jobs Chart */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Jobs Status" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {stats.jobs.total > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              { name: 'Active', value: stats.jobs.active },
                              { name: 'Inactive', value: stats.jobs.total - stats.jobs.active }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            <Cell fill="#4CAF50" />
                            <Cell fill="#9E9E9E" />
                          </Pie>
                          <RechartsTooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    ) : (
                      <Box sx={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: 'text.secondary'
                      }}>
                        <WorkIcon sx={{ fontSize: 40, mb: 1, opacity: 0.5 }} />
                        <Typography variant="body1">No job data available</Typography>
                        <Typography variant="caption">Add jobs to see distribution</Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Leave Requests Chart */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Leave Requests Status" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {stats.leaveRequests.total > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: 'Approved', value: stats.leaveRequests.approved, fill: '#4CAF50' },
                            { name: 'Rejected', value: stats.leaveRequests.rejected, fill: '#F44336' },
                            { name: 'Pending', value: stats.leaveRequests.pending, fill: '#FFC107' }
                          ]}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip formatter={(value) => [`${value} requests`, 'Count']} />
                          <Bar dataKey="value" name="Count" radius={[4, 4, 0, 0]}>
                            {[
                              { name: 'Approved', value: stats.leaveRequests.approved, fill: '#4CAF50' },
                              { name: 'Rejected', value: stats.leaveRequests.rejected, fill: '#F44336' },
                              { name: 'Pending', value: stats.leaveRequests.pending, fill: '#FFC107' }
                            ].map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.fill} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    ) : (
                      <Box sx={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: 'text.secondary'
                      }}>
                        <EventNoteIcon sx={{ fontSize: 40, mb: 1, opacity: 0.5 }} />
                        <Typography variant="body1">No leave request data available</Typography>
                        <Typography variant="caption">Submit leave requests to see distribution</Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Applications Chart */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Applications Status" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {stats.applications.total > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: 'Approved', value: stats.applications.approved, fill: '#4CAF50' },
                            { name: 'Rejected', value: stats.applications.rejected, fill: '#F44336' },
                            { name: 'Pending', value: stats.applications.pending, fill: '#FFC107' }
                          ]}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <RechartsTooltip formatter={(value) => [`${value} applications`, 'Count']} />
                          <Bar dataKey="value" name="Count" radius={[4, 4, 0, 0]}>
                            {[
                              { name: 'Approved', value: stats.applications.approved, fill: '#4CAF50' },
                              { name: 'Rejected', value: stats.applications.rejected, fill: '#F44336' },
                              { name: 'Pending', value: stats.applications.pending, fill: '#FFC107' }
                            ].map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.fill} />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>
                    ) : (
                      <Box sx={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: 'text.secondary'
                      }}>
                        <AssignmentIcon sx={{ fontSize: 40, mb: 1, opacity: 0.5 }} />
                        <Typography variant="body1">No application data available</Typography>
                        <Typography variant="caption">Submit applications to see distribution</Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>



            {/* Login Activity Chart */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardHeader title="Login Success Rate" />
                <Divider />
                <CardContent>
                  <Box sx={{ height: 300 }}>
                    {stats.logins.total > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              { name: 'Successful', value: stats.logins.successful },
                              { name: 'Failed', value: stats.logins.failed }
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            <Cell fill="#4CAF50" />
                            <Cell fill="#F44336" />
                          </Pie>
                          <RechartsTooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    ) : (
                      <Box sx={{
                        height: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        flexDirection: 'column',
                        color: 'text.secondary'
                      }}>
                        <PeopleIcon sx={{ fontSize: 40, mb: 1, opacity: 0.5 }} />
                        <Typography variant="body1">No login activity data available</Typography>
                        <Typography variant="caption">User logins will be tracked here</Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Empty Grid Item for Balance */}
            <Grid item xs={12} md={6}></Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default DashboardAnalytics;
