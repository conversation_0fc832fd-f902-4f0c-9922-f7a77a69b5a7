import sys
import os
import pdfplumber
from paddleocr import PaddleOC<PERSON>
from pdf2image import convert_from_path
import tempfile

def extract_text_from_pdf(file_path):
    """
    Extract text from a PDF file using pdfplumber and PaddleOCR as a fallback
    
    Args:
        file_path (str): Path to the PDF file
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        print(f"Attempting to open PDF file: {file_path}", file=sys.stderr)
        
        # Check if file exists
        if not os.path.exists(file_path):
            print(f"File does not exist: {file_path}", file=sys.stderr)
            return f"File not found: {file_path}"
        
        # First try: Extract text using pdfplumber
        try:
            with pdfplumber.open(file_path) as pdf:
                print(f"PDF opened successfully with pdfplumber", file=sys.stderr)
                
                # Extract text from each page
                text = ""
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n\n"
                
                # If we got text, return it
                if text.strip():
                    return text
                else:
                    print("No text extracted with pdfplumber, trying OCR...", file=sys.stderr)
        except Exception as e:
            print(f"Error with pdfplumber: {str(e)}, trying OCR...", file=sys.stderr)
        
        # Second try: Use PaddleOCR
        try:
            print("Initializing PaddleOCR...", file=sys.stderr)
            ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False)
            
            # Create a temporary directory for the images
            with tempfile.TemporaryDirectory() as temp_dir:
                # Convert PDF to images
                images = convert_from_path(file_path, output_folder=temp_dir)
                print(f"Converted PDF to {len(images)} images", file=sys.stderr)
                
                # Extract text from each image using PaddleOCR
                ocr_text = ""
                for i, image in enumerate(images):
                    print(f"Processing image {i+1}/{len(images)}", file=sys.stderr)
                    
                    # Save the image to a temporary file
                    img_path = os.path.join(temp_dir, f"page_{i}.jpg")
                    image.save(img_path)
                    
                    # Run OCR on the image
                    result = ocr.ocr(img_path, cls=True)
                    
                    # Extract text from the result
                    page_text = ""
                    if result:
                        for line in result[0]:
                            if line and len(line) >= 1:
                                if isinstance(line, list) and len(line) > 1:
                                    text_info = line[1]
                                    if isinstance(text_info, tuple) and len(text_info) > 0:
                                        page_text += text_info[0] + " "
                    
                    ocr_text += page_text + "\n\n"
                
                if ocr_text.strip():
                    return ocr_text
                else:
                    return "No text could be extracted from the PDF"
        except Exception as e:
            print(f"Error with PaddleOCR: {str(e)}", file=sys.stderr)
            return f"Error processing PDF with OCR: {str(e)}"
            
    except Exception as e:
        print(f"Error extracting text from PDF: {str(e)}", file=sys.stderr)
        return f"Error processing PDF: {str(e)}"
