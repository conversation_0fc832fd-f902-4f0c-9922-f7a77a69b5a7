import { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { jwtDecode } from 'jwt-decode';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  SupervisorAccount as SupervisorAccountIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Work as WorkIcon,
  CalendarToday as CalendarTodayIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Close as CloseIcon,
  Security as SecurityIcon,
  History as HistoryIcon,
  Description as DescriptionIcon,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
} from '@mui/icons-material';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Button,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Divider,
  IconButton,
  Tooltip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import api from '../Services/ApiService';
import DashboardLayout from '../components/layout/DashboardLayout';
import StatCard from '../components/dashboard/StatCard';
import DataTable from '../components/dashboard/DataTable';
import AuditTrail from '../components/admin/AuditTrail';
import LoginHistory from '../components/admin/LoginHistory';
import UserAccountManagement from '../components/admin/UserAccountManagement';
import DashboardAnalytics from '../components/admin/DashboardAnalytics';
import ReportGenerator from '../components/admin/ReportGenerator';

const NewAdminDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeSection, setActiveSection] = useState('dashboard');
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authChecking, setAuthChecking] = useState(true);
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const userSearchTimeoutRef = useRef(null);

  // User form data
  const [userData, setUserData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'user',
    job: '',
    department: 'Other',
    birthdate: ''
  });

  // Check authentication on component mount - instant validation
  useEffect(() => {
    setAuthChecking(true);
    const token = localStorage.getItem('token');

    if (!token) {
      toast.error('You must be logged in to access this page');
      navigate('/', { replace: true });
      return;
    }

    try {
      // Quick token validation without API call
      const decoded = jwtDecode(token);

      // Check if token is expired
      if (decoded.exp * 1000 <= Date.now()) {
        toast.error('Session expired. Please log in again.');
        localStorage.removeItem('token');
        navigate('/', { replace: true });
        return;
      }

      // Check role authorization
      if (decoded.role !== 'admin') {
        toast.error('You do not have permission to access this page');
        navigate('/', { replace: true });
        return;
      }

      // Authentication successful - proceed immediately
      setIsAuthenticated(true);
      setAuthChecking(false);
      fetchUsers();

    } catch (error) {
      console.error('Token validation error:', error);
      toast.error('Authentication failed. Please log in again.');
      localStorage.removeItem('token');
      navigate('/', { replace: true });
    }
  }, [navigate]);

  // Cleanup function to clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (userSearchTimeoutRef.current) {
        clearTimeout(userSearchTimeoutRef.current);
      }
    };
  }, []);

  const handleUserSearch = async (query) => {
    // Update the search query state
    setUserSearchQuery(query);

    // Clear any existing timeout
    if (userSearchTimeoutRef.current) {
      clearTimeout(userSearchTimeoutRef.current);
    }

    // Set a new timeout for debouncing
    userSearchTimeoutRef.current = setTimeout(async () => {
      try {
        setLoading(true);
        const response = await api.get('/admin/users/search', {
          params: { search: query }
        });

        if (Array.isArray(response.data)) {
          setUsers(response.data);
        } else if (response.data && Array.isArray(response.data.users)) {
          setUsers(response.data.users);
        } else {
          console.warn('User search data is not in expected format:', response.data);
        }
      } catch (error) {
        console.error('Error searching users:', error);
        toast.error('Error searching users');
      } finally {
        setLoading(false);
      }
    }, 300); // 300ms delay for more responsive feel
  };

  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Optimized single API call with instant processing
      const response = await api.get('/admin/users/all');
      const users = response.data || [];

      console.log('Users fetched successfully:', users);
      setUsers(users);

      // Calculate stats instantly without additional processing
      const stats = {
        totalUsers: users.length,
        adminUsers: users.filter(user => user.role === 'admin').length,
        hrUsers: users.filter(user => user.role === 'hr').length,
        normalUsers: users.filter(user => user.role === 'user').length
      };

      setStats(stats);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users!');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    // Instant logout - no API calls needed
    localStorage.removeItem('token');
    toast.info('You have been logged out');
    navigate('/', { replace: true });
  };

  const handleOpenDialog = (type, user = null) => {
    setDialogType(type);

    if (type === 'add') {
      setUserData({
        name: '',
        email: '',
        password: '',
        role: 'user',
        job: '',
        department: 'Other',
        birthdate: ''
      });
    } else if (type === 'edit' && user) {
      setUserData({
        name: user.name || '',
        email: user.email || '',
        password: '', // Empty password field - will only be updated if user enters a new one
        role: user.role || 'user',
        job: user.job || '',
        department: user.department || 'Other',
        birthdate: user.birthdate ? formatDate(user.birthdate) : ''
      });
      setSelectedUser(user);
    } else if (type === 'view' && user) {
      setSelectedUser(user);
    }

    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setDialogType(null);
    setSelectedUser(null);
  };

  // Format date for display
  const formatDate = (isoString) => {
    if (!isoString) return '';
    return new Date(isoString).toISOString().split('T')[0];
  };

  const handleDeleteUser = (id) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      api.delete(`/admin/users/delete/${id}`)
        .then(() => {
          toast.success('User deleted successfully!');
          fetchUsers(); // Refresh the user list
        })
        .catch(error => {
          console.error('Error deleting user:', error);
          toast.error('Failed to delete user: ' + (error.response?.data?.message || 'Unknown error'));
        });
    }
  };

  const handleUserFormSubmit = async (e) => {
    e.preventDefault();

    // Validate form data
    if (!userData.name || !userData.email || (!selectedUser && !userData.password) || !userData.role || !userData.job) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Validate name (at least 3 characters)
    if (userData.name.trim().length < 3) {
      toast.error('Name must be at least 3 characters');
      return;
    }

    // Validate job (at least 3 characters)
    if (userData.job.trim().length < 3) {
      toast.error('Job must be at least 3 characters');
      return;
    }

    // Validate password (at least 8 characters for new users or when changing password)
    if (userData.password && userData.password.trim() !== '' && userData.password.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    // Validate age (must be at least 18)
    if (userData.birthdate) {
      const birthDate = new Date(userData.birthdate);
      const today = new Date();
      const minDate = new Date();
      minDate.setFullYear(today.getFullYear() - 18);

      if (birthDate > minDate) {
        toast.error('User must be at least 18 years old');
        return;
      }
    }

    try {
      if (selectedUser) {
        // Update existing user
        await api.put(`/admin/users/update/${selectedUser._id}`, userData);
        toast.success('User updated successfully!');
      } else {
        // Create new user
        await api.post('/admin/users/create', userData);
        toast.success('User created successfully!');
      }

      // Refresh the user list
      fetchUsers();
      handleCloseDialog();
    } catch (error) {
      console.error('Error saving user:', error);

      // Display specific error message if available
      if (error.response?.data?.errors) {
        // Handle validation errors
        const validationErrors = error.response.data.errors;
        validationErrors.forEach(err => {
          toast.error(err.msg);
        });
      } else if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Failed to save user. Please try again.');
      }
    }
  };

  // Menu items for the sidebar
  const menuItems = [
    {
      text: 'Dashboard',
      icon: <DashboardIcon />,
      onClick: () => setActiveSection('dashboard'),
      active: activeSection === 'dashboard',
    },
    {
      text: 'User Management',
      icon: <PeopleIcon />,
      onClick: () => setActiveSection('users'),
      active: activeSection === 'users',
    },
    {
      text: 'User & Access Oversight',
      icon: <SecurityIcon />,
      items: [
        {
          text: 'Account Management',
          icon: <PersonIcon />,
          onClick: () => setActiveSection('account-management'),
          active: activeSection === 'account-management',
        },
        {
          text: 'Audit Trail',
          icon: <HistoryIcon />,
          onClick: () => setActiveSection('audit-trail'),
          active: activeSection === 'audit-trail',
        },
        {
          text: 'Login History',
          icon: <CalendarTodayIcon />,
          onClick: () => setActiveSection('login-history'),
          active: activeSection === 'login-history',
        },
      ],
    },
    {
      text: 'Analytics & Reporting',
      icon: <AssessmentIcon />,
      items: [
        {
          text: 'Dashboard Analytics',
          icon: <BarChartIcon />,
          onClick: () => setActiveSection('analytics'),
          active: activeSection === 'analytics',
        },
        {
          text: 'Report Generator',
          icon: <DescriptionIcon />,
          onClick: () => setActiveSection('report-generator'),
          active: activeSection === 'report-generator',
        },
      ],
    },

  ];

  // User table columns
  const userColumns = [
    { id: 'name', label: 'Name', minWidth: 150 },
    { id: 'email', label: 'Email', minWidth: 200 },
    { id: 'role', label: 'Role', minWidth: 100,
      render: (value) => (
        <Box
          sx={{
            display: 'inline-block',
            px: 1,
            py: 0.5,
            borderRadius: 1,
            backgroundColor:
              value === 'admin' ? theme.palette.error.light :
              value === 'hr' ? theme.palette.warning.light :
              theme.palette.info.light,
            color:
              value === 'admin' ? theme.palette.error.dark :
              value === 'hr' ? theme.palette.warning.dark :
              theme.palette.info.dark,
            fontWeight: 500,
            fontSize: '0.75rem',
            textTransform: 'capitalize',
          }}
        >
          {value || 'user'}
        </Box>
      )
    },
    { id: 'job', label: 'Job Title', minWidth: 150 },
    { id: 'department', label: 'Department', minWidth: 150 },
    {
      id: 'birthdate',
      label: 'Birthdate',
      minWidth: 120,
      render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
    {
      id: 'creationDate',
      label: 'Created On',
      minWidth: 120,
      render: (value) => value ? new Date(value).toLocaleDateString() : 'N/A'
    },
  ];

  // User table actions
  const userActions = [
    {
      name: 'View User',
      icon: <VisibilityIcon fontSize="small" />,
    },
    {
      name: 'Edit User',
      icon: <EditIcon fontSize="small" />,
    },
    {
      name: 'Delete User',
      icon: <DeleteIcon fontSize="small" />,
    },
  ];

  // Calculate stats
  const stats = {
    totalUsers: users.length,
    adminUsers: users.filter(user => user.role === 'admin').length,
    hrUsers: users.filter(user => user.role === 'hr').length,
    normalUsers: users.filter(user => user.role === 'user').length,
  };

  // Render the dashboard content based on the active section
  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <Box>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
              Admin Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Welcome to the Admin Dashboard. Here's an overview of your system.
            </Typography>

            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <StatCard
                  title="Total Users"
                  value={stats.totalUsers}
                  icon={<PeopleIcon style={{ fontSize: 80 }} />}
                  color="primary"
                  subtitle="All users in the system"
                  onClick={() => setActiveSection('users')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatCard
                  title="Admin Users"
                  value={stats.adminUsers}
                  icon={<SupervisorAccountIcon style={{ fontSize: 80 }} />}
                  color="error"
                  subtitle="Users with admin privileges"
                  onClick={() => setActiveSection('users')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatCard
                  title="HR Users"
                  value={stats.hrUsers}
                  icon={<WorkIcon style={{ fontSize: 80 }} />}
                  color="warning"
                  subtitle="Users with HR role"
                  onClick={() => setActiveSection('users')}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <StatCard
                  title="Normal Users"
                  value={stats.normalUsers}
                  icon={<PersonIcon style={{ fontSize: 80 }} />}
                  color="info"
                  subtitle="Regular employees"
                  onClick={() => setActiveSection('users')}
                />
              </Grid>
            </Grid>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h5" gutterBottom fontWeight={600}>
                Recent Users
              </Typography>
              <DataTable
                columns={userColumns}
                data={users.slice(0, 5).map((user, index) => ({
                  ...user,
                  id: user._id || index,
                }))}
                actions={userActions}
                onActionClick={(action, row) => {
                  if (action.name === 'View User') {
                    handleOpenDialog('view', row);
                  } else if (action.name === 'Edit User') {
                    handleOpenDialog('edit', row);
                  } else if (action.name === 'Delete User') {
                    handleDeleteUser(row._id);
                  }
                }}
                pagination={false}
                onRowClick={(row) => handleOpenDialog('view', row)}
                emptyMessage="No users found"
              />
              <Box sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => setActiveSection('users')}
                >
                  View All Users
                </Button>
              </Box>
            </Box>
          </Box>
        );

      case 'users':
        return (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <div>
                <Typography variant="h4" component="h1" gutterBottom fontWeight={700}>
                  User Management
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Manage all users in the system
                </Typography>
              </div>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog('add')}
              >
                Add User
              </Button>
            </Box>

            <DataTable
              title="All Users"
              columns={userColumns}
              data={users.map((user, index) => ({
                ...user,
                id: user._id || index,
              }))}
              actions={userActions}
              onActionClick={(action, row) => {
                if (action.name === 'View User') {
                  handleOpenDialog('view', row);
                } else if (action.name === 'Edit User') {
                  handleOpenDialog('edit', row);
                } else if (action.name === 'Delete User') {
                  handleDeleteUser(row._id);
                }
              }}
              searchable={true}
              onSearch={handleUserSearch}
              searchQuery={userSearchQuery}
              onRowClick={(row) => handleOpenDialog('view', row)}
              emptyMessage="No users found"
              loading={loading}
            />
          </Box>
        );

      case 'account-management':
        return <UserAccountManagement />;

      case 'audit-trail':
        return <AuditTrail />;

      case 'login-history':
        return <LoginHistory />;

      case 'analytics':
        return <DashboardAnalytics />;

      case 'report-generator':
        return <ReportGenerator />;

      default:
        return (
          <Box sx={{ textAlign: 'center', py: 5 }}>
            <Typography variant="h5" color="text.secondary">
              Section under development
            </Typography>
          </Box>
        );
    }
  };

  // Show loading or nothing while checking authentication
  if (authChecking) {
    return null; // Return nothing while checking authentication to prevent flash
  }

  return (
    <>
      <DashboardLayout
        title={
          activeSection === 'dashboard' ? 'Admin Dashboard' :
          activeSection === 'users' ? 'User Management' :
          activeSection === 'account-management' ? 'User Account Management' :
          activeSection === 'audit-trail' ? 'Audit Trail' :
          activeSection === 'login-history' ? 'Login History' :
          activeSection === 'analytics' ? 'Dashboard Analytics' :
          activeSection === 'report-generator' ? 'Report Generator' :
          'Admin Dashboard'
        }
        menuItems={menuItems}
        userName="Admin"
        userRole="System Administrator"
        onLogout={handleLogout}
      >

        {renderContent()}
      </DashboardLayout>

      {/* User Form Dialog */}
      <Dialog
        open={openDialog && (dialogType === 'add' || dialogType === 'edit')}
        onClose={handleCloseDialog}
        fullWidth
        maxWidth="md"
      >
        <DialogTitle>
          {dialogType === 'add' ? 'Add New User' : 'Edit User'}
        </DialogTitle>
        <DialogContent>
          <form id="user-form" onSubmit={handleUserFormSubmit}>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Name"
                  fullWidth
                  required
                  value={userData.name}
                  onChange={(e) => setUserData({ ...userData, name: e.target.value })}
                  helperText="Full name of the user"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Email"
                  type="email"
                  fullWidth
                  required
                  value={userData.email}
                  onChange={(e) => setUserData({ ...userData, email: e.target.value })}
                  helperText="Email address (used for login)"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Password"
                  type="password"
                  fullWidth
                  required={dialogType === 'add'}
                  value={userData.password}
                  onChange={(e) => setUserData({ ...userData, password: e.target.value })}
                  helperText={dialogType === 'edit' ? "Leave blank to keep current password" : "Minimum 8 characters"}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Role</InputLabel>
                  <Select
                    value={userData.role}
                    onChange={(e) => setUserData({ ...userData, role: e.target.value })}
                    label="Role"
                  >
                    <MenuItem value="admin">Admin</MenuItem>
                    <MenuItem value="hr">HR</MenuItem>
                    <MenuItem value="user">User</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Job Title"
                  fullWidth
                  required
                  value={userData.job}
                  onChange={(e) => setUserData({ ...userData, job: e.target.value })}
                  helperText="User's job position"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Department</InputLabel>
                  <Select
                    value={userData.department}
                    onChange={(e) => setUserData({ ...userData, department: e.target.value })}
                    label="Department"
                  >
                    <MenuItem value="Engineering">Engineering</MenuItem>
                    <MenuItem value="Marketing">Marketing</MenuItem>
                    <MenuItem value="Sales">Sales</MenuItem>
                    <MenuItem value="HR">HR</MenuItem>
                    <MenuItem value="Finance">Finance</MenuItem>
                    <MenuItem value="Operations">Operations</MenuItem>
                    <MenuItem value="IT">IT</MenuItem>
                    <MenuItem value="Customer Support">Customer Support</MenuItem>
                    <MenuItem value="Executive">Executive</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </Select>
                  <FormHelperText>User's department</FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Birthdate"
                  type="date"
                  fullWidth
                  required
                  InputLabelProps={{ shrink: true }}
                  value={userData.birthdate}
                  onChange={(e) => setUserData({ ...userData, birthdate: e.target.value })}
                  helperText="User must be at least 18 years old"
                />
              </Grid>
            </Grid>
          </form>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="inherit">Cancel</Button>
          <Button type="submit" form="user-form" color="primary" variant="contained">
            {dialogType === 'add' ? 'Create User' : 'Update User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* User View Dialog */}
      <Dialog
        open={openDialog && dialogType === 'view'}
        onClose={handleCloseDialog}
        fullWidth
        maxWidth="sm"
      >
        {selectedUser && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar
                  sx={{
                    bgcolor:
                      selectedUser.role === 'admin' ? theme.palette.error.main :
                      selectedUser.role === 'hr' ? theme.palette.warning.main :
                      theme.palette.primary.main,
                    width: 56,
                    height: 56
                  }}
                >
                  {selectedUser.name ? selectedUser.name.charAt(0).toUpperCase() : 'U'}
                </Avatar>
                <Box>
                  <Typography variant="h6">{selectedUser.name}</Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ textTransform: 'capitalize' }}>
                    {selectedUser.role} • {selectedUser.job} • {selectedUser.department || 'No Department'}
                  </Typography>
                </Box>
              </Box>
              <IconButton
                aria-label="close"
                onClick={handleCloseDialog}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500],
                }}
              >
                <CloseIcon />
              </IconButton>
            </DialogTitle>
            <DialogContent>
              <Box sx={{ py: 1 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                    <Typography variant="body1">{selectedUser.email}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">Role</Typography>
                    <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>{selectedUser.role}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">Job Title</Typography>
                    <Typography variant="body1">{selectedUser.job}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">Department</Typography>
                    <Typography variant="body1">{selectedUser.department || 'Not specified'}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">Birthdate</Typography>
                    <Typography variant="body1">
                      {selectedUser.birthdate ? new Date(selectedUser.birthdate).toLocaleDateString() : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="text.secondary">Account Created</Typography>
                    <Typography variant="body1">
                      {selectedUser.creationDate ? new Date(selectedUser.creationDate).toLocaleDateString() : 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button
                startIcon={<EditIcon />}
                onClick={() => {
                  handleCloseDialog();
                  handleOpenDialog('edit', selectedUser);
                }}
              >
                Edit
              </Button>
              <Button
                color="error"
                startIcon={<DeleteIcon />}
                onClick={() => {
                  handleCloseDialog();
                  handleDeleteUser(selectedUser._id);
                }}
              >
                Delete
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
};

export default NewAdminDashboard;
