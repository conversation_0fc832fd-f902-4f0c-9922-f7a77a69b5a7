import axios from "axios";
import { navigate } from "../Utils/navigation";

// Create an Axios instance
const api = axios.create({
  baseURL: "http://localhost:5000", // Using HTTP with the standard port
  headers: {
    "Content-Type": "application/json",
  }
});

// Helper function to ensure URL has the correct prefix
const ensureApiPrefix = (url) => {
  // If the URL already starts with /api, return it as is
  if (url.startsWith('/api/')) {
    return url;
  }

  // Otherwise, add the /api prefix
  return `/api${url.startsWith('/') ? '' : '/'}${url}`;
};

// Request interceptor to add JWT token and ensure correct URL prefix
api.interceptors.request.use(
  (config) => {
    // Add token to request headers
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Ensure URL has the correct prefix
    if (config.url && !config.url.includes('http')) {
      config.url = ensureApiPrefix(config.url);
    }

    console.log(`API Request: ${config.method.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
    (response) => {
      // Log successful responses for debugging
      console.log(`API Success [${response.config.method.toUpperCase()}] ${response.config.url}:`, response.status);
      return response;
    },
    (error) => {
      // Enhanced error logging
      console.log('API Error:', {
        url: error.config?.url,
        method: error.config?.method?.toUpperCase(),
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      // Check if this is a login request
      const isLoginRequest = error.config?.url === '/auth/login' ||
                            error.config?.url === '/auth/user/login' ||
                            error.config?.url.includes('login');

      // Only redirect for 401 errors on non-login requests
      if (error.response && error.response.status === 401 && !isLoginRequest) {
        console.log("Unauthorized access, redirecting to login");
        localStorage.removeItem("token");
        navigate("/");
      }

      // Add more detailed error information to the error object
      if (error.response) {
        error.friendlyMessage = error.response.data?.message ||
                               error.response.data?.error ||
                               `Server error: ${error.response.status}`;
      } else if (error.request) {
        error.friendlyMessage = 'No response received from server. Please check your network connection.';
      } else {
        error.friendlyMessage = error.message || 'An unknown error occurred';
      }

      return Promise.reject(error);
    }
  );

export default api;
