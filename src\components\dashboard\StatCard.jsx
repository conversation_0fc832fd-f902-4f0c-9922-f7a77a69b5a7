import React from 'react';
import { <PERSON>, Card, CardContent, Typography, IconButton, Tooltip } from '@mui/material';
import { styled } from '@mui/material/styles';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

const StyledCard = styled(Card)(({ theme, color = 'primary' }) => {
  const getGradient = (colorName) => {
    const colors = {
      primary: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
      secondary: `linear-gradient(135deg, ${theme.palette.secondary.light} 0%, ${theme.palette.secondary.main} 100%)`,
      success: `linear-gradient(135deg, ${theme.palette.success.light} 0%, ${theme.palette.success.main} 100%)`,
      error: `linear-gradient(135deg, ${theme.palette.error.light} 0%, ${theme.palette.error.main} 100%)`,
      warning: `linear-gradient(135deg, ${theme.palette.warning.light} 0%, ${theme.palette.warning.main} 100%)`,
      info: `linear-gradient(135deg, ${theme.palette.info.light} 0%, ${theme.palette.info.main} 100%)`,
      grey: `linear-gradient(135deg, ${theme.palette.grey[300]} 0%, ${theme.palette.grey[500]} 100%)`,
    };
    return colors[colorName] || colors.primary;
  };

  return {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: theme.shape.borderRadius * 2,
    boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      background: getGradient(color),
      opacity: 0.85,
      zIndex: 0,
    },
    '& .MuiCardContent-root': {
      position: 'relative',
      zIndex: 1,
      color: theme.palette.common.white,
    },
  };
});

const IconWrapper = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: -15,
  right: -15,
  opacity: 0.2,
  fontSize: 120,
  transform: 'rotate(15deg)',
  zIndex: 0,
}));

const StatCard = ({ 
  title, 
  value, 
  icon, 
  color = 'primary', 
  subtitle, 
  tooltip,
  trend,
  trendValue,
  onClick
}) => {
  return (
    <StyledCard color={color} onClick={onClick} sx={{ cursor: onClick ? 'pointer' : 'default' }}>
      <CardContent sx={{ position: 'relative', p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" component="div" sx={{ fontWeight: 500, mb: 0.5 }}>
            {title}
          </Typography>
          {tooltip && (
            <Tooltip title={tooltip} arrow placement="top">
              <IconButton size="small" sx={{ color: 'white', opacity: 0.7 }}>
                <InfoOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
        
        <Typography variant="h3" component="div" sx={{ fontWeight: 700, mb: 1 }}>
          {value}
        </Typography>
        
        {subtitle && (
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            {subtitle}
          </Typography>
        )}
        
        {trend && (
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            mt: 1.5,
            backgroundColor: 'rgba(255, 255, 255, 0.15)',
            borderRadius: 1,
            px: 1,
            py: 0.5,
            width: 'fit-content'
          }}>
            {trend.icon}
            <Typography variant="caption" sx={{ ml: 0.5, fontWeight: 500 }}>
              {trendValue}
            </Typography>
          </Box>
        )}
        
        <IconWrapper>
          {icon}
        </IconWrapper>
      </CardContent>
    </StyledCard>
  );
};

export default StatCard;
