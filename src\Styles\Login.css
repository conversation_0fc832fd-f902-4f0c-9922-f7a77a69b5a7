/* Background Gradient */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(to right, #2193b0, #6dd5ed);
  font-family: 'Roboto', sans-serif;
  padding: 0 15px;
}

/* Login Card */
.login-card {
  width: 100%;
  max-width: 450px;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
  background: white;
  /* Remove hover effect */
}

/* Login Form */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.login-form .MuiTextField-root {
  margin-bottom: 15px;
}

/* Form Input Fields */
.login-form input {
  font-size: 16px;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ddd;
  outline: none;
}

/* Login Button */
.login-btn {
  padding: 12px;
  background-color: #2193b0;
  color: white;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  width: 100%;
}

/* Hover Effect for Button */
.login-btn:hover {
  background-color: #1a7d9b;
}

/* Responsive Styles */
@media (max-width: 480px) {
  .login-card {
    width: 100%;
    padding: 20px;
  }
  .login-btn {
    font-size: 14px;
  }
}
