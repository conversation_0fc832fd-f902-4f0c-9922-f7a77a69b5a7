const mongoose = require('mongoose');

// AI Insight Schema for storing AI-generated insights and recommendations
const aiInsightSchema = new mongoose.Schema({
  // User this insight is for
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Type of insight
  type: {
    type: String,
    enum: [
      'leave_suggestion',
      'task_optimization',
      'burnout_warning',
      'performance_insight',
      'workload_analysis',
      'skill_recommendation',
      'career_guidance',
      'policy_reminder',
      'deadline_alert',
      'wellness_tip'
    ],
    required: true
  },

  // Insight category for grouping
  category: {
    type: String,
    enum: ['productivity', 'wellness', 'career', 'compliance', 'performance'],
    required: true
  },

  // Priority level
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },

  // Insight title
  title: {
    type: String,
    required: true
  },

  // Detailed insight content
  content: {
    type: String,
    required: true
  },

  // Actionable recommendations
  recommendations: [{
    action: {
      type: String,
      required: true
    },
    description: String,
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    estimatedImpact: String,
    deadline: Date
  }],

  // Data sources used for generating insight
  dataSources: [{
    type: {
      type: String,
      enum: ['tasks', 'attendance', 'evaluations', 'leave_requests', 'applications', 'user_profile'],
      required: true
    },
    resourceId: mongoose.Schema.Types.ObjectId,
    dataRange: {
      startDate: Date,
      endDate: Date
    },
    relevanceScore: {
      type: Number,
      min: 0,
      max: 1
    }
  }],

  // AI model and generation metadata
  aiMetadata: {
    model: {
      type: String,
      required: true
    },
    version: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    processingTime: Number, // in milliseconds
    tokens: {
      input: Number,
      output: Number,
      total: Number
    },
    temperature: Number,
    prompt: String // Store the prompt used for generation
  },

  // Insight metrics and analytics
  metrics: {
    // Accuracy metrics (if feedback is available)
    accuracy: {
      type: Number,
      min: 0,
      max: 1
    },
    
    // User engagement
    views: {
      type: Number,
      default: 0
    },
    
    // Time spent reading
    readTime: {
      type: Number,
      default: 0
    },
    
    // Actions taken based on insight
    actionsTaken: [{
      action: String,
      timestamp: Date,
      result: String
    }]
  },

  // User feedback
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    helpful: Boolean,
    comment: String,
    timestamp: Date
  },

  // Insight status
  status: {
    type: String,
    enum: ['active', 'acknowledged', 'acted_upon', 'dismissed', 'expired'],
    default: 'active'
  },

  // Visibility and targeting
  visibility: {
    // Who can see this insight
    audience: {
      type: String,
      enum: ['user', 'hr', 'manager', 'admin'],
      default: 'user'
    },
    
    // Auto-dismiss conditions
    expiresAt: Date,
    
    // Conditions for showing insight
    showConditions: {
      minRole: String,
      departments: [String],
      tags: [String]
    }
  },

  // Related resources
  relatedResources: [{
    type: {
      type: String,
      enum: ['task', 'leave_request', 'evaluation', 'application', 'document', 'policy']
    },
    resourceId: mongoose.Schema.Types.ObjectId,
    relationship: String // How this resource relates to the insight
  }],

  // Tags for categorization and search
  tags: [String],

  // Insight scheduling
  schedule: {
    // For recurring insights
    isRecurring: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly']
    },
    nextGeneration: Date
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
aiInsightSchema.index({ userId: 1, createdAt: -1 });
aiInsightSchema.index({ type: 1, category: 1 });
aiInsightSchema.index({ priority: 1, status: 1 });
aiInsightSchema.index({ 'visibility.audience': 1 });
aiInsightSchema.index({ tags: 1 });
aiInsightSchema.index({ 'visibility.expiresAt': 1 });

// Virtual for checking if insight is expired
aiInsightSchema.virtual('isExpired').get(function() {
  return this.visibility.expiresAt && this.visibility.expiresAt < new Date();
});

// Virtual for checking if insight is recent
aiInsightSchema.virtual('isRecent').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diff = now - created;
  return diff < 24 * 60 * 60 * 1000; // 24 hours
});

// Method to mark insight as viewed
aiInsightSchema.methods.markAsViewed = function(readTime = 0) {
  this.metrics.views += 1;
  this.metrics.readTime += readTime;
  return this.save();
};

// Method to add user feedback
aiInsightSchema.methods.addFeedback = function(feedbackData) {
  this.feedback = {
    ...feedbackData,
    timestamp: new Date()
  };
  return this.save();
};

// Method to record action taken
aiInsightSchema.methods.recordAction = function(action, result = '') {
  this.metrics.actionsTaken.push({
    action,
    result,
    timestamp: new Date()
  });
  
  if (this.status === 'active') {
    this.status = 'acted_upon';
  }
  
  return this.save();
};

// Method to dismiss insight
aiInsightSchema.methods.dismiss = function() {
  this.status = 'dismissed';
  return this.save();
};

// Static method to get active insights for user
aiInsightSchema.statics.getActiveInsights = function(userId, options = {}) {
  const query = {
    userId,
    status: 'active',
    $or: [
      { 'visibility.expiresAt': { $exists: false } },
      { 'visibility.expiresAt': { $gt: new Date() } }
    ]
  };

  if (options.category) {
    query.category = options.category;
  }

  if (options.priority) {
    query.priority = options.priority;
  }

  return this.find(query)
    .sort({ priority: -1, createdAt: -1 })
    .limit(options.limit || 10);
};

// Static method to get insights by type
aiInsightSchema.statics.getInsightsByType = function(userId, type, limit = 5) {
  return this.find({
    userId,
    type,
    status: { $in: ['active', 'acknowledged'] }
  })
  .sort({ createdAt: -1 })
  .limit(limit);
};

// Static method to get performance analytics
aiInsightSchema.statics.getAnalytics = function(userId, dateRange = {}) {
  const matchStage = { userId };
  
  if (dateRange.startDate || dateRange.endDate) {
    matchStage.createdAt = {};
    if (dateRange.startDate) matchStage.createdAt.$gte = dateRange.startDate;
    if (dateRange.endDate) matchStage.createdAt.$lte = dateRange.endDate;
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        avgRating: { $avg: '$feedback.rating' },
        totalViews: { $sum: '$metrics.views' },
        actionRate: {
          $avg: {
            $cond: [
              { $gt: [{ $size: '$metrics.actionsTaken' }, 0] },
              1,
              0
            ]
          }
        }
      }
    }
  ]);
};

// Pre-save middleware to auto-expire insights
aiInsightSchema.pre('save', function(next) {
  if (this.isExpired && this.status === 'active') {
    this.status = 'expired';
  }
  next();
});

const AIInsight = mongoose.model('AIInsight', aiInsightSchema);

module.exports = AIInsight;
