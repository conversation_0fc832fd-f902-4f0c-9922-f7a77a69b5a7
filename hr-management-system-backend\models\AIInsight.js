const mongoose = require('mongoose');

// AI Insight Schema for storing AI-generated insights and recommendations
const aiInsightSchema = new mongoose.Schema({
  // User this insight is for
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },

  // Type of insight
  type: {
    type: String,
    enum: ['proactive_suggestion', 'reactive_suggestion', 'pattern_alert', 'recommendation'],
    required: true,
    default: 'proactive_suggestion'
  },

  // Insight category for grouping
  category: {
    type: String,
    enum: [
      'user_management',
      'recruitment',
      'workflow',
      'leave_management',
      'productivity',
      'task_management',
      'analytics',
      'performance',
      'attendance'
    ],
    required: true,
    index: true
  },

  // Priority level
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },

  // Insight title
  title: {
    type: String,
    required: true
  },

  // Detailed insight content
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },

  // Action type for the suggestion
  actionType: {
    type: String,
    required: true,
    enum: [
      'review_evaluations',
      'improve_job_posting',
      'set_attendance_reminder',
      'investigate_leave_patterns',
      'optimize_workflow',
      'expedite_assignments',
      'update_user_profiles',
      'check_task_progress',
      'review_applications'
    ]
  },

  // Confidence score
  confidence: {
    type: Number,
    min: 0,
    max: 1,
    required: true,
    default: 0.5
  },

  // Metadata for additional context
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },

  // Actionable recommendations
  recommendations: [{
    action: {
      type: String,
      required: true
    },
    description: String,
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    estimatedImpact: String,
    deadline: Date
  }],

  // Data sources used for generating insight
  dataSources: [{
    type: {
      type: String,
      enum: ['tasks', 'attendance', 'evaluations', 'leave_requests', 'applications', 'user_profile'],
      required: true
    },
    resourceId: mongoose.Schema.Types.ObjectId,
    dataRange: {
      startDate: Date,
      endDate: Date
    },
    relevanceScore: {
      type: Number,
      min: 0,
      max: 1
    }
  }],

  // AI model and generation metadata
  aiMetadata: {
    model: {
      type: String,
      required: true
    },
    version: String,
    confidence: {
      type: Number,
      min: 0,
      max: 1
    },
    processingTime: Number, // in milliseconds
    tokens: {
      input: Number,
      output: Number,
      total: Number
    },
    temperature: Number,
    prompt: String // Store the prompt used for generation
  },

  // Insight metrics and analytics
  metrics: {
    // Accuracy metrics (if feedback is available)
    accuracy: {
      type: Number,
      min: 0,
      max: 1
    },
    
    // User engagement
    views: {
      type: Number,
      default: 0
    },
    
    // Time spent reading
    readTime: {
      type: Number,
      default: 0
    },
    
    // Actions taken based on insight
    actionsTaken: [{
      action: String,
      timestamp: Date,
      result: String
    }]
  },

  // User feedback array
  feedback: [{
    rating: {
      type: String,
      enum: ['up', 'down'],
      required: true
    },
    comment: {
      type: String,
      default: ''
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],

  // Insight status
  status: {
    type: String,
    enum: ['active', 'viewed', 'acted_upon', 'dismissed', 'expired'],
    default: 'active',
    index: true
  },

  // Tracking fields
  viewedAt: {
    type: Date
  },
  viewDuration: {
    type: Number, // in milliseconds
    default: 0
  },
  actedUponAt: {
    type: Date
  },
  dismissedAt: {
    type: Date
  },
  expiresAt: {
    type: Date,
    index: { expireAfterSeconds: 0 }
  },

  // Visibility and targeting
  visibility: {
    // Who can see this insight
    audience: {
      type: String,
      enum: ['user', 'hr', 'manager', 'admin'],
      default: 'user'
    },
    
    // Auto-dismiss conditions
    expiresAt: Date,
    
    // Conditions for showing insight
    showConditions: {
      minRole: String,
      departments: [String],
      tags: [String]
    }
  },

  // Related resources
  relatedResources: [{
    type: {
      type: String,
      enum: ['task', 'leave_request', 'evaluation', 'application', 'document', 'policy']
    },
    resourceId: mongoose.Schema.Types.ObjectId,
    relationship: String // How this resource relates to the insight
  }],

  // Tags for categorization and search
  tags: [String],

  // Insight scheduling
  schedule: {
    // For recurring insights
    isRecurring: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'quarterly']
    },
    nextGeneration: Date
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
aiInsightSchema.index({ userId: 1, createdAt: -1 });
aiInsightSchema.index({ type: 1, category: 1 });
aiInsightSchema.index({ priority: 1, status: 1 });
aiInsightSchema.index({ 'visibility.audience': 1 });
aiInsightSchema.index({ tags: 1 });
aiInsightSchema.index({ 'visibility.expiresAt': 1 });

// Virtual for checking if insight is expired
aiInsightSchema.virtual('isExpired').get(function() {
  return this.visibility.expiresAt && this.visibility.expiresAt < new Date();
});

// Virtual for checking if insight is recent
aiInsightSchema.virtual('isRecent').get(function() {
  const now = new Date();
  const created = new Date(this.createdAt);
  const diff = now - created;
  return diff < 24 * 60 * 60 * 1000; // 24 hours
});

// Method to mark insight as viewed
aiInsightSchema.methods.markAsViewed = function(readTime = 0) {
  this.status = 'viewed';
  this.viewedAt = new Date();
  this.viewDuration = readTime;
  this.metrics.views += 1;
  this.metrics.readTime += readTime;
  return this.save();
};

// Method to add user feedback
aiInsightSchema.methods.addFeedback = function(feedbackData) {
  this.feedback.push(feedbackData);

  // Update status based on feedback
  if (feedbackData.rating === 'up') {
    this.status = 'acted_upon';
    this.actedUponAt = new Date();
  } else if (feedbackData.rating === 'down') {
    this.status = 'dismissed';
    this.dismissedAt = new Date();
  }

  return this.save();
};

// Method to record action taken
aiInsightSchema.methods.recordAction = function(action, result = '') {
  this.metrics.actionsTaken.push({
    action,
    result,
    timestamp: new Date()
  });

  if (this.status === 'active') {
    this.status = 'acted_upon';
    this.actedUponAt = new Date();
  }

  return this.save();
};

// Method to dismiss insight
aiInsightSchema.methods.dismiss = function() {
  this.status = 'dismissed';
  this.dismissedAt = new Date();
  return this.save();
};

// Method to set expiration
aiInsightSchema.methods.setExpiration = function(hours = 24) {
  this.expiresAt = new Date(Date.now() + hours * 60 * 60 * 1000);
  return this.save();
};

// Static method to get active insights for user
aiInsightSchema.statics.getActiveInsights = function(userId, options = {}) {
  const query = {
    userId,
    status: 'active',
    $or: [
      { 'visibility.expiresAt': { $exists: false } },
      { 'visibility.expiresAt': { $gt: new Date() } }
    ]
  };

  if (options.category) {
    query.category = options.category;
  }

  if (options.priority) {
    query.priority = options.priority;
  }

  return this.find(query)
    .sort({ priority: -1, createdAt: -1 })
    .limit(options.limit || 10);
};

// Static method to get insights by type
aiInsightSchema.statics.getInsightsByType = function(userId, type, limit = 5) {
  return this.find({
    userId,
    type,
    status: { $in: ['active', 'acknowledged'] }
  })
  .sort({ createdAt: -1 })
  .limit(limit);
};

// Static method to get performance analytics
aiInsightSchema.statics.getAnalytics = function(userId, dateRange = {}) {
  const matchStage = { userId };
  
  if (dateRange.startDate || dateRange.endDate) {
    matchStage.createdAt = {};
    if (dateRange.startDate) matchStage.createdAt.$gte = dateRange.startDate;
    if (dateRange.endDate) matchStage.createdAt.$lte = dateRange.endDate;
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        avgRating: { $avg: '$feedback.rating' },
        totalViews: { $sum: '$metrics.views' },
        actionRate: {
          $avg: {
            $cond: [
              { $gt: [{ $size: '$metrics.actionsTaken' }, 0] },
              1,
              0
            ]
          }
        }
      }
    }
  ]);
};

// Additional static methods
aiInsightSchema.statics.findActiveForUser = function(userId, options = {}) {
  const query = { userId, status: 'active' };

  if (options.category) {
    query.category = options.category;
  }

  if (options.priority) {
    query.priority = options.priority;
  }

  return this.find(query)
    .sort({ priority: -1, createdAt: -1 })
    .limit(options.limit || 10);
};

aiInsightSchema.statics.findByCategory = function(userId, category, options = {}) {
  return this.find({ userId, category })
    .sort({ createdAt: -1 })
    .limit(options.limit || 20);
};

aiInsightSchema.statics.getStatistics = function(userId, timeRange = 30) {
  const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);

  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgConfidence: { $avg: '$confidence' },
        categories: { $addToSet: '$category' }
      }
    }
  ]);
};

aiInsightSchema.statics.cleanupExpired = function() {
  return this.deleteMany({
    $or: [
      { expiresAt: { $lte: new Date() } },
      {
        status: 'dismissed',
        dismissedAt: { $lte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // 7 days old
      }
    ]
  });
};

aiInsightSchema.statics.findSimilar = function(userId, actionType, timeWindow = 24) {
  const startTime = new Date(Date.now() - timeWindow * 60 * 60 * 1000);

  return this.find({
    userId,
    actionType,
    createdAt: { $gte: startTime },
    status: { $in: ['active', 'viewed'] }
  });
};

// Virtual for feedback summary
aiInsightSchema.virtual('feedbackSummary').get(function() {
  const upvotes = this.feedback.filter(f => f.rating === 'up').length;
  const downvotes = this.feedback.filter(f => f.rating === 'down').length;
  return {
    upvotes,
    downvotes,
    total: upvotes + downvotes,
    ratio: upvotes + downvotes > 0 ? upvotes / (upvotes + downvotes) : 0
  };
});

// Virtual for age in hours
aiInsightSchema.virtual('ageInHours').get(function() {
  return Math.floor((Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60));
});

// Pre-save middleware to auto-expire insights and set defaults
aiInsightSchema.pre('save', function(next) {
  // Set default expiration if not set
  if (!this.expiresAt) {
    const hoursToExpire = this.priority === 'high' ? 48 :
                         this.priority === 'medium' ? 24 : 12;
    this.expiresAt = new Date(Date.now() + hoursToExpire * 60 * 60 * 1000);
  }

  // Auto-expire if needed
  if (this.isExpired && this.status === 'active') {
    this.status = 'expired';
  }
  next();
});

// Post-save middleware for logging
aiInsightSchema.post('save', function(doc) {
  console.log(`💡 AI Insight ${doc.isNew ? 'created' : 'updated'}: ${doc.title} (${doc.category})`);
});

const AIInsight = mongoose.model('AIInsight', aiInsightSchema);

module.exports = AIInsight;
