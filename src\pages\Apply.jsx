import React, { useState, useEffect } from "react";
import axios from "axios";
import "../styles/apply.css";
import { Alert, Box, Paper, Typography } from "@mui/material";

const Apply = () => {
  const [jobs, setJobs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedJob, setSelectedJob] = useState(null);

  const [formData, setFormData] = useState({
    fullname: "",
    email: "",
    position: "",
    cv: null,
    phone: "",
    jobId: "",
    jobTitle: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [applicationSuccess, setApplicationSuccess] = useState(false);
  const [applicationId, setApplicationId] = useState("");

  // Fetch available jobs when component mounts
  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        const response = await axios.get("http://localhost:5000/api/applications/available-jobs");
        setJobs(response.data);
        setError(null);
      } catch (err) {
        console.error("Error fetching jobs:", err);
        setError("Failed to load available jobs. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, []);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    if (name === "cv") {
      setFormData({ ...formData, cv: files[0] });
    } else if (name === "jobId") {
      // When job is selected, find the job details and update position and jobTitle
      const selectedJob = jobs.find(job => job._id === value);
      if (selectedJob) {
        setSelectedJob(selectedJob);
        setFormData({
          ...formData,
          jobId: value,
          jobTitle: selectedJob.title,
          position: selectedJob.jobType // Use the job type as the position
        });
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validation
    if (!formData.fullname || !formData.email || !formData.phone || !formData.cv || !formData.jobId) {
      alert("Please fill in all fields and upload your CV.");
      setIsSubmitting(false);
      return;
    }

    const data = new FormData();
    data.append("fullname", formData.fullname);
    data.append("email", formData.email);
    data.append("position", formData.position);
    data.append("phone", formData.phone);
    data.append("cv", formData.cv);
    data.append("jobId", formData.jobId);
    data.append("jobTitle", formData.jobTitle);

    try {
      const response = await axios.post("http://localhost:5000/api/applications", data, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      // Set success state and store application ID
      setApplicationSuccess(true);
      console.log('Application response:', response.data);
      if (response.data.application && response.data.application.id) {
        setApplicationId(response.data.application.id);
      } else {
        setApplicationId('Application ID not available');
        console.error('Application ID not found in response:', response.data);
      }

      // Reset form
      setFormData({
        fullname: "",
        email: "",
        position: "",
        cv: null,
        phone: "",
        jobId: "",
        jobTitle: "",
      });
      setSelectedJob(null);
    } catch (err) {
      console.error("Error submitting application:", err);
      alert(err.response?.data?.message || "Error submitting application");
      setApplicationSuccess(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="apply-container">
      <h2>Job Application</h2>

      {applicationSuccess ? (
        <Paper elevation={3} sx={{ p: 3, maxWidth: 600, mx: 'auto', mt: 4, borderRadius: 2 }}>
          <Alert severity="success" sx={{ mb: 2 }}>
            Your application has been submitted successfully!
          </Alert>

          <Typography variant="h6" gutterBottom>
            Application ID
          </Typography>

          <Box
            sx={{
              p: 2,
              border: '1px dashed #ccc',
              borderRadius: 1,
              bgcolor: '#f9f9f9',
              textAlign: 'center',
              mb: 3
            }}
          >
            <Typography variant="h5" fontWeight="bold" color="primary">
              {applicationId}
            </Typography>
          </Box>

          <Typography variant="body2" color="text.secondary" paragraph>
            Please save this application ID. You can use it to track the status of your application
            on our website's application tracking section.
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <button
              onClick={() => {
                setApplicationSuccess(false);
                setApplicationId('');
              }}
              className="apply-button"
            >
              Submit Another Application
            </button>
          </Box>
        </Paper>
      ) : loading ? (
        <p>Loading available jobs...</p>
      ) : error ? (
        <p className="error-message">{error}</p>
      ) : (
        <>
          {jobs.length === 0 ? (
            <p>No jobs are currently available. Please check back later.</p>
          ) : (
            <form onSubmit={handleSubmit} className="apply-form">
              <div className="form-group">
                <label htmlFor="jobId">Select a Job Position:</label>
                <select
                  id="jobId"
                  name="jobId"
                  value={formData.jobId}
                  onChange={handleChange}
                  required
                >
                  <option value="">-- Select a job --</option>
                  {jobs.map((job) => (
                    <option key={job._id} value={job._id}>
                      {job.title} - {job.jobType} ({job.location})
                    </option>
                  ))}
                </select>
              </div>

              {selectedJob && (
                <div className="job-details">
                  <h3>{selectedJob.title}</h3>
                  <p><strong>Type:</strong> {selectedJob.jobType}</p>
                  <p><strong>Location:</strong> {selectedJob.location}</p>
                  {selectedJob.endDate && (
                    <p><strong>Application Deadline:</strong> {new Date(selectedJob.endDate).toLocaleDateString()}</p>
                  )}
                  <p><strong>Description:</strong> {selectedJob.description}</p>
                </div>
              )}

              <input
                type="text"
                name="fullname"
                placeholder="Full Name"
                value={formData.fullname}
                onChange={handleChange}
                required
              />
              <input
                type="email"
                name="email"
                placeholder="Email Address"
                value={formData.email}
                onChange={handleChange}
                required
              />
              <input
                type="tel"
                name="phone"
                placeholder="Phone Number"
                value={formData.phone}
                onChange={handleChange}
                required
              />
              <div className="file-input">
                <label htmlFor="cv">Upload CV (PDF, DOC, DOCX):</label>
                <input
                  type="file"
                  id="cv"
                  name="cv"
                  accept=".pdf,.doc,.docx"
                  onChange={handleChange}
                  required
                />
              </div>
              <button type="submit" disabled={isSubmitting || !selectedJob} className="apply-button">
                {isSubmitting ? "Submitting..." : "Submit Application"}
              </button>
            </form>
          )}
        </>
      )}
    </div>
  );
};

export default Apply;
