import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5174, // Explicitly set the server port
    hmr: {
      // Explicitly set the HMR protocol to ws
      protocol: 'ws',
      // Set the host to localhost
      host: 'localhost',
      // Ensure the port is the same as your Vite server
      port: 5174
    },
    // Ensure the server is accessible from all network interfaces
    host: true
  },
  // Resolve React version conflicts
  resolve: {
    dedupe: ['react', 'react-dom', '@emotion/react', '@emotion/styled']
  }
})
