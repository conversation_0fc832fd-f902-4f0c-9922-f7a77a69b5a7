import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000, // Use a standard port
    hmr: {
      // Explicitly set the HMR protocol to ws
      protocol: 'ws',
      // Set the host to localhost
      host: 'localhost',
      // Ensure the port is the same as your Vite server
      port: 3000
    },
    // Ensure the server is accessible from all network interfaces
    host: true,
    // Add proxy for API calls to avoid CORS issues
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        secure: false
      }
    }
  },
  // Resolve React version conflicts
  resolve: {
    dedupe: ['react', 'react-dom', '@emotion/react', '@emotion/styled']
  }
})
