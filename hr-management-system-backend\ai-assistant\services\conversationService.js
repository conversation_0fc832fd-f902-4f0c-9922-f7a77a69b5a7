const Conversation = require('../../models/Conversation');
const { v4: uuidv4 } = require('uuid');

class ConversationService {
  constructor() {
    this.activeConversations = new Map(); // In-memory cache for active conversations
  }

  /**
   * Create a new conversation
   * @param {string} userId - User ID
   * @param {string} initialMessage - First message content
   * @param {string} domain - Conversation domain
   * @returns {Promise<Object>} - Created conversation
   */
  async createConversation(userId, initialMessage = null, domain = 'general') {
    try {
      const conversation = new Conversation({
        userId,
        title: 'New Conversation',
        context: {
          domain,
          state: 'idle',
          currentIntent: null,
          trackedEntities: {},
          formData: {}
        }
      });

      // Add initial message if provided
      if (initialMessage) {
        conversation.addMessage({
          role: 'user',
          content: initialMessage,
          type: 'text'
        });
      }

      await conversation.save();
      
      // Cache the conversation
      this.activeConversations.set(conversation._id.toString(), conversation);
      
      return conversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  /**
   * Get conversation by ID
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID for security
   * @returns {Promise<Object>} - Conversation object
   */
  async getConversation(conversationId, userId) {
    try {
      // Check cache first
      let conversation = this.activeConversations.get(conversationId);
      
      if (!conversation) {
        // Load from database
        conversation = await Conversation.findOne({
          _id: conversationId,
          userId
        });
        
        if (conversation) {
          this.activeConversations.set(conversationId, conversation);
        }
      }

      return conversation;
    } catch (error) {
      console.error('Error getting conversation:', error);
      throw error;
    }
  }

  /**
   * Add message to conversation
   * @param {string} conversationId - Conversation ID
   * @param {Object} messageData - Message data
   * @returns {Promise<Object>} - Updated conversation
   */
  async addMessage(conversationId, messageData) {
    try {
      const conversation = await this.getConversation(conversationId, messageData.userId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // Add message
      const message = conversation.addMessage(messageData);
      
      // Save to database
      await conversation.save();
      
      // Update cache
      this.activeConversations.set(conversationId, conversation);
      
      return { conversation, message };
    } catch (error) {
      console.error('Error adding message:', error);
      throw error;
    }
  }

  /**
   * Update conversation context
   * @param {string} conversationId - Conversation ID
   * @param {Object} contextUpdate - Context updates
   * @returns {Promise<Object>} - Updated conversation
   */
  async updateContext(conversationId, contextUpdate) {
    try {
      const conversation = this.activeConversations.get(conversationId);
      
      if (!conversation) {
        throw new Error('Conversation not found in cache');
      }

      conversation.updateContext(contextUpdate);
      await conversation.save();
      
      return conversation;
    } catch (error) {
      console.error('Error updating context:', error);
      throw error;
    }
  }

  /**
   * Get user's conversations
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} - List of conversations
   */
  async getUserConversations(userId, options = {}) {
    try {
      const {
        status = 'active',
        domain = null,
        limit = 10,
        offset = 0
      } = options;

      const query = { userId, status };
      
      if (domain) {
        query['context.domain'] = domain;
      }

      const conversations = await Conversation.find(query)
        .sort({ 'context.lastActivity': -1 })
        .limit(limit)
        .skip(offset)
        .select('title context.domain context.lastActivity messageCount createdAt');

      return conversations;
    } catch (error) {
      console.error('Error getting user conversations:', error);
      throw error;
    }
  }

  /**
   * Search conversations
   * @param {string} userId - User ID
   * @param {string} query - Search query
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Search results
   */
  async searchConversations(userId, query, options = {}) {
    try {
      return await Conversation.searchConversations(userId, query, options);
    } catch (error) {
      console.error('Error searching conversations:', error);
      throw error;
    }
  }

  /**
   * Archive conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Archived conversation
   */
  async archiveConversation(conversationId, userId) {
    try {
      const conversation = await this.getConversation(conversationId, userId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      await conversation.archive();
      
      // Remove from cache
      this.activeConversations.delete(conversationId);
      
      return conversation;
    } catch (error) {
      console.error('Error archiving conversation:', error);
      throw error;
    }
  }

  /**
   * Delete conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Success status
   */
  async deleteConversation(conversationId, userId) {
    try {
      const result = await Conversation.findOneAndUpdate(
        { _id: conversationId, userId },
        { status: 'deleted' },
        { new: true }
      );

      if (result) {
        // Remove from cache
        this.activeConversations.delete(conversationId);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  }

  /**
   * Get conversation analytics
   * @param {string} userId - User ID
   * @param {Object} dateRange - Date range for analytics
   * @returns {Promise<Object>} - Analytics data
   */
  async getAnalytics(userId, dateRange = {}) {
    try {
      const matchStage = { userId, status: { $ne: 'deleted' } };
      
      if (dateRange.startDate || dateRange.endDate) {
        matchStage.createdAt = {};
        if (dateRange.startDate) matchStage.createdAt.$gte = dateRange.startDate;
        if (dateRange.endDate) matchStage.createdAt.$lte = dateRange.endDate;
      }

      const analytics = await Conversation.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$context.domain',
            conversationCount: { $sum: 1 },
            totalMessages: { $sum: { $size: '$messages' } },
            avgMessagesPerConversation: { $avg: { $size: '$messages' } },
            avgDuration: { $avg: '$duration' }
          }
        },
        { $sort: { conversationCount: -1 } }
      ]);

      return analytics;
    } catch (error) {
      console.error('Error getting analytics:', error);
      throw error;
    }
  }

  /**
   * Clean up inactive conversations from cache
   * @param {number} maxAge - Maximum age in milliseconds
   */
  cleanupCache(maxAge = 30 * 60 * 1000) { // 30 minutes default
    const now = Date.now();
    
    for (const [conversationId, conversation] of this.activeConversations.entries()) {
      const lastActivity = new Date(conversation.context.lastActivity).getTime();
      
      if (now - lastActivity > maxAge) {
        this.activeConversations.delete(conversationId);
      }
    }
  }

  /**
   * Get conversation summary for long conversations
   * @param {string} conversationId - Conversation ID
   * @returns {Promise<string>} - Conversation summary
   */
  async getConversationSummary(conversationId) {
    try {
      const conversation = await Conversation.findById(conversationId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // If summary exists, return it
      if (conversation.summary) {
        return conversation.summary;
      }

      // Generate summary for conversations with more than 10 messages
      if (conversation.messages.length > 10) {
        const userMessages = conversation.messages
          .filter(m => m.role === 'user')
          .map(m => m.content)
          .slice(0, 5); // Take first 5 user messages

        const summary = `Conversation about: ${userMessages.join(', ')}`.substring(0, 200);
        
        // Save summary
        conversation.summary = summary;
        await conversation.save();
        
        return summary;
      }

      return 'Short conversation - no summary needed';
    } catch (error) {
      console.error('Error getting conversation summary:', error);
      throw error;
    }
  }

  /**
   * Export conversation data
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @param {string} format - Export format ('json', 'text')
   * @returns {Promise<Object>} - Exported data
   */
  async exportConversation(conversationId, userId, format = 'json') {
    try {
      const conversation = await this.getConversation(conversationId, userId);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      if (format === 'text') {
        const textExport = conversation.messages
          .map(m => `[${m.timestamp.toISOString()}] ${m.role.toUpperCase()}: ${m.content}`)
          .join('\n');
        
        return {
          format: 'text',
          content: textExport,
          filename: `conversation_${conversationId}_${Date.now()}.txt`
        };
      }

      // Default JSON export
      return {
        format: 'json',
        content: conversation.toJSON(),
        filename: `conversation_${conversationId}_${Date.now()}.json`
      };
    } catch (error) {
      console.error('Error exporting conversation:', error);
      throw error;
    }
  }
}

// Singleton instance
const conversationService = new ConversationService();

// Cleanup cache every 15 minutes
setInterval(() => {
  conversationService.cleanupCache();
}, 15 * 60 * 1000);

module.exports = conversationService;
