# HR Management System - Deployment Diagram

## System Architecture Overview

This deployment diagram illustrates the complete infrastructure and component deployment for the HR Management System, showing how different services, databases, and external integrations are deployed and interconnected.

```plantuml
@startuml HR Management System - Deployment Diagram

!define RECTANGLE class
!define COMPONENT component
!define DATABASE database
!define CLOUD cloud
!define NODE node

skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam databaseStyle rectangle
skinparam cloudStyle rectangle

' Client Tier
node "Client Devices" as ClientTier {
  COMPONENT [Web Browser] as Browser
  COMPONENT [Mobile Browser] as MobileBrowser
}

' Load Balancer / Reverse Proxy (Optional)
node "Load Balancer" as Lo<PERSON><PERSON>alan<PERSON> {
  COMPONENT [Nginx/Apache] as WebServer
}

' Application Server Tier
node "Application Server" as AppServer {
  
  ' Frontend Application
  RECTANGLE "Frontend Application" as Frontend {
    COMPONENT [React.js App] as ReactApp
    COMPONENT [Vite Build] as ViteBuild
    COMPONENT [Material-UI Components] as MUIComponents
    COMPONENT [Socket.io Client] as SocketClient
    COMPONENT [React Router] as ReactRouter
    COMPONENT [Framer Motion] as FramerMotion
  }
  
  ' Backend API Server
  RECTANGLE "Backend API Server" as Backend {
    COMPONENT [Node.js Runtime] as NodeJS
    COMPONENT [Express.js Framework] as Express
    COMPONENT [Socket.io Server] as SocketServer
    COMPONENT [JWT Authentication] as JWTAuth
    COMPONENT [Multer File Upload] as FileUpload
    COMPONENT [CORS Middleware] as CORS
  }
  
  ' API Routes & Controllers
  RECTANGLE "API Layer" as APILayer {
    COMPONENT [Auth Routes] as AuthRoutes
    COMPONENT [Admin Routes] as AdminRoutes
    COMPONENT [HR Routes] as HRRoutes
    COMPONENT [User Routes] as UserRoutes
    COMPONENT [Job Routes] as JobRoutes
    COMPONENT [Application Routes] as AppRoutes
    COMPONENT [Leave Routes] as LeaveRoutes
    COMPONENT [Task Routes] as TaskRoutes
    COMPONENT [Report Routes] as ReportRoutes
    COMPONENT [Notification Routes] as NotificationRoutes
  }
}

' AI/ML Processing Server
node "AI/ML Processing Server" as AIServer {
  
  ' AI Assistant Services
  RECTANGLE "AI Assistant (Jarvis)" as AIAssistant {
    COMPONENT [OpenAI Service] as OpenAIService
    COMPONENT [Intent Classifier] as IntentClassifier
    COMPONENT [Conversation Service] as ConversationService
    COMPONENT [Context Aware Service] as ContextService
    COMPONENT [Intelligent Agent Core] as AgentCore
  }
  
  ' NLP Processing Services
  RECTANGLE "NLP Processing" as NLPProcessing {
    COMPONENT [Python Runtime] as PythonRuntime
    COMPONENT [Flair NLP Parser] as FlairParser
    COMPONENT [CV Parser Service] as CVParser
    COMPONENT [Natural Language Toolkit] as NLTK
    COMPONENT [Document Embeddings] as DocEmbeddings
  }
  
  ' Vector Database
  RECTANGLE "Vector Storage" as VectorDB {
    COMPONENT [ChromaDB] as ChromaDB
    COMPONENT [Document Vectors] as DocVectors
    COMPONENT [Semantic Search] as SemanticSearch
  }
}

' Database Tier
node "Database Server" as DatabaseServer {
  
  DATABASE "MongoDB Instance" as MongoDB {
    [Users Collection] as UsersDB
    [Jobs Collection] as JobsDB
    [Applications Collection] as ApplicationsDB
    [Leave Requests Collection] as LeaveDB
    [Tasks Collection] as TasksDB
    [Notifications Collection] as NotificationsDB
    [Reports Collection] as ReportsDB
    [Conversations Collection] as ConversationsDB
    [AI Insights Collection] as AIInsightsDB
    [Knowledge Base Collection] as KnowledgeBaseDB
    [Attendance Collection] as AttendanceDB
    [Evaluations Collection] as EvaluationsDB
    [GEK Estimates Collection] as GEKDB
    [Audit Logs Collection] as AuditDB
    [Login History Collection] as LoginHistoryDB
  }
}

' File Storage
node "File Storage Server" as FileServer {
  RECTANGLE "File System" as FileSystem {
    COMPONENT [CV Uploads] as CVUploads
    COMPONENT [Document Storage] as DocStorage
    COMPONENT [Report Files] as ReportFiles
    COMPONENT [Static Assets] as StaticAssets
  }
}

' External Services
CLOUD "External Services" as ExternalServices {
  
  CLOUD "AI/ML Services" as AIServices {
    [OpenAI API] as OpenAI
    [OpenRouter API] as OpenRouter
    [Hugging Face] as HuggingFace
  }
  
  CLOUD "Email Services" as EmailServices {
    [EmailJS] as EmailJS
    [Nodemailer SMTP] as NodemailerSMTP
    [Gmail SMTP] as GmailSMTP
  }
  
  CLOUD "Monitoring & Analytics" as MonitoringServices {
    [Application Monitoring] as AppMonitoring
    [Error Tracking] as ErrorTracking
    [Performance Analytics] as PerfAnalytics
  }
}

' Network Connections
Browser --> WebServer : HTTPS/HTTP
MobileBrowser --> WebServer : HTTPS/HTTP
WebServer --> ReactApp : Static Files
WebServer --> Express : API Requests

ReactApp --> Express : REST API Calls
SocketClient --> SocketServer : WebSocket Connection
ReactRouter --> ReactApp : Client-side Routing
MUIComponents --> ReactApp : UI Components
FramerMotion --> ReactApp : Animations

Express --> APILayer : Route Handling
APILayer --> Backend : Business Logic
JWTAuth --> Express : Authentication
FileUpload --> Express : File Processing
CORS --> Express : Cross-Origin Requests

Backend --> MongoDB : Database Operations
Backend --> AIAssistant : AI Processing
Backend --> NLPProcessing : NLP Tasks
Backend --> FileSystem : File Operations

OpenAIService --> OpenAI : API Calls
OpenAIService --> OpenRouter : API Calls
FlairParser --> PythonRuntime : NLP Processing
CVParser --> FlairParser : CV Analysis
ChromaDB --> VectorDB : Vector Storage

MongoDB --> UsersDB : User Data
MongoDB --> JobsDB : Job Postings
MongoDB --> ApplicationsDB : Applications
MongoDB --> LeaveDB : Leave Requests
MongoDB --> TasksDB : Task Management
MongoDB --> NotificationsDB : Notifications
MongoDB --> ReportsDB : Reports
MongoDB --> ConversationsDB : Chat History
MongoDB --> AIInsightsDB : AI Analytics
MongoDB --> KnowledgeBaseDB : Knowledge Base
MongoDB --> AttendanceDB : Attendance Records
MongoDB --> EvaluationsDB : Performance Data
MongoDB --> GEKDB : GEK Estimates
MongoDB --> AuditDB : Audit Trails
MongoDB --> LoginHistoryDB : Login History

Backend --> EmailJS : Email Notifications
Backend --> NodemailerSMTP : Email Sending
NodemailerSMTP --> GmailSMTP : SMTP Relay

' Deployment Notes
note right of AppServer
  **Deployment Environment:**
  - Node.js v18+
  - PM2 Process Manager
  - Environment Variables
  - SSL/TLS Certificates
  - Port: 5000 (Backend)
  - Port: 3000 (Frontend Dev)
end note

note right of AIServer
  **AI/ML Requirements:**
  - Python 3.8+
  - Flair NLP Library
  - OpenAI API Key
  - ChromaDB Instance
  - GPU Support (Optional)
end note

note right of DatabaseServer
  **Database Configuration:**
  - MongoDB 6.0+
  - Replica Set (Production)
  - Backup Strategy
  - Indexing Optimization
  - Connection: mongodb://localhost:27017/hr
end note

note right of FileServer
  **Storage Configuration:**
  - Local File System
  - Upload Directory: /uploads
  - File Size Limits
  - Security Scanning
  - Backup Strategy
end note

@enduml
```

## Deployment Architecture Details

### 1. **Client Tier**
- **Web Browsers**: Modern browsers supporting ES6+, WebSocket, and File API
- **Mobile Browsers**: Responsive design supporting mobile devices
- **Requirements**: JavaScript enabled, modern browser features

### 2. **Application Server Tier**

#### Frontend Application
- **Technology**: React.js 19.0.0 with Vite build system
- **UI Framework**: Material-UI (MUI) 6.4.11
- **Routing**: React Router DOM 7.5.0
- **Real-time**: Socket.io Client 4.8.1
- **Animations**: Framer Motion 12.9.2
- **Build Output**: Static files served by web server

#### Backend API Server
- **Runtime**: Node.js with Express.js 4.21.2
- **Authentication**: JWT-based authentication
- **File Upload**: Multer for handling file uploads
- **Real-time**: Socket.io Server 4.8.1
- **Security**: CORS, input validation, rate limiting

### 3. **AI/ML Processing Server**

#### AI Assistant (Jarvis)
- **Primary AI**: OpenAI GPT-4.1 via OpenRouter
- **Capabilities**: Natural language processing, intent classification, context awareness
- **Services**: Conversation management, intelligent responses, system knowledge

#### NLP Processing
- **Python Runtime**: Python 3.8+ with Flair NLP
- **CV Processing**: Advanced CV parsing and job matching
- **Text Analysis**: Named entity recognition, sentiment analysis
- **Embeddings**: Document vectorization for semantic search

### 4. **Database Tier**
- **Primary Database**: MongoDB 6.14.1
- **Collections**: 15+ specialized collections for different data types
- **Features**: Indexing, aggregation pipelines, change streams
- **Connection**: Local instance at mongodb://localhost:27017/hr

### 5. **File Storage**
- **Type**: Local file system storage
- **Location**: /uploads directory
- **Supported Formats**: PDF, DOC, DOCX, images
- **Security**: File type validation, size limits, virus scanning

### 6. **External Services**

#### AI/ML Services
- **OpenAI API**: GPT-4.1 model access
- **OpenRouter**: Alternative AI model access
- **Hugging Face**: Additional NLP models and embeddings

#### Email Services
- **EmailJS**: Client-side email functionality
- **Nodemailer**: Server-side email sending
- **SMTP**: Gmail and custom SMTP support

## Deployment Requirements

### System Requirements
- **Operating System**: Linux/Windows/macOS
- **Node.js**: Version 18.0 or higher
- **Python**: Version 3.8 or higher
- **MongoDB**: Version 6.0 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: Minimum 10GB free space
- **Network**: Internet connectivity for external APIs

### Environment Variables
```bash
# Database
MONGODB_URI=mongodb://localhost:27017/hr

# AI Services
OPENAI_API_KEY=sk-proj-...
OPENAI_MODEL=openai/gpt-4.1
ENABLE_OPENAI=true

# Server Configuration
PORT=5000
NODE_ENV=production
JWT_SECRET=your-jwt-secret

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### Security Considerations
- **HTTPS**: SSL/TLS encryption for all communications
- **Authentication**: JWT-based with secure token storage
- **Authorization**: Role-based access control (Admin, HR, User)
- **File Security**: Upload validation and virus scanning
- **Database Security**: Connection encryption and access controls
- **API Security**: Rate limiting and input validation

### Scalability Options
- **Horizontal Scaling**: Multiple application server instances
- **Load Balancing**: Nginx/Apache reverse proxy
- **Database Clustering**: MongoDB replica sets
- **CDN**: Static asset delivery optimization
- **Caching**: Redis for session and data caching
- **Microservices**: Service decomposition for larger deployments

This deployment diagram provides a comprehensive view of the HR Management System's architecture, showing how all components work together to deliver a complete, AI-powered human resources management solution.
