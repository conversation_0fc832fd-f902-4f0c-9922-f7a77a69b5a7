const jwt = require('jsonwebtoken');
const User = require('../models/user');
const AuditLog = require('../models/AuditLog');

// Middleware to authenticate JWT
const authenticate = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const ipAddress = req.ip || req.connection.remoteAddress || 'Unknown';
  const userAgentString = req.headers['user-agent'] || 'Unknown';

  console.log('Auth header:', authHeader ? 'Present' : 'Missing');

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.status(401).json({ success: false, message: "Unauthorized: No token provided" });
  }

  const token = authHeader.split(" ")[1];
  console.log('Token received:', token ? token.substring(0, 20) + '...' : 'None');

  try {
    const decoded = jwt.verify(token, 'yourSecretKey'); // use your real secret in prod
    console.log('Decoded token:', decoded);

    // Accept any valid token format
    const userId = decoded.id || decoded._id || decoded.userId;

    if (!userId) {
      console.error('Token missing user ID');
      return res.status(401).json({ success: false, message: "Unauthorized: Invalid token format" });
    }

    // Check if user exists and is active
    const user = await User.findById(userId);

    if (!user) {
      console.error('User not found in database');
      return res.status(401).json({ success: false, message: "Unauthorized: User not found" });
    }

    if (!user.active) {
      console.error('User account is deactivated');

      // Log the access attempt by inactive user
      try {
        await new AuditLog({
          userId: userId,
          userInfo: {
            name: user.name,
            email: user.email,
            role: user.role
          },
          action: 'USER_ACCESS_DENIED',
          resourceType: 'USER',
          resourceId: userId,
          description: `Access denied for inactive user: ${user.name} (${user.email})`,
          ipAddress,
          userAgent: userAgentString
        }).save();
      } catch (logError) {
        console.error('Error logging access attempt by inactive user:', logError);
      }

      return res.status(403).json({
        success: false,
        message: "Forbidden: Account is deactivated. Please contact an administrator."
      });
    }

    if (user.accountLocked && user.accountLockedUntil && user.accountLockedUntil > new Date()) {
      console.error('User account is locked');

      // Log the access attempt by locked user
      try {
        await new AuditLog({
          userId: userId,
          userInfo: {
            name: user.name,
            email: user.email,
            role: user.role
          },
          action: 'USER_ACCESS_DENIED',
          resourceType: 'USER',
          resourceId: userId,
          description: `Access denied for locked user: ${user.name} (${user.email})`,
          ipAddress,
          userAgent: userAgentString
        }).save();
      } catch (logError) {
        console.error('Error logging access attempt by locked user:', logError);
      }

      // Calculate time remaining in lock
      const timeRemaining = Math.ceil((user.accountLockedUntil - new Date()) / (1000 * 60)); // in minutes

      return res.status(403).json({
        success: false,
        message: `Forbidden: Account is locked. Please try again in ${timeRemaining} minutes.`
      });
    }

    // Attach full user info to request
    req.user = {
      id: userId,
      email: user.email,
      role: user.role,
      name: user.name,
      active: user.active
    };

    console.log('User authenticated:', req.user);
    next();
  } catch (error) {
    console.error('Token verification error:', error.message);
    return res.status(401).json({ success: false, message: "Unauthorized: Invalid token" });
  }
};
const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    console.log('Authorizing roles:', roles);
    console.log('User role:', req.user ? req.user.role : 'No user');

    if (!req.user) {
      console.error('No user object in request');
      return res.status(401).json({ success: false, message: "Unauthorized: Authentication required" });
    }

    // For normal user routes, allow any authenticated user
    if (roles.includes('user') && req.user.role) {
      console.log('User authenticated with role:', req.user.role);
      console.log('Authorization successful for normal user route');
      next();
      return;
    }

    // For admin and HR routes, check specific roles
    if (!roles.includes(req.user.role)) {
      console.error(`Access denied: User role ${req.user.role} not in allowed roles [${roles.join(', ')}]`);
      return res.status(403).json({
        success: false,
        message: "Forbidden: Access denied",
        details: `Required role: ${roles.join(' or ')}, your role: ${req.user.role}`
      });
    }

    console.log('Authorization successful for role:', req.user.role);
    next();
  };
};

module.exports = { authenticate  , authorizeRoles  };
