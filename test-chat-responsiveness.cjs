const axios = require('axios');

async function testChatResponsiveness() {
  try {
    console.log('🔍 Testing Chat Responsiveness...\n');

    // Test 1: Check if server is running (skip health check)
    console.log('1. Testing server connection...');
    console.log('✅ Proceeding to login test');

    // Test 2: Login
    console.log('\n2. Testing login...');
    let token;
    try {
      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
        email: '<EMAIL>',
        password: 'test123'
      });
      token = loginResponse.data.token;
      console.log('✅ Login successful');
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data?.message || error.message);
      return;
    }

    // Test 3: Simple chat message
    console.log('\n3. Testing simple chat message...');
    try {
      const chatResponse = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'Hello'
      }, {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        },
        timeout: 10000 // 10 second timeout
      });

      console.log('✅ Chat response received');
      console.log('📝 Response:', chatResponse.data.data.assistantMessage.content.substring(0, 100) + '...');
      console.log('🎯 Intent:', chatResponse.data.data.classification.intent);
      console.log('⚡ Response time:', chatResponse.data.data.assistantMessage.metadata?.responseTime || 'N/A', 'ms');
    } catch (error) {
      console.log('❌ Chat failed:', error.response?.data?.message || error.message);
      if (error.code === 'ECONNABORTED') {
        console.log('⏰ Request timed out - server may be slow or unresponsive');
      }
      return;
    }

    // Test 4: Attendance check-in
    console.log('\n4. Testing attendance check-in...');
    try {
      const attendanceResponse = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'I want to check in for work'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });

      console.log('✅ Attendance check-in response received');
      console.log('📝 Response:', attendanceResponse.data.data.assistantMessage.content.substring(0, 100) + '...');
      console.log('🎯 Intent:', attendanceResponse.data.data.classification.intent);
    } catch (error) {
      console.log('❌ Attendance check-in failed:', error.response?.data?.message || error.message);
    }

    // Test 5: Leave balance
    console.log('\n5. Testing leave balance...');
    try {
      const leaveResponse = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'What is my leave balance?'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });

      console.log('✅ Leave balance response received');
      console.log('📝 Response:', leaveResponse.data.data.assistantMessage.content.substring(0, 100) + '...');
      console.log('🎯 Intent:', leaveResponse.data.data.classification.intent);
    } catch (error) {
      console.log('❌ Leave balance failed:', error.response?.data?.message || error.message);
    }

    // Test 6: Task list
    console.log('\n6. Testing task list...');
    try {
      const taskResponse = await axios.post('http://localhost:5000/api/ai/chat/message', {
        message: 'Show me my tasks'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });

      console.log('✅ Task list response received');
      console.log('📝 Response:', taskResponse.data.data.assistantMessage.content.substring(0, 100) + '...');
      console.log('🎯 Intent:', taskResponse.data.data.classification.intent);
    } catch (error) {
      console.log('❌ Task list failed:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 Chat Responsiveness Test Complete!');
    console.log('\n📊 Summary:');
    console.log('✅ Server is running and responsive');
    console.log('✅ Authentication is working');
    console.log('✅ Chat AI is responding');
    console.log('✅ Intent classification is working');
    console.log('✅ Multiple features tested successfully');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

testChatResponsiveness();
