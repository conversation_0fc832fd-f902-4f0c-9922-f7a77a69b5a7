/* Table styles for HR Dashboard */
.table-container {
  overflow-x: auto;
  max-width: 100%;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  table-layout: auto;
}

th, td {
  padding: 8px;
  border: 1px solid #ddd;
  text-align: left;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

th {
  background-color: #ecf0f1;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Special column widths */
table tr th:nth-child(5),
table tr td:nth-child(5),
table tr th:nth-child(6),
table tr td:nth-child(6),
table tr th:nth-child(7),
table tr td:nth-child(7) {
  max-width: 100px;
}

/* Reason column can be wider */
table tr th:nth-child(7),
table tr td:nth-child(7) {
  max-width: 200px;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
}

.status-pending {
  background-color: #f39c12;
}

.status-approved {
  background-color: #2ecc71;
}

.status-rejected {
  background-color: #e74c3c;
}
