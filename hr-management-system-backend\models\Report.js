const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  // Report metadata
  title: {
    type: String,
    required: true,
    trim: true
  },
  reportType: {
    type: String,
    required: true,
    enum: ['nlp', 'attendance', 'user', 'job', 'leave', 'task'],
    default: 'nlp'
  },

  // Who created the report
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // Make optional for backward compatibility
  },

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },

  // Additional timestamp field for compatibility with frontend
  timestamp: {
    type: String,
    default: () => new Date().toISOString()
  },

  // The actual report data (stored as JSON)
  reportData: {
    type: Object,
    required: true
  },

  // For NLP reports, store reference to the application
  applicationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Application',
    required: false
  }
});

// Create a compound index for efficient querying
reportSchema.index({ reportType: 1, createdAt: -1 });

const Report = mongoose.model('Report', reportSchema);

module.exports = Report;
