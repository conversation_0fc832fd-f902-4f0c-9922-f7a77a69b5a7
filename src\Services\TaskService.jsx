import api from './ApiService';

/**
 * Task Service - Provides methods to interact with the Task API
 */
class TaskService {
  /**
   * Get all tasks for HR
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Tasks data
   */
  async getHRTasks(params = {}) {
    try {
      const response = await api.get('/tasks/hr', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting HR tasks:', error);
      throw error;
    }
  }

  /**
   * Get all tasks for a user
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - Tasks data
   */
  async getUserTasks(params = {}) {
    try {
      const response = await api.get('/tasks/user', { params });
      return response.data;
    } catch (error) {
      console.error('Error getting user tasks:', error);
      throw error;
    }
  }

  /**
   * Get a task by ID
   * @param {string} taskId - Task ID
   * @returns {Promise<Object>} - Task data
   */
  async getTask(taskId) {
    try {
      const response = await api.get(`/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting task:', error);
      throw error;
    }
  }

  /**
   * Create a new task
   * @param {Object} taskData - Task data
   * @returns {Promise<Object>} - Created task data
   */
  async createTask(taskData) {
    try {
      const response = await api.post('/tasks', taskData);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      throw error;
    }
  }

  /**
   * Update a task
   * @param {string} taskId - Task ID
   * @param {Object} taskData - Updated task data
   * @returns {Promise<Object>} - Updated task data
   */
  async updateTask(taskId, taskData) {
    try {
      const response = await api.put(`/tasks/${taskId}`, taskData);
      return response.data;
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }

  /**
   * Delete a task
   * @param {string} taskId - Task ID
   * @returns {Promise<Object>} - Response data
   */
  async deleteTask(taskId) {
    try {
      const response = await api.delete(`/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }

  /**
   * Update task status
   * @param {string} taskId - Task ID
   * @param {string} status - New status
   * @returns {Promise<Object>} - Updated task data
   */
  async updateTaskStatus(taskId, status) {
    try {
      const response = await api.patch(`/tasks/${taskId}/status`, { status });
      return response.data;
    } catch (error) {
      console.error('Error updating task status:', error);
      throw error;
    }
  }

  /**
   * Get task statistics
   * @returns {Promise<Object>} - Task statistics data
   */
  async getTaskStats() {
    try {
      const response = await api.get('/tasks/stats');
      return response.data;
    } catch (error) {
      console.error('Error getting task statistics:', error);
      throw error;
    }
  }
}

export default new TaskService();
